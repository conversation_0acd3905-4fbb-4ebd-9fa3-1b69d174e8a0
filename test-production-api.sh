#!/bin/bash

# 生产API功能验证脚本
# 测试主要API端点的可用性和数据完整性

echo "🧪 Quotese生产API功能验证"
echo "================================"

# API端点配置
PROD_API_BASE="https://api.quotese.com/api"
PROD_GRAPHQL="https://api.quotese.com/graphql"
LOCAL_FRONTEND="http://localhost:8083"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "🔍 测试: $test_name ... "
    
    # 执行测试命令
    local result=$(eval "$test_command" 2>/dev/null)
    local exit_code=$?
    
    # 检查结果
    if [ $exit_code -eq 0 ] && [[ "$result" =~ $expected_pattern ]]; then
        echo -e "${GREEN}✅ 通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        echo "   预期: $expected_pattern"
        echo "   实际: $result"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 测试HTTP状态码
test_http_status() {
    local url="$1"
    local test_name="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "🌐 HTTP测试: $test_name ... "
    
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$url")
    
    if [ "$status_code" = "200" ]; then
        echo -e "${GREEN}✅ 通过 (HTTP $status_code)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}❌ 失败 (HTTP $status_code)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 测试JSON响应
test_json_response() {
    local url="$1"
    local test_name="$2"
    local min_count="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "📊 JSON测试: $test_name ... "
    
    local response=$(curl -s --max-time 10 "$url")
    local count=$(echo "$response" | jq '. | length' 2>/dev/null)
    
    if [ "$count" -ge "$min_count" ] 2>/dev/null; then
        echo -e "${GREEN}✅ 通过 (获取到 $count 条数据)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}❌ 失败 (数据量不足: $count < $min_count)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 测试GraphQL查询
test_graphql_query() {
    local query="$1"
    local test_name="$2"
    local expected_field="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "🔗 GraphQL测试: $test_name ... "
    
    local response=$(curl -s --max-time 10 \
        -H "Content-Type: application/json" \
        -d "{\"query\":\"$query\"}" \
        "$PROD_GRAPHQL")
    
    if echo "$response" | jq -e ".data.$expected_field" >/dev/null 2>&1; then
        local count=$(echo "$response" | jq ".data.$expected_field | length" 2>/dev/null)
        echo -e "${GREEN}✅ 通过 (获取到 $count 条数据)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        echo "   响应: $(echo "$response" | head -c 100)..."
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

echo "📡 开始测试生产API端点..."
echo ""

# 1. 基础连接测试
echo -e "${BLUE}=== 基础连接测试 ===${NC}"
test_http_status "$PROD_API_BASE/" "API根端点"
test_http_status "$PROD_GRAPHQL" "GraphQL端点"
echo ""

# 2. REST API端点测试
echo -e "${BLUE}=== REST API端点测试 ===${NC}"
test_http_status "$PROD_API_BASE/categories/" "Categories端点"
test_http_status "$PROD_API_BASE/authors/" "Authors端点"
test_http_status "$PROD_API_BASE/sources/" "Sources端点"
test_http_status "$PROD_API_BASE/quotes/" "Quotes端点"
echo ""

# 3. 数据完整性测试（需要jq工具）
if command -v jq &> /dev/null; then
    echo -e "${BLUE}=== 数据完整性测试 ===${NC}"
    test_json_response "$PROD_API_BASE/categories/" "Categories数据" 5
    test_json_response "$PROD_API_BASE/authors/" "Authors数据" 5
    test_json_response "$PROD_API_BASE/sources/" "Sources数据" 5
    echo ""
    
    # 4. GraphQL查询测试
    echo -e "${BLUE}=== GraphQL查询测试 ===${NC}"
    test_graphql_query "{ allCategories { id name slug } }" "Categories查询" "allCategories"
    test_graphql_query "{ allAuthors { id name slug } }" "Authors查询" "allAuthors"
    test_graphql_query "{ allSources { id name slug } }" "Sources查询" "allSources"
    echo ""
else
    echo -e "${YELLOW}⚠️  跳过数据完整性测试 (需要安装jq工具)${NC}"
    echo "   安装命令: brew install jq"
    echo ""
fi

# 5. CORS测试
echo -e "${BLUE}=== CORS配置测试 ===${NC}"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
echo -n "🔒 CORS测试: 跨域访问 ... "

cors_response=$(curl -s --max-time 10 \
    -H "Origin: http://localhost:8083" \
    -H "Access-Control-Request-Method: GET" \
    -X OPTIONS \
    "$PROD_API_BASE/categories/")

if echo "$cors_response" | grep -q "Access-Control-Allow-Origin" 2>/dev/null; then
    echo -e "${GREEN}✅ 通过${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ 失败${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
echo ""

# 6. 前端集成测试
echo -e "${BLUE}=== 前端集成测试 ===${NC}"
test_http_status "$LOCAL_FRONTEND" "本地前端服务器"
test_http_status "$LOCAL_FRONTEND/test-production-api.html" "API测试页面"
echo ""

# 测试结果汇总
echo "================================"
echo -e "${BLUE}📊 测试结果汇总${NC}"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！生产API完全可用${NC}"
    exit 0
else
    echo -e "${RED}⚠️  有 $FAILED_TESTS 个测试失败${NC}"
    exit 1
fi
