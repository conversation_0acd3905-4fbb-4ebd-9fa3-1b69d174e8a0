#!/bin/bash

# Quotese Nginx配置部署脚本
# 版本：v2.0 - SEO重启实施
# 更新日期：2025年6月16日

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NGINX_CONFIG_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
BACKUP_DIR="/etc/nginx/backup/$(date +%Y%m%d_%H%M%S)"

# 检查是否以root权限运行
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0 $@"
        exit 1
    fi
}

# 检查Nginx是否安装
check_nginx() {
    if ! command -v nginx &> /dev/null; then
        log_error "Nginx未安装，请先安装Nginx"
        exit 1
    fi
    log_success "Nginx已安装: $(nginx -v 2>&1)"
}

# 创建备份
create_backup() {
    log_info "创建配置备份..."
    mkdir -p "$BACKUP_DIR"
    
    if [ -f "$NGINX_CONFIG_DIR/quotese" ]; then
        cp "$NGINX_CONFIG_DIR/quotese" "$BACKUP_DIR/"
        log_success "已备份现有配置到: $BACKUP_DIR/quotese"
    fi
    
    if [ -f "$NGINX_ENABLED_DIR/quotese" ]; then
        cp "$NGINX_ENABLED_DIR/quotese" "$BACKUP_DIR/quotese.enabled"
        log_success "已备份启用的配置"
    fi
}

# 验证配置文件语法
validate_config() {
    local config_file="$1"
    local config_name="$2"
    
    log_info "验证 $config_name 配置语法..."
    
    # 临时复制配置文件进行测试
    local temp_config="/tmp/nginx_test_$(date +%s).conf"
    cp "$config_file" "$temp_config"
    
    # 测试配置语法
    if nginx -t -c "$temp_config" 2>/dev/null; then
        log_success "$config_name 配置语法正确"
        rm -f "$temp_config"
        return 0
    else
        log_error "$config_name 配置语法错误"
        nginx -t -c "$temp_config"
        rm -f "$temp_config"
        return 1
    fi
}

# 部署配置文件
deploy_config() {
    local environment="$1"
    local config_file=""
    
    case "$environment" in
        "production"|"prod")
            config_file="$SCRIPT_DIR/nginx_frontend.conf"
            ;;
        "test")
            config_file="$SCRIPT_DIR/nginx_test.conf"
            ;;
        "docker")
            log_info "Docker配置不需要系统级部署"
            return 0
            ;;
        *)
            log_error "未知环境: $environment"
            log_info "支持的环境: production, test, docker"
            exit 1
            ;;
    esac
    
    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        exit 1
    fi
    
    # 验证配置语法
    if ! validate_config "$config_file" "$environment"; then
        exit 1
    fi
    
    # 复制配置文件
    log_info "部署 $environment 环境配置..."
    cp "$config_file" "$NGINX_CONFIG_DIR/quotese"
    
    # 启用站点
    if [ ! -L "$NGINX_ENABLED_DIR/quotese" ]; then
        ln -s "$NGINX_CONFIG_DIR/quotese" "$NGINX_ENABLED_DIR/quotese"
        log_success "已启用Quotese站点"
    fi
    
    # 测试完整Nginx配置
    log_info "测试完整Nginx配置..."
    if nginx -t; then
        log_success "Nginx配置测试通过"
    else
        log_error "Nginx配置测试失败"
        exit 1
    fi
}

# 重载Nginx配置
reload_nginx() {
    log_info "重载Nginx配置..."
    if systemctl reload nginx; then
        log_success "Nginx配置重载成功"
    else
        log_error "Nginx配置重载失败"
        exit 1
    fi
}

# 验证部署结果
verify_deployment() {
    log_info "验证部署结果..."
    
    # 检查Nginx状态
    if systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
    else
        log_error "Nginx服务未运行"
        return 1
    fi
    
    # 检查配置文件
    if [ -f "$NGINX_CONFIG_DIR/quotese" ] && [ -L "$NGINX_ENABLED_DIR/quotese" ]; then
        log_success "配置文件部署正确"
    else
        log_error "配置文件部署异常"
        return 1
    fi
    
    # 测试基本URL
    log_info "测试基本URL响应..."
    local test_urls=("/" "/authors/" "/categories/" "/quotes/")
    
    for url in "${test_urls[@]}"; do
        if curl -s -o /dev/null -w "%{http_code}" "http://localhost$url" | grep -q "200\|404"; then
            log_success "URL $url 响应正常"
        else
            log_warning "URL $url 响应异常"
        fi
    done
}

# 显示帮助信息
show_help() {
    echo "Quotese Nginx配置部署脚本"
    echo ""
    echo "用法: $0 [选项] <环境>"
    echo ""
    echo "环境:"
    echo "  production, prod    部署生产环境配置"
    echo "  test               部署测试环境配置"
    echo "  docker             显示Docker配置信息"
    echo ""
    echo "选项:"
    echo "  -h, --help         显示此帮助信息"
    echo "  -v, --validate     仅验证配置文件语法"
    echo "  -b, --backup       仅创建备份"
    echo "  --no-reload        不重载Nginx配置"
    echo ""
    echo "示例:"
    echo "  $0 production      部署生产环境配置"
    echo "  $0 test            部署测试环境配置"
    echo "  $0 -v production   仅验证生产环境配置"
}

# 主函数
main() {
    local environment=""
    local validate_only=false
    local backup_only=false
    local no_reload=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--validate)
                validate_only=true
                shift
                ;;
            -b|--backup)
                backup_only=true
                shift
                ;;
            --no-reload)
                no_reload=true
                shift
                ;;
            production|prod|test|docker)
                environment="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境参数
    if [ -z "$environment" ]; then
        log_error "请指定部署环境"
        show_help
        exit 1
    fi
    
    # 执行操作
    log_info "开始Quotese Nginx配置部署..."
    log_info "环境: $environment"
    
    if [ "$backup_only" = true ]; then
        check_root
        create_backup
        log_success "备份完成"
        exit 0
    fi
    
    if [ "$validate_only" = true ]; then
        local config_file=""
        case "$environment" in
            "production"|"prod")
                config_file="$SCRIPT_DIR/nginx_frontend.conf"
                ;;
            "test")
                config_file="$SCRIPT_DIR/nginx_test.conf"
                ;;
            "docker")
                config_file="$SCRIPT_DIR/nginx_frontend_docker.conf"
                ;;
        esac
        
        if validate_config "$config_file" "$environment"; then
            log_success "配置验证通过"
            exit 0
        else
            exit 1
        fi
    fi
    
    # 完整部署流程
    check_root
    check_nginx
    create_backup
    deploy_config "$environment"
    
    if [ "$no_reload" != true ]; then
        reload_nginx
        verify_deployment
    fi
    
    log_success "Quotese Nginx配置部署完成！"
    log_info "配置文件位置: $NGINX_CONFIG_DIR/quotese"
    log_info "备份位置: $BACKUP_DIR"
}

# 运行主函数
main "$@"
