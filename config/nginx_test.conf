# Quotese.com 前端 Nginx 配置 - 测试环境
# 支持语义化URL架构和SEO优化
# 版本：v2.0 - SEO重启实施
# 更新日期：2025年6月16日

server {
    listen 80;
    server_name test.quotese.com localhost;
    
    # 测试环境网站根目录
    root /var/www/quotese/test/frontend;
    index index.html;
    
    # 启用详细日志用于调试
    access_log /var/log/nginx/quotese_test_access.log;
    error_log /var/log/nginx/quotese_test_error.log debug;
    
    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # ==================== 语义化URL路由规则 ====================
    
    # 作者页面路由
    # /authors/ -> authors.html (作者列表页)
    location = /authors/ {
        try_files /authors.html /authors.html;
        add_header X-Debug-Route "authors-list" always;
    }
    
    # /authors/{slug}/ -> author.html (作者详情页)
    location ~ ^/authors/([^/]+)/?$ {
        try_files /author.html /author.html;
        add_header X-Debug-Route "author-detail" always;
        add_header X-Debug-Slug "$1" always;
    }
    
    # /authors/{slug}/quotes/ -> author.html (作者名言列表页)
    location ~ ^/authors/([^/]+)/quotes/?$ {
        try_files /author.html /author.html;
        add_header X-Debug-Route "author-quotes" always;
        add_header X-Debug-Slug "$1" always;
    }
    
    # 类别页面路由
    # /categories/ -> categories.html (类别列表页)
    location = /categories/ {
        try_files /categories.html /categories.html;
        add_header X-Debug-Route "categories-list" always;
    }
    
    # /categories/{slug}/ -> category.html (类别详情页)
    location ~ ^/categories/([^/]+)/?$ {
        try_files /category.html /category.html;
        add_header X-Debug-Route "category-detail" always;
        add_header X-Debug-Slug "$1" always;
    }
    
    # /categories/{slug}/quotes/ -> category.html (类别名言列表页)
    location ~ ^/categories/([^/]+)/quotes/?$ {
        try_files /category.html /category.html;
        add_header X-Debug-Route "category-quotes" always;
        add_header X-Debug-Slug "$1" always;
    }
    
    # 来源页面路由
    # /sources/ -> sources.html (来源列表页)
    location = /sources/ {
        try_files /sources.html /sources.html;
        add_header X-Debug-Route "sources-list" always;
    }
    
    # /sources/{slug}/ -> source.html (来源详情页)
    location ~ ^/sources/([^/]+)/?$ {
        try_files /source.html /source.html;
        add_header X-Debug-Route "source-detail" always;
        add_header X-Debug-Slug "$1" always;
    }
    
    # 名言页面路由
    # /quotes/ -> quotes.html (名言列表页)
    location = /quotes/ {
        try_files /quotes.html /quotes.html;
        add_header X-Debug-Route "quotes-list" always;
    }
    
    # /quotes/{id}/ -> quote.html (名言详情页)
    location ~ ^/quotes/(\d+)/?$ {
        try_files /quote.html /quote.html;
        add_header X-Debug-Route "quote-detail" always;
        add_header X-Debug-ID "$1" always;
    }
    
    # 搜索页面路由
    # /search/ -> search.html (搜索页面)
    location = /search/ {
        try_files /search.html /search.html;
        add_header X-Debug-Route "search" always;
    }
    
    # ==================== 静态文件处理（测试环境简化） ====================
    
    # JavaScript和CSS文件 - 短期缓存便于测试
    location ~* \.(js|css)$ {
        expires 1h;
        add_header Cache-Control "public, max-age=3600";
        add_header X-Debug-File-Type "script-style" always;
    }
    
    # 图片文件 - 短期缓存
    location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
        expires 1h;
        add_header Cache-Control "public, max-age=3600";
        add_header X-Debug-File-Type "image" always;
    }
    
    # 字体文件
    location ~* \.(woff|woff2|ttf|eot|otf)$ {
        expires 1d;
        add_header Cache-Control "public, max-age=86400";
        add_header Access-Control-Allow-Origin "*";
        add_header X-Debug-File-Type "font" always;
    }
    
    # HTML文件 - 不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
        add_header Pragma "no-cache";
        add_header X-Debug-File-Type "html" always;
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }
    
    # ==================== 特殊文件处理 ====================
    
    # 网站地图和robots.txt
    location = /sitemap.xml {
        expires 1h;
        add_header Cache-Control "public, max-age=3600";
        add_header Content-Type "application/xml; charset=utf-8";
        add_header X-Debug-File-Type "sitemap" always;
    }
    
    location = /robots.txt {
        expires 1h;
        add_header Cache-Control "public, max-age=3600";
        add_header Content-Type "text/plain; charset=utf-8";
        add_header X-Debug-File-Type "robots" always;
    }
    
    # Favicon
    location = /favicon.ico {
        expires 1d;
        add_header Cache-Control "public, max-age=86400";
        log_not_found off;
        access_log off;
    }
    
    # ==================== 测试和调试功能 ====================
    
    # 测试页面路由
    location = /test/ {
        try_files /test-all-pages.html /test-all-pages.html;
        add_header X-Debug-Route "test-pages" always;
    }
    
    # API测试页面
    location = /api-test/ {
        try_files /api-test.html /api-test.html;
        add_header X-Debug-Route "api-test" always;
    }
    
    # 调试信息端点
    location = /debug/nginx {
        add_header Content-Type "application/json";
        return 200 '{"server":"nginx","environment":"test","timestamp":"$time_iso8601","request_uri":"$request_uri"}';
    }
    
    # ==================== 错误处理 ====================
    
    # 自定义错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # 404错误页面配置
    location = /404.html {
        internal;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header X-Debug-Error "404" always;
    }
    
    # ==================== 安全配置（测试环境宽松） ====================
    
    # 显示Nginx版本信息用于调试
    server_tokens on;
    
    # 基础安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Test-Environment "true" always;
    
    # 允许访问隐藏文件用于调试（生产环境禁止）
    location ~ /\.well-known {
        allow all;
    }
    
    # 禁止访问敏感配置文件
    location ~* \.(conf|config|ini|log|bak)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # ==================== 默认路由处理 ====================
    
    # 默认路由 - 必须放在最后
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
        add_header X-Debug-Route "default" always;
    }
}
