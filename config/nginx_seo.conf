# Nginx SEO优化配置
# 实现动态渲染：为搜索引擎爬虫提供预渲染HTML，为普通用户提供客户端渲染
# 基于SEO最佳实践实施

# 上游预渲染服务配置
upstream prerender_service {
    server 127.0.0.1:8082;
    # 可以添加多个服务器实现负载均衡
    # server 127.0.0.1:8083 backup;
}

# 搜索引擎爬虫检测映射
map $http_user_agent $is_crawler {
    default 0;
    ~*googlebot 1;
    ~*bingbot 1;
    ~*slurp 1;
    ~*duckduckbot 1;
    ~*baiduspider 1;
    ~*yandexbot 1;
    ~*facebookexternalhit 1;
    ~*twitterbot 1;
    ~*linkedinbot 1;
    ~*whatsapp 1;
    ~*telegrambot 1;
    ~*applebot 1;
    ~*petalbot 1;
    ~*semrushbot 1;
    ~*ahrefsbot 1;
}

# 主服务器配置
server {
    listen 80;
    server_name quotese.com www.quotese.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name quotese.com www.quotese.com;
    
    # SSL配置（需要根据实际情况配置证书）
    # ssl_certificate /path/to/certificate.crt;
    # ssl_certificate_key /path/to/private.key;
    
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 根目录配置
    root /var/www/quotese.com/frontend;
    index index.html;
    
    # 日志配置
    access_log /var/log/nginx/quotese_access.log combined;
    error_log /var/log/nginx/quotese_error.log warn;
    
    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/ld+json;
    
    # 静态文件缓存配置
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # 启用Brotli压缩（如果可用）
        # brotli on;
        # brotli_types text/css application/javascript;
    }
    
    # Authors列表页面 - 动态渲染配置
    location = /authors/ {
        # 检查是否为搜索引擎爬虫
        if ($is_crawler = 1) {
            # 为爬虫提供预渲染内容
            proxy_pass http://prerender_service/prerender/authors/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header User-Agent $http_user_agent;
            
            # 预渲染服务超时配置
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
            
            # 添加预渲染标识头部
            add_header X-Prerendered "true";
            add_header X-Prerender-Service "quotese-seo";
            
            break;
        }
        
        # 普通用户访问静态文件
        try_files /authors.html /authors.html;
        
        # 添加SEO相关头部
        add_header X-Robots-Tag "index, follow";
        add_header Cache-Control "public, max-age=3600";
    }
    
    # Categories列表页面 - 动态渲染配置
    location = /categories/ {
        # 检查是否为搜索引擎爬虫
        if ($is_crawler = 1) {
            # 为爬虫提供预渲染内容
            proxy_pass http://prerender_service/prerender/categories/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header User-Agent $http_user_agent;
            
            # 预渲染服务超时配置
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
            
            # 添加预渲染标识头部
            add_header X-Prerendered "true";
            add_header X-Prerender-Service "quotese-seo";
            
            break;
        }
        
        # 普通用户访问静态文件
        try_files /categories.html /categories.html;
        
        # 添加SEO相关头部
        add_header X-Robots-Tag "index, follow";
        add_header Cache-Control "public, max-age=3600";
    }
    
    # Sources列表页面 - 动态渲染配置
    location = /sources/ {
        # 检查是否为搜索引擎爬虫
        if ($is_crawler = 1) {
            # 为爬虫提供预渲染内容
            proxy_pass http://prerender_service/prerender/sources/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header User-Agent $http_user_agent;
            
            # 预渲染服务超时配置
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
            
            # 添加预渲染标识头部
            add_header X-Prerendered "true";
            add_header X-Prerender-Service "quotese-seo";
            
            break;
        }
        
        # 普通用户访问静态文件
        try_files /sources.html /sources.html;
        
        # 添加SEO相关头部
        add_header X-Robots-Tag "index, follow";
        add_header Cache-Control "public, max-age=3600";
    }
    
    # 首页配置
    location = / {
        try_files /index.html /index.html;
        add_header X-Robots-Tag "index, follow";
        add_header Cache-Control "public, max-age=1800"; # 30分钟缓存
    }
    
    # API代理配置（如果需要）
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API缓存配置
        add_header Cache-Control "public, max-age=300"; # 5分钟缓存
    }
    
    # GraphQL端点配置
    location /graphql/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # GraphQL不缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # Sitemap配置
    location = /sitemap.xml {
        try_files /sitemap.xml /sitemap.xml;
        add_header Content-Type "application/xml";
        add_header Cache-Control "public, max-age=86400"; # 24小时缓存
    }
    
    # Robots.txt配置
    location = /robots.txt {
        try_files /robots.txt /robots.txt;
        add_header Content-Type "text/plain";
        add_header Cache-Control "public, max-age=86400"; # 24小时缓存
    }
    
    # 健康检查端点
    location = /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 预渲染服务健康检查
    location = /prerender-health {
        proxy_pass http://prerender_service/health;
        proxy_set_header Host $host;
        access_log off;
    }
    
    # 404错误页面
    error_page 404 /404.html;
    location = /404.html {
        internal;
        try_files /404.html /index.html;
    }
    
    # 50x错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        internal;
        try_files /50x.html /index.html;
    }
    
    # 安全配置：隐藏敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 防止访问配置文件
    location ~* \.(conf|config|bak|backup|old|orig|original)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# 预渲染服务监控配置
server {
    listen 8083;
    server_name localhost;
    
    # 仅允许本地访问
    allow 127.0.0.1;
    deny all;
    
    location /nginx-status {
        stub_status on;
        access_log off;
    }
    
    location /prerender-status {
        proxy_pass http://prerender_service/health;
        proxy_set_header Host $host;
    }
}
