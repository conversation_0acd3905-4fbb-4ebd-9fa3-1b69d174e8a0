# Quotese.com 前端 Nginx 配置 - Docker版本
# 支持语义化URL架构和SEO优化
# 版本：v2.0 - SEO重启实施
# 更新日期：2025年6月16日

server {
    listen 80;
    server_name localhost;

    # Docker容器内的网站根目录
    root /usr/share/nginx/html;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # ==================== 语义化URL路由规则 ====================

    # 作者页面路由
    # /authors/ -> authors.html (作者列表页)
    location = /authors/ {
        try_files /authors.html /authors.html;
    }

    # /authors/{slug}/ -> author.html (作者详情页)
    location ~ ^/authors/([^/]+)/?$ {
        try_files /author.html /author.html;
    }

    # /authors/{slug}/quotes/ -> author.html (作者名言列表页)
    location ~ ^/authors/([^/]+)/quotes/?$ {
        try_files /author.html /author.html;
    }

    # 类别页面路由
    # /categories/ -> categories.html (类别列表页)
    location = /categories/ {
        try_files /categories.html /categories.html;
    }

    # /categories/{slug}/ -> category.html (类别详情页)
    location ~ ^/categories/([^/]+)/?$ {
        try_files /category.html /category.html;
    }

    # /categories/{slug}/quotes/ -> category.html (类别名言列表页)
    location ~ ^/categories/([^/]+)/quotes/?$ {
        try_files /category.html /category.html;
    }

    # 来源页面路由
    # /sources/ -> sources.html (来源列表页)
    location = /sources/ {
        try_files /sources.html /sources.html;
    }

    # /sources/{slug}/ -> source.html (来源详情页)
    location ~ ^/sources/([^/]+)/?$ {
        try_files /source.html /source.html;
    }

    # 名言页面路由
    # /quotes/ -> quotes.html (名言列表页)
    location = /quotes/ {
        try_files /quotes.html /quotes.html;
    }

    # /quotes/{id}/ -> quote.html (名言详情页)
    location ~ ^/quotes/(\d+)/?$ {
        try_files /quote.html /quote.html;
    }

    # 搜索页面路由
    # /search/ -> search.html (搜索页面)
    location = /search/ {
        try_files /search.html /search.html;
    }

    # ==================== 静态文件缓存优化 ====================

    # JavaScript和CSS文件 - 长期缓存
    location ~* \.(js|css)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform, immutable";
        add_header Vary "Accept-Encoding";
        etag on;
        add_header Access-Control-Allow-Origin "*";
    }

    # 图片文件 - 长期缓存
    location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
        expires 90d;
        add_header Cache-Control "public, no-transform, immutable";
        add_header Vary "Accept-Encoding";
        etag on;
        add_header X-Content-Type-Options "nosniff";
    }

    # 字体文件 - 长期缓存
    location ~* \.(woff|woff2|ttf|eot|otf)$ {
        expires 365d;
        add_header Cache-Control "public, no-transform, immutable";
        add_header Access-Control-Allow-Origin "*";
        etag on;
    }

    # HTML文件 - 不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Pragma "no-cache";
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # ==================== 特殊文件处理 ====================

    # 网站地图和robots.txt
    location = /sitemap.xml {
        expires 1d;
        add_header Cache-Control "public, max-age=86400";
        add_header Content-Type "application/xml; charset=utf-8";
    }

    location = /robots.txt {
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
        add_header Content-Type "text/plain; charset=utf-8";
    }

    # Favicon
    location = /favicon.ico {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        log_not_found off;
        access_log off;
    }

    # ==================== 错误处理和安全 ====================

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    # 安全头
    server_tokens off;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 禁止访问隐藏文件和配置文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~* \.(conf|config|ini|log|bak)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # ==================== 默认路由 ====================

    # 默认路由处理
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
}
