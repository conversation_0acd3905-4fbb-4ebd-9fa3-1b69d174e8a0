server {
   listen 80;
   server_name localhost;
   
   root /usr/share/nginx/html;
   index index.html;
   
   # 静态文件缓存
   location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$ {
       expires 30d;
       add_header Cache-Control "public, no-transform";
   }
   
   # 处理HTML文件
   location ~* \.html$ {
       expires -1;
       add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate";
   }
   
   # 处理网站地图和robots.txt
   location = /sitemap.xml {
       expires 1d;
   }
   
   location = /robots.txt {
       expires 1d;
   }
   
   # 处理404错误
   error_page 404 /404.html;
   
   # 默认处理
   location / {
       try_files $uri $uri/ /index.html;
   }
}
