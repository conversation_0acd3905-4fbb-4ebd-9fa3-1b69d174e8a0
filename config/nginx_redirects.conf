# Nginx 301重定向配置
# 用于将旧URL格式重定向到新的语义化URL格式
# 包含在主Nginx配置文件中使用

# ==================== 旧HTML文件重定向 ====================

# 重定向HTML文件到目录
location = /index.html {
    return 301 /;
}

location = /authors.html {
    return 301 /authors/;
}

location = /categories.html {
    return 301 /categories/;
}

location = /sources.html {
    return 301 /sources/;
}

location = /quotes.html {
    return 301 /quotes/;
}

# ==================== 查询参数格式重定向 ====================

# 作者页面重定向：author.html?name=xxx&id=xxx → /authors/xxx/
location = /author.html {
    # 处理带name参数的请求
    if ($args ~ "^name=([^&]+)") {
        set $author_name $1;
        return 301 /authors/$author_name/;
    }
    
    # 处理带id和name参数的请求
    if ($args ~ "^id=([0-9]+)&name=([^&]+)$") {
        set $author_name $2;
        return 301 /authors/$author_name/;
    }
    
    # 处理只有id参数的请求
    if ($args ~ "^id=([0-9]+)$") {
        return 301 /authors/;
    }
    
    # 无参数时重定向到列表页
    return 301 /authors/;
}

# 类别页面重定向：category.html?name=xxx&id=xxx → /categories/xxx/
location = /category.html {
    # 处理带name参数的请求
    if ($args ~ "^name=([^&]+)") {
        set $category_name $1;
        return 301 /categories/$category_name/;
    }
    
    # 处理带id和name参数的请求
    if ($args ~ "^id=([0-9]+)&name=([^&]+)$") {
        set $category_name $2;
        return 301 /categories/$category_name/;
    }
    
    # 处理只有id参数的请求
    if ($args ~ "^id=([0-9]+)$") {
        return 301 /categories/;
    }
    
    # 无参数时重定向到列表页
    return 301 /categories/;
}

# 来源页面重定向：source.html?name=xxx&id=xxx → /sources/xxx/
location = /source.html {
    # 处理带name参数的请求
    if ($args ~ "^name=([^&]+)") {
        set $source_name $1;
        return 301 /sources/$source_name/;
    }
    
    # 处理带id和name参数的请求
    if ($args ~ "^id=([0-9]+)&name=([^&]+)$") {
        set $source_name $2;
        return 301 /sources/$source_name/;
    }
    
    # 处理只有id参数的请求
    if ($args ~ "^id=([0-9]+)$") {
        return 301 /sources/;
    }
    
    # 无参数时重定向到列表页
    return 301 /sources/;
}

# 名言页面重定向：quote.html?id=xxx → /quotes/xxx/
location = /quote.html {
    # 处理带id参数的请求
    if ($args ~ "^id=([0-9]+)$") {
        set $quote_id $1;
        return 301 /quotes/$quote_id/;
    }
    
    # 无参数时重定向到列表页
    return 301 /quotes/;
}

# ==================== URL规范化重定向 ====================

# 强制添加尾部斜杠（对于目录URL）
location ~ ^/(authors|categories|sources|quotes)$ {
    return 301 $uri/;
}

# 移除多余的斜杠
location ~ ^(.+)/+$ {
    return 301 $1/;
}

# ==================== 旧URL模式重定向 ====================

# 重定向旧的ID-name格式到新的slug格式
# 例如：/authors/albert-einstein-1/ → /authors/albert-einstein/
location ~ ^/(authors|categories|sources)/([^/]+)-([0-9]+)/?$ {
    return 301 /$1/$2/;
}

# 重定向旧的名言格式
# 例如：/quotes/quote-123/ → /quotes/123/
location ~ ^/quotes/quote-([0-9]+)/?$ {
    return 301 /quotes/$1/;
}

# ==================== 搜索和API重定向 ====================

# 重定向搜索页面到新格式
location = /search.html {
    if ($args ~ "^q=(.+)$") {
        set $query $1;
        return 301 /search/?q=$query;
    }
    return 301 /search/;
}

# 重定向API测试页面
location ~ ^/(api-test|test|simple-api-test)\.html$ {
    return 301 /;
}

# ==================== HTTPS重定向 ====================

# 如果使用HTTP，重定向到HTTPS
# 注意：这个规则通常在server块的HTTP配置中使用
# server {
#     listen 80;
#     server_name quotese.com www.quotese.com;
#     return 301 https://quotese.com$request_uri;
# }

# ==================== WWW重定向 ====================

# 重定向www到非www版本（可选）
# server {
#     listen 443 ssl;
#     server_name www.quotese.com;
#     return 301 https://quotese.com$request_uri;
# }
