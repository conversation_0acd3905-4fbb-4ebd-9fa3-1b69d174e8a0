#!/bin/bash

# Quotese API模式切换脚本
# 用于在本地API和生产API之间快速切换

echo "🔄 Quotese API模式切换工具"
echo "================================"

# 检查是否在正确的目录
if [ ! -f "frontend/js/config.js" ]; then
    echo "❌ 错误：请在项目根目录下运行此脚本"
    echo "   当前目录: $(pwd)"
    echo "   应该包含 frontend/js/config.js 文件"
    exit 1
fi

# 显示当前配置
show_current_config() {
    echo "📊 当前配置状态："
    
    # 检查config.js中的getCurrent函数
    if grep -q "use-production-api" frontend/js/config.js; then
        echo "✅ 配置文件已支持API模式切换"
    else
        echo "⚠️  配置文件可能需要更新以支持API模式切换"
    fi
    
    # 检查localStorage设置（需要在浏览器中查看）
    echo "💡 在浏览器控制台中运行以下命令查看当前模式："
    echo "   QuoteseAPIMode.getCurrentMode()"
    echo ""
}

# 切换到生产API模式
switch_to_production() {
    echo "🔄 切换到生产API模式..."
    echo ""
    echo "📝 手动步骤："
    echo "1. 在浏览器中打开: http://localhost:8083"
    echo "2. 按F12打开开发者工具"
    echo "3. 在控制台中运行: QuoteseAPIMode.useProductionAPI()"
    echo "4. 刷新页面"
    echo ""
    echo "🔗 生产API端点："
    echo "   REST API: https://api.quotese.com/api/"
    echo "   GraphQL: https://api.quotese.com/graphql/"
    echo ""
    echo "🧪 测试页面: http://localhost:8083/test-production-api.html"
}

# 切换到本地API模式
switch_to_local() {
    echo "🔄 切换到本地API模式..."
    echo ""
    echo "📝 手动步骤："
    echo "1. 在浏览器中打开: http://localhost:8083"
    echo "2. 按F12打开开发者工具"
    echo "3. 在控制台中运行: QuoteseAPIMode.useLocalAPI()"
    echo "4. 刷新页面"
    echo ""
    echo "🔗 本地API端点："
    echo "   REST API: http://127.0.0.1:8000/api/"
    echo "   GraphQL: http://127.0.0.1:8000/graphql/"
    echo ""
    echo "⚠️  确保本地Django服务器正在运行 (端口8000)"
}

# 测试API连接
test_api_connection() {
    echo "🔍 测试API连接..."
    echo ""
    
    # 测试生产API
    echo "📡 测试生产API连接..."
    if curl -s --max-time 10 https://api.quotese.com/api/ > /dev/null; then
        echo "✅ 生产API连接正常"
    else
        echo "❌ 生产API连接失败"
    fi
    
    # 测试本地API
    echo "🏠 测试本地API连接..."
    if curl -s --max-time 5 http://127.0.0.1:8000/api/ > /dev/null; then
        echo "✅ 本地API连接正常"
    else
        echo "❌ 本地API连接失败（确保Django服务器正在运行）"
    fi
    echo ""
}

# 显示帮助信息
show_help() {
    echo "📖 使用说明："
    echo ""
    echo "命令选项："
    echo "  production, prod, p    - 切换到生产API模式"
    echo "  local, dev, l         - 切换到本地API模式"
    echo "  status, s             - 显示当前配置状态"
    echo "  test, t               - 测试API连接"
    echo "  help, h               - 显示此帮助信息"
    echo ""
    echo "示例："
    echo "  ./switch-api-mode.sh production"
    echo "  ./switch-api-mode.sh local"
    echo "  ./switch-api-mode.sh test"
    echo ""
    echo "🌐 快速访问："
    echo "  网站首页: http://localhost:8083"
    echo "  API测试页面: http://localhost:8083/test-production-api.html"
    echo "  Django管理: http://localhost:8000/admin"
    echo ""
}

# 主逻辑
case "${1:-help}" in
    "production"|"prod"|"p")
        switch_to_production
        ;;
    "local"|"dev"|"l")
        switch_to_local
        ;;
    "status"|"s")
        show_current_config
        ;;
    "test"|"t")
        test_api_connection
        ;;
    "help"|"h"|*)
        show_help
        ;;
esac

echo "================================"
echo "💡 提示：使用 './switch-api-mode.sh help' 查看完整帮助"
