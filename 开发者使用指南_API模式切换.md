# Quotese开发者使用指南 - API模式切换

## 🎯 概述

本指南为Quotese项目开发者提供在本地API和生产API之间切换的完整说明，确保开发和测试的灵活性。

## 🚀 快速开始

### 1. 启动本地环境

```bash
# 在项目根目录下
./start_local.sh
```

**访问地址：**
- 🌐 **网站首页**: http://localhost:8083
- 🧪 **API测试页面**: http://localhost:8083/test-production-api.html
- 📊 **兼容性测试**: http://localhost:8083/test-production-compatibility.html

### 2. API模式切换

#### 方法一：浏览器控制台（推荐）

```javascript
// 切换到生产API
QuoteseAPIMode.useProductionAPI()
// 然后刷新页面

// 切换到本地API
QuoteseAPIMode.useLocalAPI()
// 然后刷新页面

// 查看当前模式
QuoteseAPIMode.getCurrentMode()

// 测试连接
QuoteseAPIMode.testConnection()
```

#### 方法二：URL参数（临时）

```
# 强制使用生产API（仅当前页面）
http://localhost:8083/?use-production-api=true

# 正常使用本地API
http://localhost:8083/
```

#### 方法三：命令行工具

```bash
# 切换到生产API
./switch-api-mode.sh production

# 切换到本地API
./switch-api-mode.sh local

# 测试连接状态
./switch-api-mode.sh test
```

## 📊 API端点对比

| 环境 | REST API | GraphQL API | 数据库 | 响应时间 |
|------|----------|-------------|--------|----------|
| **本地** | `http://127.0.0.1:8000/api/` | `http://127.0.0.1:8000/graphql/` | SQLite | 5-50ms |
| **生产** | `https://api.quotese.com/api/` | `https://api.quotese.com/graphql/` | MySQL | 100-500ms |

## 🧪 测试工作流

### 开发阶段

```bash
# 1. 启动本地环境
./start_local.sh

# 2. 使用本地API进行开发
# 默认配置，无需额外操作

# 3. 功能开发完成后，切换到生产API测试
# 在浏览器控制台执行：
# QuoteseAPIMode.useProductionAPI()
```

### 测试阶段

```bash
# 1. 运行自动化测试
./test-production-api.sh

# 2. 手动功能测试
# 访问: http://localhost:8083/test-production-compatibility.html
# 点击"运行兼容性测试"

# 3. 性能对比测试
# 分别在本地API和生产API模式下测试相同功能
```

### 部署前验证

```bash
# 1. 确保使用生产API
# QuoteseAPIMode.useProductionAPI()

# 2. 测试所有主要功能
# - 首页加载
# - 分类/作者/来源页面
# - 搜索功能
# - 热门模块导航

# 3. 验证优化功能
# - 测试热门模块跳转性能
# - 验证实体缓存工作正常
```

## 🔍 功能兼容性状态

### ✅ 完全兼容

- **REST API端点**: 所有端点完全兼容
- **页面数据加载**: 首页、列表页、详情页
- **优化导航**: 热门模块跳转优化
- **搜索功能**: 关键词搜索和筛选
- **语义化URL**: 路由解析和跳转
- **CORS配置**: 跨域请求支持

### ⚠️ 需要注意

- **GraphQL查询**: 生产环境schema可能略有差异
- **响应时间**: 生产API响应时间较长
- **数据量**: 生产环境数据量更大

### ❌ 已知限制

- **GraphQL某些字段**: 如`slug`字段在生产环境可能缺失
- **实时数据**: 生产环境数据可能与本地测试数据不同

## 🛠️ 故障排除

### 常见问题

**1. 切换模式后页面显示异常**

```javascript
// 解决方案：清除缓存并刷新
localStorage.clear();
location.reload(true);
```

**2. 生产API连接失败**

```bash
# 检查网络连接
curl -I https://api.quotese.com/api/

# 检查CORS设置
./switch-api-mode.sh test
```

**3. GraphQL查询错误**

```javascript
// 使用兼容性查询
const query = `
  query {
    categories { id name }
    authors { id name }
    sources { id name }
  }
`;
```

**4. 性能问题**

```javascript
// 增加超时时间
const config = {
  timeout: 15000, // 15秒
  retries: 3
};
```

### 调试工具

**浏览器控制台命令：**

```javascript
// 查看当前配置
console.log(window.AppConfig);

// 查看API模式
QuoteseAPIMode.getCurrentMode();

// 测试API连接
QuoteseAPIMode.testConnection();

// 查看实体缓存
console.log(window.entityCache);

// 查看优化导航状态
console.log(typeof window.navigateToEntityWithId);
```

## 📝 开发最佳实践

### 1. 代码编写

```javascript
// 推荐：使用配置化的API调用
const config = window.AppConfig;
const response = await fetch(`${config.apiEndpoint}categories/`);

// 避免：硬编码API地址
const response = await fetch('http://localhost:8000/api/categories/');
```

### 2. 错误处理

```javascript
// 兼容不同环境的错误处理
async function fetchWithFallback(url, options = {}) {
  try {
    const response = await fetch(url, {
      timeout: 15000,
      ...options
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API调用失败:', error);
    
    // 根据错误类型提供不同的处理
    if (error.name === 'TypeError') {
      // 网络错误
      return { error: '网络连接失败，请检查网络设置' };
    }
    
    throw error;
  }
}
```

### 3. 性能优化

```javascript
// 根据API模式调整行为
const config = window.AppConfig;
const isProduction = config.apiEndpoint.includes('api.quotese.com');

// 生产环境使用更长的缓存时间
const cacheTime = isProduction ? 300000 : 60000; // 5分钟 vs 1分钟

// 生产环境显示加载状态
if (isProduction) {
  showLoadingIndicator();
}
```

## 📋 检查清单

### 开发完成检查

- [ ] 功能在本地API模式下正常工作
- [ ] 切换到生产API模式测试通过
- [ ] 错误处理覆盖网络异常情况
- [ ] 性能在可接受范围内
- [ ] 兼容性测试全部通过

### 部署前检查

- [ ] 生产API连接正常
- [ ] 所有页面数据加载正确
- [ ] 搜索功能工作正常
- [ ] 优化导航功能正常
- [ ] 无JavaScript错误
- [ ] 响应时间在可接受范围

## 🔗 相关文档

- **技术栈分析**: `tech-stack-analysis-20250628-122619.md`
- **API切换指南**: `API模式切换和测试指南.md`
- **兼容性报告**: `生产API兼容性测试报告.md`
- **本地启动指南**: `README_本地启动.md`

## 📞 支持

如果遇到问题，请：

1. 查看浏览器控制台错误信息
2. 运行 `./test-production-api.sh` 检查API状态
3. 访问测试页面进行诊断
4. 查看相关文档获取详细信息

---

**🎉 现在您可以灵活地在本地API和生产API之间切换，确保开发和测试的高效进行！**
