#!/usr/bin/env python3
"""
Quotese开发环境最佳实践实施方案
提供具体的改进措施和实施脚本
"""

import os
import json
import shutil
import requests
from pathlib import Path

class BestPracticesImplementer:
    def __init__(self):
        self.project_root = Path.cwd()
        self.frontend_dir = self.project_root / "frontend"
        self.backend_dir = self.project_root / "backend"
        
    def fix_google_fonts_dependency(self):
        """修复Google Fonts依赖，实现完全离线开发"""
        print("🔧 修复Google Fonts依赖...")
        
        # 创建本地字体目录
        fonts_dir = self.frontend_dir / "fonts"
        fonts_dir.mkdir(exist_ok=True)
        
        # 下载常用字体文件 (模拟，实际需要从Google Fonts下载)
        font_files = [
            "noto-serif-regular.woff2",
            "noto-sans-regular.woff2"
        ]
        
        # 创建本地字体CSS文件
        local_fonts_css = """
/* 本地字体定义 - 替代Google Fonts */
@font-face {
    font-family: 'Noto Serif';
    font-style: normal;
    font-weight: 400;
    src: url('./fonts/noto-serif-regular.woff2') format('woff2');
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    font-style: normal;
    font-weight: 400;
    src: url('./fonts/noto-sans-regular.woff2') format('woff2');
    font-display: swap;
}
"""
        
        # 保存本地字体CSS
        with open(self.frontend_dir / "css" / "local-fonts.css", "w", encoding="utf-8") as f:
            f.write(local_fonts_css)
        
        print("✅ 本地字体配置已创建")
        print("📝 请手动下载字体文件到 frontend/fonts/ 目录")
        print("📝 请在HTML文件中将Google Fonts链接替换为: <link href='/css/local-fonts.css' rel='stylesheet'>")
    
    def create_data_generator(self):
        """创建测试数据生成器"""
        print("📊 创建测试数据生成器...")
        
        data_generator_script = '''#!/usr/bin/env python3
"""
测试数据生成器
用于生成大量测试数据以支持性能测试和UI测试
"""

import os
import sys
import django
from django.conf import settings

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quotes_admin.settings_local')
django.setup()

from quotesapp.models import Authors, Categories, Sources, Quotes, QuoteCategories, QuoteSources
import random
from datetime import datetime, timedelta

class TestDataGenerator:
    def __init__(self):
        self.famous_quotes = [
            "The only way to do great work is to love what you do.",
            "Innovation distinguishes between a leader and a follower.",
            "Life is what happens to you while you're busy making other plans.",
            "The future belongs to those who believe in the beauty of their dreams.",
            "It is during our darkest moments that we must focus to see the light.",
            "Success is not final, failure is not fatal: it is the courage to continue that counts.",
            "The only impossible journey is the one you never begin.",
            "In the end, we will remember not the words of our enemies, but the silence of our friends.",
            "The way to get started is to quit talking and begin doing.",
            "Don't be afraid to give up the good to go for the great."
        ]
        
        self.authors_pool = [
            "Steve Jobs", "Albert Einstein", "John Lennon", "Eleanor Roosevelt",
            "Aristotle", "Maya Angelou", "Winston Churchill", "Tony Robbins",
            "Walt Disney", "Martin Luther King Jr.", "Nelson Mandela", "Gandhi",
            "Mark Twain", "Oscar Wilde", "Benjamin Franklin", "Theodore Roosevelt"
        ]
        
        self.categories_pool = [
            "Inspirational", "Motivational", "Success", "Leadership", "Wisdom",
            "Life", "Love", "Happiness", "Education", "Philosophy", "Science",
            "Art", "Business", "Technology", "Health", "Friendship"
        ]
        
        self.sources_pool = [
            "Stanford Commencement Address", "Nobel Prize Speech", "TED Talk",
            "Biography", "Interview", "Book", "Speech", "Letter", "Diary",
            "Conference", "Documentary", "Podcast", "Article", "Essay"
        ]
    
    def generate_authors(self, count=50):
        """生成作者数据"""
        print(f"生成 {count} 个作者...")
        
        for i in range(count):
            author_name = f"{random.choice(self.authors_pool)} {i+1}"
            author, created = Authors.objects.get_or_create(
                name=author_name,
                defaults={
                    'created_at': datetime.now() - timedelta(days=random.randint(1, 365)),
                    'updated_at': datetime.now(),
                    'quotes_count': 0
                }
            )
            if created:
                print(f"  创建作者: {author_name}")
    
    def generate_categories(self, count=20):
        """生成类别数据"""
        print(f"生成 {count} 个类别...")
        
        for category_name in self.categories_pool[:count]:
            category, created = Categories.objects.get_or_create(
                name=category_name,
                defaults={
                    'created_at': datetime.now() - timedelta(days=random.randint(1, 365)),
                    'updated_at': datetime.now(),
                    'quotes_count': 0
                }
            )
            if created:
                print(f"  创建类别: {category_name}")
    
    def generate_sources(self, count=15):
        """生成来源数据"""
        print(f"生成 {count} 个来源...")
        
        for source_name in self.sources_pool[:count]:
            source, created = Sources.objects.get_or_create(
                name=source_name,
                defaults={
                    'created_at': datetime.now() - timedelta(days=random.randint(1, 365)),
                    'updated_at': datetime.now(),
                    'quotes_count': 0
                }
            )
            if created:
                print(f"  创建来源: {source_name}")
    
    def generate_quotes(self, count=100):
        """生成名言数据"""
        print(f"生成 {count} 条名言...")
        
        authors = list(Authors.objects.all())
        categories = list(Categories.objects.all())
        sources = list(Sources.objects.all())
        
        for i in range(count):
            # 生成名言内容
            base_quote = random.choice(self.famous_quotes)
            quote_content = f"{base_quote} (Variation {i+1})"
            
            # 随机选择作者
            author = random.choice(authors)
            
            # 创建名言
            quote, created = Quotes.objects.get_or_create(
                content=quote_content,
                defaults={
                    'author': author,
                    'created_at': datetime.now() - timedelta(days=random.randint(1, 365)),
                    'updated_at': datetime.now()
                }
            )
            
            if created:
                # 随机关联类别 (1-3个)
                selected_categories = random.sample(categories, random.randint(1, 3))
                for category in selected_categories:
                    QuoteCategories.objects.get_or_create(
                        quote=quote,
                        category=category,
                        defaults={'created_at': datetime.now()}
                    )
                
                # 随机关联来源 (0-2个)
                if random.random() > 0.3:  # 70%的概率有来源
                    selected_sources = random.sample(sources, random.randint(1, 2))
                    for source in selected_sources:
                        QuoteSources.objects.get_or_create(
                            quote=quote,
                            source=source,
                            defaults={'created_at': datetime.now()}
                        )
                
                if (i + 1) % 10 == 0:
                    print(f"  已生成 {i + 1} 条名言")
    
    def update_counts(self):
        """更新统计数据"""
        print("更新统计数据...")
        
        # 更新作者名言数量
        for author in Authors.objects.all():
            count = Quotes.objects.filter(author=author).count()
            author.quotes_count = count
            author.save()
        
        # 更新类别名言数量
        for category in Categories.objects.all():
            count = QuoteCategories.objects.filter(category=category).count()
            category.quotes_count = count
            category.save()
        
        # 更新来源名言数量
        for source in Sources.objects.all():
            count = QuoteSources.objects.filter(source=source).count()
            source.quotes_count = count
            source.save()
        
        print("✅ 统计数据更新完成")
    
    def generate_all(self):
        """生成所有测试数据"""
        print("🚀 开始生成测试数据...")
        
        self.generate_authors(50)
        self.generate_categories(20)
        self.generate_sources(15)
        self.generate_quotes(100)
        self.update_counts()
        
        print("🎉 测试数据生成完成！")
        print(f"📊 数据统计:")
        print(f"   作者: {Authors.objects.count()}")
        print(f"   类别: {Categories.objects.count()}")
        print(f"   来源: {Sources.objects.count()}")
        print(f"   名言: {Quotes.objects.count()}")

if __name__ == "__main__":
    generator = TestDataGenerator()
    generator.generate_all()
'''
        
        # 保存数据生成器脚本
        script_path = self.backend_dir / "generate_test_data.py"
        with open(script_path, "w", encoding="utf-8") as f:
            f.write(data_generator_script)
        
        # 设置执行权限
        os.chmod(script_path, 0o755)
        
        print(f"✅ 测试数据生成器已创建: {script_path}")
    
    def create_environment_sync_tool(self):
        """创建环境同步工具"""
        print("🔄 创建环境同步工具...")
        
        sync_tool_script = '''#!/usr/bin/env python3
"""
环境同步工具
用于同步不同环境之间的数据结构和配置
"""

import requests
import json
import os
import sys
import django
from django.conf import settings

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quotes_admin.settings_local')
django.setup()

from quotesapp.models import Authors, Categories, Sources

class EnvironmentSyncTool:
    def __init__(self):
        self.prod_api = "https://api.quotese.com/api/"
        self.local_api = "http://127.0.0.1:8000/api/"
    
    def sync_schema_from_production(self):
        """从生产环境同步数据结构"""
        print("📥 从生产环境同步数据结构...")
        
        try:
            # 获取生产环境的数据结构信息
            prod_authors = requests.get(f"{self.prod_api}authors/").json()
            prod_categories = requests.get(f"{self.prod_api}categories/").json()
            
            print(f"生产环境数据量:")
            print(f"  作者: {len(prod_authors)}")
            print(f"  类别: {len(prod_categories)}")
            
            # 同步作者数据结构（仅结构，不包含具体内容）
            for author_data in prod_authors[:10]:  # 只同步前10个作为示例
                author, created = Authors.objects.get_or_create(
                    name=author_data['name'],
                    defaults={
                        'quotes_count': 0,
                        'created_at': author_data.get('created_at'),
                        'updated_at': author_data.get('updated_at')
                    }
                )
                if created:
                    print(f"  同步作者: {author.name}")
            
            # 同步类别数据结构
            for category_data in prod_categories[:15]:  # 只同步前15个
                category, created = Categories.objects.get_or_create(
                    name=category_data['name'],
                    defaults={
                        'quotes_count': 0,
                        'created_at': category_data.get('created_at'),
                        'updated_at': category_data.get('updated_at')
                    }
                )
                if created:
                    print(f"  同步类别: {category.name}")
            
            print("✅ 数据结构同步完成")
            
        except Exception as e:
            print(f"❌ 同步失败: {e}")
    
    def compare_environments(self):
        """比较不同环境的差异"""
        print("🔍 比较环境差异...")
        
        try:
            # 获取本地和生产环境数据
            local_authors = requests.get(f"{self.local_api}authors/").json()
            prod_authors = requests.get(f"{self.prod_api}authors/").json()
            
            print(f"📊 环境对比:")
            print(f"  本地作者数量: {len(local_authors)}")
            print(f"  生产作者数量: {len(prod_authors)}")
            
            # 找出差异
            local_names = {author['name'] for author in local_authors}
            prod_names = {author['name'] for author in prod_authors}
            
            only_in_prod = prod_names - local_names
            only_in_local = local_names - prod_names
            
            if only_in_prod:
                print(f"  仅在生产环境: {len(only_in_prod)} 个作者")
            if only_in_local:
                print(f"  仅在本地环境: {len(only_in_local)} 个作者")
            
        except Exception as e:
            print(f"❌ 比较失败: {e}")

if __name__ == "__main__":
    tool = EnvironmentSyncTool()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "sync":
            tool.sync_schema_from_production()
        elif sys.argv[1] == "compare":
            tool.compare_environments()
        else:
            print("用法: python sync_environments.py [sync|compare]")
    else:
        tool.compare_environments()
'''
        
        # 保存同步工具脚本
        script_path = self.backend_dir / "sync_environments.py"
        with open(script_path, "w", encoding="utf-8") as f:
            f.write(sync_tool_script)
        
        # 设置执行权限
        os.chmod(script_path, 0o755)
        
        print(f"✅ 环境同步工具已创建: {script_path}")
    
    def create_health_check_script(self):
        """创建环境健康检查脚本"""
        print("🏥 创建环境健康检查脚本...")
        
        health_check_script = '''#!/usr/bin/env python3
"""
环境健康检查脚本
定期检查开发环境的健康状态
"""

import requests
import time
import json
from datetime import datetime

class HealthChecker:
    def __init__(self):
        self.local_api = "http://127.0.0.1:8000/api/"
        self.local_frontend = "http://localhost:8081/"
        self.checks = []
    
    def check_api_health(self):
        """检查API健康状态"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.local_api}authors/", timeout=5)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                self.checks.append({
                    'service': 'Local API',
                    'status': '✅ 正常',
                    'response_time': f"{response_time:.3f}s",
                    'data_count': len(data)
                })
            else:
                self.checks.append({
                    'service': 'Local API',
                    'status': f'❌ 错误 ({response.status_code})',
                    'response_time': f"{response_time:.3f}s"
                })
        except Exception as e:
            self.checks.append({
                'service': 'Local API',
                'status': f'❌ 连接失败: {e}'
            })
    
    def check_frontend_health(self):
        """检查前端健康状态"""
        try:
            start_time = time.time()
            response = requests.get(self.local_frontend, timeout=5)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.checks.append({
                    'service': 'Frontend',
                    'status': '✅ 正常',
                    'response_time': f"{response_time:.3f}s"
                })
            else:
                self.checks.append({
                    'service': 'Frontend',
                    'status': f'❌ 错误 ({response.status_code})'
                })
        except Exception as e:
            self.checks.append({
                'service': 'Frontend',
                'status': f'❌ 连接失败: {e}'
            })
    
    def run_health_check(self):
        """运行完整的健康检查"""
        print(f"🏥 环境健康检查 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        self.check_api_health()
        self.check_frontend_health()
        
        # 输出结果
        for check in self.checks:
            print(f"{check['service']}: {check['status']}")
            if 'response_time' in check:
                print(f"  响应时间: {check['response_time']}")
            if 'data_count' in check:
                print(f"  数据量: {check['data_count']}")
        
        # 总体状态
        failed_checks = [c for c in self.checks if '❌' in c['status']]
        if failed_checks:
            print(f"\n⚠️  发现 {len(failed_checks)} 个问题")
            return False
        else:
            print(f"\n✅ 所有服务正常运行")
            return True

if __name__ == "__main__":
    checker = HealthChecker()
    checker.run_health_check()
'''
        
        # 保存健康检查脚本
        script_path = self.project_root / "health_check.py"
        with open(script_path, "w", encoding="utf-8") as f:
            f.write(health_check_script)
        
        # 设置执行权限
        os.chmod(script_path, 0o755)
        
        print(f"✅ 环境健康检查脚本已创建: {script_path}")
    
    def implement_all(self):
        """实施所有最佳实践"""
        print("🚀 实施Quotese开发环境最佳实践...")
        print("=" * 60)
        
        self.fix_google_fonts_dependency()
        print()
        self.create_data_generator()
        print()
        self.create_environment_sync_tool()
        print()
        self.create_health_check_script()
        
        print("\n" + "=" * 60)
        print("🎉 最佳实践实施完成！")
        print("\n📋 后续步骤:")
        print("1. 运行数据生成器: cd backend && python generate_test_data.py")
        print("2. 检查环境健康: python health_check.py")
        print("3. 同步环境数据: cd backend && python sync_environments.py sync")
        print("4. 手动下载Google Fonts字体文件到 frontend/fonts/")

if __name__ == "__main__":
    implementer = BestPracticesImplementer()
    implementer.implement_all()
