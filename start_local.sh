#!/bin/bash

# Quotese.com 本地开发环境快速启动脚本
# 用于验证热门模块优化效果

echo "🚀 启动Quotese.com本地开发环境..."
echo "📍 项目路径: $(pwd)"

# 检查是否在正确的目录
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "❌ 错误：请在项目根目录下运行此脚本"
    echo "   当前目录: $(pwd)"
    echo "   应该包含 backend/ 和 frontend/ 目录"
    exit 1
fi

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误：未找到Python3，请确保Python3已安装"
    exit 1
fi

echo "✅ Python版本: $(python3 --version)"

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  警告：端口 $port 已被占用"
        echo "   请先停止占用进程或使用其他端口"
        echo "   查看占用进程: lsof -i :$port"
        return 1
    fi
    return 0
}

# 检查端口
echo "🔍 检查端口占用情况..."
if ! check_port 8000; then
    echo "❌ Django端口8000被占用，请先停止相关服务"
    exit 1
fi

if ! check_port 8083; then
    echo "❌ 前端端口8083被占用，请先停止相关服务"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动Django后端
echo "🔧 启动Django后端服务器..."
cd backend

# 检查Django依赖
if [ ! -f "manage.py" ]; then
    echo "❌ 错误：未找到manage.py文件"
    exit 1
fi

# 启动Django服务器
echo "   启动命令: python3 manage.py runserver 8000 --settings=quotes_admin.settings_local"
python3 manage.py runserver 8000 --settings=quotes_admin.settings_local > ../logs/django.log 2>&1 &
DJANGO_PID=$!

if [ $? -eq 0 ]; then
    echo "✅ Django后端服务器启动成功 (PID: $DJANGO_PID)"
    echo "   地址: http://localhost:8000"
    echo "   日志: logs/django.log"
else
    echo "❌ Django后端服务器启动失败"
    exit 1
fi

# 等待Django启动
echo "⏳ 等待Django服务器启动..."
sleep 5

# 检查Django是否真正启动
if ! curl -s http://localhost:8000 > /dev/null; then
    echo "⚠️  Django服务器可能未完全启动，继续启动前端..."
fi

# 启动前端服务器
echo "🌐 启动前端语义化URL服务器..."
cd ../frontend

# 检查前端文件
if [ ! -f "semantic_url_server.py" ]; then
    echo "❌ 错误：未找到semantic_url_server.py文件"
    kill $DJANGO_PID
    exit 1
fi

if [ ! -f "index.html" ]; then
    echo "❌ 错误：未找到index.html文件"
    kill $DJANGO_PID
    exit 1
fi

# 启动前端服务器
echo "   启动命令: python3 semantic_url_server.py 8083"
python3 semantic_url_server.py 8083 > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!

if [ $? -eq 0 ]; then
    echo "✅ 前端服务器启动成功 (PID: $FRONTEND_PID)"
    echo "   地址: http://localhost:8083"
    echo "   日志: logs/frontend.log"
else
    echo "❌ 前端服务器启动失败"
    kill $DJANGO_PID
    exit 1
fi

# 等待前端启动
echo "⏳ 等待前端服务器启动..."
sleep 3

# 验证服务状态
echo "🔍 验证服务状态..."

# 检查Django
if curl -s http://localhost:8000 > /dev/null; then
    echo "✅ Django后端服务正常"
else
    echo "⚠️  Django后端服务可能有问题"
fi

# 检查前端
if curl -s http://localhost:8083 > /dev/null; then
    echo "✅ 前端服务正常"
else
    echo "⚠️  前端服务可能有问题"
fi

# 显示启动完成信息
echo ""
echo "🎉 Quotese.com本地开发环境启动完成！"
echo ""
echo "📍 访问地址："
echo "   🌐 网站首页: http://localhost:8083"
echo "   🔧 Django管理: http://localhost:8000/admin"
echo "   📡 API根地址: http://localhost:8000/api"
echo ""
echo "🧪 热门模块优化验证："
echo "   1. 访问任意详情页面（如: http://localhost:8083/categories/life/）"
echo "   2. 点击右侧热门模块中的实体"
echo "   3. 观察跳转速度（应该 < 5ms）"
echo "   4. 查看控制台日志确认优化路径"
echo ""
echo "🔧 性能测试："
echo "   - 添加 ?perf-test=true 到URL"
echo "   - 或按 Ctrl+Shift+P 启动测试工具"
echo ""
echo "📋 进程信息："
echo "   Django PID: $DJANGO_PID"
echo "   Frontend PID: $FRONTEND_PID"
echo ""
echo "📝 日志文件："
echo "   Django: logs/django.log"
echo "   Frontend: logs/frontend.log"
echo ""
echo "⚠️  按 Ctrl+C 停止所有服务"

# 创建停止函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    
    if kill -0 $DJANGO_PID 2>/dev/null; then
        echo "   停止Django服务器 (PID: $DJANGO_PID)"
        kill $DJANGO_PID
    fi
    
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "   停止前端服务器 (PID: $FRONTEND_PID)"
        kill $FRONTEND_PID
    fi
    
    echo "✅ 所有服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup INT TERM

# 等待用户中断
echo "💡 提示：在新终端窗口中可以继续开发工作"
echo "   当前终端将保持运行以维持服务"
echo ""

# 保持脚本运行
while true; do
    # 检查进程是否还在运行
    if ! kill -0 $DJANGO_PID 2>/dev/null; then
        echo "❌ Django服务器意外停止"
        break
    fi
    
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "❌ 前端服务器意外停止"
        break
    fi
    
    sleep 5
done

# 如果到达这里，说明有服务意外停止
echo "⚠️  检测到服务意外停止，正在清理..."
cleanup
