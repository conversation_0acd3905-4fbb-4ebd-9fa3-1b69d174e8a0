<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 1600">
  <defs>
    <style>
      .node { fill: #f9f9f9; stroke: #333; stroke-width: 2px; }
      .decision { fill: #ffe4b5; stroke: #deb887; stroke-width: 2px; }
      .success { fill: #90ee90; stroke: #006400; stroke-width: 3px; }
      .api { fill: #87ceeb; stroke: #4682b4; stroke-width: 2px; }
      .error { fill: #ffb6c1; stroke: #dc143c; stroke-width: 2px; }
      .final { fill: #98fb98; stroke: #32cd32; stroke-width: 3px; }
      .mapper { fill: #ffe4b5; stroke: #deb887; stroke-width: 2px; }
      .check { fill: #e6e6fa; stroke: #9370db; stroke-width: 2px; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .title { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 2px; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="30" class="text" style="font-size: 18px; font-weight: bold;">Quotese.com 数据流架构完整业务逻辑流程图</text>
  
  <!-- Start Node -->
  <rect x="450" y="60" width="300" height="60" rx="10" class="node"/>
  <text x="600" y="85" class="text">用户访问语义化URL</text>
  <text x="600" y="105" class="title">例: /categories/life/</text>
  
  <!-- URL Server -->
  <rect x="450" y="160" width="300" height="60" rx="10" class="node"/>
  <text x="600" y="185" class="text">语义化URL服务器</text>
  <text x="600" y="205" class="title">semantic_url_server.py</text>
  
  <!-- URL Processing -->
  <rect x="400" y="260" width="400" height="60" rx="10" class="node"/>
  <text x="600" y="285" class="text">正则表达式匹配URL模式</text>
  <text x="600" y="305" class="title">重定向到对应HTML页面</text>
  
  <!-- PageRouter -->
  <rect x="450" y="360" width="300" height="60" rx="10" class="node"/>
  <text x="600" y="385" class="text">PageRouter.initializePage()</text>
  
  <!-- UrlHandler -->
  <rect x="450" y="460" width="300" height="60" rx="10" class="node"/>
  <text x="600" y="485" class="text">UrlHandler.getCurrentPageType()</text>
  
  <!-- Page Type Decision -->
  <polygon points="600,560 700,600 600,640 500,600" class="decision"/>
  <text x="600" y="605" class="text">页面类型检测</text>
  
  <!-- Page Initializers -->
  <rect x="100" y="700" width="200" height="50" rx="10" class="node"/>
  <text x="200" y="730" class="text">initAuthorPage()</text>
  
  <rect x="350" y="700" width="200" height="50" rx="10" class="node"/>
  <text x="450" y="730" class="text">initCategoryPage()</text>
  
  <rect x="600" y="700" width="200" height="50" rx="10" class="node"/>
  <text x="700" y="730" class="text">initSourcePage()</text>
  
  <rect x="850" y="700" width="150" height="50" rx="10" class="error"/>
  <text x="925" y="730" class="text">handle404()</text>
  
  <!-- URL Parsing -->
  <rect x="350" y="800" width="500" height="60" rx="10" class="node"/>
  <text x="600" y="825" class="text">UrlHandler解析函数</text>
  <text x="600" y="845" class="title">parseAuthorFromPath() / parseCategoryFromPath() / parseSourceFromPath()</text>
  
  <!-- Deslugify -->
  <rect x="400" y="900" width="400" height="60" rx="10" class="node"/>
  <text x="600" y="925" class="text">UrlHandler.deslugify()</text>
  <text x="600" y="945" class="title">slug转换为名称</text>
  
  <!-- EntityIdMapper -->
  <rect x="350" y="1000" width="500" height="60" rx="10" class="mapper"/>
  <text x="600" y="1025" class="text">EntityIdMapper优先级查询</text>
  <text x="600" y="1045" class="title">findEntityWithPriority()</text>
  
  <!-- Mapping Check -->
  <polygon points="600,1100 750,1140 600,1180 450,1140" class="check"/>
  <text x="600" y="1135" class="text">检查已知ID映射表</text>
  <text x="600" y="1155" class="title">KNOWN_ENTITY_IDS</text>
  
  <!-- Success Path -->
  <rect x="100" y="1220" width="300" height="80" rx="10" class="success"/>
  <text x="250" y="1245" class="text">返回已知ID</text>
  <text x="250" y="1265" class="title">响应时间 &lt; 5ms</text>
  <text x="250" y="1285" class="title">性能提升40-50倍</text>
  
  <!-- API Fallback -->
  <rect x="700" y="1220" width="300" height="60" rx="10" class="node"/>
  <text x="850" y="1245" class="text">API Fallback查询机制</text>
  <text x="850" y="1265" class="title">多重查询策略</text>
  
  <!-- API Query 1 -->
  <rect x="700" y="1320" width="300" height="60" rx="10" class="node"/>
  <text x="850" y="1345" class="text">查询1: 原始slug</text>
  <text x="850" y="1365" class="title">例: mehmet-murat-ildan</text>
  
  <!-- API Response 1 -->
  <polygon points="850,1420 950,1460 850,1500 750,1460" class="decision"/>
  <text x="850" y="1465" class="text">API响应检查</text>
  
  <!-- Success with Learning -->
  <rect x="500" y="1540" width="300" height="80" rx="10" class="api"/>
  <text x="650" y="1565" class="text">返回结果 + 自动学习</text>
  <text x="650" y="1585" class="title">添加到映射表</text>
  
  <!-- Error Path -->
  <rect x="950" y="1540" width="200" height="60" rx="10" class="error"/>
  <text x="1050" y="1565" class="text">显示错误信息</text>
  <text x="1050" y="1585" class="title">Entity not found</text>
  
  <!-- Arrows -->
  <line x1="600" y1="120" x2="600" y2="160" class="arrow"/>
  <line x1="600" y1="220" x2="600" y2="260" class="arrow"/>
  <line x1="600" y1="320" x2="600" y2="360" class="arrow"/>
  <line x1="600" y1="420" x2="600" y2="460" class="arrow"/>
  <line x1="600" y1="520" x2="600" y2="560" class="arrow"/>
  
  <!-- Decision branches -->
  <line x1="500" y1="600" x2="200" y2="700" class="arrow"/>
  <line x1="550" y1="600" x2="450" y2="700" class="arrow"/>
  <line x1="650" y1="600" x2="700" y2="700" class="arrow"/>
  <line x1="700" y1="600" x2="925" y2="700" class="arrow"/>
  
  <!-- Convergence -->
  <line x1="200" y1="750" x2="600" y2="800" class="arrow"/>
  <line x1="450" y1="750" x2="600" y2="800" class="arrow"/>
  <line x1="700" y1="750" x2="600" y2="800" class="arrow"/>
  
  <line x1="600" y1="860" x2="600" y2="900" class="arrow"/>
  <line x1="600" y1="960" x2="600" y2="1000" class="arrow"/>
  <line x1="600" y1="1060" x2="600" y2="1100" class="arrow"/>
  
  <!-- Mapping decision -->
  <line x1="450" y1="1140" x2="250" y2="1220" class="arrow"/>
  <line x1="750" y1="1140" x2="850" y2="1220" class="arrow"/>
  
  <line x1="850" y1="1280" x2="850" y2="1320" class="arrow"/>
  <line x1="850" y1="1380" x2="850" y2="1420" class="arrow"/>
  
  <!-- API responses -->
  <line x1="750" y1="1460" x2="650" y2="1540" class="arrow"/>
  <line x1="950" y1="1460" x2="1050" y2="1540" class="arrow"/>
  
  <!-- Success convergence -->
  <line x1="250" y1="1300" x2="250" y2="1450" class="arrow"/>
  <line x1="250" y1="1450" x2="600" y2="1450" class="arrow"/>
  <line x1="650" y1="1540" x2="650" y2="1450" class="arrow"/>
  
  <!-- Labels -->
  <text x="350" y="650" class="title">author-detail</text>
  <text x="450" y="650" class="title">category-detail</text>
  <text x="700" y="650" class="title">source-detail</text>
  <text x="925" y="650" class="title">unknown</text>
  
  <text x="350" y="1180" class="title">命中</text>
  <text x="850" y="1180" class="title">未命中</text>
  
  <text x="700" y="1500" class="title">成功</text>
  <text x="1000" y="1500" class="title">失败</text>
</svg>
