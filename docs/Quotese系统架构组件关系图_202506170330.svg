<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000">
  <defs>
    <style>
      .layer-bg { fill: #f8f9fa; stroke: #dee2e6; stroke-width: 2px; stroke-dasharray: 5,5; }
      .url-layer { fill: #ffe4b5; stroke: #deb887; stroke-width: 2px; }
      .data-layer { fill: #e6e6fa; stroke: #9370db; stroke-width: 2px; }
      .page-layer { fill: #f0f8ff; stroke: #4682b4; stroke-width: 2px; }
      .seo-layer { fill: #f5f5dc; stroke: #d2b48c; stroke-width: 2px; }
      .api-layer { fill: #ffa07a; stroke: #ff6347; stroke-width: 2px; }
      .monitor-layer { fill: #98fb98; stroke: #32cd32; stroke-width: 2px; }
      .mapper-highlight { fill: #ffe4b5; stroke: #deb887; stroke-width: 3px; }
      .cache-highlight { fill: #90ee90; stroke: #006400; stroke-width: 3px; }
      .api-highlight { fill: #87ceeb; stroke: #4682b4; stroke-width: 2px; }
      .db-highlight { fill: #ffa07a; stroke: #ff6347; stroke-width: 2px; }
      .stats-highlight { fill: #98fb98; stroke: #32cd32; stroke-width: 2px; }
      .text { font-family: Arial, sans-serif; font-size: 11px; text-anchor: middle; }
      .title { font-family: Arial, sans-serif; font-size: 9px; text-anchor: middle; }
      .layer-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 2px; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #666; stroke-width: 1.5px; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" class="layer-title" style="font-size: 18px;">Quotese.com 系统架构组件关系图</text>
  
  <!-- Frontend Architecture Layer Background -->
  <rect x="50" y="60" width="1300" height="600" rx="15" class="layer-bg"/>
  <text x="700" y="85" class="layer-title">前端架构层</text>
  
  <!-- URL Processing Layer -->
  <rect x="80" y="110" width="380" height="180" rx="10" class="layer-bg"/>
  <text x="270" y="135" class="layer-title" style="font-size: 12px;">URL处理层</text>
  
  <rect x="100" y="150" width="120" height="60" rx="8" class="url-layer"/>
  <text x="160" y="175" class="text">semantic_url_server.py</text>
  <text x="160" y="195" class="title">语义化URL服务器</text>
  
  <rect x="240" y="150" width="100" height="60" rx="8" class="url-layer"/>
  <text x="290" y="175" class="text">page-router.js</text>
  <text x="290" y="195" class="title">页面路由管理器</text>
  
  <rect x="100" y="220" width="120" height="60" rx="8" class="url-layer"/>
  <text x="160" y="245" class="text">url-handler.js</text>
  <text x="160" y="265" class="title">URL解析处理器</text>
  
  <!-- Data Acquisition Layer -->
  <rect x="500" y="110" width="380" height="180" rx="10" class="layer-bg"/>
  <text x="690" y="135" class="layer-title" style="font-size: 12px;">数据获取层</text>
  
  <rect x="520" y="150" width="120" height="60" rx="8" class="mapper-highlight"/>
  <text x="580" y="175" class="text">entity-id-mapper.js</text>
  <text x="580" y="195" class="title">实体ID映射器</text>
  
  <rect x="660" y="150" width="100" height="60" rx="8" class="api-highlight"/>
  <text x="710" y="175" class="text">api-client.js</text>
  <text x="710" y="195" class="title">API客户端</text>
  
  <rect x="520" y="220" width="120" height="60" rx="8" class="cache-highlight"/>
  <text x="580" y="245" class="text">KNOWN_ENTITY_IDS</text>
  <text x="580" y="265" class="title">已知ID映射表</text>
  
  <!-- Page Control Layer -->
  <rect x="920" y="110" width="400" height="180" rx="10" class="layer-bg"/>
  <text x="1120" y="135" class="layer-title" style="font-size: 12px;">页面控制层</text>
  
  <rect x="940" y="150" width="110" height="50" rx="8" class="page-layer"/>
  <text x="995" y="170" class="text">pages/category.js</text>
  <text x="995" y="185" class="title">类别页面控制器</text>
  
  <rect x="1070" y="150" width="110" height="50" rx="8" class="page-layer"/>
  <text x="1125" y="170" class="text">pages/author.js</text>
  <text x="1125" y="185" class="title">作者页面控制器</text>
  
  <rect x="1200" y="150" width="110" height="50" rx="8" class="page-layer"/>
  <text x="1255" y="170" class="text">pages/source.js</text>
  <text x="1255" y="185" class="title">来源页面控制器</text>
  
  <!-- SEO Management Layer -->
  <rect x="920" y="220" width="400" height="70" rx="10" class="layer-bg"/>
  <text x="1120" y="245" class="layer-title" style="font-size: 12px;">SEO管理层</text>
  
  <rect x="1040" y="250" width="160" height="30" rx="8" class="seo-layer"/>
  <text x="1120" y="270" class="text">seo-manager.js - SEO标签管理器</text>
  
  <!-- Backend API Layer -->
  <rect x="50" y="700" width="600" height="120" rx="15" class="layer-bg"/>
  <text x="350" y="725" class="layer-title">后端API层</text>
  
  <rect x="100" y="750" width="200" height="60" rx="8" class="db-highlight"/>
  <text x="200" y="775" class="text">GraphQL API</text>
  <text x="200" y="795" class="title">api.quotese.com</text>
  
  <rect x="350" y="750" width="200" height="60" rx="8" class="api-layer"/>
  <text x="450" y="775" class="text">数据库</text>
  <text x="450" y="795" class="title">MySQL/SQLite</text>
  
  <!-- Performance Monitoring Layer -->
  <rect x="700" y="700" width="650" height="120" rx="15" class="layer-bg"/>
  <text x="1025" y="725" class="layer-title">性能监控层</text>
  
  <rect x="730" y="750" width="150" height="60" rx="8" class="stats-highlight"/>
  <text x="805" y="775" class="text">性能统计</text>
  <text x="805" y="795" class="title">EntityIdMapper.stats</text>
  
  <rect x="900" y="750" width="150" height="60" rx="8" class="monitor-layer"/>
  <text x="975" y="775" class="text">缓存系统</text>
  <text x="975" y="795" class="title">ApiClient.cache</text>
  
  <rect x="1070" y="750" width="150" height="60" rx="8" class="monitor-layer"/>
  <text x="1145" y="775" class="text">错误日志</text>
  <text x="1145" y="795" class="title">Console.log</text>
  
  <!-- Data Flow Arrows -->
  <!-- URL Processing Flow -->
  <line x1="220" y1="180" x2="240" y2="180" class="arrow"/>
  <line x1="290" y1="210" x2="160" y2="220" class="arrow"/>
  
  <!-- URL to Data Layer -->
  <line x1="340" y1="180" x2="520" y2="180" class="arrow"/>
  <line x1="220" y1="250" x2="520" y2="180" class="arrow"/>
  
  <!-- Data Layer Internal -->
  <line x1="580" y1="210" x2="580" y2="220" class="arrow"/>
  <line x1="640" y1="180" x2="660" y2="180" class="arrow"/>
  
  <!-- Data to API -->
  <line x1="710" y1="210" x2="200" y2="750" class="arrow"/>
  <line x1="200" y1="810" x2="350" y2="780" class="arrow"/>
  
  <!-- Router to Page Controllers -->
  <line x1="340" y1="180" x2="940" y2="175" class="arrow"/>
  <line x1="340" y1="180" x2="1070" y2="175" class="arrow"/>
  <line x1="340" y1="180" x2="1200" y2="175" class="arrow"/>
  
  <!-- Page Controllers to Data Layer -->
  <line x1="940" y1="175" x2="640" y2="180" class="arrow"/>
  <line x1="1070" y1="175" x2="640" y2="180" class="arrow"/>
  <line x1="1200" y1="175" x2="640" y2="180" class="arrow"/>
  
  <!-- Page Controllers to SEO -->
  <line x1="995" y1="200" x2="1040" y2="265" class="arrow"/>
  <line x1="1125" y1="200" x2="1120" y2="250" class="arrow"/>
  <line x1="1255" y1="200" x2="1200" y2="265" class="arrow"/>
  
  <!-- Data Layer to Monitoring -->
  <line x1="580" y1="280" x2="730" y2="780" class="arrow"/>
  <line x1="710" y1="210" x2="900" y2="780" class="arrow"/>
  <line x1="580" y1="280" x2="1070" y2="780" class="arrow"/>
  <line x1="710" y1="210" x2="1070" y2="780" class="arrow"/>
  
  <!-- Key Performance Indicators -->
  <rect x="80" y="320" width="300" height="120" rx="10" style="fill: #fffacd; stroke: #daa520; stroke-width: 2px;"/>
  <text x="230" y="345" class="layer-title" style="font-size: 12px;">关键性能指标</text>
  <text x="230" y="365" class="title">映射表查询: &lt; 5ms</text>
  <text x="230" y="380" class="title">API查询: 180-250ms</text>
  <text x="230" y="395" class="title">命中率: 87%</text>
  <text x="230" y="410" class="title">成功率: 95%+</text>
  <text x="230" y="425" class="title">性能提升: 40-50倍</text>
  
  <!-- Technology Stack -->
  <rect x="420" y="320" width="300" height="120" rx="10" style="fill: #f0f0f0; stroke: #808080; stroke-width: 2px;"/>
  <text x="570" y="345" class="layer-title" style="font-size: 12px;">核心技术栈</text>
  <text x="570" y="365" class="title">前端: JavaScript ES6+</text>
  <text x="570" y="380" class="title">后端: GraphQL API</text>
  <text x="570" y="395" class="title">数据库: MySQL/SQLite</text>
  <text x="570" y="410" class="title">缓存: 内存映射表</text>
  <text x="570" y="425" class="title">监控: 实时性能统计</text>
  
  <!-- Data Flow Legend -->
  <rect x="750" y="320" width="250" height="120" rx="10" style="fill: #e0e0e0; stroke: #808080; stroke-width: 2px;"/>
  <text x="875" y="345" class="layer-title" style="font-size: 12px;">数据流向说明</text>
  <line x1="770" y1="360" x2="800" y2="360" class="arrow"/>
  <text x="820" y="365" class="title">主要数据流</text>
  <line x1="770" y1="380" x2="800" y2="380" class="data-flow"/>
  <text x="820" y="385" class="title">监控数据流</text>
  <rect x="770" y="395" width="20" height="15" class="cache-highlight"/>
  <text x="820" y="405" class="title">高性能组件</text>
  <rect x="770" y="415" width="20" height="15" class="api-highlight"/>
  <text x="820" y="425" class="title">API通信组件</text>
</svg>
