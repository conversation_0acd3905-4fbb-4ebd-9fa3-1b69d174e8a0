# 本地开发环境设置指南

## 概述

本文档描述了Quotese.com项目的本地开发环境设置，包括Django后端API服务器和前端语义化URL服务器的配置和使用。

## 环境架构

### 服务器配置
- **Django后端API服务器**: `http://localhost:8000`
  - 使用SQLite数据库 (`backend/db.sqlite3`)
  - 配置文件: `backend/quotes_admin/settings_local.py`
  - API端点: `http://localhost:8000/api/`

- **前端语义化URL服务器**: `http://localhost:8081`
  - 支持所有语义化URL路由
  - 静态资源自动路径修复
  - 配置文件: `frontend/js/config.js`

### 数据库配置
- **本地开发**: SQLite (`backend/db.sqlite3`)
- **生产环境**: MySQL (Docker容器)
- 数据迁移: 自动应用Django migrations

## 快速启动

### 1. 自动启动（推荐）
```bash
# 启动完整开发环境
bash scripts/start_local_development.sh

# 测试环境是否正常
bash scripts/test_local_development.sh

# 停止开发环境
bash scripts/stop_local_development.sh
```

### 2. 手动启动
```bash
# 启动Django后端
cd backend
python3 manage.py runserver 8000 --settings=quotes_admin.settings_local

# 启动前端服务器（新终端）
cd frontend
python3 semantic_url_server.py
```

## 访问地址

### 前端页面
- **首页**: http://localhost:8081/
- **Authors列表**: http://localhost:8081/authors/
- **Categories列表**: http://localhost:8081/categories/
- **Sources列表**: http://localhost:8081/sources/
- **Author详情**: http://localhost:8081/authors/albert-einstein/
- **Category详情**: http://localhost:8081/categories/life/
- **Source详情**: http://localhost:8081/sources/the-art-of-war/

### 后端管理
- **Django管理后台**: http://localhost:8000/admin/
- **API根目录**: http://localhost:8000/api/
- **Authors API**: http://localhost:8000/api/authors/
- **Categories API**: http://localhost:8000/api/categories/
- **Sources API**: http://localhost:8000/api/sources/

## 配置文件

### 前端配置 (`frontend/js/config.js`)
```javascript
development: {
    apiEndpoint: 'http://localhost:8000/api/',  // 本地API端点
    useMockData: false,  // 使用真实API数据
    debug: true
}
```

### Django配置 (`backend/quotes_admin/settings_local.py`)
```python
# SQLite数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# 开发环境设置
DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1']
```

## 语义化URL支持

### URL模式
- `/authors/` → `authors.html`
- `/authors/{slug}/` → `author.html`
- `/categories/` → `categories.html`
- `/categories/{slug}/` → `category.html`
- `/sources/` → `sources.html`
- `/sources/{slug}/` → `source.html`
- `/quotes/` → `quotes.html`
- `/quotes/{id}/` → `quote.html`

### 静态资源路径修复
前端服务器自动修复语义化URL中的静态资源路径：
- `/authors/albert-einstein/css/styles.css` → `/css/styles.css`
- `/categories/life/js/api-client.js` → `/js/api-client.js`

## 数据库管理

### 初始化数据库
```bash
cd backend
python3 manage.py migrate --settings=quotes_admin.settings_local
python3 manage.py loaddata fixtures/initial_data.json --settings=quotes_admin.settings_local
```

### 创建超级用户
```bash
cd backend
python3 manage.py createsuperuser --settings=quotes_admin.settings_local
```

## 测试数据

### 预置数据
- **Authors**: 11个作者（包括Albert Einstein, Winston Churchill等）
- **Categories**: 10个分类（Life, Inspirational, Motivational等）
- **Sources**: 20个来源（各种书籍、演讲、文章等）

### API测试
```bash
# 测试Authors API
curl "http://localhost:8000/api/authors/"

# 测试Categories API
curl "http://localhost:8000/api/categories/"

# 测试Sources API
curl "http://localhost:8000/api/sources/"
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8000
   lsof -i :8081
   
   # 停止占用进程
   kill -9 <PID>
   ```

2. **数据库文件不存在**
   ```bash
   cd backend
   python3 manage.py migrate --settings=quotes_admin.settings_local
   ```

3. **API连接失败**
   - 检查Django服务器是否运行在端口8000
   - 检查`frontend/js/config.js`中的API端点配置
   - 查看浏览器开发者工具的网络请求

4. **语义化URL不工作**
   - 确保前端服务器运行在端口8081
   - 检查`semantic_url_server.py`的URL模式配置
   - 查看服务器日志输出

### 日志查看
- **Django日志**: 查看运行`manage.py runserver`的终端输出
- **前端日志**: 查看运行`semantic_url_server.py`的终端输出
- **浏览器日志**: 打开开发者工具查看Console和Network标签

## 开发工作流

### 1. 启动环境
```bash
bash scripts/start_local_development.sh
```

### 2. 验证环境
```bash
bash scripts/test_local_development.sh
```

### 3. 开发调试
- 前端代码修改后刷新浏览器即可看到效果
- Django代码修改后服务器会自动重启
- 数据库修改需要运行migrations

### 4. 停止环境
```bash
bash scripts/stop_local_development.sh
```

## 性能优化

### EntityIdMapper缓存
- 本地开发环境支持完整的EntityIdMapper功能
- 已知ID映射表提供40-50倍性能提升
- 查询响应时间 < 5ms

### API优化
- 本地SQLite数据库提供快速响应
- 支持所有生产环境API功能
- 完整的数据关系和约束

## 部署差异

### 本地 vs 生产环境
| 配置项 | 本地开发 | 生产环境 |
|--------|----------|----------|
| 数据库 | SQLite | MySQL (Docker) |
| API端点 | localhost:8000 | ************:8000 |
| 前端服务器 | semantic_url_server.py | Nginx |
| 调试模式 | 启用 | 禁用 |
| 静态文件 | 开发服务器 | Nginx |

### 配置同步
- 本地开发环境尽可能模拟生产环境
- API接口完全兼容
- 数据结构保持一致
- URL路由规则相同

---

**最后更新**: 2025年6月20日 10:26
**版本**: v1.0
**维护者**: Quotese开发团队
