# Quotese运维操作手册

**版本**: 2.0.0  
**更新时间**: 2025年6月27日 03:00  
**适用范围**: 第1-2周优化后的系统  

## 📋 手册概述

本手册提供Quotese名言网站优化后系统的日常运维操作指南，包括性能监控、故障排查、配置管理和维护操作。

## 🔍 日常监控操作

### 1. 启动性能监控

#### 方法1: 快捷键启动
```
1. 在任意页面按 Ctrl+Shift+M
2. 监控仪表板将在右下角显示
3. 每5秒自动更新性能指标
```

#### 方法2: 控制台启动
```javascript
// 在浏览器控制台执行
if (!window.unifiedMonitor) {
    window.unifiedMonitor = new UnifiedPerformanceMonitor();
}
window.unifiedMonitor.startRealTimeMonitoring();
```

### 2. 关键指标监控

#### 必须监控的指标
| 指标名称 | 正常范围 | 警告阈值 | 紧急阈值 |
|----------|----------|----------|----------|
| **映射表命中率** | 95%+ | < 90% | < 80% |
| **API查询次数** | < 10/分钟 | 10-20/分钟 | > 20/分钟 |
| **平均响应时间** | < 10ms | 10-100ms | > 100ms |
| **内存使用** | < 100MB | 100-150MB | > 150MB |
| **错误率** | < 1% | 1-5% | > 5% |

#### 监控频率
- **实时监控**: 关键业务时段 (9:00-21:00)
- **定期检查**: 每日早晚各一次
- **深度分析**: 每周一次性能报告

### 3. 性能数据导出

```javascript
// 导出当前性能数据
window.unifiedMonitor.exportData();

// 获取性能报告
const report = window.unifiedMonitor.getPerformanceReport();
console.log(report);
```

## 🚨 故障排查指南

### 1. 映射表命中率下降

#### 症状
- 映射表命中率 < 90%
- API查询次数增加
- 页面响应时间变慢

#### 排查步骤
```javascript
// 1. 检查映射表配置
console.log(window.EntityIdMapper.getStats());

// 2. 验证映射表数据
console.log(window.KNOWN_ENTITY_IDS);

// 3. 检查最近访问的实体
console.log(window.entityCache);
```

#### 解决方案
1. **更新映射表**: 添加新的热门实体ID
2. **清理缓存**: 重启浏览器或清理缓存
3. **检查API**: 验证后端API是否正常

### 2. API查询过多

#### 症状
- API查询次数 > 20/分钟
- 后端服务器负载高
- 用户体验变慢

#### 排查步骤
```bash
# 检查Django后端日志
tail -f /path/to/django.log | grep "GET /api/"

# 统计API查询频率
grep "GET /api/" django.log | wc -l
```

#### 解决方案
1. **扩展映射表**: 添加频繁查询的实体
2. **增加缓存**: 提高缓存大小限制
3. **优化查询**: 检查是否有重复查询

### 3. 内存使用过高

#### 症状
- 内存使用 > 150MB
- 页面响应变慢
- 浏览器卡顿

#### 排查步骤
```javascript
// 1. 检查内存使用
if (performance.memory) {
    console.log('Memory usage:', performance.memory);
}

// 2. 检查缓存大小
['categories', 'authors', 'sources'].forEach(type => {
    const cache = window.entityCache[type];
    console.log(`${type} cache size:`, cache?.size || 0);
});

// 3. 检查移动端优化
console.log('Mobile optimizer:', window.mobileOptimizer?.getStats());
```

#### 解决方案
1. **手动清理**: 执行积极清理
```javascript
window.mobileOptimizer.performEmergencyCleanup();
```

2. **调整配置**: 降低缓存大小限制
```javascript
window.mobileOptimizer.config.maxCacheSize = 20; // 降低到20
```

3. **重启优化**: 重新启动移动端优化
```javascript
window.mobileOptimizer.cleanup();
window.mobileOptimizer = new MobilePerformanceOptimizer();
window.mobileOptimizer.optimizeForMobile();
```

### 4. 页面标题显示错误

#### 症状
- 页面标题显示通用模板
- 标题未显示实体名称
- SEO效果差

#### 排查步骤
```javascript
// 1. 检查页面状态
console.log('Category state:', categoryPageState);
console.log('Author state:', authorPageState);
console.log('Source state:', sourcePageState);

// 2. 检查API响应
console.log('Last API response:', window.lastApiResponse);

// 3. 检查标题元素
console.log('Title element:', document.title);
console.log('Category name element:', document.getElementById('category-name'));
```

#### 解决方案
1. **刷新页面**: 重新加载页面
2. **清理缓存**: 清除浏览器缓存
3. **检查API**: 验证API返回正确的实体名称

## ⚙️ 配置管理

### 1. 映射表配置更新

#### 添加新的Categories映射
```javascript
// 编辑 frontend/js/entity-id-mapper.js
const KNOWN_ENTITY_IDS = {
    categories: {
        // 添加新映射
        'new-category': 123,  // 新类别ID
        // 现有映射...
    }
};
```

#### 添加新的Authors映射
```javascript
authors: {
    // 添加新映射
    'new-author': 456,  // 新作者ID
    // 现有映射...
}
```

#### 添加新的Sources映射
```javascript
sources: {
    // 添加新映射
    'new-source': 789,  // 新来源ID
    // 现有映射...
}
```

### 2. 移动端优化配置

#### 调整缓存大小
```javascript
// 编辑 frontend/js/mobile-performance-optimizer.js
this.config = {
    maxCacheSize: this.isMobile ? 25 : 50,  // 调整缓存大小
    aggressiveCleanup: this.isMobile,
    memoryThreshold: this.isMobile ? 40 : 80,  // 调整内存阈值
    cleanupInterval: this.isMobile ? 20000 : 40000  // 调整清理间隔
};
```

#### 调整设备检测
```javascript
detectMobileDevice() {
    // 自定义检测逻辑
    const customMobileCheck = window.innerWidth <= 768;
    return customMobileCheck;
}
```

### 3. 监控配置调整

#### 调整监控间隔
```javascript
// 修改监控更新频率
this.monitoringInterval = setInterval(() => {
    this.collectAllMetrics();
    this.updateMonitoringUI();
}, 3000);  // 改为3秒更新一次
```

#### 调整历史记录大小
```javascript
this.maxHistorySize = 200;  // 增加到200条记录
```

## 🔧 维护操作

### 1. 定期清理操作

#### 每日清理 (自动)
```javascript
// 系统自动执行，无需手动操作
// 清理过期缓存、未使用DOM元素
```

#### 每周清理 (手动)
```javascript
// 1. 清理所有缓存
Object.keys(window.entityCache).forEach(type => {
    window.entityCache[type].clear();
});

// 2. 重启移动端优化
window.mobileOptimizer.cleanup();
window.mobileOptimizer = new MobilePerformanceOptimizer();
window.mobileOptimizer.optimizeForMobile();

// 3. 导出性能数据
window.unifiedMonitor.exportData();
```

#### 每月清理 (深度)
```bash
# 1. 清理浏览器缓存
# 2. 重启Django服务器
# 3. 检查日志文件大小
# 4. 更新映射表数据
```

### 2. 性能优化操作

#### 优化映射表覆盖率
```javascript
// 1. 收集热门实体数据
// 访问 http://localhost:8081/test-collect-entity-ids.html

// 2. 分析访问频率
const stats = window.unifiedMonitor.getPerformanceReport();
console.log('Query history:', stats.recentQueries);

// 3. 更新映射表配置
// 将频繁查询的实体添加到映射表
```

#### 优化缓存策略
```javascript
// 1. 分析缓存命中率
['categories', 'authors', 'sources'].forEach(type => {
    const cache = window.entityCache[type];
    console.log(`${type} cache hit rate:`, cache.hitRate);
});

// 2. 调整缓存大小
if (hitRate < 90%) {
    // 增加缓存大小
    window.mobileOptimizer.config.maxCacheSize += 10;
}
```

### 3. 备份和恢复

#### 备份配置
```bash
# 备份映射表配置
cp frontend/js/entity-id-mapper.js backup/entity-id-mapper_$(date +%Y%m%d).js

# 备份性能数据
# 通过监控界面导出JSON文件
```

#### 恢复配置
```bash
# 恢复映射表配置
cp backup/entity-id-mapper_20250627.js frontend/js/entity-id-mapper.js

# 重启服务
# 刷新浏览器缓存
```

## 📊 性能报告生成

### 1. 日报生成
```javascript
// 每日性能报告
const dailyReport = {
    date: new Date().toISOString().split('T')[0],
    metrics: window.unifiedMonitor.metrics,
    summary: window.unifiedMonitor.getPerformanceReport().summary
};

console.log('Daily Report:', dailyReport);
```

### 2. 周报生成
```javascript
// 每周性能汇总
const weeklyReport = {
    week: 'Week 1-2',
    totalQueries: window.unifiedMonitor.metrics.totalQueries,
    avgResponseTime: window.unifiedMonitor.metrics.avgResponseTime,
    cacheHitRate: window.unifiedMonitor.metrics.cacheHitRate,
    errorRate: window.unifiedMonitor.metrics.errorRate
};

console.log('Weekly Report:', weeklyReport);
```

## 🆘 紧急响应流程

### 1. 系统完全无响应
1. **检查网络连接**
2. **重启Django服务器**
3. **清理浏览器缓存**
4. **检查映射表配置**

### 2. 性能严重下降
1. **启动性能监控** (Ctrl+Shift+M)
2. **检查关键指标**
3. **执行紧急清理**
4. **联系技术支持**

### 3. 数据不一致
1. **清理所有缓存**
2. **重新加载页面**
3. **验证API响应**
4. **更新映射表**

## 📞 联系信息

- **技术支持**: 性能优化项目组
- **紧急联系**: 24/7技术热线
- **文档更新**: 每次重大变更后更新

---

**手册维护**: 请在系统更新后及时更新此手册  
**最后更新**: 2025年6月27日 03:00
