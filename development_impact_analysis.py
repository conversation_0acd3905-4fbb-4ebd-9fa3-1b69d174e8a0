#!/usr/bin/env python3
"""
Quotese开发环境数据配置影响分析
评估当前数据配置对各种开发场景的影响
"""

import requests
import json
import time
import subprocess
from datetime import datetime

class DevelopmentImpactAnalyzer:
    def __init__(self):
        self.local_api = "http://127.0.0.1:8000/api/"
        self.local_graphql = "http://127.0.0.1:8000/graphql/"
        self.prod_api = "https://api.quotese.com/api/"
        self.prod_graphql = "https://api.quotese.com/graphql/"
        
    def test_api_connectivity(self):
        """测试API连接性"""
        print("🔗 API连接性测试")
        print("=" * 40)
        
        results = {}
        
        # 测试本地API
        try:
            response = requests.get(f"{self.local_api}authors/", timeout=5)
            if response.status_code == 200:
                data = response.json()
                results['local_api'] = {
                    'status': '✅ 正常',
                    'response_time': response.elapsed.total_seconds(),
                    'data_count': len(data)
                }
            else:
                results['local_api'] = {'status': f'❌ 错误 ({response.status_code})'}
        except Exception as e:
            results['local_api'] = {'status': f'❌ 连接失败: {e}'}
        
        # 测试生产API
        try:
            response = requests.get(f"{self.prod_api}authors/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                results['prod_api'] = {
                    'status': '✅ 正常',
                    'response_time': response.elapsed.total_seconds(),
                    'data_count': len(data)
                }
            else:
                results['prod_api'] = {'status': f'❌ 错误 ({response.status_code})'}
        except Exception as e:
            results['prod_api'] = {'status': f'❌ 连接失败: {e}'}
        
        # 输出结果
        print(f"本地API: {results['local_api']['status']}")
        if 'response_time' in results['local_api']:
            print(f"  响应时间: {results['local_api']['response_time']:.3f}s")
            print(f"  数据量: {results['local_api']['data_count']} 条")
        
        print(f"生产API: {results['prod_api']['status']}")
        if 'response_time' in results['prod_api']:
            print(f"  响应时间: {results['prod_api']['response_time']:.3f}s")
            print(f"  数据量: {results['prod_api']['data_count']} 条")
        
        return results
    
    def analyze_data_differences(self):
        """分析数据差异"""
        print("\n📊 数据差异分析")
        print("=" * 40)
        
        try:
            # 获取本地数据
            local_authors = requests.get(f"{self.local_api}authors/").json()
            local_quotes = requests.get(f"{self.local_api}quotes/").json()
            local_categories = requests.get(f"{self.local_api}categories/").json()
            
            # 获取生产数据
            prod_authors = requests.get(f"{self.prod_api}authors/").json()
            prod_quotes = requests.get(f"{self.prod_api}quotes/").json()
            prod_categories = requests.get(f"{self.prod_api}categories/").json()
            
            print(f"📈 数据量对比:")
            print(f"  作者: 本地 {len(local_authors)} vs 生产 {len(prod_authors)}")
            print(f"  名言: 本地 {len(local_quotes)} vs 生产 {len(prod_quotes)}")
            print(f"  类别: 本地 {len(local_categories)} vs 生产 {len(prod_categories)}")
            
            print(f"\n📋 数据质量对比:")
            
            # 分析作者数据质量
            local_authors_with_quotes = sum(1 for a in local_authors if a['quotes_count'] > 0)
            prod_authors_with_quotes = sum(1 for a in prod_authors if a['quotes_count'] > 0)
            
            print(f"  有名言的作者: 本地 {local_authors_with_quotes}/{len(local_authors)} vs 生产 {prod_authors_with_quotes}/{len(prod_authors)}")
            
            # 分析类别数据质量
            local_categories_with_quotes = sum(1 for c in local_categories if c['quotes_count'] > 0)
            prod_categories_with_quotes = sum(1 for c in prod_categories if c['quotes_count'] > 0)
            
            print(f"  有名言的类别: 本地 {local_categories_with_quotes}/{len(local_categories)} vs 生产 {prod_categories_with_quotes}/{len(prod_categories)}")
            
            return {
                'local': {'authors': len(local_authors), 'quotes': len(local_quotes), 'categories': len(local_categories)},
                'prod': {'authors': len(prod_authors), 'quotes': len(prod_quotes), 'categories': len(prod_categories)}
            }
            
        except Exception as e:
            print(f"❌ 数据分析失败: {e}")
            return None
    
    def test_offline_capability(self):
        """测试离线开发能力"""
        print("\n🔌 离线开发能力测试")
        print("=" * 40)
        
        # 模拟网络断开（通过访问不存在的地址）
        print("📡 测试网络依赖性...")
        
        try:
            # 测试本地API（应该正常工作）
            response = requests.get(f"{self.local_api}authors/", timeout=2)
            if response.status_code == 200:
                print("✅ 本地API: 离线可用")
            else:
                print("❌ 本地API: 离线不可用")
        except:
            print("❌ 本地API: 连接失败")
        
        # 检查前端是否有网络依赖
        print("🌐 检查前端网络依赖...")
        try:
            response = requests.get("http://localhost:8081/", timeout=5)
            html_content = response.text
            
            # 检查是否有外部CDN依赖
            external_deps = []
            if 'googleapis.com' in html_content:
                external_deps.append('Google Fonts')
            if 'cdnjs.cloudflare.com' in html_content:
                external_deps.append('Cloudflare CDN')
            if 'unpkg.com' in html_content:
                external_deps.append('unpkg CDN')
            
            if external_deps:
                print(f"⚠️  发现外部依赖: {', '.join(external_deps)}")
            else:
                print("✅ 无外部CDN依赖")
                
        except Exception as e:
            print(f"❌ 前端检查失败: {e}")
    
    def evaluate_development_scenarios(self):
        """评估开发场景"""
        print("\n🛠️ 开发场景评估")
        print("=" * 40)
        
        scenarios = {
            "功能开发": {
                "description": "新功能开发和调试",
                "current_impact": "✅ 良好 - 本地数据完整，API响应快速",
                "pros": ["数据结构完整", "响应速度快", "可控制数据状态"],
                "cons": ["数据量较少", "可能与生产环境有差异"]
            },
            "数据结构变更测试": {
                "description": "测试数据模型和API变更",
                "current_impact": "✅ 优秀 - 完全隔离的测试环境",
                "pros": ["安全的测试环境", "不影响生产数据", "可重复测试"],
                "cons": ["需要手动同步schema变更"]
            },
            "离线开发": {
                "description": "无网络环境下的开发",
                "current_impact": "✅ 优秀 - 完全离线可用",
                "pros": ["无网络依赖", "本地数据库", "快速响应"],
                "cons": ["无法获取最新生产数据"]
            },
            "性能测试": {
                "description": "API和前端性能测试",
                "current_impact": "⚠️ 一般 - 数据量较小",
                "pros": ["快速响应", "可控制负载"],
                "cons": ["数据量不足", "无法模拟真实负载"]
            },
            "UI/UX测试": {
                "description": "用户界面和体验测试",
                "current_impact": "✅ 良好 - 数据多样性足够",
                "pros": ["多样化测试数据", "可控制边界情况"],
                "cons": ["数据量可能不足以测试分页等功能"]
            }
        }
        
        for scenario, details in scenarios.items():
            print(f"\n📋 {scenario}")
            print(f"   描述: {details['description']}")
            print(f"   当前影响: {details['current_impact']}")
            print(f"   优势: {', '.join(details['pros'])}")
            print(f"   劣势: {', '.join(details['cons'])}")
    
    def generate_recommendations(self):
        """生成改进建议"""
        print("\n💡 改进建议")
        print("=" * 40)
        
        recommendations = [
            {
                "category": "数据管理",
                "items": [
                    "增加本地测试数据量，模拟真实使用场景",
                    "建立数据同步机制，定期从生产环境同步数据结构",
                    "创建数据生成脚本，支持大量测试数据生成"
                ]
            },
            {
                "category": "开发环境",
                "items": [
                    "保持当前的本地API配置，确保离线开发能力",
                    "添加环境切换功能，支持连接不同环境的API",
                    "建立Docker化的完整开发环境"
                ]
            },
            {
                "category": "测试策略",
                "items": [
                    "建立自动化测试套件，覆盖API和前端功能",
                    "添加性能测试，使用大量数据进行压力测试",
                    "建立数据一致性检查机制"
                ]
            }
        ]
        
        for rec in recommendations:
            print(f"\n🎯 {rec['category']}")
            for item in rec['items']:
                print(f"   • {item}")
    
    def run_analysis(self):
        """运行完整分析"""
        print("🚀 Quotese开发环境数据配置影响分析")
        print("=" * 60)
        print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 执行各项分析
        api_results = self.test_api_connectivity()
        data_diff = self.analyze_data_differences()
        self.test_offline_capability()
        self.evaluate_development_scenarios()
        self.generate_recommendations()
        
        print("\n" + "=" * 60)
        print("📊 分析完成")
        
        # 生成总结
        print("\n🎯 总结:")
        print("✅ 当前配置优势:")
        print("   • 完全离线开发能力")
        print("   • 快速API响应")
        print("   • 安全的测试环境")
        print("   • 数据结构完整")
        
        print("\n⚠️ 需要改进的方面:")
        print("   • 测试数据量偏少")
        print("   • 缺少数据同步机制")
        print("   • 性能测试数据不足")

if __name__ == "__main__":
    analyzer = DevelopmentImpactAnalyzer()
    analyzer.run_analysis()
