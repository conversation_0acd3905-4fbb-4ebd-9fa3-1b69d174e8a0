#!/usr/bin/env python3
"""
修复前端资源路径脚本
统一所有HTML文件中的CSS和JavaScript引用路径，使其支持环境自适应
"""

import os
import re
import glob
from pathlib import Path

def fix_html_file(file_path):
    """修复单个HTML文件的资源引用路径"""
    print(f"正在处理: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 修复CSS引用 - 将绝对URL改为相对路径
    content = re.sub(
        r'<link\s+href="https://quotese\.com(/css/[^"]+)"\s+rel="stylesheet">',
        r'<link href="\1" rel="stylesheet">',
        content
    )
    
    # 修复JavaScript引用 - 将绝对URL改为相对路径
    content = re.sub(
        r'<script\s+src="https://quotese\.com(/js/[^"]+)"[^>]*></script>',
        r'<script src="\1"></script>',
        content
    )
    
    # 修复组件引用
    content = re.sub(
        r'<script\s+src="https://quotese\.com(/components/[^"]+)"[^>]*></script>',
        r'<script src="\1"></script>',
        content
    )
    
    # 添加版本控制参数到本地JS文件（如果没有的话）
    content = re.sub(
        r'<script\s+src="(/js/[^"?]+)"\s*></script>',
        r'<script src="\1?v=20250626"></script>',
        content
    )
    
    # 修复相对路径引用，确保以/开头
    content = re.sub(
        r'<link\s+rel="stylesheet"\s+href="(/css/[^"]+)">',
        r'<link href="\1" rel="stylesheet">',
        content
    )
    
    # 如果内容有变化，写回文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ 已修复: {file_path}")
        return True
    else:
        print(f"  ⏭️  无需修改: {file_path}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复前端资源路径...")
    print("=" * 50)
    
    # 获取frontend目录下的所有HTML文件
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return
    
    html_files = list(frontend_dir.glob("*.html"))
    
    if not html_files:
        print("❌ 未找到HTML文件")
        return
    
    print(f"📁 找到 {len(html_files)} 个HTML文件")
    print()
    
    fixed_count = 0
    
    for html_file in html_files:
        if fix_html_file(html_file):
            fixed_count += 1
    
    print()
    print("=" * 50)
    print(f"🎉 修复完成！")
    print(f"📊 总文件数: {len(html_files)}")
    print(f"🔧 已修复: {fixed_count}")
    print(f"⏭️  无需修改: {len(html_files) - fixed_count}")
    
    if fixed_count > 0:
        print()
        print("📝 修复内容:")
        print("  - 将绝对URL改为相对路径")
        print("  - 统一CSS和JS引用格式")
        print("  - 添加版本控制参数")
        print()
        print("🔄 建议重启前端服务器以应用更改")

if __name__ == "__main__":
    main()
