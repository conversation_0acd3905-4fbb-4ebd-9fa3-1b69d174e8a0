#!/usr/bin/env python3
"""
环境健康检查脚本
定期检查开发环境的健康状态
"""

import requests
import time
import json
from datetime import datetime

class HealthChecker:
    def __init__(self):
        self.local_api = "http://127.0.0.1:8000/api/"
        self.local_frontend = "http://localhost:8081/"
        self.checks = []
    
    def check_api_health(self):
        """检查API健康状态"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.local_api}authors/", timeout=5)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                self.checks.append({
                    'service': 'Local API',
                    'status': '✅ 正常',
                    'response_time': f"{response_time:.3f}s",
                    'data_count': len(data)
                })
            else:
                self.checks.append({
                    'service': 'Local API',
                    'status': f'❌ 错误 ({response.status_code})',
                    'response_time': f"{response_time:.3f}s"
                })
        except Exception as e:
            self.checks.append({
                'service': 'Local API',
                'status': f'❌ 连接失败: {e}'
            })
    
    def check_frontend_health(self):
        """检查前端健康状态"""
        try:
            start_time = time.time()
            response = requests.get(self.local_frontend, timeout=5)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.checks.append({
                    'service': 'Frontend',
                    'status': '✅ 正常',
                    'response_time': f"{response_time:.3f}s"
                })
            else:
                self.checks.append({
                    'service': 'Frontend',
                    'status': f'❌ 错误 ({response.status_code})'
                })
        except Exception as e:
            self.checks.append({
                'service': 'Frontend',
                'status': f'❌ 连接失败: {e}'
            })
    
    def run_health_check(self):
        """运行完整的健康检查"""
        print(f"🏥 环境健康检查 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        self.check_api_health()
        self.check_frontend_health()
        
        # 输出结果
        for check in self.checks:
            print(f"{check['service']}: {check['status']}")
            if 'response_time' in check:
                print(f"  响应时间: {check['response_time']}")
            if 'data_count' in check:
                print(f"  数据量: {check['data_count']}")
        
        # 总体状态
        failed_checks = [c for c in self.checks if '❌' in c['status']]
        if failed_checks:
            print(f"\n⚠️  发现 {len(failed_checks)} 个问题")
            return False
        else:
            print(f"\n✅ 所有服务正常运行")
            return True

if __name__ == "__main__":
    checker = HealthChecker()
    checker.run_health_check()
