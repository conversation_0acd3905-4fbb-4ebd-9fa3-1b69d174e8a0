<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Card Entity ID Checker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .page-link {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .page-link:hover { background-color: #218838; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .quote-card-analysis {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>Quote Card Entity ID Checker</h1>
    <p>This tool checks if quote cards on different pages have valid entity IDs and can properly navigate to detail pages.</p>

    <div class="test-section">
        <h2>Page Testing</h2>
        <div>
            <a href="/?use-production-api=true" target="_blank" class="page-link">Homepage</a>
            <a href="/categories/love-quotes/?use-production-api=true" target="_blank" class="page-link">Category Page</a>
            <a href="/authors/lailah-gifty-akita/?use-production-api=true" target="_blank" class="page-link">Author Page</a>
            <a href="/sources/and-being-free/?use-production-api=true" target="_blank" class="page-link">Source Page</a>
        </div>
        <button onclick="checkCurrentPageQuoteCards()">🔍 Check Current Page Quote Cards</button>
        <button onclick="testQuoteCardGeneration()">🧪 Test Quote Card Generation</button>
        <button onclick="testDataFlow()">🔄 Test Complete Data Flow</button>
        <div id="page-testing-results"></div>
    </div>

    <div class="test-section">
        <h2>Quote Card Component Testing</h2>
        <button onclick="testQuoteCardComponent()">🧪 Test Quote Card Component</button>
        <div id="component-testing-results"></div>
    </div>

    <div class="test-section">
        <h2>URL Handler Testing</h2>
        <button onclick="testUrlHandler()">🔗 Test URL Generation & Parsing</button>
        <div id="url-handler-results"></div>
    </div>

    <div class="test-section">
        <h2>API Integration Testing</h2>
        <button onclick="testAPIIntegration()">📡 Test API Integration</button>
        <div id="api-integration-results"></div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/components/quote-card.js"></script>

    <script>
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
        }

        function checkCurrentPageQuoteCards() {
            const container = 'page-testing-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Checking quote cards on current page...');
            
            // Find all quote cards on the page
            const quoteCards = document.querySelectorAll('.quote-card-component');
            addResult(container, 'info', `Found ${quoteCards.length} quote cards`);
            
            if (quoteCards.length === 0) {
                addResult(container, 'warning', 'No quote cards found. This might be a page without quote cards or they haven\'t loaded yet.');
                return;
            }
            
            let cardsWithValidIds = 0;
            let cardsWithMissingIds = 0;
            let cardsWithInvalidIds = 0;
            
            quoteCards.forEach((card, index) => {
                const quoteId = card.getAttribute('data-quote-id');
                
                if (!quoteId) {
                    cardsWithMissingIds++;
                    addResult(container, 'error', `Card ${index + 1}: Missing data-quote-id attribute`);
                } else {
                    const id = parseInt(quoteId);
                    if (isNaN(id) || id <= 0) {
                        cardsWithInvalidIds++;
                        addResult(container, 'error', `Card ${index + 1}: Invalid quote ID "${quoteId}"`);
                    } else {
                        cardsWithValidIds++;
                        addResult(container, 'success', `Card ${index + 1}: Valid quote ID "${quoteId}"`);
                    }
                }
                
                // Check if card has click event
                const hasClickEvent = card.style.cursor === 'pointer' || card.classList.contains('cursor-pointer');
                if (hasClickEvent) {
                    addResult(container, 'success', `Card ${index + 1}: Has click styling`);
                } else {
                    addResult(container, 'warning', `Card ${index + 1}: Missing click styling`);
                }
            });
            
            // Summary
            const totalCards = quoteCards.length;
            const successRate = ((cardsWithValidIds / totalCards) * 100).toFixed(1);
            
            addResult(container, 'info', 
                `Summary: ${cardsWithValidIds}/${totalCards} cards with valid IDs (${successRate}% success rate)`);
            
            if (cardsWithMissingIds > 0) {
                addResult(container, 'error', `${cardsWithMissingIds} cards missing entity IDs`);
            }
            
            if (cardsWithInvalidIds > 0) {
                addResult(container, 'error', `${cardsWithInvalidIds} cards with invalid entity IDs`);
            }
        }

        async function testQuoteCardComponent() {
            const container = 'component-testing-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing QuoteCardComponent...');
            
            // Test with sample quote data
            const sampleQuote = {
                id: '499001',
                content: 'This is a test quote for component testing.',
                author: {
                    id: '1',
                    name: 'Test Author'
                },
                categories: [
                    { id: '1', name: 'Test Category' }
                ]
            };
            
            try {
                // Test quote card generation
                const quoteCard = QuoteCardComponent.render(sampleQuote, 0, { showActions: true });
                
                if (quoteCard) {
                    addResult(container, 'success', 'Quote card component rendered successfully');
                    
                    // Check if it has the required attributes
                    const quoteId = quoteCard.getAttribute('data-quote-id');
                    if (quoteId === sampleQuote.id) {
                        addResult(container, 'success', `Quote card has correct data-quote-id: ${quoteId}`);
                    } else {
                        addResult(container, 'error', `Quote card has incorrect data-quote-id: ${quoteId}, expected: ${sampleQuote.id}`);
                    }
                    
                    // Check if it has click styling
                    const hasClickStyling = quoteCard.classList.contains('cursor-pointer');
                    if (hasClickStyling) {
                        addResult(container, 'success', 'Quote card has click styling');
                    } else {
                        addResult(container, 'error', 'Quote card missing click styling');
                    }
                    
                    // Test click event
                    let clickEventTriggered = false;
                    const originalLocation = window.location.href;
                    
                    // Mock the navigation function to test click
                    const originalNavigate = window.navigateToEntityWithId;
                    window.navigateToEntityWithId = (type, entity, url) => {
                        clickEventTriggered = true;
                        addResult(container, 'success', `Click event triggered: ${type}, ${url}`);
                    };
                    
                    // Simulate click
                    quoteCard.click();
                    
                    // Restore original function
                    if (originalNavigate) {
                        window.navigateToEntityWithId = originalNavigate;
                    } else {
                        delete window.navigateToEntityWithId;
                    }
                    
                    if (!clickEventTriggered) {
                        addResult(container, 'warning', 'Click event not triggered (might use direct navigation)');
                    }
                    
                    // Add the card to the page for visual inspection
                    const testContainer = document.createElement('div');
                    testContainer.className = 'quote-card-analysis';
                    testContainer.innerHTML = '<h4>Generated Quote Card (for visual inspection):</h4>';
                    testContainer.appendChild(quoteCard);
                    document.getElementById(container).appendChild(testContainer);
                    
                } else {
                    addResult(container, 'error', 'Quote card component failed to render');
                }
                
            } catch (error) {
                addResult(container, 'error', `Quote card component test failed: ${error.message}`);
            }
        }

        function testUrlHandler() {
            const container = 'url-handler-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing URL handler functions...');
            
            // Test URL generation
            const testQuotes = [
                { id: '499001' },
                { id: '499002' },
                { id: 123 },
                { id: '0' },
                { id: null },
                { id: undefined },
                {}
            ];
            
            testQuotes.forEach((quote, index) => {
                try {
                    const url = UrlHandler.getQuoteUrl(quote);
                    addResult(container, 'success', `URL generation test ${index + 1}: ${JSON.stringify(quote)} → ${url}`);
                } catch (error) {
                    addResult(container, 'error', `URL generation test ${index + 1}: ${JSON.stringify(quote)} → ERROR: ${error.message}`);
                }
            });
            
            // Test URL parsing
            const testUrls = [
                '/quotes/499001/',
                '/quotes/123/',
                '/quotes/0/',
                '/quotes/abc/',
                '/quotes/',
                '/categories/love/',
                '/'
            ];
            
            testUrls.forEach((testUrl, index) => {
                // Temporarily change the URL for testing
                const originalPath = window.location.pathname;
                
                try {
                    // Mock the pathname
                    Object.defineProperty(window.location, 'pathname', {
                        writable: true,
                        value: testUrl
                    });
                    
                    const quoteId = UrlHandler.parseQuoteIdFromPath();
                    addResult(container, quoteId ? 'success' : 'info', 
                        `URL parsing test ${index + 1}: ${testUrl} → ${quoteId || 'null'}`);
                    
                } catch (error) {
                    addResult(container, 'error', `URL parsing test ${index + 1}: ${testUrl} → ERROR: ${error.message}`);
                } finally {
                    // Restore original pathname
                    Object.defineProperty(window.location, 'pathname', {
                        writable: true,
                        value: originalPath
                    });
                }
            });
        }

        async function testAPIIntegration() {
            const container = 'api-integration-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing API integration...');
            
            try {
                // Switch to production API
                if (window.QuoteseAPIMode && typeof window.QuoteseAPIMode.useProductionAPI === 'function') {
                    window.QuoteseAPIMode.useProductionAPI();
                    addResult(container, 'success', 'Switched to production API');
                }
                
                // Test API calls with sample IDs
                const testIds = ['499001', '499002', '499003'];
                
                for (const id of testIds) {
                    try {
                        const quote = await window.ApiClient.getQuoteById(id);
                        if (quote) {
                            addResult(container, 'success', 
                                `API test ID ${id}: "${quote.content.substring(0, 30)}..." by ${quote.author.name}`);
                        } else {
                            addResult(container, 'warning', `API test ID ${id}: No data returned`);
                        }
                    } catch (error) {
                        addResult(container, 'error', `API test ID ${id}: ${error.message}`);
                    }
                }
                
            } catch (error) {
                addResult(container, 'error', `API integration test failed: ${error.message}`);
            }
        }

        async function testDataFlow() {
            const container = 'page-testing-results';
            addResult(container, 'info', 'Testing complete data flow...');
            
            try {
                // Step 1: Generate a quote card
                const sampleQuote = {
                    id: '499001',
                    content: 'Test quote for data flow testing.',
                    author: { id: '1', name: 'Test Author' }
                };
                
                const quoteCard = QuoteCardComponent.render(sampleQuote);
                addResult(container, 'success', 'Step 1: Quote card generated');
                
                // Step 2: Check entity ID
                const quoteId = quoteCard.getAttribute('data-quote-id');
                if (quoteId === sampleQuote.id) {
                    addResult(container, 'success', `Step 2: Entity ID correctly set: ${quoteId}`);
                } else {
                    addResult(container, 'error', `Step 2: Entity ID mismatch: ${quoteId} vs ${sampleQuote.id}`);
                    return;
                }
                
                // Step 3: Test URL generation
                const quoteUrl = UrlHandler.getQuoteUrl(sampleQuote);
                addResult(container, 'success', `Step 3: URL generated: ${quoteUrl}`);
                
                // Step 4: Test API call
                const quote = await window.ApiClient.getQuoteById(quoteId);
                if (quote) {
                    addResult(container, 'success', 
                        `Step 4: API call successful: "${quote.content.substring(0, 30)}..."`);
                } else {
                    addResult(container, 'error', 'Step 4: API call returned null');
                    return;
                }
                
                addResult(container, 'success', '🎉 Complete data flow test passed!');
                
            } catch (error) {
                addResult(container, 'error', `Data flow test failed: ${error.message}`);
            }
        }

        async function testQuoteCardGeneration() {
            const container = 'page-testing-results';
            addResult(container, 'info', 'Testing quote card generation with real API data...');
            
            try {
                // Switch to production API
                if (window.QuoteseAPIMode && typeof window.QuoteseAPIMode.useProductionAPI === 'function') {
                    window.QuoteseAPIMode.useProductionAPI();
                }
                
                // Get some real quotes from API
                const quotesData = await window.ApiClient.getQuotes(1, 3);
                
                if (quotesData && quotesData.quotes && quotesData.quotes.length > 0) {
                    addResult(container, 'success', `Retrieved ${quotesData.quotes.length} quotes from API`);
                    
                    // Generate quote cards for each quote
                    quotesData.quotes.forEach((quote, index) => {
                        try {
                            const quoteCard = QuoteCardComponent.render(quote, index, { showActions: true });
                            
                            // Check entity ID
                            const quoteId = quoteCard.getAttribute('data-quote-id');
                            if (quoteId === quote.id.toString()) {
                                addResult(container, 'success', 
                                    `Quote ${index + 1}: ID ${quoteId} correctly set`);
                            } else {
                                addResult(container, 'error', 
                                    `Quote ${index + 1}: ID mismatch - card: ${quoteId}, data: ${quote.id}`);
                            }
                            
                            // Add to page for visual inspection
                            const testContainer = document.createElement('div');
                            testContainer.className = 'quote-card-analysis';
                            testContainer.innerHTML = `<h4>Quote Card ${index + 1} (ID: ${quote.id}):</h4>`;
                            testContainer.appendChild(quoteCard);
                            document.getElementById(container).appendChild(testContainer);
                            
                        } catch (error) {
                            addResult(container, 'error', 
                                `Quote ${index + 1} card generation failed: ${error.message}`);
                        }
                    });
                    
                } else {
                    addResult(container, 'error', 'Failed to retrieve quotes from API');
                }
                
            } catch (error) {
                addResult(container, 'error', `Quote card generation test failed: ${error.message}`);
            }
        }

        // Auto-run basic checks on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                addResult('page-testing-results', 'info', 
                    'Entity ID checker ready. Use the buttons above to run tests.');
            }, 1000);
        });
    </script>
</body>
</html>
