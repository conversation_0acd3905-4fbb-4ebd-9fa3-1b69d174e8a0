<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connectivity Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .loading { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <h1>API 连接性测试</h1>
    <p>测试生产API (https://api.quotese.com/api/) 的可用性和功能</p>
    
    <button onclick="runAllTests()">运行所有测试</button>
    <button onclick="clearResults()">清除结果</button>
    
    <div id="test-results"></div>

    <script>
        const API_ENDPOINT = 'https://api.quotese.com/api/';
        const resultsContainer = document.getElementById('test-results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsContainer.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults() {
            resultsContainer.innerHTML = '';
        }

        async function testApiConnectivity() {
            addResult('🔍 测试 1: API 基础连接性', 'loading');
            
            try {
                const response = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: '{ __schema { types { name } } }'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ API 连接成功 (状态: ${response.status})`, 'success');
                    addResult(`<pre>Schema types count: ${data.data?.__schema?.types?.length || 'N/A'}</pre>`, 'info');
                    return true;
                } else {
                    addResult(`❌ API 连接失败 (状态: ${response.status})`, 'error');
                    const text = await response.text();
                    addResult(`<pre>响应内容: ${text}</pre>`, 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ API 连接异常: ${error.message}`, 'error');
                return false;
            }
        }

        async function testAuthorsQuery() {
            addResult('🔍 测试 2: Authors 查询', 'loading');
            
            try {
                const query = `
                    query {
                        authors(first: 5, orderBy: "quotes_count", orderDirection: "desc") {
                            id
                            name
                            quotesCount
                        }
                    }
                `;

                const response = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.errors) {
                        addResult(`❌ Authors 查询有错误: ${JSON.stringify(data.errors)}`, 'error');
                        return false;
                    } else {
                        addResult(`✅ Authors 查询成功，返回 ${data.data?.authors?.length || 0} 个作者`, 'success');
                        addResult(`<pre>${JSON.stringify(data.data.authors?.slice(0, 2), null, 2)}</pre>`, 'info');
                        return true;
                    }
                } else {
                    addResult(`❌ Authors 查询失败 (状态: ${response.status})`, 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ Authors 查询异常: ${error.message}`, 'error');
                return false;
            }
        }

        async function testCategoriesQuery() {
            addResult('🔍 测试 3: Categories 查询', 'loading');
            
            try {
                const query = `
                    query {
                        categories(first: 5, orderBy: "quotes_count", orderDirection: "desc") {
                            id
                            name
                            quotesCount
                        }
                    }
                `;

                const response = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.errors) {
                        addResult(`❌ Categories 查询有错误: ${JSON.stringify(data.errors)}`, 'error');
                        return false;
                    } else {
                        addResult(`✅ Categories 查询成功，返回 ${data.data?.categories?.length || 0} 个分类`, 'success');
                        addResult(`<pre>${JSON.stringify(data.data.categories?.slice(0, 2), null, 2)}</pre>`, 'info');
                        return true;
                    }
                } else {
                    addResult(`❌ Categories 查询失败 (状态: ${response.status})`, 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ Categories 查询异常: ${error.message}`, 'error');
                return false;
            }
        }

        async function testSourcesQuery() {
            addResult('🔍 测试 4: Sources 查询', 'loading');
            
            try {
                const query = `
                    query {
                        sources(first: 5, orderBy: "quotes_count", orderDirection: "desc") {
                            id
                            name
                            quotesCount
                        }
                    }
                `;

                const response = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.errors) {
                        addResult(`❌ Sources 查询有错误: ${JSON.stringify(data.errors)}`, 'error');
                        addResult(`<pre>错误详情: ${JSON.stringify(data.errors, null, 2)}</pre>`, 'error');
                        return false;
                    } else {
                        addResult(`✅ Sources 查询成功，返回 ${data.data?.sources?.length || 0} 个来源`, 'success');
                        addResult(`<pre>${JSON.stringify(data.data.sources?.slice(0, 2), null, 2)}</pre>`, 'info');
                        return true;
                    }
                } else {
                    addResult(`❌ Sources 查询失败 (状态: ${response.status})`, 'error');
                    const text = await response.text();
                    addResult(`<pre>响应内容: ${text}</pre>`, 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ Sources 查询异常: ${error.message}`, 'error');
                return false;
            }
        }

        async function testSourceByNameQuery() {
            addResult('🔍 测试 5: Source by Name 查询 (healology)', 'loading');
            
            try {
                const query = `
                    query {
                        sourceByExactName(name: "healology") {
                            id
                            name
                            quotesCount
                        }
                    }
                `;

                const response = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.errors) {
                        addResult(`❌ Source by Name 查询有错误: ${JSON.stringify(data.errors)}`, 'error');
                        return false;
                    } else {
                        addResult(`✅ Source by Name 查询成功`, 'success');
                        addResult(`<pre>${JSON.stringify(data.data, null, 2)}</pre>`, 'info');
                        return true;
                    }
                } else {
                    addResult(`❌ Source by Name 查询失败 (状态: ${response.status})`, 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ Source by Name 查询异常: ${error.message}`, 'error');
                return false;
            }
        }

        async function runAllTests() {
            clearResults();
            addResult('🚀 开始 API 连接性测试...', 'info');
            
            const test1 = await testApiConnectivity();
            if (!test1) {
                addResult('❌ 基础连接失败，停止后续测试', 'error');
                return;
            }
            
            await testAuthorsQuery();
            await testCategoriesQuery();
            await testSourcesQuery();
            await testSourceByNameQuery();
            
            addResult('✅ 所有测试完成', 'success');
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            addResult('📋 API 测试工具已加载，点击"运行所有测试"开始测试', 'info');
        });
    </script>
</body>
</html>
