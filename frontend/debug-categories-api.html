<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Categories API - Quotese.com</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .debug-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .debug-button:hover {
            background: #0056b3;
        }
        .debug-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .category-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .category-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .category-count {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>Categories API Debug Tool</h1>
        
        <!-- Test Section 1: API Client Test -->
        <div class="debug-section">
            <h2>1. API Client 测试</h2>
            <button class="debug-button" onclick="testApiClient()">测试 ApiClient</button>
            <div id="api-client-result" class="debug-result"></div>
        </div>
        
        <!-- Test Section 2: Direct API Call -->
        <div class="debug-section">
            <h2>2. 直接API调用测试</h2>
            <button class="debug-button" onclick="testDirectApiCall()">调用 getPopularCategories(500)</button>
            <button class="debug-button" onclick="testDirectApiCall(100)">调用 getPopularCategories(100)</button>
            <button class="debug-button" onclick="testDirectApiCall(20)">调用 getPopularCategories(20)</button>
            <div id="direct-api-result" class="debug-result"></div>
        </div>
        
        <!-- Test Section 3: Data Processing -->
        <div class="debug-section">
            <h2>3. 数据处理测试</h2>
            <button class="debug-button" onclick="testDataProcessing()">测试数据处理流程</button>
            <div id="data-processing-result" class="debug-result"></div>
        </div>
        
        <!-- Test Section 4: Visual Display -->
        <div class="debug-section">
            <h2>4. 可视化显示测试</h2>
            <button class="debug-button" onclick="testVisualDisplay()">显示分类卡片</button>
            <div id="visual-display-result" class="debug-result"></div>
            <div id="categories-display" class="categories-grid"></div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="js/dist/core.js"></script>
    <script src="js/url-handler.js"></script>
    
    <script>
        // Test functions
        function testApiClient() {
            const result = document.getElementById('api-client-result');
            result.textContent = '正在测试 ApiClient...';
            result.className = 'debug-result info';
            
            let output = 'ApiClient 检查结果:\n';
            
            try {
                output += `✅ window.ApiClient 存在: ${!!window.ApiClient}\n`;
                
                if (window.ApiClient) {
                    output += `✅ getPopularCategories 方法存在: ${typeof window.ApiClient.getPopularCategories === 'function'}\n`;
                    output += `✅ getCategories 方法存在: ${typeof window.ApiClient.getCategories === 'function'}\n`;
                    output += `✅ query 方法存在: ${typeof window.ApiClient.query === 'function'}\n`;
                    output += `✅ useMockData: ${window.ApiClient.useMockData}\n`;
                    output += `✅ baseUrl: ${window.ApiClient.baseUrl}\n`;
                } else {
                    output += '❌ ApiClient 不可用\n';
                }
                
                result.textContent = output;
                result.className = 'debug-result success';
            } catch (error) {
                result.textContent = `测试失败: ${error.message}`;
                result.className = 'debug-result error';
            }
        }
        
        async function testDirectApiCall(limit = 500) {
            const result = document.getElementById('direct-api-result');
            result.textContent = `正在调用 getPopularCategories(${limit})...`;
            result.className = 'debug-result info';
            
            try {
                if (!window.ApiClient) {
                    throw new Error('ApiClient 不可用');
                }
                
                console.log(`🔍 Calling getPopularCategories(${limit})`);
                const startTime = Date.now();
                const categories = await window.ApiClient.getPopularCategories(limit);
                const endTime = Date.now();
                
                let output = `✅ API调用成功! (耗时: ${endTime - startTime}ms)\n`;
                output += `📊 返回数据类型: ${typeof categories}\n`;
                output += `📊 是否为数组: ${Array.isArray(categories)}\n`;
                output += `📊 数据长度: ${categories ? categories.length : 'N/A'}\n\n`;
                
                if (categories && categories.length > 0) {
                    output += `📋 前5个分类:\n`;
                    categories.slice(0, 5).forEach((cat, index) => {
                        output += `${index + 1}. ${cat.name} (ID: ${cat.id}, Count: ${cat.count || cat.quotesCount || 0})\n`;
                    });
                    
                    output += `\n📋 最后5个分类:\n`;
                    categories.slice(-5).forEach((cat, index) => {
                        output += `${categories.length - 4 + index}. ${cat.name} (ID: ${cat.id}, Count: ${cat.count || cat.quotesCount || 0})\n`;
                    });
                    
                    output += `\n🔍 数据结构示例:\n`;
                    output += JSON.stringify(categories[0], null, 2);
                } else {
                    output += '❌ 没有返回数据\n';
                }
                
                result.textContent = output;
                result.className = 'debug-result success';
                
                // Store for other tests
                window.debugCategories = categories;
                
            } catch (error) {
                console.error('API调用失败:', error);
                result.textContent = `❌ API调用失败: ${error.message}\n\n堆栈跟踪:\n${error.stack}`;
                result.className = 'debug-result error';
            }
        }
        
        async function testDataProcessing() {
            const result = document.getElementById('data-processing-result');
            result.textContent = '正在测试数据处理...';
            result.className = 'debug-result info';
            
            try {
                // First get the data
                if (!window.debugCategories) {
                    await testDirectApiCall(100);
                }
                
                if (!window.debugCategories || window.debugCategories.length === 0) {
                    throw new Error('没有可用的测试数据');
                }
                
                const categories = window.debugCategories;
                let output = '🔄 数据处理测试:\n\n';
                
                // Test pagination
                const pageSize = 48;
                const currentPage = 1;
                const startIndex = (currentPage - 1) * pageSize;
                const endIndex = startIndex + pageSize;
                const displayedCategories = categories.slice(startIndex, endIndex);
                
                output += `📄 分页测试:\n`;
                output += `- 总数据: ${categories.length}\n`;
                output += `- 页面大小: ${pageSize}\n`;
                output += `- 当前页: ${currentPage}\n`;
                output += `- 开始索引: ${startIndex}\n`;
                output += `- 结束索引: ${endIndex}\n`;
                output += `- 显示数量: ${displayedCategories.length}\n\n`;
                
                // Test sorting
                const sortedByName = [...categories].sort((a, b) => a.name.localeCompare(b.name));
                const sortedByCount = [...categories].sort((a, b) => (b.count || 0) - (a.count || 0));
                
                output += `🔤 排序测试:\n`;
                output += `- 按名称排序前3个: ${sortedByName.slice(0, 3).map(c => c.name).join(', ')}\n`;
                output += `- 按数量排序前3个: ${sortedByCount.slice(0, 3).map(c => `${c.name}(${c.count || 0})`).join(', ')}\n\n`;
                
                // Test data mapping
                output += `🗺️ 数据映射测试:\n`;
                const sampleCategory = categories[0];
                output += `- 原始字段: ${Object.keys(sampleCategory).join(', ')}\n`;
                output += `- ID: ${sampleCategory.id}\n`;
                output += `- Name: ${sampleCategory.name}\n`;
                output += `- Count: ${sampleCategory.count}\n`;
                output += `- QuotesCount: ${sampleCategory.quotesCount}\n`;
                
                result.textContent = output;
                result.className = 'debug-result success';
                
                // Store processed data
                window.debugDisplayedCategories = displayedCategories;
                
            } catch (error) {
                result.textContent = `数据处理失败: ${error.message}`;
                result.className = 'debug-result error';
            }
        }
        
        async function testVisualDisplay() {
            const result = document.getElementById('visual-display-result');
            const display = document.getElementById('categories-display');
            
            result.textContent = '正在测试可视化显示...';
            result.className = 'debug-result info';
            
            try {
                // Get processed data
                if (!window.debugDisplayedCategories) {
                    await testDataProcessing();
                }
                
                if (!window.debugDisplayedCategories || window.debugDisplayedCategories.length === 0) {
                    throw new Error('没有可用的显示数据');
                }
                
                const categories = window.debugDisplayedCategories;
                
                // Clear display
                display.innerHTML = '';
                
                // Render categories
                categories.forEach((category, index) => {
                    const categoryCard = document.createElement('div');
                    categoryCard.className = 'category-card';
                    
                    categoryCard.innerHTML = `
                        <div class="category-name">${category.name}</div>
                        <div class="category-count">${category.count || 0} quotes</div>
                        <div style="font-size: 0.8em; color: #999;">ID: ${category.id}</div>
                    `;
                    
                    display.appendChild(categoryCard);
                });
                
                let output = `✅ 可视化显示成功!\n`;
                output += `📊 显示了 ${categories.length} 个分类\n`;
                output += `🎨 创建了 ${display.children.length} 个卡片元素\n`;
                
                result.textContent = output;
                result.className = 'debug-result success';
                
            } catch (error) {
                result.textContent = `可视化显示失败: ${error.message}`;
                result.className = 'debug-result error';
            }
        }
        
        // Auto-run basic tests on load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Debug page loaded');
            setTimeout(() => {
                testApiClient();
            }, 1000);
        });
    </script>
</body>
</html>
