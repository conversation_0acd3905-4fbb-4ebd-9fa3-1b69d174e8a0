<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sources生产API验证测试 - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .test-result {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #1f2937;
            color: #f3f4f6;
            padding: 12px;
            border-radius: 6px;
            margin: 8px 0;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-success {
            background: #065f46;
            color: #d1fae5;
        }
        .test-error {
            background: #7f1d1d;
            color: #fecaca;
        }
        .test-warning {
            background: #92400e;
            color: #fef3c7;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-check-circle mr-3 text-green-500"></i>
                Sources生产API验证测试
            </h1>
            
            <!-- 配置验证 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">1. 配置验证</h2>
                <div id="config-test" class="test-result">检测中...</div>
            </div>
            
            <!-- API一致性验证 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">2. API一致性验证</h2>
                <div id="consistency-test" class="test-result">检测中...</div>
            </div>
            
            <!-- Sources API测试 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">3. Sources API测试</h2>
                <div id="sources-api-test" class="test-result">检测中...</div>
            </div>
            
            <!-- 快速链接测试 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">4. 快速链接测试</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="space-y-2">
                        <h3 class="font-semibold text-gray-700">Sources页面</h3>
                        <a href="/sources/healology/" target="_blank" class="block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 text-center">
                            Healology
                        </a>
                        <a href="/sources/atlas-shrugged/" target="_blank" class="block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 text-center">
                            Atlas Shrugged
                        </a>
                    </div>
                    <div class="space-y-2">
                        <h3 class="font-semibold text-gray-700">Authors页面</h3>
                        <a href="/authors/amit-kalantri/" target="_blank" class="block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 text-center">
                            Amit Kalantri
                        </a>
                        <a href="/authors/ayn-rand/" target="_blank" class="block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 text-center">
                            Ayn Rand
                        </a>
                    </div>
                    <div class="space-y-2">
                        <h3 class="font-semibold text-gray-700">Categories页面</h3>
                        <a href="/categories/fantasy/" target="_blank" class="block bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 text-center">
                            Fantasy
                        </a>
                        <a href="/categories/life/" target="_blank" class="block bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 text-center">
                            Life
                        </a>
                    </div>
                </div>
                <p class="text-sm text-gray-600">点击上面的链接在新标签页中测试各个页面是否都能正常加载生产API数据</p>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>

    <script>
        // 自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runAllTests, 500);
        });
        
        async function runAllTests() {
            testConfig();
            testConsistency();
            await testSourcesAPI();
        }
        
        function testConfig() {
            const configTest = document.getElementById('config-test');
            try {
                let results = '配置验证结果:\n';
                
                results += `AppConfig存在: ${!!window.AppConfig}\n`;
                if (window.AppConfig) {
                    results += `API端点: ${window.AppConfig.apiEndpoint}\n`;
                    results += `使用模拟数据: ${window.AppConfig.useMockData}\n`;
                    results += `调试模式: ${window.AppConfig.debug}\n`;
                }
                
                results += `\nApiClient存在: ${!!window.ApiClient}\n`;
                if (window.ApiClient) {
                    results += `ApiClient API端点: ${window.ApiClient.apiEndpoint}\n`;
                    results += `ApiClient 使用模拟数据: ${window.ApiClient.useMockData}\n`;
                }
                
                // 检查是否使用生产API
                if (window.AppConfig && window.AppConfig.apiEndpoint === 'https://api.quotese.com/api/' && !window.AppConfig.useMockData) {
                    results += '\n✅ 配置正确：使用生产API\n';
                    configTest.className = 'test-result test-success';
                } else {
                    results += '\n⚠️  配置可能有问题：未使用生产API\n';
                    configTest.className = 'test-result test-warning';
                }
                
                configTest.textContent = results;
            } catch (error) {
                configTest.textContent = `配置验证错误: ${error.message}`;
                configTest.className = 'test-result test-error';
            }
        }
        
        function testConsistency() {
            const consistencyTest = document.getElementById('consistency-test');
            try {
                let results = 'API一致性验证结果:\n';
                
                // 检查所有页面类型是否使用相同的API配置
                const expectedEndpoint = 'https://api.quotese.com/api/';
                const expectedUseMockData = false;
                
                results += `期望的API端点: ${expectedEndpoint}\n`;
                results += `期望的useMockData: ${expectedUseMockData}\n\n`;
                
                // 检查当前配置
                const currentEndpoint = window.AppConfig?.apiEndpoint;
                const currentUseMockData = window.AppConfig?.useMockData;
                
                results += `当前API端点: ${currentEndpoint}\n`;
                results += `当前useMockData: ${currentUseMockData}\n\n`;
                
                // 验证一致性
                const endpointMatch = currentEndpoint === expectedEndpoint;
                const mockDataMatch = currentUseMockData === expectedUseMockData;
                
                results += `API端点匹配: ${endpointMatch ? '✅' : '❌'}\n`;
                results += `模拟数据设置匹配: ${mockDataMatch ? '✅' : '❌'}\n\n`;
                
                if (endpointMatch && mockDataMatch) {
                    results += '🎉 API配置与authors/categories页面完全一致！\n';
                    results += 'Sources页面现在应该能正常调用生产API了。\n';
                    consistencyTest.className = 'test-result test-success';
                } else {
                    results += '⚠️  API配置与authors/categories页面不一致\n';
                    consistencyTest.className = 'test-result test-warning';
                }
                
                consistencyTest.textContent = results;
            } catch (error) {
                consistencyTest.textContent = `一致性验证错误: ${error.message}`;
                consistencyTest.className = 'test-result test-error';
            }
        }
        
        async function testSourcesAPI() {
            const sourcesApiTest = document.getElementById('sources-api-test');
            try {
                let results = 'Sources API测试结果:\n';
                
                // 强制使用生产API
                if (window.ApiClient) {
                    window.ApiClient.useMockData = false;
                    results += '✅ 强制设置ApiClient使用生产API\n\n';
                }
                
                // 测试getSourceByName
                results += '1. 测试getSourceByName API:\n';
                try {
                    const testSource = await window.ApiClient.getSourceByName('atlas-shrugged');
                    if (testSource) {
                        results += `   ✅ 成功获取来源: ${testSource.name} (ID: ${testSource.id})\n`;
                    } else {
                        results += `   ❌ 未找到来源: atlas-shrugged\n`;
                    }
                } catch (error) {
                    results += `   ❌ API调用失败: ${error.message}\n`;
                }
                
                // 测试getPopularSources
                results += '\n2. 测试getPopularSources API:\n';
                try {
                    const popularSources = await window.ApiClient.getPopularSources(5);
                    if (popularSources && popularSources.length > 0) {
                        results += `   ✅ 成功获取${popularSources.length}个热门来源:\n`;
                        popularSources.slice(0, 3).forEach((source, index) => {
                            results += `      ${index + 1}. ${source.name} (${source.count} 条名言)\n`;
                        });
                    } else {
                        results += `   ❌ 未获取到热门来源数据\n`;
                    }
                } catch (error) {
                    results += `   ❌ API调用失败: ${error.message}\n`;
                }
                
                // 测试getQuotes with sourceId
                results += '\n3. 测试getQuotes with sourceId:\n';
                try {
                    // 使用一个已知的sourceId进行测试
                    const quotesData = await window.ApiClient.getQuotes(1, 3, { sourceId: 1 });
                    if (quotesData && quotesData.quotes && quotesData.quotes.length > 0) {
                        results += `   ✅ 成功获取名言数据: ${quotesData.totalCount} 条总计，当前页 ${quotesData.quotes.length} 条\n`;
                        results += `   第一条名言: "${quotesData.quotes[0].content.substring(0, 50)}..."\n`;
                    } else {
                        results += `   ❌ 未获取到名言数据\n`;
                    }
                } catch (error) {
                    results += `   ❌ API调用失败: ${error.message}\n`;
                }
                
                results += '\n' + '='.repeat(50) + '\n';
                results += '🎯 测试总结:\n';
                results += 'Sources页面现在应该能够:\n';
                results += '✅ 使用与authors/categories相同的生产API配置\n';
                results += '✅ 正确调用getSourceByName获取来源信息\n';
                results += '✅ 正确调用getQuotes获取来源相关的名言\n';
                results += '✅ 正确显示来源页面内容，而不是"Failed to initialize page"错误\n';
                
                sourcesApiTest.textContent = results;
                sourcesApiTest.className = 'test-result test-success';
                
            } catch (error) {
                sourcesApiTest.textContent = `Sources API测试错误: ${error.message}\n${error.stack}`;
                sourcesApiTest.className = 'test-result test-error';
            }
        }
    </script>
</body>
</html>
