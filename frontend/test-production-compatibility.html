<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产API兼容性测试 - Quotese</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success { background-color: #d4edda; border-color: #28a745; color: #155724; }
        .error { background-color: #f8d7da; border-color: #dc3545; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffc107; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .api-mode {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .mode-production { background: #28a745; color: white; }
        .mode-local { background: #17a2b8; color: white; }
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .performance-meter {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .performance-bar {
            height: 100%;
            transition: width 0.3s ease;
        }
        .fast { background: #28a745; }
        .medium { background: #ffc107; }
        .slow { background: #dc3545; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔗 生产API兼容性测试</h1>
        <p>全面测试本地前端与生产API的兼容性和功能完整性</p>
        <div id="current-mode">
            <span>当前API模式:</span>
            <span id="api-mode-indicator" class="api-mode">检测中...</span>
        </div>
    </div>

    <div class="test-grid">
        <div class="test-card">
            <h3>🎛️ API模式控制</h3>
            <button onclick="switchToProductionAPI()">切换到生产API</button>
            <button onclick="switchToLocalAPI()">切换到本地API</button>
            <button onclick="getCurrentMode()">查看当前模式</button>
            <button onclick="runCompatibilityTest()">运行兼容性测试</button>
        </div>

        <div class="test-card">
            <h3>📊 页面功能测试</h3>
            <button onclick="testHomepage()">测试首页</button>
            <button onclick="testCategoriesPage()">测试分类页</button>
            <button onclick="testAuthorsPage()">测试作者页</button>
            <button onclick="testSourcesPage()">测试来源页</button>
        </div>

        <div class="test-card">
            <h3>🚀 性能测试</h3>
            <button onclick="testOptimizedNavigation()">测试优化导航</button>
            <button onclick="testAPIPerformance()">测试API性能</button>
            <button onclick="comparePerformance()">性能对比</button>
            <div id="performance-display"></div>
        </div>

        <div class="test-card">
            <h3>🔍 数据完整性测试</h3>
            <button onclick="testDataIntegrity()">测试数据完整性</button>
            <button onclick="testSearchFunctionality()">测试搜索功能</button>
            <button onclick="testPopularModules()">测试热门模块</button>
        </div>
    </div>

    <div id="test-results"></div>

    <!-- 加载配置和依赖 -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/page-router.js"></script>
    
    <script>
        // 页面加载时初始化
        window.addEventListener('load', function() {
            updateModeIndicator();
            addTestResult('页面加载', 'info', '生产API兼容性测试页面已加载');
        });

        // 更新模式指示器
        function updateModeIndicator() {
            const config = window.AppConfig;
            const indicator = document.getElementById('api-mode-indicator');
            
            if (config.apiEndpoint.includes('api.quotese.com')) {
                indicator.textContent = '生产API';
                indicator.className = 'api-mode mode-production';
            } else {
                indicator.textContent = '本地API';
                indicator.className = 'api-mode mode-local';
            }
        }

        // API模式切换函数
        function switchToProductionAPI() {
            const result = window.QuoteseAPIMode.useProductionAPI();
            addTestResult('切换API模式', 'success', result);
            setTimeout(() => location.reload(), 1000);
        }

        function switchToLocalAPI() {
            const result = window.QuoteseAPIMode.useLocalAPI();
            addTestResult('切换API模式', 'success', result);
            setTimeout(() => location.reload(), 1000);
        }

        function getCurrentMode() {
            const mode = window.QuoteseAPIMode.getCurrentMode();
            addTestResult('当前API模式', 'info', 
                `API端点: ${mode.apiEndpoint}<br>` +
                `GraphQL端点: ${mode.graphqlEndpoint}<br>` +
                `使用生产API: ${mode.isUsingProduction}<br>` +
                `调试模式: ${mode.debug}`
            );
        }

        // 运行完整兼容性测试
        async function runCompatibilityTest() {
            addTestResult('兼容性测试', 'info', '开始运行完整兼容性测试...', true);
            
            // 基础API测试
            await testBasicAPI();
            
            // 页面功能测试
            await testAllPages();
            
            // 性能测试
            await testPerformance();
            
            // 数据完整性测试
            await testDataIntegrity();
            
            addTestResult('兼容性测试', 'success', '所有兼容性测试已完成');
        }

        // 基础API测试
        async function testBasicAPI() {
            addTestSection('基础API测试');
            
            const config = window.AppConfig;
            const endpoints = [
                { name: 'Categories', url: `${config.apiEndpoint}categories/` },
                { name: 'Authors', url: `${config.apiEndpoint}authors/` },
                { name: 'Sources', url: `${config.apiEndpoint}sources/` },
                { name: 'Quotes', url: `${config.apiEndpoint}quotes/` }
            ];

            for (const endpoint of endpoints) {
                try {
                    const startTime = performance.now();
                    const response = await fetch(endpoint.url);
                    const endTime = performance.now();
                    const responseTime = Math.round(endTime - startTime);
                    
                    if (response.ok) {
                        const data = await response.json();
                        addTestResult(`${endpoint.name} API`, 'success', 
                            `状态码: ${response.status}, 数据量: ${data.length || 'N/A'}, 响应时间: ${responseTime}ms`);
                    } else {
                        addTestResult(`${endpoint.name} API`, 'error', 
                            `HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    addTestResult(`${endpoint.name} API`, 'error', 
                        `请求失败: ${error.message}`);
                }
            }
        }

        // 测试所有页面
        async function testAllPages() {
            addTestSection('页面功能测试');
            
            await testHomepage();
            await testCategoriesPage();
            await testAuthorsPage();
            await testSourcesPage();
        }

        // 测试首页
        async function testHomepage() {
            try {
                const config = window.AppConfig;
                
                // 测试首页数据加载
                const queries = [
                    { name: '热门分类', query: '{ categories(first: 10) { id name } }' },
                    { name: '热门作者', query: '{ authors(first: 10) { id name } }' },
                    { name: '热门来源', query: '{ sources(first: 10) { id name } }' }
                ];

                for (const queryTest of queries) {
                    try {
                        const response = await fetch(config.graphqlEndpoint, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ query: queryTest.query })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.errors) {
                                addTestResult(`首页-${queryTest.name}`, 'error', 
                                    `GraphQL错误: ${data.errors[0]?.message}`);
                            } else {
                                addTestResult(`首页-${queryTest.name}`, 'success', 
                                    `数据加载成功`);
                            }
                        } else {
                            addTestResult(`首页-${queryTest.name}`, 'error', 
                                `HTTP ${response.status}`);
                        }
                    } catch (error) {
                        addTestResult(`首页-${queryTest.name}`, 'error', 
                            `请求失败: ${error.message}`);
                    }
                }
            } catch (error) {
                addTestResult('首页测试', 'error', `测试失败: ${error.message}`);
            }
        }

        // 测试分类页
        async function testCategoriesPage() {
            try {
                const config = window.AppConfig;
                const response = await fetch(`${config.apiEndpoint}categories/`);
                
                if (response.ok) {
                    const categories = await response.json();
                    addTestResult('分类页测试', 'success', 
                        `获取到 ${categories.length} 个分类`);
                    
                    // 测试第一个分类的详情
                    if (categories.length > 0) {
                        const firstCategory = categories[0];
                        addTestResult('分类详情', 'success', 
                            `分类示例: ${firstCategory.name} (ID: ${firstCategory.id})`);
                    }
                } else {
                    addTestResult('分类页测试', 'error', 
                        `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addTestResult('分类页测试', 'error', `测试失败: ${error.message}`);
            }
        }

        // 测试作者页
        async function testAuthorsPage() {
            try {
                const config = window.AppConfig;
                const response = await fetch(`${config.apiEndpoint}authors/`);
                
                if (response.ok) {
                    const authors = await response.json();
                    addTestResult('作者页测试', 'success', 
                        `获取到 ${authors.length} 个作者`);
                } else {
                    addTestResult('作者页测试', 'error', 
                        `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addTestResult('作者页测试', 'error', `测试失败: ${error.message}`);
            }
        }

        // 测试来源页
        async function testSourcesPage() {
            try {
                const config = window.AppConfig;
                const response = await fetch(`${config.apiEndpoint}sources/`);
                
                if (response.ok) {
                    const sources = await response.json();
                    addTestResult('来源页测试', 'success', 
                        `获取到 ${sources.length} 个来源`);
                } else {
                    addTestResult('来源页测试', 'error', 
                        `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addTestResult('来源页测试', 'error', `测试失败: ${error.message}`);
            }
        }

        // 测试优化导航
        async function testOptimizedNavigation() {
            addTestSection('优化导航测试');
            
            // 检查优化导航功能是否存在
            if (typeof window.navigateToEntityWithId === 'function') {
                addTestResult('优化导航功能', 'success', '优化导航函数已加载');
                
                // 检查实体缓存
                if (window.entityCache) {
                    const cacheSize = Object.keys(window.entityCache).length;
                    addTestResult('实体缓存', 'success', `缓存中有 ${cacheSize} 个实体`);
                } else {
                    addTestResult('实体缓存', 'warning', '实体缓存未初始化');
                }
            } else {
                addTestResult('优化导航功能', 'error', '优化导航函数未找到');
            }
        }

        // 测试API性能
        async function testAPIPerformance() {
            addTestSection('API性能测试');
            
            const config = window.AppConfig;
            const testUrl = `${config.apiEndpoint}categories/`;
            const iterations = 5;
            const times = [];

            for (let i = 0; i < iterations; i++) {
                const startTime = performance.now();
                try {
                    const response = await fetch(testUrl);
                    if (response.ok) {
                        await response.json();
                    }
                } catch (error) {
                    // 忽略错误，继续测试
                }
                const endTime = performance.now();
                times.push(endTime - startTime);
            }

            const avgTime = Math.round(times.reduce((a, b) => a + b, 0) / times.length);
            const minTime = Math.round(Math.min(...times));
            const maxTime = Math.round(Math.max(...times));

            addTestResult('API性能测试', 'success', 
                `平均响应时间: ${avgTime}ms (最快: ${minTime}ms, 最慢: ${maxTime}ms)`);
            
            // 显示性能条
            displayPerformanceMeter(avgTime);
        }

        // 显示性能条
        function displayPerformanceMeter(responseTime) {
            const container = document.getElementById('performance-display');
            const percentage = Math.min(100, (responseTime / 1000) * 100);
            let className = 'fast';
            let label = '快速';
            
            if (responseTime > 500) {
                className = 'slow';
                label = '较慢';
            } else if (responseTime > 200) {
                className = 'medium';
                label = '中等';
            }
            
            container.innerHTML = `
                <div>响应时间: ${responseTime}ms (${label})</div>
                <div class="performance-meter">
                    <div class="performance-bar ${className}" style="width: ${percentage}%"></div>
                </div>
            `;
        }

        // 测试数据完整性
        async function testDataIntegrity() {
            addTestSection('数据完整性测试');
            
            const config = window.AppConfig;
            
            try {
                // 测试数据关联性
                const quotesResponse = await fetch(`${config.apiEndpoint}quotes/?limit=10`);
                if (quotesResponse.ok) {
                    const quotes = await quotesResponse.json();
                    
                    if (quotes.length > 0) {
                        const firstQuote = quotes[0];
                        addTestResult('数据结构', 'success', 
                            `名言数据结构完整: ${Object.keys(firstQuote).join(', ')}`);
                        
                        // 检查必要字段
                        const requiredFields = ['id', 'content', 'author'];
                        const missingFields = requiredFields.filter(field => !firstQuote.hasOwnProperty(field));
                        
                        if (missingFields.length === 0) {
                            addTestResult('必要字段', 'success', '所有必要字段都存在');
                        } else {
                            addTestResult('必要字段', 'error', 
                                `缺少字段: ${missingFields.join(', ')}`);
                        }
                    }
                } else {
                    addTestResult('数据完整性', 'error', 
                        `无法获取测试数据: HTTP ${quotesResponse.status}`);
                }
            } catch (error) {
                addTestResult('数据完整性测试', 'error', `测试失败: ${error.message}`);
            }
        }

        // 辅助函数
        function addTestResult(title, type, message, showLoading = false) {
            const container = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            const loadingIcon = showLoading ? '<span class="loading"></span> ' : '';
            
            resultDiv.innerHTML = `
                <strong>[${timestamp}] ${title}:</strong><br>
                ${loadingIcon}${message}
            `;
            
            container.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function addTestSection(title) {
            const container = document.getElementById('test-results');
            const sectionDiv = document.createElement('div');
            sectionDiv.className = 'test-card';
            sectionDiv.innerHTML = `<h3>📋 ${title}</h3>`;
            container.appendChild(sectionDiv);
        }
    </script>
</body>
</html>
