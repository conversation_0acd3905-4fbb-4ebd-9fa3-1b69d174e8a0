/**
 * 移动端性能优化器
 * 专门针对移动设备的缓存和性能优化
 * @version 1.0.0
 * @date 2025-06-26
 * <AUTHOR>
 */

class MobilePerformanceOptimizer {
    constructor() {
        // 检测移动设备
        this.isMobile = this.detectMobileDevice();
        
        // 移动端专用配置
        this.config = {
            maxCacheSize: this.isMobile ? 30 : 60,        // 移动端减少缓存大小
            aggressiveCleanup: this.isMobile,             // 移动端启用积极清理
            memoryThreshold: this.isMobile ? 50 : 100,    // 内存阈值 (MB)
            cleanupInterval: this.isMobile ? 30000 : 60000, // 清理间隔 (ms)
            preloadLimit: this.isMobile ? 5 : 10          // 预加载限制
        };
        
        // 性能监控
        this.stats = {
            memoryUsage: 0,
            cacheHits: 0,
            cacheMisses: 0,
            cleanupCount: 0,
            optimizationCount: 0
        };
        
        // 初始化标志
        this.isInitialized = false;
        this.cleanupTimer = null;
        
        console.log('🔧 MobilePerformanceOptimizer initialized:', {
            isMobile: this.isMobile,
            config: this.config
        });
    }
    
    /**
     * 检测移动设备
     * @returns {boolean} 是否为移动设备
     */
    detectMobileDevice() {
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;
        
        // 检测移动设备User-Agent
        const mobileRegex = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i;
        const isMobileUA = mobileRegex.test(userAgent);
        
        // 检测屏幕尺寸
        const isSmallScreen = window.innerWidth <= 768 || window.innerHeight <= 768;
        
        // 检测触摸设备
        const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        // 综合判断
        const isMobile = isMobileUA || (isSmallScreen && isTouchDevice);
        
        console.log('📱 Mobile detection:', {
            userAgent: isMobileUA,
            smallScreen: isSmallScreen,
            touchDevice: isTouchDevice,
            result: isMobile
        });
        
        return isMobile;
    }
    
    /**
     * 启用移动端优化
     */
    optimizeForMobile() {
        if (this.isInitialized) {
            console.log('⚠️ MobilePerformanceOptimizer already initialized');
            return;
        }
        
        console.log('🚀 Starting mobile performance optimization...');
        
        if (this.isMobile) {
            // 1. 限制缓存大小
            this.limitCacheSize();
            
            // 2. 启用积极清理
            this.enableAggressiveCleanup();
            
            // 3. 优化内存使用
            this.optimizeMemoryUsage();
            
            // 4. 启动定期清理
            this.startPeriodicCleanup();
            
            // 5. 监听内存压力事件
            this.setupMemoryPressureHandling();
            
            this.stats.optimizationCount++;
            console.log('✅ Mobile optimization enabled');
        } else {
            console.log('ℹ️ Desktop device detected, using standard configuration');
        }
        
        this.isInitialized = true;
    }
    
    /**
     * 限制缓存大小
     */
    limitCacheSize() {
        if (!window.entityCache) {
            console.log('⚠️ EntityCache not found, skipping cache size limit');
            return;
        }
        
        console.log('🔧 Limiting cache size for mobile...');
        
        ['categories', 'authors', 'sources'].forEach(type => {
            const cache = window.entityCache[type];
            if (cache && cache.size > this.config.maxCacheSize) {
                console.log(`📦 Cleaning ${type} cache: ${cache.size} -> ${this.config.maxCacheSize}`);
                
                // 获取所有缓存条目并按时间和访问频率排序
                const entries = Array.from(cache.entries());
                entries.sort((a, b) => {
                    const scoreA = (a[1].cachedAt || 0) + ((a[1].accessCount || 0) * 10000);
                    const scoreB = (b[1].cachedAt || 0) + ((b[1].accessCount || 0) * 10000);
                    return scoreB - scoreA; // 降序排列
                });
                
                // 清空缓存并保留最重要的条目
                cache.clear();
                entries.slice(0, this.config.maxCacheSize).forEach(([id, data]) => {
                    cache.set(id, data);
                });
                
                this.stats.cleanupCount++;
            }
        });
    }
    
    /**
     * 启用积极清理
     */
    enableAggressiveCleanup() {
        if (!this.config.aggressiveCleanup) return;
        
        console.log('🧹 Enabling aggressive cleanup for mobile...');
        
        // 清理过期的缓存条目
        this.cleanupExpiredCache();
        
        // 清理未使用的DOM元素
        this.cleanupUnusedDOMElements();
        
        // 强制垃圾回收（如果支持）
        if (window.gc && typeof window.gc === 'function') {
            try {
                window.gc();
                console.log('🗑️ Manual garbage collection triggered');
            } catch (e) {
                // 忽略错误，gc可能不可用
            }
        }
    }
    
    /**
     * 清理过期缓存
     */
    cleanupExpiredCache() {
        if (!window.entityCache) return;
        
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30分钟
        
        ['categories', 'authors', 'sources'].forEach(type => {
            const cache = window.entityCache[type];
            if (!cache) return;
            
            let cleanedCount = 0;
            for (const [id, data] of cache.entries()) {
                if (data.cachedAt && (now - data.cachedAt) > maxAge) {
                    cache.delete(id);
                    cleanedCount++;
                }
            }
            
            if (cleanedCount > 0) {
                console.log(`🧹 Cleaned ${cleanedCount} expired ${type} cache entries`);
            }
        });
    }
    
    /**
     * 清理未使用的DOM元素
     */
    cleanupUnusedDOMElements() {
        // 清理隐藏的或不可见的元素
        const hiddenElements = document.querySelectorAll('[style*="display: none"], .hidden');
        let cleanedCount = 0;
        
        hiddenElements.forEach(element => {
            // 只清理非关键元素
            if (!element.classList.contains('critical') && 
                !element.hasAttribute('data-keep')) {
                element.remove();
                cleanedCount++;
            }
        });
        
        if (cleanedCount > 0) {
            console.log(`🧹 Cleaned ${cleanedCount} unused DOM elements`);
        }
    }
    
    /**
     * 优化内存使用
     */
    optimizeMemoryUsage() {
        console.log('💾 Optimizing memory usage for mobile...');
        
        // 估算当前内存使用
        this.estimateMemoryUsage();
        
        // 如果内存使用过高，进行清理
        if (this.stats.memoryUsage > this.config.memoryThreshold) {
            console.log('⚠️ High memory usage detected, performing cleanup...');
            this.performEmergencyCleanup();
        }
    }
    
    /**
     * 估算内存使用
     */
    estimateMemoryUsage() {
        let estimatedUsage = 0;
        
        // 估算缓存使用的内存
        if (window.entityCache) {
            ['categories', 'authors', 'sources'].forEach(type => {
                const cache = window.entityCache[type];
                if (cache) {
                    estimatedUsage += cache.size * 2; // 每个条目约2KB
                }
            });
        }
        
        // 估算DOM元素使用的内存
        const elementCount = document.querySelectorAll('*').length;
        estimatedUsage += elementCount * 0.1; // 每个元素约0.1KB
        
        this.stats.memoryUsage = estimatedUsage;
        
        console.log(`💾 Estimated memory usage: ${estimatedUsage.toFixed(2)} MB`);
        
        return estimatedUsage;
    }
    
    /**
     * 执行紧急清理
     */
    performEmergencyCleanup() {
        console.log('🚨 Performing emergency cleanup...');
        
        // 大幅减少缓存大小
        const emergencyLimit = Math.floor(this.config.maxCacheSize / 2);
        
        if (window.entityCache) {
            ['categories', 'authors', 'sources'].forEach(type => {
                const cache = window.entityCache[type];
                if (cache && cache.size > emergencyLimit) {
                    const entries = Array.from(cache.entries());
                    cache.clear();
                    
                    // 只保留最重要的条目
                    entries.slice(0, emergencyLimit).forEach(([id, data]) => {
                        cache.set(id, data);
                    });
                    
                    console.log(`🚨 Emergency cleanup: ${type} cache reduced to ${emergencyLimit}`);
                }
            });
        }
        
        // 强制垃圾回收
        this.enableAggressiveCleanup();
        
        this.stats.cleanupCount++;
    }
    
    /**
     * 启动定期清理
     */
    startPeriodicCleanup() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        
        this.cleanupTimer = setInterval(() => {
            console.log('🔄 Performing periodic cleanup...');
            this.limitCacheSize();
            this.cleanupExpiredCache();
            this.estimateMemoryUsage();
            
            // 如果内存使用过高，执行清理
            if (this.stats.memoryUsage > this.config.memoryThreshold) {
                this.performEmergencyCleanup();
            }
        }, this.config.cleanupInterval);
        
        console.log(`⏰ Periodic cleanup started (interval: ${this.config.cleanupInterval}ms)`);
    }
    
    /**
     * 设置内存压力处理
     */
    setupMemoryPressureHandling() {
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                console.log('📱 Page hidden, performing cleanup...');
                this.enableAggressiveCleanup();
            }
        });
        
        // 监听页面卸载
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        
        // 监听内存警告（如果支持）
        if ('memory' in performance) {
            const checkMemory = () => {
                const memInfo = performance.memory;
                const usedPercent = (memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit) * 100;
                
                if (usedPercent > 80) {
                    console.log('⚠️ High memory usage detected:', usedPercent.toFixed(2) + '%');
                    this.performEmergencyCleanup();
                }
            };
            
            setInterval(checkMemory, 60000); // 每分钟检查一次
        }
    }
    
    /**
     * 获取性能统计
     */
    getStats() {
        return {
            ...this.stats,
            isMobile: this.isMobile,
            config: this.config,
            isInitialized: this.isInitialized
        };
    }
    
    /**
     * 清理资源
     */
    cleanup() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        
        console.log('🧹 MobilePerformanceOptimizer cleanup completed');
    }
}

// 全局实例
window.MobilePerformanceOptimizer = MobilePerformanceOptimizer;

// 页面加载时自动启用移动端优化
document.addEventListener('DOMContentLoaded', () => {
    if (!window.mobileOptimizer) {
        window.mobileOptimizer = new MobilePerformanceOptimizer();
        window.mobileOptimizer.optimizeForMobile();
    }
});

console.log('📱 Mobile Performance Optimizer loaded');
