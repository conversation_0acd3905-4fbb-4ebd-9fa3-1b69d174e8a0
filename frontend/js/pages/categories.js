/**
 * Categories List Page Controller
 * Independent implementation for /categories/ page
 * Uses same APIs as Popular Categories module but with different logic
 * Architecture pattern consistent with Authors and Sources pages
 */

// Page state - using independent namespace to avoid conflicts
const categoriesListPageState = {
    // Data state
    allCategories: [],
    displayedCategories: [],
    filteredCategories: [],
    
    // Pagination state
    currentPage: 1,
    pageSize: 48,  // Grid view: 6x8 or 4x12
    totalPages: 0,
    totalCount: 0,
    
    // UI state
    isLoading: false,
    searchQuery: '',
    sortOrder: 'popularity',
    viewMode: 'grid',
    
    // Performance state
    loadedCount: 0,
    maxCategories: 500,
    hasMore: true
};

/**
 * Page initialization function
 * Called by PageRouter: initCategoriesListPage()
 */
async function initCategoriesListPage(params) {
    try {
        console.log('🚀 Initializing Categories List Page...');
        console.log('📋 Params received:', params);
        console.log('🔍 Available global objects:', {
            ApiClient: !!window.ApiClient,
            ComponentLoader: !!window.ComponentLoader,
            PageRouter: !!window.PageRouter,
            UrlHandler: !!window.UrlHandler
        });

        // Show loading state
        showLoadingState();
        console.log('⏳ Loading state shown');

        // Load page components
        try {
            await loadPageComponents();
            console.log('✅ Page components loaded');
        } catch (componentError) {
            console.warn('⚠️ Component loading failed, continuing anyway:', componentError);
        }

        // Load categories data
        try {
            await loadCategoriesData();
            console.log('✅ Categories data loaded');
        } catch (dataError) {
            console.error('❌ Categories data loading failed:', dataError);
            throw dataError; // This is critical, so we should fail
        }

        // Initialize UI controls
        try {
            initializeControls();
            console.log('✅ UI controls initialized');
        } catch (controlsError) {
            console.warn('⚠️ UI controls initialization failed:', controlsError);
        }

        // Update page metadata
        updatePageMetadata();
        console.log('✅ Page metadata updated');

        // Hide loading state and show content
        hideLoadingState();
        console.log('✅ Categories List Page initialization complete');

    } catch (error) {
        console.error('❌ Categories List Page initialization failed:', error);
        showErrorState(error.message);
        throw error;
    }
}

/**
 * Load page components
 */
async function loadPageComponents() {
    if (window.ComponentLoader) {
        await window.ComponentLoader.loadComponents([
            'navigation',
            'breadcrumb',
            'footer'
        ]);
    }
}

/**
 * Load categories data
 * Uses same API as Popular Categories module but with different logic
 */
async function loadCategoriesData() {
    try {
        console.log('Loading categories data...');

        // Check if ApiClient is available
        if (!window.ApiClient) {
            throw new Error('ApiClient not available');
        }

        console.log('ApiClient available, calling getPopularCategories(500)...');

        // Use same API as Popular Categories module
        // But get 500 categories instead of 100, and use all data instead of random selection
        let popularCategories;

        try {
            popularCategories = await window.ApiClient.getPopularCategories(500);
            console.log('🎯 API response received:', {
                type: typeof popularCategories,
                isArray: Array.isArray(popularCategories),
                length: popularCategories ? popularCategories.length : 'N/A',
                firstItem: popularCategories && popularCategories.length > 0 ? popularCategories[0] : 'N/A'
            });

            // Detailed data structure validation
            if (popularCategories && popularCategories.length > 0) {
                const sampleCategory = popularCategories[0];
                console.log('📊 Sample category structure:', {
                    id: sampleCategory.id,
                    name: sampleCategory.name,
                    count: sampleCategory.count,
                    quotesCount: sampleCategory.quotesCount,
                    allKeys: Object.keys(sampleCategory)
                });
            }
        } catch (apiError) {
            console.warn('❌ API call failed, using fallback data:', apiError);
            // Fallback to a smaller number if 500 fails
            try {
                popularCategories = await window.ApiClient.getPopularCategories(100);
                console.log('🔄 Fallback API response received:', popularCategories);
            } catch (fallbackError) {
                console.error('❌ Both API calls failed:', fallbackError);
                // Use mock data as last resort
                popularCategories = getMockCategories();
                console.log('🎭 Using mock data as fallback');
            }
        }

        // Validate and process data
        if (!popularCategories || !Array.isArray(popularCategories)) {
            throw new Error('Invalid categories data received from API');
        }

        if (popularCategories.length === 0) {
            console.warn('⚠️ No categories data received, using mock data');
            popularCategories = getMockCategories();
        }

        // Store data in page state
        categoriesListPageState.allCategories = popularCategories;
        categoriesListPageState.filteredCategories = [...popularCategories];
        categoriesListPageState.totalCount = popularCategories.length;
        categoriesListPageState.totalPages = Math.ceil(popularCategories.length / categoriesListPageState.pageSize);

        console.log('📊 Categories data processed:', {
            totalCategories: categoriesListPageState.totalCount,
            totalPages: categoriesListPageState.totalPages,
            pageSize: categoriesListPageState.pageSize
        });

        // Apply initial sorting and pagination
        applySorting();
        updatePagination();
        renderCategories();

    } catch (error) {
        console.error('❌ Failed to load categories data:', error);
        throw error;
    }
}

/**
 * Apply sorting to categories
 */
function applySorting() {
    const { sortOrder, filteredCategories } = categoriesListPageState;
    
    switch (sortOrder) {
        case 'popularity':
            filteredCategories.sort((a, b) => (b.quotesCount || b.count || 0) - (a.quotesCount || a.count || 0));
            break;
        case 'alphabetical':
            filteredCategories.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'recent':
            // If we have creation dates, sort by them, otherwise keep current order
            break;
        default:
            // Keep current order
            break;
    }
}

/**
 * Update pagination
 */
function updatePagination() {
    const { currentPage, pageSize, filteredCategories } = categoriesListPageState;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    categoriesListPageState.displayedCategories = filteredCategories.slice(startIndex, endIndex);
    categoriesListPageState.totalPages = Math.ceil(filteredCategories.length / pageSize);
}

/**
 * Render categories
 */
function renderCategories() {
    const container = document.getElementById('categories-container');
    if (!container) {
        console.error('❌ Categories container not found');
        return;
    }

    const { displayedCategories } = categoriesListPageState;

    if (displayedCategories.length === 0) {
        container.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-tags text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-semibold mb-2">No categories found</h3>
                <p class="text-gray-600">Try adjusting your search or filters</p>
            </div>
        `;
        return;
    }

    const categoriesHTML = displayedCategories.map(category => {
        const categorySlug = window.UrlHandler ? window.UrlHandler.slugify(category.name) : category.name.toLowerCase().replace(/\s+/g, '-');
        const quotesCount = category.quotesCount || category.count || 0;
        
        return `
            <div class="category-card bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-tag text-2xl text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">
                        <a href="/categories/${categorySlug}/" class="hover:text-purple-600 dark:hover:text-purple-400 transition-colors">
                            ${category.name}
                        </a>
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">
                        ${quotesCount} quote${quotesCount !== 1 ? 's' : ''}
                    </p>
                    <a href="/categories/${categorySlug}/" class="inline-flex items-center text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 text-sm font-medium">
                        View Quotes
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            ${categoriesHTML}
        </div>
    `;
}

/**
 * Initialize UI controls
 */
function initializeControls() {
    // Search functionality
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
        console.log('✅ Search input event listener attached');
    } else {
        console.warn('⚠️ Search input element not found');
    }

    // Sort controls
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', handleSortChange);
        console.log('✅ Sort select event listener attached');
    } else {
        console.warn('⚠️ Sort select element not found');
    }

    // Pagination controls will be added when needed
}

/**
 * Handle search
 */
function handleSearch(event) {
    const query = event.target.value.toLowerCase().trim();
    categoriesListPageState.searchQuery = query;
    categoriesListPageState.currentPage = 1; // Reset to first page

    if (query === '') {
        categoriesListPageState.filteredCategories = [...categoriesListPageState.allCategories];
    } else {
        categoriesListPageState.filteredCategories = categoriesListPageState.allCategories.filter(category =>
            category.name.toLowerCase().includes(query)
        );
    }

    applySorting();
    updatePagination();
    renderCategories();
}

/**
 * Handle sort change
 */
function handleSortChange(event) {
    categoriesListPageState.sortOrder = event.target.value;
    applySorting();
    updatePagination();
    renderCategories();
}

/**
 * Show loading state
 */
function showLoadingState() {
    categoriesListPageState.isLoading = true;

    const loadingContainer = document.getElementById('loading-container');
    const categoriesContainer = document.getElementById('categories-container');
    const errorContainer = document.getElementById('error-container');

    if (loadingContainer) {
        loadingContainer.style.display = 'block';
    }
    if (categoriesContainer) {
        categoriesContainer.style.display = 'none';
    }
    if (errorContainer) {
        errorContainer.style.display = 'none';
    }
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    categoriesListPageState.isLoading = false;

    const loadingContainer = document.getElementById('loading-container');
    const categoriesContainer = document.getElementById('categories-container');

    if (loadingContainer) {
        loadingContainer.style.display = 'none';
    }
    if (categoriesContainer) {
        categoriesContainer.style.display = 'block';
    }
}

/**
 * Show error state
 */
function showErrorState(message) {
    const errorContainer = document.getElementById('error-container');
    const loadingContainer = document.getElementById('loading-container');
    const categoriesContainer = document.getElementById('categories-container');

    if (errorContainer) {
        errorContainer.style.display = 'block';
        errorContainer.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                <h3 class="text-lg font-semibold mb-2">Failed to load categories</h3>
                <p class="text-gray-600 mb-4">${message}</p>
                <button onclick="location.reload()" class="btn-primary">
                    <i class="fas fa-refresh mr-2"></i>Retry
                </button>
            </div>
        `;
    }
    if (loadingContainer) {
        loadingContainer.style.display = 'none';
    }
    if (categoriesContainer) {
        categoriesContainer.style.display = 'none';
    }
}

/**
 * Update page metadata
 */
function updatePageMetadata() {
    // Update page title
    document.title = 'Browse All Categories - Quotese.com';

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        metaDescription.content = 'Explore our complete collection of quote categories. Find inspirational quotes by topic including life, love, success, wisdom and more.';
    }
}

/**
 * Get mock categories data for fallback
 */
function getMockCategories() {
    return [
        { id: 1, name: 'Life', quotesCount: 45 },
        { id: 2, name: 'Love', quotesCount: 38 },
        { id: 3, name: 'Success', quotesCount: 32 },
        { id: 4, name: 'Wisdom', quotesCount: 28 },
        { id: 5, name: 'Motivation', quotesCount: 25 }
    ];
}

// Expose functions to global scope for PageRouter
window.initCategoriesListPage = initCategoriesListPage;

// Add immediate console log to verify script loading
console.log('✅ Categories.js script loaded successfully');
console.log('🔍 initCategoriesListPage function available:', !!window.initCategoriesListPage);
