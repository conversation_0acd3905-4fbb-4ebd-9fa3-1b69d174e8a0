/**
 * Sources List Page Controller
 * Independent implementation for /sources/ page
 * Uses same APIs as Popular Sources module but with different logic
 */

// Page state - using independent namespace to avoid conflicts
const sourcesListPageState = {
    // Data state
    allSources: [],
    displayedSources: [],
    filteredSources: [],
    
    // Pagination state
    currentPage: 1,
    pageSize: 48,  // Grid view: 6x8 or 4x12
    totalPages: 0,
    totalCount: 0,
    
    // UI state
    isLoading: false,
    searchQuery: '',
    sortOrder: 'popularity',
    viewMode: 'grid',
    
    // Performance state
    loadedCount: 0,
    maxSources: 500,
    hasMore: true
};

/**
 * Page initialization function
 * Called by PageRouter: initSourcesListPage()
 */
async function initSourcesListPage(params) {
    try {
        console.log('🚀 Initializing Sources List Page...');
        console.log('📋 Params received:', params);
        console.log('🔍 Available global objects:', {
            ApiClient: !!window.ApiClient,
            ComponentLoader: !!window.ComponentLoader,
            PageRouter: !!window.PageRouter,
            UrlHandler: !!window.UrlHandler
        });

        // Show loading state
        showLoadingState();
        console.log('⏳ Loading state shown');

        // Load page components
        try {
            await loadPageComponents();
            console.log('✅ Page components loaded');
        } catch (componentError) {
            console.warn('⚠️ Component loading failed, continuing anyway:', componentError);
        }

        // Load sources data
        try {
            await loadSourcesData();
            console.log('✅ Sources data loaded');
        } catch (dataError) {
            console.error('❌ Sources data loading failed:', dataError);
            throw dataError; // This is critical, so we should fail
        }

        // Initialize UI controls
        try {
            initializeControls();
            console.log('✅ UI controls initialized');
        } catch (controlsError) {
            console.warn('⚠️ UI controls initialization failed:', controlsError);
        }

        // Update page metadata
        updatePageMetadata();
        console.log('✅ Page metadata updated');

        // Hide loading state and show content
        hideLoadingState();
        console.log('✅ Sources List Page initialization complete');

    } catch (error) {
        console.error('❌ Sources List Page initialization failed:', error);
        showErrorState(error.message);
        throw error;
    }
}

/**
 * Load page components
 */
async function loadPageComponents() {
    if (window.ComponentLoader) {
        await window.ComponentLoader.loadComponents([
            'navigation',
            'breadcrumb',
            'footer'
        ]);
    }
}

/**
 * Load sources data
 * Uses same API as Popular Sources module but with different logic
 */
async function loadSourcesData() {
    try {
        console.log('Loading sources data...');

        // Check if ApiClient is available
        if (!window.ApiClient) {
            throw new Error('ApiClient not available');
        }

        console.log('ApiClient available, calling getPopularSources(500)...');

        // Use same API as Popular Sources module
        // But get 500 sources instead of 100, and use all data instead of random selection
        let popularSources;

        try {
            popularSources = await window.ApiClient.getPopularSources(500);
            console.log('🎯 API response received:', {
                type: typeof popularSources,
                isArray: Array.isArray(popularSources),
                length: popularSources ? popularSources.length : 'N/A',
                firstItem: popularSources && popularSources.length > 0 ? popularSources[0] : 'N/A'
            });

            // Detailed data structure validation
            if (popularSources && popularSources.length > 0) {
                const sampleSource = popularSources[0];
                console.log('📊 Sample source structure:', {
                    id: sampleSource.id,
                    name: sampleSource.name,
                    count: sampleSource.count,
                    quotesCount: sampleSource.quotesCount,
                    allKeys: Object.keys(sampleSource)
                });
            }
        } catch (apiError) {
            console.warn('❌ API call failed, using fallback data:', apiError);
            // Fallback to a smaller number if 500 fails
            try {
                popularSources = await window.ApiClient.getPopularSources(100);
                console.log('🔄 Fallback API response received:', popularSources);
            } catch (fallbackError) {
                console.error('❌ Both API calls failed:', fallbackError);
                // Use mock data as last resort
                popularSources = getMockSources();
                console.log('🎭 Using mock data as fallback');
            }
        }

        // Validate and process data
        if (!popularSources || !Array.isArray(popularSources)) {
            throw new Error('Invalid sources data received from API');
        }

        if (popularSources.length === 0) {
            console.warn('⚠️ No sources data received, using mock data');
            popularSources = getMockSources();
        }

        // Store data in page state
        sourcesListPageState.allSources = popularSources;
        sourcesListPageState.filteredSources = [...popularSources];
        sourcesListPageState.totalCount = popularSources.length;
        sourcesListPageState.totalPages = Math.ceil(popularSources.length / sourcesListPageState.pageSize);

        console.log('📊 Sources data processed:', {
            totalSources: sourcesListPageState.totalCount,
            totalPages: sourcesListPageState.totalPages,
            pageSize: sourcesListPageState.pageSize
        });

        // Apply initial sorting and pagination
        applySorting();
        updatePagination();
        renderSources();

    } catch (error) {
        console.error('❌ Failed to load sources data:', error);
        throw error;
    }
}

/**
 * Apply sorting to sources
 */
function applySorting() {
    const { sortOrder, filteredSources } = sourcesListPageState;
    
    switch (sortOrder) {
        case 'popularity':
            filteredSources.sort((a, b) => (b.quotesCount || b.count || 0) - (a.quotesCount || a.count || 0));
            break;
        case 'alphabetical':
            filteredSources.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'recent':
            // If we have creation dates, sort by them, otherwise keep current order
            break;
        default:
            // Keep current order
            break;
    }
}

/**
 * Update pagination
 */
function updatePagination() {
    const { currentPage, pageSize, filteredSources } = sourcesListPageState;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    sourcesListPageState.displayedSources = filteredSources.slice(startIndex, endIndex);
    sourcesListPageState.totalPages = Math.ceil(filteredSources.length / pageSize);
}

/**
 * Render sources
 */
function renderSources() {
    const container = document.getElementById('sources-container');
    if (!container) {
        console.error('❌ Sources container not found');
        return;
    }

    const { displayedSources } = sourcesListPageState;

    if (displayedSources.length === 0) {
        container.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-book text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-semibold mb-2">No sources found</h3>
                <p class="text-gray-600">Try adjusting your search or filters</p>
            </div>
        `;
        return;
    }

    const sourcesHTML = displayedSources.map(source => {
        const sourceSlug = window.UrlHandler ? window.UrlHandler.slugify(source.name) : source.name.toLowerCase().replace(/\s+/g, '-');
        const quotesCount = source.quotesCount || source.count || 0;
        
        return `
            <div class="source-card bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-book text-2xl text-green-600 dark:text-green-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">
                        <a href="/sources/${sourceSlug}/" class="hover:text-green-600 dark:hover:text-green-400 transition-colors">
                            ${source.name}
                        </a>
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">
                        ${quotesCount} quote${quotesCount !== 1 ? 's' : ''}
                    </p>
                    <a href="/sources/${sourceSlug}/" class="inline-flex items-center text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 text-sm font-medium">
                        View Quotes
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            ${sourcesHTML}
        </div>
    `;
}

/**
 * Initialize UI controls
 */
function initializeControls() {
    // Search functionality
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }

    // Sort controls
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', handleSortChange);
    }

    // Pagination controls will be added when needed
}

/**
 * Handle search
 */
function handleSearch(event) {
    const query = event.target.value.toLowerCase().trim();
    sourcesListPageState.searchQuery = query;
    sourcesListPageState.currentPage = 1; // Reset to first page

    if (query === '') {
        sourcesListPageState.filteredSources = [...sourcesListPageState.allSources];
    } else {
        sourcesListPageState.filteredSources = sourcesListPageState.allSources.filter(source =>
            source.name.toLowerCase().includes(query)
        );
    }

    applySorting();
    updatePagination();
    renderSources();
}

/**
 * Handle sort change
 */
function handleSortChange(event) {
    sourcesListPageState.sortOrder = event.target.value;
    applySorting();
    updatePagination();
    renderSources();
}

/**
 * Show loading state
 */
function showLoadingState() {
    sourcesListPageState.isLoading = true;

    const loadingContainer = document.getElementById('loading-container');
    const sourcesContainer = document.getElementById('sources-container');
    const errorContainer = document.getElementById('error-container');

    if (loadingContainer) {
        loadingContainer.style.display = 'block';
    }
    if (sourcesContainer) {
        sourcesContainer.style.display = 'none';
    }
    if (errorContainer) {
        errorContainer.style.display = 'none';
    }
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    sourcesListPageState.isLoading = false;

    const loadingContainer = document.getElementById('loading-container');
    const sourcesContainer = document.getElementById('sources-container');

    if (loadingContainer) {
        loadingContainer.style.display = 'none';
    }
    if (sourcesContainer) {
        sourcesContainer.style.display = 'block';
    }
}

/**
 * Show error state
 */
function showErrorState(message) {
    const errorContainer = document.getElementById('error-container');
    const loadingContainer = document.getElementById('loading-container');
    const sourcesContainer = document.getElementById('sources-container');

    if (errorContainer) {
        errorContainer.style.display = 'block';
        errorContainer.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                <h3 class="text-lg font-semibold mb-2">Failed to load sources</h3>
                <p class="text-gray-600 mb-4">${message}</p>
                <button onclick="location.reload()" class="btn-primary">
                    <i class="fas fa-refresh mr-2"></i>Retry
                </button>
            </div>
        `;
    }
    if (loadingContainer) {
        loadingContainer.style.display = 'none';
    }
    if (sourcesContainer) {
        sourcesContainer.style.display = 'none';
    }
}

/**
 * Update page metadata
 */
function updatePageMetadata() {
    // Update page title
    document.title = 'Quote Sources | Books and Speeches - Quotese.com';
    
    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        metaDescription.content = 'Browse quotes by source. Discover wisdom from famous books, speeches, and works.';
    }
}

/**
 * Get mock sources data for fallback
 */
function getMockSources() {
    return [
        { id: 1, name: 'The Art of War', quotesCount: 15 },
        { id: 2, name: 'Think and Grow Rich', quotesCount: 12 },
        { id: 3, name: 'The Great Gatsby', quotesCount: 8 },
        { id: 4, name: 'To Kill a Mockingbird', quotesCount: 10 },
        { id: 5, name: '1984', quotesCount: 14 }
    ];
}

// Expose functions to global scope for PageRouter
window.initSourcesListPage = initSourcesListPage;

// Add immediate console log to verify script loading
console.log('✅ Sources.js script loaded successfully');
console.log('🔍 initSourcesListPage function available:', !!window.initSourcesListPage);
