/**
 * 类别页面控制器 - 重构版本
 * 负责类别页面的数据加载和交互，支持新的语义化URL格式
 *
 * @version 2.0.0
 * @date 2025-06-16
 * <AUTHOR>
 */

// 已知类别ID映射表（用于快速查找和防止API查询失败）
const KNOWN_CATEGORY_IDS = {
    'life': 71523,
    'writing': 142145,
    'friendship': null, // 待确认
    'wisdom': null,     // 待确认
    'love': null,       // 待确认
    'success': null,    // 待确认
    'motivation': null, // 待确认
    'happiness': null   // 待确认
};

// 页面状态 - 使用命名空间避免与其他页面冲突
const categoryPageState = {
    currentPage: 1,
    pageSize: 20,
    totalPages: 0,
    totalQuotes: 0,
    isLoading: false,
    categoryName: '',
    categorySlug: '',
    categoryId: null
};

// 将页面状态暴露给全局作用域
window.categoryPageState = categoryPageState;

/**
 * 初始化类别页面
 * @param {Object} params - 页面参数（可选，由PageRouter传递）
 */
async function initCategoryPage(params = null) {
    try {
        // 🚀 优化路径：检查是否有优化导航数据
        const optimizedData = window.getOptimizedNavigationData();
        if (optimizedData && optimizedData.entityType === 'category') {
            console.log('✅ OptimizedNavigation: Using optimized navigation data for category page', optimizedData);

            // 直接使用实体ID，绕过EntityIdMapper查询
            categoryPageState.categoryId = optimizedData.entityId;
            categoryPageState.categoryName = optimizedData.entityName;
            categoryPageState.categorySlug = window.UrlHandler.slugify(optimizedData.entityName);

            // 清除优化导航数据
            window.clearOptimizedNavigationData();

            // 更新页面元数据
            updatePageMetadata(categoryPageState.categoryName);

            // 加载组件
            await loadPageComponents();

            // 直接加载页面数据，响应时间 < 5ms
            await loadPageData();

            // 初始化事件监听器
            initEventListeners();

            console.log('✅ OptimizedNavigation: Category page loaded with direct ID query');
            return;
        }

        // 如果有参数传入，使用传入的参数；否则从URL解析
        let categorySlug, categoryName;

        if (params && params.categorySlug && params.categoryName) {
            // 使用PageRouter传递的参数
            categorySlug = params.categorySlug;
            categoryName = params.categoryName;
            console.log('Category page: Using parameters from PageRouter', params);
        } else {
            // 向后兼容：从URL获取类别slug（使用新的解析方法）
            categorySlug = UrlHandler.parseCategoryFromPath();
            console.log('Category slug from URL:', categorySlug);
            if (!categorySlug) {
                showErrorMessage('Category not found. Please check the URL.');
                return;
            }
            // 将slug转换为可读的类别名称
            categoryName = UrlHandler.deslugify(categorySlug);
            console.log('Category page: Parsed from URL - slug:', categorySlug, 'name:', categoryName);
        }

        // 设置页面状态
        categoryPageState.categoryName = categoryName;
        categoryPageState.categorySlug = categorySlug;

        // 获取页码参数
        const pageParam = UrlHandler.getQueryParam('page');
        if (pageParam) {
            categoryPageState.currentPage = parseInt(pageParam) || 1;
        }

        // 更新页面标题和描述
        updatePageMetadata(categoryName);

        // 加载组件
        await loadPageComponents();

        // 使用统一的EntityIdMapper系统进行优先级查找
        console.log('🚀 Using EntityIdMapper for category lookup:', categoryName);
        try {
            const category = await window.findEntityWithPriority(
                'categories',
                categorySlug,
                categoryName,
                window.ApiClient.getCategoryByName.bind(window.ApiClient)
            );

            if (!category) {
                showErrorMessage(`Category "${categoryName}" not found.`);
                return;
            }

            categoryPageState.categoryId = category.id;
            categoryPageState.categoryName = category.name;
            console.log('✅ Category ID set to:', categoryPageState.categoryId);
            console.log('✅ Category name set to:', categoryPageState.categoryName);

            // 更新页面标题和元数据，使用API返回的正确名称
            updatePageMetadata(category.name);
            updatePageTitle(category.name);
        } catch (categoryError) {
            console.error('Error getting category by name:', categoryError);
            showErrorMessage(`Failed to get category "${categoryName}". Please try refreshing.`);
            return;
        }

        // 加载数据
        await loadPageData();

        // 初始化事件监听器
        initEventListeners();

        // 触发页面初始化完成事件
        const event = new CustomEvent('pageInitialized', {
            detail: {
                page: 'category',
                optimized: true, // 标记为已使用EntityIdMapper优化
                categoryId: categoryPageState.categoryId,
                categoryName: categoryPageState.categoryName
            }
        });
        window.dispatchEvent(event);

        console.log('✅ Category page initialization completed');
    } catch (error) {
        console.error('Error initializing category page:', error);
        showErrorMessage('Failed to initialize page. Please try refreshing.');

        // 触发页面初始化失败事件
        const errorEvent = new CustomEvent('pageInitializationFailed', {
            detail: {
                page: 'category',
                error: error.message
            }
        });
        window.dispatchEvent(errorEvent);
    }
}

/**
 * 更新页面元数据
 * @param {string} categoryName - 类别名称
 */
function updatePageMetadata(categoryName) {
    // 使用新的SEO元数据模板
    if (window.SEOManager) {
        const seoData = {
            category_name: categoryName,
            quote_count: categoryPageState.totalQuotes,
            category_context: 'daily life', // 可以根据类别动态设置
            canonical_url: UrlHandler.getCanonicalUrl(),
            category_page_url: UrlHandler.getCategoryUrl({ name: categoryName })
        };

        window.SEOManager.updatePageSEO('categoryDetail', seoData);
    } else {
        // 向后兼容的元数据更新 - 使用动态类别名称
        document.title = `${categoryName} Quotes | Wisdom Collection - quotese.com`;

        // 更新meta描述
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
            metaDescription.content = `Best ${categoryName} quotes collection on Quotese.com. Discover inspiring ${categoryName} quotes from famous authors and thinkers. Find motivation and wisdom for your daily life.`;
        }

        // 更新canonical链接
        const canonicalLink = document.querySelector('link[rel="canonical"]');
        if (canonicalLink) {
            canonicalLink.href = UrlHandler.getCanonicalUrl();
        }
    }

    // 添加类别结构化数据
    addCategoryStructuredData(categoryName, categoryPageState.categoryId);
}

/**
 * 更新页面标题
 * @param {string} categoryName - 类别名称
 */
function updatePageTitle(categoryName) {
    const titleElement = document.getElementById('category-name');
    if (titleElement) {
        titleElement.textContent = categoryName;
    }

    const inlineTitleElement = document.getElementById('category-name-inline');
    if (inlineTitleElement) {
        inlineTitleElement.textContent = categoryName;
    }
}

/**
 * 加载页面组件
 */
async function loadPageComponents() {
    try {
        console.log('Loading page components...');

        // 使用初始化检查器等待ComponentLoader
        if (!window.ComponentLoader) {
            console.log('[INFO] ComponentLoader not immediately available, using init checker...');

            // 如果有初始化检查器，使用它
            if (window.ComponentLoaderInitChecker) {
                const success = await window.ComponentLoaderInitChecker.wait(15000);
                if (!success) {
                    throw new Error('ComponentLoader initialization failed via init checker');
                }
            } else {
                // 回退到原有的等待机制
                console.log('[FALLBACK] Using fallback wait mechanism...');
                let attempts = 0;
                const maxAttempts = 150; // 15秒

                while (!window.ComponentLoader && attempts < maxAttempts) {
                    if (attempts % 20 === 0) {
                        console.log(`[INFO] Waiting for ComponentLoader... Attempt ${attempts}/${maxAttempts}`);
                    }
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }

                if (!window.ComponentLoader) {
                    console.error('[ERROR] ComponentLoader failed to initialize after 15 seconds');
                    console.error('[ERROR] Available window properties:', Object.keys(window).filter(key => key.includes('Component')));
                    throw new Error('ComponentLoader failed to initialize after 15 seconds');
                }
            }
        }

        console.log('[SUCCESS] ComponentLoader is available, proceeding with component loading...');

        // 加载面包屑组件
        console.log('Loading breadcrumb component...');
        await window.ComponentLoader.loadComponent('breadcrumb-container', 'breadcrumb');

        // 初始化面包屑导航（使用正确的容器ID）
        // 等待DOM更新后再初始化面包屑
        setTimeout(() => {
            try {
                BreadcrumbComponent.autoInit('breadcrumb-container');
                console.log('✅ Breadcrumb component initialized successfully');
            } catch (error) {
                console.error('❌ Failed to initialize breadcrumb component:', error);
                // 如果自动初始化失败，尝试手动创建面包屑
                createFallbackBreadcrumb();
            }
        }, 100);

        // 首先加载热门主题组件
        console.log('Loading popular-topics component...');
        const popularTopicsLoaded = await window.ComponentLoader.loadComponent('popular-topics-container', 'popular-topics');
        console.log('popular-topics component loaded:', popularTopicsLoaded);

        // 等待一小段时间，确保 DOM 元素完全渲染
        await new Promise(resolve => setTimeout(resolve, 100));

        // 确保热门主题组件已加载
        if (!document.getElementById('categories-container') ||
            !document.getElementById('authors-container') ||
            !document.getElementById('sources-container')) {
            console.warn('Popular topics containers not found, trying to load again...');
            // 尝试再次加载热门主题组件
            await window.ComponentLoader.loadComponent('popular-topics-container', 'popular-topics', {}, true);
            console.log('Popular topics component reloaded');

            // 再次等待一小段时间
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // 更新quotes-list标题
        const quotesListTitle = document.getElementById('quotes-list-title');
        if (quotesListTitle) {
            quotesListTitle.textContent = `Quotes about ${categoryPageState.categoryName}`;
            console.log('Updated quotes-list title to:', `Quotes about ${categoryPageState.categoryName}`);
        }

        // 加载分页组件
        console.log('Loading pagination component...');
        const paginationLoaded = await window.ComponentLoader.loadComponent('pagination-container', 'pagination');
        console.log('pagination component loaded:', paginationLoaded);

        // 直接加载右侧数据
        console.log('Loading side data directly...');
        try {
            // 并行加载数据
            const [categoriesData, authorsData, sourcesData] = await Promise.all([
                loadCategories(),
                loadAuthors(),
                loadSources()
            ]);
            console.log('Side data loaded successfully:', {
                categories: categoriesData ? categoriesData.categories : [],
                authors: authorsData ? authorsData.authors : [],
                sources: sourcesData ? sourcesData.sources : []
            });
        } catch (sideDataError) {
            console.error('Error loading side data directly:', sideDataError);
            // 尝试单独加载每个数据
            loadCategories().catch(err => console.error('Error loading categories directly:', err));
            loadAuthors().catch(err => console.error('Error loading authors directly:', err));
            loadSources().catch(err => console.error('Error loading sources directly:', err));
        }

        console.log('All components loaded successfully.');
    } catch (error) {
        console.error('Error loading page components:', error);
    }
}

/**
 * 加载页面数据
 */
async function loadPageData() {
    // 显示加载状态
    showLoadingState();

    try {
        // 先加载名言数据
        console.log('Loading quotes data with categoryId:', categoryPageState.categoryId);
        const quotesData = await loadQuotes(categoryPageState.currentPage, categoryPageState.pageSize);
        console.log('Quotes data loaded:', quotesData);

        // 更新页面状态
        updatePageState(quotesData);

        // 更新引用计数
        updateQuoteCount(quotesData.totalCount);

        // 同时保持原有的引用计数更新（虽然已隐藏）
        const quoteCountElement = document.getElementById('quote-count');
        if (quoteCountElement) {
            quoteCountElement.textContent = quotesData.totalCount;
            console.log('Updated quote count directly to:', quotesData.totalCount);
        } else {
            console.error('Quote count element not found in direct update!');
            // 尝试使用document.querySelector
            const countElement = document.querySelector('#quote-count');
            if (countElement) {
                countElement.textContent = quotesData.totalCount;
                console.log('Updated quote count using querySelector to:', quotesData.totalCount);
            } else {
                console.error('Quote count element not found using querySelector either!');
            }
        }

        // 检查右侧容器是否存在
        let categoriesContainer = document.getElementById('categories-container');
        let authorsContainer = document.getElementById('authors-container');
        let sourcesContainer = document.getElementById('sources-container');

        // 如果右侧容器不存在，尝试重新加载组件
        if (!categoriesContainer || !authorsContainer || !sourcesContainer) {
            console.warn('Side containers not found, trying to reload popular-topics component...');
            await window.ComponentLoader.loadComponent('popular-topics-container', 'popular-topics');

            // 等待一小段时间，确保 DOM 元素完全渲染
            await new Promise(resolve => setTimeout(resolve, 100));

            // 重新获取容器
            categoriesContainer = document.getElementById('categories-container');
            authorsContainer = document.getElementById('authors-container');
            sourcesContainer = document.getElementById('sources-container');

            console.log('Containers after reload:', {
                categoriesContainer: !!categoriesContainer,
                authorsContainer: !!authorsContainer,
                sourcesContainer: !!sourcesContainer
            });
        }

        // 如果右侧容器存在但为空，或者刚刚重新加载了组件，则加载数据
        if (categoriesContainer || authorsContainer || sourcesContainer) {
            console.log('Loading side data...');
            try {
                // 并行加载数据
                const [categoriesData, authorsData, sourcesData] = await Promise.all([
                    loadCategories(),
                    loadAuthors(),
                    loadSources()
                ]);
                console.log('Side data loaded successfully from loadPageData:', {
                    categories: categoriesData ? categoriesData.categories : [],
                    authors: authorsData ? authorsData.authors : [],
                    sources: sourcesData ? sourcesData.sources : []
                });
            } catch (sideDataError) {
                console.error('Error loading side data from loadPageData:', sideDataError);
                // 尝试单独加载每个数据
                loadCategories().catch(err => console.error('Error loading categories from loadPageData:', err));
                loadAuthors().catch(err => console.error('Error loading authors from loadPageData:', err));
                loadSources().catch(err => console.error('Error loading sources from loadPageData:', err));
            }
        } else {
            console.warn('Side containers still not found after reload attempt');
        }

        // 隐藏加载状态
        hideLoadingState();

    } catch (error) {
        console.error('Error loading page data:', error);
        showErrorMessage('Failed to load data. Please try again later.');
        hideLoadingState();
    }
}

/**
 * 加载名言列表
 * @param {number} page - 页码
 * @param {number} pageSize - 每页数量
 * @returns {Promise<Object>} - 名言列表和分页信息
 */
async function loadQuotes(page, pageSize) {
    try {
        // 获取名言列表
        const filters = { categoryId: categoryPageState.categoryId };
        console.log('Loading quotes with filters:', filters);
        const quotesData = await window.ApiClient.getQuotes(page, pageSize, filters);
        console.log('Quotes data received:', quotesData);

        // 检查quotes-list元素是否存在
        const quotesListElement = document.getElementById('quotes-list');
        console.log('quotes-list element:', quotesListElement);

        if (!quotesListElement) {
            console.error('quotes-list element not found, trying to load quotes-list component again');
            // 尝试再次加载名言列表组件
            await window.ComponentLoader.loadComponent('quotes-container', 'quotes-list', {
                title: `Quotes about ${categoryPageState.categoryName}`
            });
        }

        // 渲染名言列表
        console.log('Rendering quotes:', quotesData.quotes);
        renderQuotes(quotesData.quotes);

        // 更新分页
        updatePagination(quotesData);

        return quotesData;
    } catch (error) {
        console.error('Error loading quotes:', error);
        throw error;
    }
}

/**
 * 加载类别列表
 * @returns {Promise<Object>} - 类别列表
 */
async function loadCategories() {
    try {
        console.log('Loading popular categories...');
        // 强制使用真实API数据
        window.ApiClient.useMockData = false;

        // 获取热门类别列表（top 100）
        const popularCategories = await window.ApiClient.getPopularCategories(100);
        console.log('Popular categories loaded:', popularCategories);

        if (!popularCategories || popularCategories.length === 0) {
            console.error('No popular categories returned from API');
            return { categories: [], totalCount: 0 };
        }

        // 从前100个类别中随机选取20个
        const randomCategories = getRandomItems(popularCategories, 20);

        // 缓存热门类别数据到优化导航系统
        if (window.cachePopularEntities) {
            window.cachePopularEntities('category', popularCategories);
        }

        // 渲染类别列表
        renderCategories(randomCategories);

        console.log('Random 20 categories from top 100:', randomCategories);

        return {
            categories: randomCategories,
            totalCount: popularCategories.length
        };
    } catch (error) {
        console.error('Error loading categories:', error);
        return { categories: [], totalCount: 0 };
    }
}

/**
 * 加载作者列表
 * @returns {Promise<Object>} - 作者列表
 */
async function loadAuthors() {
    try {
        console.log('Loading popular authors...');
        // 强制使用真实API数据
        window.ApiClient.useMockData = false;

        // 获取热门作者列表（top 100）
        const popularAuthors = await window.ApiClient.getPopularAuthors(100);
        console.log('Popular authors loaded:', popularAuthors);

        if (!popularAuthors || popularAuthors.length === 0) {
            console.error('No popular authors returned from API');
            return { authors: [], totalCount: 0 };
        }

        // 从前100个作者中随机选取10个
        const randomAuthors = getRandomItems(popularAuthors, 10);

        // 缓存热门作者数据到优化导航系统
        if (window.cachePopularEntities) {
            window.cachePopularEntities('author', popularAuthors);
        }

        // 渲染作者列表
        renderAuthors(randomAuthors);

        console.log('Random 10 authors from top 100:', randomAuthors);

        return {
            authors: randomAuthors,
            totalCount: popularAuthors.length
        };
    } catch (error) {
        console.error('Error loading authors:', error);
        return { authors: [], totalCount: 0 };
    }
}

/**
 * 加载来源列表
 * @returns {Promise<Object>} - 来源列表
 */
async function loadSources() {
    try {
        console.log('Loading popular sources...');
        // 强制使用真实API数据
        window.ApiClient.useMockData = false;

        // 获取热门来源列表（top 100）
        const popularSources = await window.ApiClient.getPopularSources(100);
        console.log('Popular sources loaded:', popularSources);

        if (!popularSources || popularSources.length === 0) {
            console.error('No popular sources returned from API');
            return { sources: [], totalCount: 0 };
        }

        // 从前100个来源中随机选取10个
        const randomSources = getRandomItems(popularSources, 10);

        // 缓存热门来源数据到优化导航系统
        if (window.cachePopularEntities) {
            window.cachePopularEntities('source', popularSources);
        }

        // 渲染来源列表
        renderSources(randomSources);

        console.log('Random 10 sources from top 100:', randomSources);

        return {
            sources: randomSources,
            totalCount: popularSources.length
        };
    } catch (error) {
        console.error('Error loading sources:', error);
        return { sources: [], totalCount: 0 };
    }
}

/**
 * 渲染名言列表
 * @param {Array} quotes - 名言列表
 */
function renderQuotes(quotes) {
    console.log('Rendering quotes:', quotes);

    // 使用通用的名言卡片组件渲染名言列表
    window.QuoteCardComponent.renderList(quotes, 'quotes-list', {
        showAuthorAvatar: false, // 隐藏作者头像，与首页保持一致
        showActions: false, // 隐藏分享和跳转按钮，与首页保持一致
        highlightCurrentCategory: true,
        currentCategoryName: categoryPageState.categoryName,
        emptyMessage: `No quotes found for "${categoryPageState.categoryName}".`
    });

    console.log(`Rendered ${quotes ? quotes.length : 0} quotes using QuoteCardComponent`);
}

/**
 * 渲染类别列表
 * @param {Array} categories - 类别列表
 */
function renderCategories(categories) {
    const categoriesContainer = document.getElementById('categories-container');
    if (!categoriesContainer) {
        console.error('Categories container not found in renderCategories');
        return;
    }
    console.log('Rendering categories to container:', categoriesContainer);

    // 清空容器
    categoriesContainer.innerHTML = '';

    if (categories.length === 0) {
        categoriesContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400">No categories found.</p>';
        return;
    }

    // 类别是从前100个中随机选取的
    // 这里不需要排序

    // 创建类别标签
    categories.forEach(category => {
        // 确保类别有count属性
        const count = typeof category.count === 'number' ? category.count : 0;

        const categoryTag = document.createElement('a');
        categoryTag.href = window.UrlHandler.getCategoryUrl(category);

        // 当前类别高亮显示
        if (category.name === categoryPageState.categoryName) {
            categoryTag.className = 'tag px-3 py-1.5 text-sm rounded-lg bg-yellow-300 text-yellow-900 dark:bg-yellow-700 dark:text-yellow-100 font-semibold transition-colors duration-300';
        } else {
            categoryTag.className = 'tag px-3 py-1.5 text-sm rounded-lg bg-yellow-50 text-yellow-800 dark:bg-gray-800 dark:text-yellow-300 hover:bg-yellow-100 dark:hover:bg-gray-700 transition-colors duration-300';
        }

        categoryTag.textContent = `${category.name} (${count})`;

        // 添加优化的点击事件处理 - 直接传递实体ID
        categoryTag.addEventListener('click', function(e) {
            e.preventDefault();
            console.log(`🚀 Optimized navigation: Category "${category.name}" with ID ${category.id}`);

            // 使用优化的导航方法，直接传递实体ID
            window.navigateToEntityWithId('category', category, categoryTag.href);
        });

        categoriesContainer.appendChild(categoryTag);
    });
}

/**
 * 渲染作者列表
 * @param {Array} authors - 作者列表
 */
function renderAuthors(authors) {
    const authorsContainer = document.getElementById('authors-container');
    if (!authorsContainer) {
        console.error('Authors container not found in renderAuthors');
        return;
    }
    console.log('Rendering authors to container:', authorsContainer);

    // 清空容器
    authorsContainer.innerHTML = '';

    if (authors.length === 0) {
        authorsContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400">No authors found.</p>';
        return;
    }

    // 作者是从前100个中随机选取的
    const randomAuthors = authors;

    // 创建作者列表项
    randomAuthors.forEach(author => {
        const maxCount = randomAuthors[0].count;
        const percentage = Math.round((author.count / maxCount) * 100);

        const authorItem = document.createElement('li');
        authorItem.className = 'flex justify-between items-start py-1.5 px-2 rounded-md transition-colors duration-300';

        const authorUrl = window.UrlHandler.getAuthorUrl(author);
        authorItem.innerHTML = `
            <div class="flex items-start space-x-2 w-full">
                <div class="flex-grow pr-2">
                    <a href="${authorUrl}" class="author-link hover:text-yellow-600 dark:hover:text-yellow-400 font-medium transition-colors duration-300" title="${author.name}" data-author-id="${author.id}" data-author-name="${author.name}">${author.name}</a>
                </div>
                <span class="text-sm text-gray-500 dark:text-gray-400 ml-2 mt-0.5 flex-shrink-0">${author.count}</span>
            </div>
        `;

        // 添加优化的点击事件处理
        const authorLink = authorItem.querySelector('.author-link');
        if (authorLink) {
            authorLink.addEventListener('click', function(e) {
                e.preventDefault();
                console.log(`🚀 Optimized navigation: Author "${author.name}" with ID ${author.id}`);

                // 使用优化的导航方法，直接传递实体ID
                window.navigateToEntityWithId('author', author, authorUrl);
            });
        }

        authorsContainer.appendChild(authorItem);
    });
}

/**
 * 渲染来源列表
 * @param {Array} sources - 来源列表
 */
function renderSources(sources) {
    const sourcesContainer = document.getElementById('sources-container');
    if (!sourcesContainer) {
        console.error('Sources container not found in renderSources');
        return;
    }
    console.log('Rendering sources to container:', sourcesContainer);

    // 清空容器
    sourcesContainer.innerHTML = '';

    if (sources.length === 0) {
        sourcesContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400">No sources found.</p>';
        return;
    }

    // 来源是从前100个中随机选取的
    const randomSources = sources;

    // 创建来源列表项
    randomSources.forEach(source => {
        const sourceItem = document.createElement('li');
        sourceItem.className = 'flex justify-between items-start py-1.5 px-2 rounded-md transition-colors duration-300';

        const sourceUrl = window.UrlHandler.getSourceUrl(source);
        sourceItem.innerHTML = `
            <div class="flex items-start space-x-2 w-full">
                <div class="flex-grow pr-2">
                    <a href="${sourceUrl}" class="source-link hover:text-yellow-600 dark:hover:text-yellow-400 font-medium transition-colors duration-300" title="${source.name}" data-source-id="${source.id}" data-source-name="${source.name}">${source.name}</a>
                </div>
                <span class="text-sm text-gray-500 dark:text-gray-400 ml-2 mt-0.5 flex-shrink-0">${source.count}</span>
            </div>
        `;

        // 添加优化的点击事件处理
        const sourceLink = sourceItem.querySelector('.source-link');
        if (sourceLink) {
            sourceLink.addEventListener('click', function(e) {
                e.preventDefault();
                console.log(`🚀 Optimized navigation: Source "${source.name}" with ID ${source.id}`);

                // 使用优化的导航方法，直接传递实体ID
                window.navigateToEntityWithId('source', source, sourceUrl);
            });
        }

        sourcesContainer.appendChild(sourceItem);
    });
}

/**
 * 更新分页
 * @param {Object} quotesData - 名言数据和分页信息
 */
function updatePagination(quotesData) {
    // 初始化分页组件
    if (!window.paginationComponent) {
        window.paginationComponent = new window.PaginationComponent({
            containerId: 'pagination-container',
            onPageChange: goToPage,
            showInfo: false // 不显示分页信息
        });
    }

    // 更新分页组件
    window.paginationComponent.update({
        currentPage: quotesData.currentPage,
        totalPages: quotesData.totalPages,
        totalItems: quotesData.totalCount,
        pageSize: quotesData.pageSize
    });
}

/**
 * 更新引用计数
 * @param {number} count - 引用数量
 */
function updateQuoteCount(count) {
    console.log('Updating quote count to:', count);

    // 更新左侧名言列表区域顶部标题的右侧显示引用计数
    const quotesCountDisplay = document.getElementById('quotes-count-display');
    if (quotesCountDisplay) {
        quotesCountDisplay.textContent = `(${count})`;
        console.log('Updated quotes-count-display to:', `(${count} quotes)`);
    } else {
        console.error('quotes-count-display element not found');

        // 尝试在标题旁边创建计数元素
        const quotesListTitle = document.getElementById('quotes-list-title');
        if (quotesListTitle && quotesListTitle.parentElement) {
            const countSpan = document.createElement('span');
            countSpan.id = 'quotes-count-display';
            countSpan.className = 'ml-2 text-sm text-gray-500 dark:text-gray-400 font-normal';
            countSpan.textContent = `(${count})`;
            quotesListTitle.parentElement.appendChild(countSpan);
            console.log('Created and appended quotes-count-display element');
        }
    }

    // 更新标题文本，确保标题正确显示
    const quotesListTitle = document.getElementById('quotes-list-title');
    if (quotesListTitle) {
        quotesListTitle.textContent = `Quotes about ${categoryPageState.categoryName}`;
        console.log('Updated quotes-list-title to:', `Quotes about ${categoryPageState.categoryName}`);
    } else {
        console.error('quotes-list-title element not found');
    }

    // 同时保持原有的引用计数更新（虽然已隐藏）
    let quoteCountElement = document.getElementById('quote-count');

    if (!quoteCountElement) {
        quoteCountElement = document.querySelector('#quote-count');
    }

    if (!quoteCountElement) {
        quoteCountElement = document.querySelector('span[id="quote-count"]');
    }

    console.log('Quote count element:', quoteCountElement);

    if (quoteCountElement) {
        // 直接设置内容
        quoteCountElement.textContent = count;
        console.log('Updated quote count to:', count);

        // 尝试使用innerHTML
        quoteCountElement.innerHTML = count;

        // 尝试使用父元素
        const parentElement = quoteCountElement.parentElement;
        if (parentElement) {
            parentElement.innerHTML = `<span id="quote-count">${count}</span> quotes in this category`;
            console.log('Updated parent element');
        }
    } else {
        console.error('Quote count element not found!');
    }
}

/**
 * 跳转到指定页码
 * @param {number} page - 页码
 */
async function goToPage(page) {
    if (page === categoryPageState.currentPage || categoryPageState.isLoading) return;

    categoryPageState.currentPage = page;

    // 更新URL参数
    window.UrlHandler.updateQueryParam('page', page);

    // 重新加载数据
    await loadQuotes(page, categoryPageState.pageSize);

    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

/**
 * 更新页面状态
 * @param {Object} quotesData - 名言数据和分页信息
 */
function updatePageState(quotesData) {
    categoryPageState.currentPage = quotesData.currentPage;
    categoryPageState.totalPages = quotesData.totalPages;
    categoryPageState.totalQuotes = quotesData.totalCount;
}

/**
 * 显示加载状态
 */
function showLoadingState() {
    categoryPageState.isLoading = true;

    // 显示名言列表加载状态
    const quotesContainer = document.getElementById('quotes-list');
    if (quotesContainer) {
        quotesContainer.innerHTML = `
            <div class="flex justify-center py-12">
                <div class="loading-spinner" role="status">
                    <span class="sr-only">Loading quotes...</span>
                </div>
            </div>
        `;
    }
}

/**
 * 隐藏加载状态
 */
function hideLoadingState() {
    categoryPageState.isLoading = false;
}

/**
 * 显示错误消息
 * @param {string} message - 错误消息
 */
function showErrorMessage(message) {
    const quotesContainer = document.getElementById('quotes-list');
    if (quotesContainer) {
        quotesContainer.innerHTML = `
            <div class="bg-red-100 text-red-800 p-4 rounded-md">
                <i class="fas fa-exclamation-circle mr-2"></i>
                ${message}
            </div>
        `;
    }
}

/**
 * 初始化事件监听器
 */
function initEventListeners() {
    // 可以添加页面特定的事件监听器
}

/**
 * 从数组中随机选取指定数量的元素
 * @param {Array} items - 原始数组
 * @param {number} count - 需要选取的元素数量
 * @returns {Array} - 随机选取的元素数组
 */
function getRandomItems(items, count) {
    // 如果原始数组长度小于等于需要选取的数量，直接返回原始数组
    if (!items || items.length <= count) {
        return items || [];
    }

    // 复制原始数组，避免修改原始数组
    const itemsCopy = [...items];
    const result = [];

    // 随机选取指定数量的元素
    for (let i = 0; i < count; i++) {
        // 生成一个随机索引
        const randomIndex = Math.floor(Math.random() * itemsCopy.length);
        // 将随机选取的元素添加到结果数组
        result.push(itemsCopy[randomIndex]);
        // 从原始数组中移除已选取的元素，避免重复选取
        itemsCopy.splice(randomIndex, 1);
    }

    return result;
}

// 将函数暴露给全局作用域
window.loadCategories = loadCategories;
window.loadAuthors = loadAuthors;
window.loadSources = loadSources;
window.renderCategories = renderCategories;
window.renderAuthors = renderAuthors;
window.renderSources = renderSources;

/**
 * 添加类别结构化数据
 * @param {string} categoryName - 类别名称
 * @param {string|number} categoryId - 类别ID
 */
function addCategoryStructuredData(categoryName, categoryId) {
    // 移除旧的结构化数据
    const oldScript = document.getElementById('category-structured-data');
    if (oldScript) {
        oldScript.remove();
    }

    // 创建结构化数据
    const structuredData = {
        "@context": "https://schema.org",
        "@type": "ItemList",
        "name": `Quotes about ${categoryName}`,
        "description": `A collection of quotes about ${categoryName} from famous authors and sources.`,
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": `https://quotese.com/categories/${window.UrlHandler.slugify(categoryName)}-${categoryId}.html`
        }
    };

    // 如果有引用数量，添加到结构化数据中
    if (categoryPageState.totalQuotes > 0) {
        structuredData.numberOfItems = categoryPageState.totalQuotes;
    }

    // 添加到页面
    const script = document.createElement('script');
    script.id = 'category-structured-data';
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);
}

/**
 * 创建后备面包屑导航（当自动初始化失败时）
 */
function createFallbackBreadcrumb() {
    const container = document.getElementById('breadcrumb-container');
    if (!container) {
        console.warn('Breadcrumb container not found');
        return;
    }

    const categoryName = categoryPageState.categoryName || 'Category';

    const breadcrumbHTML = `
        <nav class="breadcrumb-nav mb-6" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <li>
                    <a href="/" class="hover:text-purple-600 dark:hover:text-purple-400 transition-colors">
                        <i class="fas fa-home mr-1"></i>Home
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right mx-2 text-gray-400"></i>
                    <a href="/categories/" class="hover:text-purple-600 dark:hover:text-purple-400 transition-colors">
                        Categories
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right mx-2 text-gray-400"></i>
                    <span class="text-gray-900 dark:text-gray-100 font-medium">${categoryName}</span>
                </li>
            </ol>
        </nav>
    `;

    container.innerHTML = breadcrumbHTML;
    console.log('✅ Fallback breadcrumb created successfully');
}

// 页面初始化现在由PageRouter负责
// 保留此函数供PageRouter调用
