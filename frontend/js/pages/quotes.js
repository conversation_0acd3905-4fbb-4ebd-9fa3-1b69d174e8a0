/**
 * 名言列表页面脚本
 * 处理名言列表的显示、搜索、过滤和分页功能
 */

// 页面状态管理
let currentPage = 1;
let currentFilters = {
    search: '',
    category: '',
    author: '',
    sortBy: 'newest'
};
let isLoading = false;
let totalPages = 1;

/**
 * 页面初始化
 */
async function initQuotesPage() {
    console.log('🚀 初始化名言列表页面...');
    
    try {
        // 切换到生产API
        if (window.QuoteseAPIMode && typeof window.QuoteseAPIMode.useProductionAPI === 'function') {
            window.QuoteseAPIMode.useProductionAPI();
            console.log('✅ 已切换到生产API');
        }
        
        // 初始化事件监听器
        initEventListeners();
        
        // 加载初始数据
        await loadQuotes();
        
        // 更新页面元数据
        updatePageMetadata();
        
        console.log('✅ 名言列表页面初始化完成');
        
    } catch (error) {
        console.error('❌ 名言列表页面初始化失败:', error);
        showErrorMessage('页面初始化失败，请刷新页面重试。');
    }
}

/**
 * 初始化事件监听器
 */
function initEventListeners() {
    // 搜索输入框
    const searchInput = document.getElementById('quote-search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                currentFilters.search = e.target.value.trim();
                currentPage = 1;
                loadQuotes();
            }, 500);
        });
    }
    
    // 分类过滤器
    const categoryFilter = document.getElementById('category-filter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', (e) => {
            currentFilters.category = e.target.value;
            currentPage = 1;
            loadQuotes();
        });
    }
    
    // 排序选择器
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', (e) => {
            currentFilters.sortBy = e.target.value;
            currentPage = 1;
            loadQuotes();
        });
    }
    
    // 分页按钮
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('pagination-btn')) {
            const page = parseInt(e.target.dataset.page);
            if (page && page !== currentPage) {
                currentPage = page;
                loadQuotes();
            }
        }
    });
}

/**
 * 加载名言数据
 */
async function loadQuotes() {
    if (isLoading) return;
    
    isLoading = true;
    showLoadingState();
    
    try {
        // 构建查询参数
        const params = {
            page: currentPage,
            pageSize: 12,
            ...currentFilters
        };
        
        // 移除空值
        Object.keys(params).forEach(key => {
            if (!params[key]) delete params[key];
        });
        
        console.log('📡 加载名言数据:', params);
        
        // 调用API
        const quotesData = await window.ApiClient.getQuotes(
            params.page, 
            params.pageSize, 
            params
        );
        
        if (quotesData && quotesData.quotes) {
            console.log(`✅ 成功加载 ${quotesData.quotes.length} 条名言`);
            
            // 渲染名言列表
            renderQuotesList(quotesData.quotes);
            
            // 更新分页
            totalPages = quotesData.totalPages || 1;
            renderPagination(quotesData.totalPages, quotesData.totalCount);
            
            // 更新结果统计
            updateResultsStats(quotesData.totalCount);
            
        } else {
            console.warn('⚠️ 未获取到名言数据');
            showEmptyState();
        }
        
    } catch (error) {
        console.error('❌ 加载名言数据失败:', error);
        showErrorMessage('加载名言数据失败，请稍后重试。');
    } finally {
        isLoading = false;
        hideLoadingState();
    }
}

/**
 * 渲染名言列表
 */
function renderQuotesList(quotes) {
    const container = document.getElementById('quotes-list-container');
    if (!container) {
        console.error('❌ 未找到名言列表容器');
        return;
    }
    
    if (!quotes || quotes.length === 0) {
        showEmptyState();
        return;
    }
    
    // 使用QuoteCardComponent渲染名言卡片
    const quotesHTML = QuoteCardComponent.renderList(quotes, {
        showActions: true,
        showAuthorAvatar: true,
        containerClass: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
    });
    
    container.innerHTML = quotesHTML;
    
    // 添加动画效果
    const cards = container.querySelectorAll('.quote-card-component');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
}

/**
 * 渲染分页组件
 */
function renderPagination(totalPages, totalCount) {
    const container = document.getElementById('pagination-container');
    if (!container || totalPages <= 1) {
        if (container) container.innerHTML = '';
        return;
    }
    
    let paginationHTML = '<div class="flex justify-center items-center space-x-2 mt-8">';
    
    // 上一页按钮
    if (currentPage > 1) {
        paginationHTML += `
            <button class="pagination-btn px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50" 
                    data-page="${currentPage - 1}">
                <i class="fas fa-chevron-left"></i> 上一页
            </button>
        `;
    }
    
    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === currentPage;
        paginationHTML += `
            <button class="pagination-btn px-4 py-2 border rounded-lg ${
                isActive 
                    ? 'bg-red-500 text-white border-red-500' 
                    : 'bg-white border-gray-300 hover:bg-gray-50'
            }" data-page="${i}">
                ${i}
            </button>
        `;
    }
    
    // 下一页按钮
    if (currentPage < totalPages) {
        paginationHTML += `
            <button class="pagination-btn px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50" 
                    data-page="${currentPage + 1}">
                下一页 <i class="fas fa-chevron-right"></i>
            </button>
        `;
    }
    
    paginationHTML += '</div>';
    container.innerHTML = paginationHTML;
}

/**
 * 更新结果统计
 */
function updateResultsStats(totalCount) {
    const statsElement = document.getElementById('results-stats');
    if (statsElement && totalCount !== undefined) {
        const startIndex = (currentPage - 1) * 12 + 1;
        const endIndex = Math.min(currentPage * 12, totalCount);
        
        statsElement.textContent = `显示第 ${startIndex}-${endIndex} 条，共 ${totalCount} 条名言`;
    }
}

/**
 * 显示加载状态
 */
function showLoadingState() {
    const container = document.getElementById('quotes-list-container');
    if (container) {
        container.innerHTML = `
            <div class="col-span-full flex justify-center items-center py-12">
                <div class="loading-spinner"></div>
                <span class="ml-3 text-gray-600">正在加载名言...</span>
            </div>
        `;
    }
}

/**
 * 隐藏加载状态
 */
function hideLoadingState() {
    // 加载状态会被实际内容替换，无需特殊处理
}

/**
 * 显示空状态
 */
function showEmptyState() {
    const container = document.getElementById('quotes-list-container');
    if (container) {
        container.innerHTML = `
            <div class="col-span-full text-center py-12">
                <i class="fas fa-quote-left text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无名言</h3>
                <p class="text-gray-500">请尝试调整搜索条件或过滤器</p>
            </div>
        `;
    }
}

/**
 * 显示错误消息
 */
function showErrorMessage(message) {
    const container = document.getElementById('quotes-list-container');
    if (container) {
        container.innerHTML = `
            <div class="col-span-full text-center py-12">
                <i class="fas fa-exclamation-triangle text-6xl text-red-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-red-600 mb-2">加载失败</h3>
                <p class="text-gray-600 mb-4">${message}</p>
                <button onclick="loadQuotes()" class="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">
                    重试
                </button>
            </div>
        `;
    }
}

/**
 * 更新页面元数据
 */
function updatePageMetadata() {
    // 更新页面标题
    document.title = '名言大全 | 经典语录收藏 - Quotese.com';
    
    // 更新面包屑导航
    if (window.BreadcrumbComponent) {
        const breadcrumbData = [
            { name: '首页', url: '/' },
            { name: '名言大全', url: '/quotes/', current: true }
        ];
        window.BreadcrumbComponent.render(breadcrumbData);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 等待其他脚本加载完成
    setTimeout(initQuotesPage, 100);
});

// 导出函数供其他脚本使用
window.QuotesPage = {
    init: initQuotesPage,
    loadQuotes: loadQuotes,
    currentPage: () => currentPage,
    currentFilters: () => currentFilters
};
