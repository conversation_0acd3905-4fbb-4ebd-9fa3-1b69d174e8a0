/**
 * Authors List Page Controller
 * Independent implementation for /authors/ page
 * Uses same APIs as Popular Authors module but with different logic
 */

// Page state - using independent namespace to avoid conflicts
const authorsListPageState = {
    // Data state
    allAuthors: <AUTHORS>
    displayedAuthors: <AUTHORS>
    filteredAuthors: <AUTHORS>
    
    // Pagination state
    currentPage: 1,
    pageSize: 48,  // Grid view: 6x8 or 4x12
    totalPages: 0,
    totalCount: 0,
    
    // UI state
    isLoading: false,
    searchQuery: '',
    sortOrder: 'popularity',
    viewMode: 'grid',
    
    // Performance state
    loadedCount: 0,
    maxAuthors: <AUTHORS>
    hasMore: true
};

/**
 * Page initialization function
 * Called by PageRouter: initAuthorsListPage()
 */
async function initAuthorsListPage(params) {
    try {
        console.log('🚀 Initializing Authors List Page...');
        console.log('📋 Params received:', params);
        console.log('🔍 Available global objects:', {
            ApiClient: !!window.ApiClient,
            ComponentLoader: !!window.ComponentLoader,
            PageRouter: !!window.PageRouter,
            UrlHandler: !!window.UrlHandler
        });

        // Show loading state
        showLoadingState();
        console.log('⏳ Loading state shown');

        // Load page components
        try {
            await loadPageComponents();
            console.log('✅ Page components loaded');
        } catch (componentError) {
            console.warn('⚠️ Component loading failed, continuing anyway:', componentError);
        }

        // Load authors data
        try {
            await loadAuthorsData();
            console.log('✅ Authors data loaded');
        } catch (dataError) {
            console.error('❌ Authors data loading failed:', dataError);
            throw dataError; // This is critical, so we should fail
        }

        // Initialize UI controls
        try {
            initializeControls();
            console.log('✅ UI controls initialized');
        } catch (controlsError) {
            console.warn('⚠️ UI controls initialization failed:', controlsError);
        }

        // Update page metadata
        updatePageMetadata();
        console.log('✅ Page metadata updated');

        // Hide loading state and show content
        hideLoadingState();
        console.log('✅ Authors List Page initialization complete');

    } catch (error) {
        console.error('❌ Authors List Page initialization failed:', error);
        showErrorState(error.message);
        throw error;
    }
}

/**
 * Load page components
 */
async function loadPageComponents() {
    if (window.ComponentLoader) {
        await window.ComponentLoader.loadComponents([
            'navigation',
            'breadcrumb',
            'footer'
        ]);
    }
}

/**
 * Load authors data
 * Uses same API as Popular Authors module but with different logic
 */
async function loadAuthorsData() {
    try {
        console.log('Loading authors data...');

        // Check if ApiClient is available
        if (!window.ApiClient) {
            throw new Error('ApiClient not available');
        }

        console.log('ApiClient available, calling getPopularAuthors(500)...');

        // Use same API as Popular Authors module
        // But get 500 authors instead of 100, and use all data instead of random selection
        let popularAuthors;

        try {
            popularAuthors = await window.ApiClient.getPopularAuthors(500);
            console.log('🎯 API response received:', {
                type: typeof popularAuthors,
                isArray: Array.isArray(popularAuthors),
                length: popularAuthors ? popularAuthors.length : 'N/A',
                firstItem: popularAuthors && popularAuthors.length > 0 ? popularAuthors[0] : 'N/A'
            });

            // Detailed data structure validation
            if (popularAuthors && popularAuthors.length > 0) {
                const sampleAuthor = popularAuthors[0];
                console.log('📊 Sample author structure:', {
                    id: sampleAuthor.id,
                    name: sampleAuthor.name,
                    count: sampleAuthor.count,
                    quotesCount: sampleAuthor.quotesCount,
                    allKeys: Object.keys(sampleAuthor)
                });
            }
        } catch (apiError) {
            console.warn('❌ API call failed, using fallback data:', apiError);
            // Fallback to a smaller number if 500 fails
            try {
                popularAuthors = await window.ApiClient.getPopularAuthors(100);
                console.log('🔄 Fallback API response received:', popularAuthors);
            } catch (fallbackError) {
                console.error('❌ Both API calls failed:', fallbackError);
                // Use mock data as last resort
                popularAuthors = getMockAuthors();
                console.log('🎭 Using mock data as fallback');
            }
        }

        // Validate and process data
        if (!popularAuthors || !Array.isArray(popularAuthors)) {
            throw new Error('Invalid authors data received from API');
        }

        if (popularAuthors.length === 0) {
            console.warn('⚠️ No authors data received, using mock data');
            popularAuthors = getMockAuthors();
        }

        // Store data in page state
        authorsListPageState.allAuthors = popularAuthors;
        authorsListPageState.filteredAuthors = [...popularAuthors];
        authorsListPageState.totalCount = popularAuthors.length;
        authorsListPageState.totalPages = Math.ceil(popularAuthors.length / authorsListPageState.pageSize);

        console.log('📊 Authors data processed:', {
            totalAuthors: <AUTHORS>
            totalPages: authorsListPageState.totalPages,
            pageSize: authorsListPageState.pageSize
        });

        // Apply initial sorting and pagination
        applySorting();
        updatePagination();
        renderAuthors();

    } catch (error) {
        console.error('❌ Failed to load authors data:', error);
        throw error;
    }
}

/**
 * Apply sorting to authors
 */
function applySorting() {
    const { sortOrder, filteredAuthors } = authorsListPageState;
    
    switch (sortOrder) {
        case 'popularity':
            filteredAuthors.sort((a, b) => (b.quotesCount || b.count || 0) - (a.quotesCount || a.count || 0));
            break;
        case 'alphabetical':
            filteredAuthors.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'recent':
            // If we have creation dates, sort by them, otherwise keep current order
            break;
        default:
            // Keep current order
            break;
    }
}

/**
 * Update pagination
 */
function updatePagination() {
    const { currentPage, pageSize, filteredAuthors } = authorsListPageState;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    authorsListPageState.displayedAuthors = filteredAuthors.slice(startIndex, endIndex);
    authorsListPageState.totalPages = Math.ceil(filteredAuthors.length / pageSize);
}

/**
 * Render authors
 */
function renderAuthors() {
    const container = document.getElementById('authors-container');
    if (!container) {
        console.error('❌ Authors container not found');
        return;
    }

    const { displayedAuthors } = authorsListPageState;

    if (displayedAuthors.length === 0) {
        container.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-semibold mb-2">No authors found</h3>
                <p class="text-gray-600">Try adjusting your search or filters</p>
            </div>
        `;
        return;
    }

    const authorsHTML = displayedAuthors.map(author => {
        const authorSlug = window.UrlHandler ? window.UrlHandler.slugify(author.name) : author.name.toLowerCase().replace(/\s+/g, '-');
        const quotesCount = author.quotesCount || author.count || 0;
        
        return `
            <div class="author-card bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user text-2xl text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">
                        <a href="/authors/${authorSlug}/" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                            ${author.name}
                        </a>
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">
                        ${quotesCount} quote${quotesCount !== 1 ? 's' : ''}
                    </p>
                    <a href="/authors/${authorSlug}/" class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium">
                        View Quotes
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            ${authorsHTML}
        </div>
    `;
}

/**
 * Initialize UI controls
 */
function initializeControls() {
    // Search functionality
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }

    // Sort controls
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', handleSortChange);
    }

    // Pagination controls will be added when needed
}

/**
 * Handle search
 */
function handleSearch(event) {
    const query = event.target.value.toLowerCase().trim();
    authorsListPageState.searchQuery = query;
    authorsListPageState.currentPage = 1; // Reset to first page

    if (query === '') {
        authorsListPageState.filteredAuthors = [...authorsListPageState.allAuthors];
    } else {
        authorsListPageState.filteredAuthors = authorsListPageState.allAuthors.filter(author =>
            author.name.toLowerCase().includes(query)
        );
    }

    applySorting();
    updatePagination();
    renderAuthors();
}

/**
 * Handle sort change
 */
function handleSortChange(event) {
    authorsListPageState.sortOrder = event.target.value;
    applySorting();
    updatePagination();
    renderAuthors();
}

/**
 * Show loading state
 */
function showLoadingState() {
    authorsListPageState.isLoading = true;

    const loadingContainer = document.getElementById('loading-container');
    const authorsContainer = document.getElementById('authors-container');
    const errorContainer = document.getElementById('error-container');

    if (loadingContainer) {
        loadingContainer.style.display = 'block';
    }
    if (authorsContainer) {
        authorsContainer.style.display = 'none';
    }
    if (errorContainer) {
        errorContainer.style.display = 'none';
    }
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    authorsListPageState.isLoading = false;

    const loadingContainer = document.getElementById('loading-container');
    const authorsContainer = document.getElementById('authors-container');

    if (loadingContainer) {
        loadingContainer.style.display = 'none';
    }
    if (authorsContainer) {
        authorsContainer.style.display = 'block';
    }
}

/**
 * Show error state
 */
function showErrorState(message) {
    const errorContainer = document.getElementById('error-container');
    const loadingContainer = document.getElementById('loading-container');
    const authorsContainer = document.getElementById('authors-container');

    if (errorContainer) {
        errorContainer.style.display = 'block';
        errorContainer.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                <h3 class="text-lg font-semibold mb-2">Failed to load authors</h3>
                <p class="text-gray-600 mb-4">${message}</p>
                <button onclick="location.reload()" class="btn-primary">
                    <i class="fas fa-refresh mr-2"></i>Retry
                </button>
            </div>
        `;
    }
    if (loadingContainer) {
        loadingContainer.style.display = 'none';
    }
    if (authorsContainer) {
        authorsContainer.style.display = 'none';
    }
}

/**
 * Update page metadata
 */
function updatePageMetadata() {
    // Update page title
    document.title = 'Famous Authors | Quote Authors Collection - Quotese.com';
    
    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        metaDescription.content = 'Browse quotes by famous authors. Discover wisdom from great minds throughout history.';
    }
}

/**
 * Get mock authors data for fallback
 */
function getMockAuthors() {
    return [
        { id: 1, name: 'Albert Einstein', quotesCount: 25 },
        { id: 2, name: 'Maya Angelou', quotesCount: 18 },
        { id: 3, name: 'Winston Churchill', quotesCount: 22 },
        { id: 4, name: 'Mark Twain', quotesCount: 30 },
        { id: 5, name: 'Oscar Wilde', quotesCount: 20 }
    ];
}

// Expose functions to global scope for PageRouter
window.initAuthorsListPage = initAuthorsListPage;

// Add immediate console log to verify script loading
console.log('✅ Authors.js script loaded successfully');
console.log('🔍 initAuthorsListPage function available:', !!window.initAuthorsListPage);
