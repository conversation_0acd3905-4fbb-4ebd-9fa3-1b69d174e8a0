/**
 * ComponentLoader调试工具
 * 用于验证ComponentLoader是否正确初始化和暴露到全局作用域
 * 
 * @version 1.0.0
 * @date 2025-06-17
 * <AUTHOR>
 */

/**
 * ComponentLoader调试管理器
 */
class ComponentLoaderDebugger {
    constructor() {
        this.checkInterval = null;
        this.isDebugMode = false;
    }

    /**
     * 启动调试模式
     */
    startDebugMode() {
        this.isDebugMode = true;
        console.log('🔧 ComponentLoaderDebugger: Debug mode activated');
        
        // 立即检查ComponentLoader状态
        this.checkComponentLoaderStatus();
        
        // 每秒检查一次ComponentLoader状态
        this.checkInterval = setInterval(() => {
            this.checkComponentLoaderStatus();
        }, 1000);
        
        // 添加调试UI
        this.createDebugUI();
    }

    /**
     * 停止调试模式
     */
    stopDebugMode() {
        this.isDebugMode = false;
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
        
        // 移除调试UI
        const debugPanel = document.getElementById('componentloader-debug-panel');
        if (debugPanel) {
            debugPanel.remove();
        }
        
        console.log('🔧 ComponentLoaderDebugger: Debug mode deactivated');
    }

    /**
     * 检查ComponentLoader状态
     */
    checkComponentLoaderStatus() {
        const status = {
            timestamp: new Date().toISOString(),
            available: !!window.ComponentLoader,
            type: typeof window.ComponentLoader,
            methods: []
        };

        if (window.ComponentLoader) {
            // 检查ComponentLoader的方法
            const methods = ['loadComponent', 'loadCommonComponents'];
            methods.forEach(method => {
                status.methods.push({
                    name: method,
                    available: typeof window.ComponentLoader[method] === 'function'
                });
            });
        }

        // 更新调试UI
        this.updateDebugUI(status);

        // 输出到控制台
        if (this.isDebugMode) {
            console.log('🔧 ComponentLoader Status:', status);
        }

        return status;
    }

    /**
     * 创建调试UI界面
     */
    createDebugUI() {
        // 创建调试控制面板
        const debugPanel = document.createElement('div');
        debugPanel.id = 'componentloader-debug-panel';
        debugPanel.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            width: 350px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            max-height: 400px;
            overflow-y: auto;
        `;

        debugPanel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px;">🔧 ComponentLoader调试器</div>
            <div id="componentloader-status" style="margin-bottom: 10px;"></div>
            <div style="margin-top: 10px;">
                <button onclick="window.ComponentLoaderDebugger.testComponentLoader()" style="margin-right: 5px;">测试ComponentLoader</button>
                <button onclick="window.ComponentLoaderDebugger.stopDebugMode()">停止调试</button>
            </div>
        `;

        document.body.appendChild(debugPanel);
    }

    /**
     * 更新调试UI
     * @param {Object} status - ComponentLoader状态
     */
    updateDebugUI(status) {
        const statusElement = document.getElementById('componentloader-status');
        if (!statusElement) return;

        const statusColor = status.available ? '#90EE90' : '#FF6B6B';
        const statusText = status.available ? '✅ 可用' : '❌ 不可用';

        let methodsInfo = '';
        if (status.methods.length > 0) {
            methodsInfo = '<div style="margin-top: 5px;">方法状态:</div>';
            status.methods.forEach(method => {
                const methodColor = method.available ? '#90EE90' : '#FF6B6B';
                const methodStatus = method.available ? '✅' : '❌';
                methodsInfo += `<div style="color: ${methodColor};">${methodStatus} ${method.name}</div>`;
            });
        }

        statusElement.innerHTML = `
            <div style="color: ${statusColor};">状态: ${statusText}</div>
            <div>类型: ${status.type}</div>
            <div>检查时间: ${new Date(status.timestamp).toLocaleTimeString()}</div>
            ${methodsInfo}
        `;
    }

    /**
     * 测试ComponentLoader功能
     */
    async testComponentLoader() {
        console.log('🧪 ComponentLoaderDebugger: Starting ComponentLoader test...');
        
        if (!window.ComponentLoader) {
            console.error('❌ ComponentLoader not available for testing');
            alert('ComponentLoader不可用，无法进行测试');
            return;
        }

        try {
            // 测试loadComponent方法
            console.log('Testing loadComponent method...');
            
            // 这里可以测试加载一个简单的组件
            // 注意：这只是测试方法是否可调用，不会实际加载组件
            if (typeof window.ComponentLoader.loadComponent === 'function') {
                console.log('✅ loadComponent method is available');
            } else {
                console.error('❌ loadComponent method is not available');
            }

            // 测试loadCommonComponents方法
            if (typeof window.ComponentLoader.loadCommonComponents === 'function') {
                console.log('✅ loadCommonComponents method is available');
            } else {
                console.error('❌ loadCommonComponents method is not available');
            }

            console.log('✅ ComponentLoader test completed successfully');
            alert('ComponentLoader测试完成，检查控制台查看详细结果');

        } catch (error) {
            console.error('❌ ComponentLoader test failed:', error);
            alert('ComponentLoader测试失败: ' + error.message);
        }
    }

    /**
     * 等待ComponentLoader初始化
     * @param {number} maxWaitTime - 最大等待时间（毫秒）
     * @returns {Promise<boolean>} - 是否成功初始化
     */
    async waitForComponentLoader(maxWaitTime = 5000) {
        const startTime = Date.now();
        
        while (!window.ComponentLoader && (Date.now() - startTime) < maxWaitTime) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        const success = !!window.ComponentLoader;
        console.log(`🔧 ComponentLoaderDebugger: Wait result - ${success ? 'Success' : 'Timeout'}`);
        
        return success;
    }
}

// 创建全局实例
window.ComponentLoaderDebugger = new ComponentLoaderDebugger();

// 在开发环境中自动启动调试模式
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    window.addEventListener('load', () => {
        // 延迟启动，确保页面完全加载
        setTimeout(() => {
            if (window.location.search.includes('debug-componentloader=true')) {
                window.ComponentLoaderDebugger.startDebugMode();
            }
        }, 1000);
    });
}

// 添加快捷键支持
document.addEventListener('keydown', (e) => {
    // Ctrl+Shift+C 启动ComponentLoader调试
    if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        e.preventDefault();
        window.ComponentLoaderDebugger.startDebugMode();
    }
});

console.log('🔧 ComponentLoaderDebugger: Debug tools loaded');
console.log('💡 Tip: Add ?debug-componentloader=true to URL or press Ctrl+Shift+C to start debugging');
