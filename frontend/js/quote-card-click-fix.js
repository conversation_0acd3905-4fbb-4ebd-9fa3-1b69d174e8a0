/**
 * 名言卡片点击功能修复脚本
 * 确保所有页面的名言卡片都能正确跳转到详情页
 */

(function() {
    'use strict';

    console.log('🔧 Loading quote card click fix...');

    // 修复配置
    const fixConfig = {
        debug: true,
        forceClickable: true,
        checkInterval: 1000, // 每秒检查一次
        maxChecks: 10 // 最多检查10次
    };

    let checkCount = 0;

    /**
     * 修复单个名言卡片的点击功能
     */
    function fixQuoteCard(card) {
        if (!card) return false;

        const quoteId = card.getAttribute('data-quote-id');
        if (!quoteId) {
            if (fixConfig.debug) {
                console.warn('Quote card missing data-quote-id:', card);
            }
            return false;
        }

        // 确保有cursor-pointer类
        if (!card.classList.contains('cursor-pointer')) {
            card.classList.add('cursor-pointer');
            if (fixConfig.debug) {
                console.log(`✅ Added cursor-pointer to quote ${quoteId}`);
            }
        }

        // 检查是否已有点击事件
        if (card.hasAttribute('data-click-fixed')) {
            return true; // 已经修复过
        }

        // 添加点击事件
        card.addEventListener('click', function(e) {
            // 避免按钮和链接的点击事件
            if (e.target.closest('button') || e.target.closest('a')) {
                return;
            }

            e.preventDefault();
            e.stopPropagation();

            if (fixConfig.debug) {
                console.log(`🖱️ Quote card clicked: ID ${quoteId}`);
            }

            try {
                // 生成详情页URL
                const quoteUrl = `/quotes/${quoteId}/`;
                
                if (fixConfig.debug) {
                    console.log(`🔗 Navigating to: ${quoteUrl}`);
                }

                // 导航到详情页
                window.location.href = quoteUrl;
            } catch (error) {
                console.error('Error navigating to quote detail:', error);
            }
        });

        // 标记为已修复
        card.setAttribute('data-click-fixed', 'true');
        
        if (fixConfig.debug) {
            console.log(`✅ Fixed click functionality for quote ${quoteId}`);
        }

        return true;
    }

    /**
     * 扫描并修复页面上的所有名言卡片
     */
    function fixAllQuoteCards() {
        const cards = document.querySelectorAll('.quote-card-component');
        let fixedCount = 0;

        if (fixConfig.debug) {
            console.log(`🔍 Found ${cards.length} quote cards to check`);
        }

        cards.forEach((card, index) => {
            if (fixQuoteCard(card)) {
                fixedCount++;
            }
        });

        if (fixConfig.debug) {
            console.log(`🔧 Fixed ${fixedCount} quote cards`);
        }

        return fixedCount;
    }

    /**
     * 定期检查并修复新添加的名言卡片
     */
    function startPeriodicCheck() {
        const checkInterval = setInterval(() => {
            checkCount++;
            
            const fixedCount = fixAllQuoteCards();
            
            if (fixConfig.debug && fixedCount > 0) {
                console.log(`🔄 Periodic check ${checkCount}: Fixed ${fixedCount} cards`);
            }

            // 达到最大检查次数后停止
            if (checkCount >= fixConfig.maxChecks) {
                clearInterval(checkInterval);
                if (fixConfig.debug) {
                    console.log('🏁 Periodic check completed');
                }
            }
        }, fixConfig.checkInterval);
    }

    /**
     * 监听DOM变化，自动修复新添加的名言卡片
     */
    function setupMutationObserver() {
        if (typeof MutationObserver === 'undefined') {
            console.warn('MutationObserver not supported, using periodic check only');
            return;
        }

        const observer = new MutationObserver((mutations) => {
            let hasNewCards = false;

            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // 检查新添加的节点是否是名言卡片或包含名言卡片
                            if (node.classList && node.classList.contains('quote-card-component')) {
                                hasNewCards = true;
                            } else if (node.querySelectorAll) {
                                const newCards = node.querySelectorAll('.quote-card-component');
                                if (newCards.length > 0) {
                                    hasNewCards = true;
                                }
                            }
                        }
                    });
                }
            });

            if (hasNewCards) {
                setTimeout(() => {
                    const fixedCount = fixAllQuoteCards();
                    if (fixConfig.debug && fixedCount > 0) {
                        console.log(`🆕 Fixed ${fixedCount} newly added quote cards`);
                    }
                }, 100); // 短暂延迟确保DOM完全更新
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        if (fixConfig.debug) {
            console.log('👀 MutationObserver setup complete');
        }
    }

    /**
     * 初始化修复功能
     */
    function init() {
        console.log('🚀 Initializing quote card click fix...');

        // 立即修复现有的名言卡片
        fixAllQuoteCards();

        // 设置定期检查
        startPeriodicCheck();

        // 设置DOM变化监听
        setupMutationObserver();

        // 添加全局调试函数
        if (fixConfig.debug) {
            window.QuoteCardClickFix = {
                fixAll: fixAllQuoteCards,
                config: fixConfig,
                checkCount: () => checkCount
            };
            console.log('🛠️ Debug functions available: window.QuoteCardClickFix');
        }

        console.log('✅ Quote card click fix initialized');
    }

    // 等待DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        // DOM已经加载完成，立即初始化
        init();
    }

    // 也在window.onload时再次检查，确保所有内容都已加载
    window.addEventListener('load', () => {
        setTimeout(() => {
            fixAllQuoteCards();
            if (fixConfig.debug) {
                console.log('🔄 Post-load quote card fix completed');
            }
        }, 500);
    });

})();
