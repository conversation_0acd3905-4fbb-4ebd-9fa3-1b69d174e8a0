/**
 * 优化导航系统
 * 为热门模块提供直接ID查询的最优跳转路径
 * 绕过EntityIdMapper的slug解析流程，实现< 5ms响应时间
 * 
 * @version 1.0.0
 * @date 2025-06-17
 * <AUTHOR>
 */

/**
 * 全局实体缓存，用于存储热门模块的实体数据
 */
window.entityCache = window.entityCache || {
    categories: new Map(),
    authors: new Map(),
    sources: new Map(),
    lastUpdated: null
};

/**
 * 优化的导航函数 - 直接使用实体ID进行跳转
 * @param {string} entityType - 实体类型 ('category', 'author', 'source')
 * @param {Object} entity - 实体对象 {id, name, count}
 * @param {string} targetUrl - 目标URL
 */
window.navigateToEntityWithId = function(entityType, entity, targetUrl) {
    console.log(`🚀 OptimizedNavigation: Starting optimized navigation for ${entityType}`, entity);
    
    // 验证参数
    if (!entity || !entity.id || !entity.name) {
        console.error('OptimizedNavigation: Invalid entity object', entity);
        // 回退到常规导航
        window.location.href = targetUrl;
        return;
    }

    // 将实体数据缓存到全局缓存中
    const cacheKey = entityType === 'category' ? 'categories' : 
                     entityType === 'author' ? 'authors' : 'sources';
    
    window.entityCache[cacheKey].set(entity.id, {
        id: entity.id,
        name: entity.name,
        count: entity.count || 0,
        slug: window.UrlHandler.slugify(entity.name),
        cachedAt: Date.now()
    });

    console.log(`📝 OptimizedNavigation: Cached ${entityType} data for ID ${entity.id}`);

    // 在sessionStorage中存储实体ID，供目标页面使用
    const navigationData = {
        entityType: entityType,
        entityId: entity.id,
        entityName: entity.name,
        entityCount: entity.count || 0,
        navigatedAt: Date.now(),
        optimized: true
    };

    sessionStorage.setItem('optimizedNavigation', JSON.stringify(navigationData));
    console.log(`💾 OptimizedNavigation: Stored navigation data in sessionStorage`, navigationData);

    // 执行导航
    window.location.href = targetUrl;
};

/**
 * 检查是否有优化导航数据
 * @returns {Object|null} - 导航数据或null
 */
window.getOptimizedNavigationData = function() {
    try {
        const data = sessionStorage.getItem('optimizedNavigation');
        if (data) {
            const navigationData = JSON.parse(data);
            
            // 检查数据是否在有效期内（5分钟）
            const maxAge = 5 * 60 * 1000; // 5分钟
            if (Date.now() - navigationData.navigatedAt < maxAge) {
                console.log(`✅ OptimizedNavigation: Found valid navigation data`, navigationData);
                return navigationData;
            } else {
                console.log(`⏰ OptimizedNavigation: Navigation data expired, clearing`);
                sessionStorage.removeItem('optimizedNavigation');
            }
        }
    } catch (error) {
        console.error('OptimizedNavigation: Error reading navigation data', error);
        sessionStorage.removeItem('optimizedNavigation');
    }
    
    return null;
};

/**
 * 清除优化导航数据
 */
window.clearOptimizedNavigationData = function() {
    sessionStorage.removeItem('optimizedNavigation');
    console.log(`🧹 OptimizedNavigation: Cleared navigation data`);
};

/**
 * 获取缓存的实体数据
 * @param {string} entityType - 实体类型
 * @param {number} entityId - 实体ID
 * @returns {Object|null} - 缓存的实体数据或null
 */
window.getCachedEntityData = function(entityType, entityId) {
    const cacheKey = entityType === 'category' ? 'categories' : 
                     entityType === 'author' ? 'authors' : 'sources';
    
    const cachedData = window.entityCache[cacheKey].get(entityId);
    
    if (cachedData) {
        // 检查缓存是否在有效期内（30分钟）
        const maxAge = 30 * 60 * 1000; // 30分钟
        if (Date.now() - cachedData.cachedAt < maxAge) {
            console.log(`📋 OptimizedNavigation: Found cached ${entityType} data for ID ${entityId}`, cachedData);
            return cachedData;
        } else {
            console.log(`⏰ OptimizedNavigation: Cached ${entityType} data expired for ID ${entityId}`);
            window.entityCache[cacheKey].delete(entityId);
        }
    }
    
    return null;
};

/**
 * 批量缓存热门实体数据
 * @param {string} entityType - 实体类型
 * @param {Array} entities - 实体数组
 */
window.cachePopularEntities = function(entityType, entities) {
    if (!entities || !Array.isArray(entities)) {
        return;
    }

    const cacheKey = entityType === 'category' ? 'categories' :
                     entityType === 'author' ? 'authors' : 'sources';

    let cachedCount = 0;
    entities.forEach(entity => {
        if (entity && entity.id && entity.name) {
            window.entityCache[cacheKey].set(entity.id, {
                id: entity.id,
                name: entity.name,
                count: entity.count || 0,
                slug: window.UrlHandler.slugify(entity.name),
                cachedAt: Date.now()
            });
            cachedCount++;
        }
    });

    window.entityCache.lastUpdated = Date.now();
    console.log(`📦 OptimizedNavigation: Cached ${cachedCount} ${entityType} entities`);

    // 🔄 智能缓存同步：自动同步到EntityIdMapper
    if (window.EntityIdMapper && window.EntityIdMapper.syncFromSmartCache) {
        window.EntityIdMapper.syncFromSmartCache(cacheKey);
    }
};

/**
 * 获取缓存统计信息
 * @returns {Object} - 缓存统计
 */
window.getCacheStats = function() {
    return {
        categories: window.entityCache.categories.size,
        authors: window.entityCache.authors.size,
        sources: window.entityCache.sources.size,
        lastUpdated: window.entityCache.lastUpdated,
        totalEntities: window.entityCache.categories.size + 
                      window.entityCache.authors.size + 
                      window.entityCache.sources.size
    };
};

/**
 * 清除过期的缓存数据
 */
window.cleanupExpiredCache = function() {
    const maxAge = 30 * 60 * 1000; // 30分钟
    const now = Date.now();
    let cleanedCount = 0;

    ['categories', 'authors', 'sources'].forEach(cacheKey => {
        const cache = window.entityCache[cacheKey];
        for (const [id, data] of cache.entries()) {
            if (now - data.cachedAt > maxAge) {
                cache.delete(id);
                cleanedCount++;
            }
        }
    });

    if (cleanedCount > 0) {
        console.log(`🧹 OptimizedNavigation: Cleaned up ${cleanedCount} expired cache entries`);

        // 清理后重新同步EntityIdMapper
        if (window.EntityIdMapper && window.EntityIdMapper.syncAllFromSmartCache) {
            window.EntityIdMapper.syncAllFromSmartCache();
        }
    }
};

/**
 * 智能缓存自动更新机制
 * 检查缓存是否需要更新，如果需要则触发更新
 */
window.autoUpdateCache = function() {
    const updateInterval = 15 * 60 * 1000; // 15分钟
    const now = Date.now();

    if (!window.entityCache.lastUpdated ||
        (now - window.entityCache.lastUpdated) > updateInterval) {

        console.log('🔄 OptimizedNavigation: Cache needs update, triggering refresh...');

        // 触发缓存更新（通过重新加载热门数据）
        if (window.location.pathname.includes('/categories/') ||
            window.location.pathname.includes('/authors/') ||
            window.location.pathname.includes('/sources/')) {

            // 在详情页面，可以触发热门模块数据的重新加载
            // 这里可以根据具体需求实现
            console.log('🔄 OptimizedNavigation: Auto-update triggered on detail page');
        }
    }
};

// 定期清理过期缓存（每5分钟）
setInterval(window.cleanupExpiredCache, 5 * 60 * 1000);

// 定期检查缓存更新（每10分钟）
setInterval(window.autoUpdateCache, 10 * 60 * 1000);

// 页面加载时立即检查缓存状态
window.addEventListener('load', function() {
    // 延迟执行，确保其他系统已初始化
    setTimeout(() => {
        window.autoUpdateCache();

        // 如果EntityIdMapper已初始化，执行一次同步
        if (window.EntityIdMapper && window.EntityIdMapper.syncAllFromSmartCache) {
            window.EntityIdMapper.syncAllFromSmartCache();
        }
    }, 1000);
});

console.log('🚀 OptimizedNavigation: System initialized with smart caching');
