/**
 * SEO管理器 v2.0
 * 负责动态生成和管理页面的SEO标签
 * 支持Meta标签、Open Graph、Twitter Card和结构化数据
 * 版本：v2.0 - SEO重启实施
 * 更新日期：2025年6月16日
 */

const SEOManager = {
    // ==================== 配置常量 ====================
    
    CONFIG: {
        SITE_NAME: 'Quotese.com',
        SITE_URL: 'https://quotese.com',
        DEFAULT_IMAGE: 'https://quotese.com/images/og-default.jpg',
        TWITTER_HANDLE: '@quotese',
        FACEBOOK_APP_ID: '', // 如果有Facebook App ID
        
        // 默认SEO配置
        DEFAULTS: {
            title: 'Famous Quotes Collection | Quotese.com',
            description: 'Discover inspiring quotes from famous authors, books, and speeches. Find wisdom and motivation for life.',
            keywords: 'famous quotes, inspirational quotes, wisdom, motivation, authors',
            image: 'https://quotese.com/images/og-default.jpg',
            type: 'website'
        }
    },

    // ==================== 页面模板配置 ====================
    
    TEMPLATES: {
        'home': {
            title: 'Famous Quotes Collection | Quotese.com',
            description: 'Discover inspiring quotes from famous authors, books, and speeches. Find wisdom and motivation for life.',
            keywords: 'famous quotes, inspirational quotes, wisdom, motivation, authors, quotations',
            type: 'website'
        },
        
        'authors-list': {
            title: 'Famous Authors | Quote Authors Collection - Quotese.com',
            description: 'Browse quotes by famous authors. Discover wisdom from great minds throughout history.',
            keywords: 'famous authors, quote authors, writers, philosophers, inspirational figures',
            type: 'website'
        },
        
        'author-detail': {
            titleTemplate: '{authorName} Quotes | Famous Quotes Collection - Quotese.com',
            descriptionTemplate: 'Discover inspiring quotes by {authorName}. Browse famous quotes and find wisdom for life from this renowned author.',
            keywordsTemplate: '{authorName} quotes, {authorName} sayings, famous quotes, wisdom, inspirational quotes',
            type: 'profile'
        },
        
        'author-quotes': {
            titleTemplate: '{authorName} Quotes Collection | All Quotes - Quotese.com',
            descriptionTemplate: 'Complete collection of quotes by {authorName}. Explore all famous sayings and wisdom from this great mind.',
            keywordsTemplate: '{authorName} quotes collection, {authorName} all quotes, famous sayings, wisdom',
            type: 'website'
        },
        
        'categories-list': {
            title: 'Quote Categories | Browse by Topic - Quotese.com',
            description: 'Browse quotes by category and topic. Find inspirational quotes organized by themes.',
            keywords: 'quote categories, quote topics, inspirational quotes, motivational quotes, wisdom by topic',
            type: 'website'
        },
        
        'category-detail': {
            titleTemplate: '{categoryName} Quotes | Inspirational Quotes - Quotese.com',
            descriptionTemplate: 'Explore {categoryName} quotes. Find inspiration and motivation through carefully curated quotes on this topic.',
            keywordsTemplate: '{categoryName} quotes, {categoryName} sayings, inspirational quotes, motivation, wisdom',
            type: 'website'
        },
        
        'category-quotes': {
            titleTemplate: '{categoryName} Quotes Collection | All {categoryName} Quotes - Quotese.com',
            descriptionTemplate: 'Complete collection of {categoryName} quotes. Discover all inspirational sayings and wisdom on this topic.',
            keywordsTemplate: '{categoryName} quotes collection, {categoryName} inspiration, motivational quotes',
            type: 'website'
        },
        
        'sources-list': {
            title: 'Quote Sources | Books and Speeches - Quotese.com',
            description: 'Browse quotes by source. Discover wisdom from famous books, speeches, and works.',
            keywords: 'quote sources, book quotes, speech quotes, literature quotes, famous works',
            type: 'website'
        },
        
        'source-detail': {
            titleTemplate: '{sourceName} Quotes | Book Quotes Collection - Quotese.com',
            descriptionTemplate: 'Discover memorable quotes from {sourceName}. Explore wisdom from this renowned work.',
            keywordsTemplate: '{sourceName} quotes, book quotes, literature quotes, famous works, wisdom',
            type: 'book'
        },
        
        'quotes-list': {
            title: 'All Famous Quotes | Quote Collection - Quotese.com',
            description: 'Browse our complete collection of famous quotes. Find inspiration and wisdom from great minds.',
            keywords: 'famous quotes, all quotes, quote collection, inspirational quotes, wisdom, motivation',
            type: 'website'
        },
        
        'quote-detail': {
            titleTemplate: 'Quote #{quoteId} | Famous Quotes - Quotese.com',
            descriptionTemplate: 'Read and share this inspiring quote. Discover wisdom and motivation from great minds.',
            keywordsTemplate: 'famous quote, inspirational quote, wisdom, motivation, quote #{quoteId}',
            type: 'article'
        },
        
        'search': {
            title: 'Search Quotes | Find Your Inspiration - Quotese.com',
            description: 'Search our vast collection of quotes. Find the perfect quote for any occasion or mood.',
            keywords: 'search quotes, find quotes, quote search, inspirational quotes, famous sayings',
            type: 'website'
        }
    },

    // ==================== 核心方法 ====================

    /**
     * 初始化SEO管理器
     */
    init() {
        console.log('SEOManager: Initializing...');
        this.setupDefaultTags();
    },

    /**
     * 设置默认的SEO标签
     */
    setupDefaultTags() {
        // 确保基础meta标签存在
        this.ensureMetaTag('charset', 'UTF-8');
        this.ensureMetaTag('viewport', 'width=device-width, initial-scale=1.0');
        this.ensureMetaTag('robots', 'index, follow');
        this.ensureMetaTag('author', 'Quotese.com');
        this.ensureMetaTag('generator', 'Quotese SEO Manager v2.0');
        
        // 设置默认的Open Graph标签
        this.ensureMetaProperty('og:site_name', this.CONFIG.SITE_NAME);
        this.ensureMetaProperty('og:locale', 'en_US');
        
        // 设置Twitter Card默认标签
        this.ensureMetaName('twitter:card', 'summary_large_image');
        this.ensureMetaName('twitter:site', this.CONFIG.TWITTER_HANDLE);
        
        console.log('SEOManager: Default tags setup completed');
    },

    /**
     * 更新页面的所有SEO标签
     * @param {Object} pageData - 页面数据
     */
    updatePageSEO(pageData) {
        console.log('SEOManager: Updating page SEO...', pageData);
        
        try {
            // 生成SEO数据
            const seoData = this.generateSEOData(pageData);
            
            // 更新基础标签
            this.updateBasicTags(seoData);
            
            // 更新Open Graph标签
            this.updateOpenGraphTags(seoData);
            
            // 更新Twitter Card标签
            this.updateTwitterCardTags(seoData);
            
            // 更新结构化数据
            this.updateStructuredData(seoData, pageData);
            
            // 更新Canonical URL
            this.updateCanonicalUrl(seoData.canonicalUrl);
            
            console.log('SEOManager: Page SEO updated successfully', seoData);
            
            // 触发SEO更新事件
            this.dispatchSEOUpdateEvent(seoData);
            
        } catch (error) {
            console.error('SEOManager: Error updating page SEO:', error);
        }
    },

    /**
     * 生成页面的SEO数据
     * @param {Object} pageData - 页面数据
     * @returns {Object} - 生成的SEO数据
     */
    generateSEOData(pageData) {
        const pageType = pageData.pageType || 'home';
        const template = this.TEMPLATES[pageType] || this.TEMPLATES['home'];
        
        const seoData = {
            pageType: pageType,
            canonicalUrl: pageData.canonicalUrl || window.location.href,
            image: pageData.image || this.CONFIG.DEFAULT_IMAGE,
            publishedTime: pageData.publishedTime || null,
            modifiedTime: pageData.modifiedTime || new Date().toISOString(),
            author: pageData.author || null,
            section: pageData.section || null
        };

        // 生成标题
        if (template.titleTemplate && pageData.params) {
            seoData.title = this.processTemplate(template.titleTemplate, pageData.params);
        } else {
            seoData.title = template.title || this.CONFIG.DEFAULTS.title;
        }

        // 生成描述
        if (template.descriptionTemplate && pageData.params) {
            seoData.description = this.processTemplate(template.descriptionTemplate, pageData.params);
        } else {
            seoData.description = template.description || this.CONFIG.DEFAULTS.description;
        }

        // 生成关键词
        if (template.keywordsTemplate && pageData.params) {
            seoData.keywords = this.processTemplate(template.keywordsTemplate, pageData.params);
        } else {
            seoData.keywords = template.keywords || this.CONFIG.DEFAULTS.keywords;
        }

        // 设置类型
        seoData.type = template.type || this.CONFIG.DEFAULTS.type;

        return seoData;
    },

    /**
     * 处理模板字符串
     * @param {string} template - 模板字符串
     * @param {Object} params - 参数对象
     * @returns {string} - 处理后的字符串
     */
    processTemplate(template, params) {
        let result = template;
        
        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                const placeholder = `{${key}}`;
                result = result.replace(new RegExp(placeholder, 'g'), value);
            }
        });
        
        return result;
    },

    /**
     * 更新基础SEO标签
     * @param {Object} seoData - SEO数据
     */
    updateBasicTags(seoData) {
        // 更新页面标题
        document.title = seoData.title;

        // 更新meta描述
        this.updateMetaName('description', seoData.description);

        // 更新meta关键词
        this.updateMetaName('keywords', seoData.keywords);

        // 更新robots标签
        this.updateMetaName('robots', 'index, follow');
    },

    /**
     * 更新Open Graph标签
     * @param {Object} seoData - SEO数据
     */
    updateOpenGraphTags(seoData) {
        const ogTags = {
            'og:title': seoData.title,
            'og:description': seoData.description,
            'og:url': seoData.canonicalUrl,
            'og:type': seoData.type,
            'og:site_name': this.CONFIG.SITE_NAME,
            'og:image': seoData.image,
            'og:image:width': '1200',
            'og:image:height': '630',
            'og:image:alt': seoData.title,
            'og:locale': 'en_US'
        };

        // 添加可选的时间标签
        if (seoData.publishedTime) {
            ogTags['article:published_time'] = seoData.publishedTime;
        }
        if (seoData.modifiedTime) {
            ogTags['article:modified_time'] = seoData.modifiedTime;
        }
        if (seoData.author) {
            ogTags['article:author'] = seoData.author;
        }
        if (seoData.section) {
            ogTags['article:section'] = seoData.section;
        }

        // 更新所有Open Graph标签
        Object.entries(ogTags).forEach(([property, content]) => {
            this.updateMetaProperty(property, content);
        });
    },

    /**
     * 更新Twitter Card标签
     * @param {Object} seoData - SEO数据
     */
    updateTwitterCardTags(seoData) {
        const twitterTags = {
            'twitter:card': 'summary_large_image',
            'twitter:site': this.CONFIG.TWITTER_HANDLE,
            'twitter:title': seoData.title,
            'twitter:description': seoData.description,
            'twitter:image': seoData.image,
            'twitter:image:alt': seoData.title
        };

        // 更新所有Twitter Card标签
        Object.entries(twitterTags).forEach(([name, content]) => {
            this.updateMetaName(name, content);
        });
    },

    /**
     * 更新结构化数据
     * @param {Object} seoData - SEO数据
     * @param {Object} pageData - 原始页面数据
     */
    updateStructuredData(seoData, pageData) {
        const structuredData = this.generateStructuredData(seoData, pageData);

        // 移除现有的结构化数据
        const existingScript = document.querySelector('script[type="application/ld+json"]');
        if (existingScript) {
            existingScript.remove();
        }

        // 添加新的结构化数据
        if (structuredData) {
            const script = document.createElement('script');
            script.type = 'application/ld+json';
            script.textContent = JSON.stringify(structuredData, null, 2);
            document.head.appendChild(script);
        }
    },

    /**
     * 生成结构化数据
     * @param {Object} seoData - SEO数据
     * @param {Object} pageData - 页面数据
     * @returns {Object} - 结构化数据对象
     */
    generateStructuredData(seoData, pageData) {
        const baseData = {
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": this.CONFIG.SITE_NAME,
            "url": this.CONFIG.SITE_URL,
            "description": this.CONFIG.DEFAULTS.description,
            "potentialAction": {
                "@type": "SearchAction",
                "target": {
                    "@type": "EntryPoint",
                    "urlTemplate": `${this.CONFIG.SITE_URL}/search/?q={search_term_string}`
                },
                "query-input": "required name=search_term_string"
            }
        };

        // 根据页面类型生成特定的结构化数据
        switch (seoData.pageType) {
            case 'author-detail':
            case 'author-quotes':
                return this.generateAuthorStructuredData(seoData, pageData);

            case 'quote-detail':
                return this.generateQuoteStructuredData(seoData, pageData);

            case 'category-detail':
            case 'category-quotes':
                return this.generateCategoryStructuredData(seoData, pageData);

            case 'source-detail':
                return this.generateSourceStructuredData(seoData, pageData);

            default:
                return baseData;
        }
    },

    /**
     * 生成作者页面的结构化数据
     * @param {Object} seoData - SEO数据
     * @param {Object} pageData - 页面数据
     * @returns {Object} - 作者结构化数据
     */
    generateAuthorStructuredData(seoData, pageData) {
        return {
            "@context": "https://schema.org",
            "@type": "Person",
            "name": pageData.params?.authorName || "Unknown Author",
            "url": seoData.canonicalUrl,
            "sameAs": [],
            "description": seoData.description,
            "knowsAbout": ["Quotes", "Wisdom", "Philosophy"],
            "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": seoData.canonicalUrl
            }
        };
    },

    /**
     * 生成名言页面的结构化数据
     * @param {Object} seoData - SEO数据
     * @param {Object} pageData - 页面数据
     * @returns {Object} - 名言结构化数据
     */
    generateQuoteStructuredData(seoData, pageData) {
        return {
            "@context": "https://schema.org",
            "@type": "Quotation",
            "text": pageData.quoteText || "",
            "author": {
                "@type": "Person",
                "name": pageData.authorName || "Unknown"
            },
            "url": seoData.canonicalUrl,
            "datePublished": seoData.publishedTime,
            "dateModified": seoData.modifiedTime,
            "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": seoData.canonicalUrl
            }
        };
    },

    /**
     * 生成类别页面的结构化数据
     * @param {Object} seoData - SEO数据
     * @param {Object} pageData - 页面数据
     * @returns {Object} - 类别结构化数据
     */
    generateCategoryStructuredData(seoData, pageData) {
        return {
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": pageData.params?.categoryName || "Quote Category",
            "description": seoData.description,
            "url": seoData.canonicalUrl,
            "about": {
                "@type": "Thing",
                "name": pageData.params?.categoryName || "Quotes"
            },
            "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": seoData.canonicalUrl
            }
        };
    },

    /**
     * 生成来源页面的结构化数据
     * @param {Object} seoData - SEO数据
     * @param {Object} pageData - 页面数据
     * @returns {Object} - 来源结构化数据
     */
    generateSourceStructuredData(seoData, pageData) {
        return {
            "@context": "https://schema.org",
            "@type": "Book",
            "name": pageData.params?.sourceName || "Unknown Source",
            "description": seoData.description,
            "url": seoData.canonicalUrl,
            "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": seoData.canonicalUrl
            }
        };
    },

    /**
     * 更新Canonical URL
     * @param {string} url - Canonical URL
     */
    updateCanonicalUrl(url) {
        let canonicalLink = document.querySelector('link[rel="canonical"]');
        if (canonicalLink) {
            canonicalLink.setAttribute('href', url);
        } else {
            canonicalLink = document.createElement('link');
            canonicalLink.setAttribute('rel', 'canonical');
            canonicalLink.setAttribute('href', url);
            document.head.appendChild(canonicalLink);
        }
    },

    // ==================== 工具方法 ====================

    /**
     * 更新或创建meta name标签
     * @param {string} name - meta name属性
     * @param {string} content - meta content属性
     */
    updateMetaName(name, content) {
        if (!content) return;

        let metaTag = document.querySelector(`meta[name="${name}"]`);
        if (metaTag) {
            metaTag.setAttribute('content', content);
        } else {
            metaTag = document.createElement('meta');
            metaTag.setAttribute('name', name);
            metaTag.setAttribute('content', content);
            document.head.appendChild(metaTag);
        }
    },

    /**
     * 更新或创建meta property标签
     * @param {string} property - meta property属性
     * @param {string} content - meta content属性
     */
    updateMetaProperty(property, content) {
        if (!content) return;

        let metaTag = document.querySelector(`meta[property="${property}"]`);
        if (metaTag) {
            metaTag.setAttribute('content', content);
        } else {
            metaTag = document.createElement('meta');
            metaTag.setAttribute('property', property);
            metaTag.setAttribute('content', content);
            document.head.appendChild(metaTag);
        }
    },

    /**
     * 确保meta标签存在
     * @param {string} attribute - 属性名（name, property, charset等）
     * @param {string} value - 属性值
     * @param {string} content - content属性值（可选）
     */
    ensureMetaTag(attribute, value, content = null) {
        let selector;
        if (attribute === 'charset') {
            selector = `meta[charset]`;
        } else {
            selector = `meta[${attribute}="${value}"]`;
        }

        let metaTag = document.querySelector(selector);
        if (!metaTag) {
            metaTag = document.createElement('meta');
            if (attribute === 'charset') {
                metaTag.setAttribute('charset', value);
            } else {
                metaTag.setAttribute(attribute, value);
                if (content) {
                    metaTag.setAttribute('content', content);
                }
            }
            document.head.appendChild(metaTag);
        }
    },

    /**
     * 确保meta name标签存在
     * @param {string} name - name属性值
     * @param {string} content - content属性值
     */
    ensureMetaName(name, content) {
        this.ensureMetaTag('name', name, content);
    },

    /**
     * 确保meta property标签存在
     * @param {string} property - property属性值
     * @param {string} content - content属性值
     */
    ensureMetaProperty(property, content) {
        this.ensureMetaTag('property', property, content);
    },

    /**
     * 触发SEO更新事件
     * @param {Object} seoData - SEO数据
     */
    dispatchSEOUpdateEvent(seoData) {
        const event = new CustomEvent('seoUpdated', {
            detail: {
                seoData: seoData,
                timestamp: Date.now()
            }
        });
        window.dispatchEvent(event);
    },

    /**
     * 获取当前页面的SEO数据
     * @returns {Object} - 当前SEO数据
     */
    getCurrentSEOData() {
        return {
            title: document.title,
            description: this.getMetaContent('name', 'description'),
            keywords: this.getMetaContent('name', 'keywords'),
            canonicalUrl: this.getLinkHref('rel', 'canonical'),
            ogTitle: this.getMetaContent('property', 'og:title'),
            ogDescription: this.getMetaContent('property', 'og:description'),
            ogImage: this.getMetaContent('property', 'og:image'),
            twitterCard: this.getMetaContent('name', 'twitter:card')
        };
    },

    /**
     * 获取meta标签的content属性
     * @param {string} attribute - 属性名（name或property）
     * @param {string} value - 属性值
     * @returns {string|null} - content属性值
     */
    getMetaContent(attribute, value) {
        const metaTag = document.querySelector(`meta[${attribute}="${value}"]`);
        return metaTag ? metaTag.getAttribute('content') : null;
    },

    /**
     * 获取link标签的href属性
     * @param {string} attribute - 属性名
     * @param {string} value - 属性值
     * @returns {string|null} - href属性值
     */
    getLinkHref(attribute, value) {
        const linkTag = document.querySelector(`link[${attribute}="${value}"]`);
        return linkTag ? linkTag.getAttribute('href') : null;
    },

    /**
     * 验证SEO数据的完整性
     * @returns {Object} - 验证结果
     */
    validateSEO() {
        const validation = {
            valid: true,
            warnings: [],
            errors: []
        };

        // 检查必需的标签
        const requiredTags = [
            { type: 'title', selector: 'title' },
            { type: 'meta description', selector: 'meta[name="description"]' },
            { type: 'canonical URL', selector: 'link[rel="canonical"]' },
            { type: 'og:title', selector: 'meta[property="og:title"]' },
            { type: 'og:description', selector: 'meta[property="og:description"]' }
        ];

        requiredTags.forEach(tag => {
            const element = document.querySelector(tag.selector);
            if (!element) {
                validation.errors.push(`Missing ${tag.type}`);
                validation.valid = false;
            } else if (tag.type === 'title' && !element.textContent.trim()) {
                validation.errors.push(`Empty ${tag.type}`);
                validation.valid = false;
            } else if (tag.type !== 'title' && !element.getAttribute('content')) {
                validation.errors.push(`Empty ${tag.type}`);
                validation.valid = false;
            }
        });

        // 检查标题长度
        const title = document.title;
        if (title.length > 60) {
            validation.warnings.push(`Title too long (${title.length} chars, recommended: <60)`);
        }

        // 检查描述长度
        const description = this.getMetaContent('name', 'description');
        if (description && description.length > 160) {
            validation.warnings.push(`Description too long (${description.length} chars, recommended: <160)`);
        }

        return validation;
    }
};

// ==================== 初始化和事件监听 ====================

// 页面加载完成后初始化SEO管理器
document.addEventListener('DOMContentLoaded', function() {
    SEOManager.init();
});

// 监听页面状态更新事件
window.addEventListener('pageStateUpdated', function(event) {
    console.log('SEOManager: Page state updated, refreshing SEO...');
    const pageData = {
        pageType: event.detail.pageType,
        params: event.detail.params,
        canonicalUrl: window.location.href
    };
    SEOManager.updatePageSEO(pageData);
});

// 监听SEO更新事件（用于调试和监控）
window.addEventListener('seoUpdated', function(event) {
    console.log('SEOManager: SEO updated', event.detail);

    // 可以在这里添加SEO监控逻辑
    if (window.gtag) {
        // Google Analytics事件跟踪
        window.gtag('event', 'seo_updated', {
            'page_type': event.detail.seoData.pageType,
            'page_title': event.detail.seoData.title
        });
    }
});

// 导出到全局作用域
window.SEOManager = SEOManager;

// 如果支持ES6模块，也导出为模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SEOManager;
}
