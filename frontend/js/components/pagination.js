/**
 * 分页组件 - 重构版本
 * 负责处理分页逻辑和UI渲染，支持新的语义化URL格式
 *
 * @version 2.0.0
 * @date 2025-06-16
 * <AUTHOR>
 */

// 防止重复声明
if (typeof window.PaginationComponent !== 'undefined') {
    console.warn('PaginationComponent already exists, skipping redeclaration');
} else {

class PaginationComponent {
    /**
     * 构造函数
     * @param {Object} options - 配置选项
     * @param {string} options.containerId - 分页容器的ID
     * @param {Function} options.onPageChange - 页码变更回调函数
     * @param {boolean} options.showInfo - 是否显示分页信息
     * @param {string} options.infoText - 分页信息文本模板，例如："Showing {start}-{end} of {total} quotes"
     * @param {boolean} options.useUrlNavigation - 是否使用URL导航（默认true）
     * @param {string} options.baseUrl - 基础URL（用于生成分页链接）
     */
    constructor(options = {}) {
        this.containerId = options.containerId || 'pagination-container';
        this.onPageChange = options.onPageChange || (() => {});
        this.showInfo = options.showInfo !== undefined ? options.showInfo : true;
        this.infoText = options.infoText || 'Showing {start}-{end} of {total} quotes';
        this.useUrlNavigation = options.useUrlNavigation !== undefined ? options.useUrlNavigation : true;
        this.baseUrl = options.baseUrl || this.getCurrentBaseUrl();

        this.currentPage = 1;
        this.totalPages = 1;
        this.totalItems = 0;
        this.pageSize = 20;
        this.isLoading = false;
    }

    /**
     * 获取当前页面的基础URL（不包含分页参数）
     * @returns {string} 基础URL
     */
    getCurrentBaseUrl() {
        const currentPath = window.location.pathname;
        // 移除可能存在的分页参数，保留其他查询参数
        const url = new URL(window.location.href);
        url.searchParams.delete('page');
        return url.pathname + (url.search ? url.search : '');
    }

    /**
     * 初始化分页组件
     * @param {Object} paginationData - 分页数据
     * @param {number} paginationData.currentPage - 当前页码
     * @param {number} paginationData.totalPages - 总页数
     * @param {number} paginationData.totalItems - 总条目数
     * @param {number} paginationData.pageSize - 每页条目数
     */
    init(paginationData) {
        this.update(paginationData);
    }

    /**
     * 更新分页组件
     * @param {Object} paginationData - 分页数据
     * @param {number} paginationData.currentPage - 当前页码
     * @param {number} paginationData.totalPages - 总页数
     * @param {number} paginationData.totalItems - 总条目数
     * @param {number} paginationData.pageSize - 每页条目数
     */
    update(paginationData) {
        // 更新分页状态
        this.currentPage = paginationData.currentPage || this.currentPage;
        this.totalPages = paginationData.totalPages || this.totalPages;
        this.totalItems = paginationData.totalItems || paginationData.totalCount || this.totalItems;
        this.pageSize = paginationData.pageSize || this.pageSize;

        // 渲染分页UI
        this.render();
    }

    /**
     * 渲染分页UI
     */
    render() {
        const container = document.getElementById(this.containerId);
        if (!container) return;

        // 清空容器
        container.innerHTML = '';

        // 创建分页组件
        const pagination = document.createElement('nav');
        pagination.className = 'flex flex-col items-center mt-10 space-y-4';
        pagination.setAttribute('aria-label', 'Pagination');

        // 计算分页信息
        const startItem = (this.currentPage - 1) * this.pageSize + 1;
        const endItem = Math.min(this.currentPage * this.pageSize, this.totalItems);

        // 创建分页按钮
        const paginationButtons = document.createElement('div');
        paginationButtons.className = 'flex items-center space-x-1';
        paginationButtons.setAttribute('role', 'navigation');
        paginationButtons.setAttribute('aria-label', 'Pagination Navigation');

        // 移除首页按钮

        // 上一页按钮
        const prevPageBtn = this.createPaginationButton(
            this.currentPage - 1,
            '<i class="fas fa-angle-left" aria-hidden="true"></i>',
            'Previous page',
            this.currentPage === 1
        );
        paginationButtons.appendChild(prevPageBtn);

        // 页码按钮
        const pageNumbersContainer = document.createElement('div');
        pageNumbersContainer.className = 'flex items-center space-x-1';
        pageNumbersContainer.id = 'pagination-numbers';

        // 计算要显示的页码范围
        let startPage = Math.max(1, this.currentPage - 2);
        let endPage = Math.min(this.totalPages, startPage + 4);

        if (endPage - startPage < 4 && this.totalPages > 4) {
            startPage = Math.max(1, endPage - 4);
        }

        // 创建页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = this.createPaginationButton(
                i,
                i.toString(),
                `Page ${i}`,
                false,
                i === this.currentPage
            );
            pageNumbersContainer.appendChild(pageBtn);
        }

        // 如果有更多页码，显示省略号
        if (endPage < this.totalPages) {
            const ellipsis = document.createElement('span');
            ellipsis.className = 'px-2 text-gray-600 dark:text-gray-400';
            ellipsis.setAttribute('aria-hidden', 'true');
            ellipsis.textContent = '...';
            pageNumbersContainer.appendChild(ellipsis);

            // 显示最后一页
            const lastPageBtn = this.createPaginationButton(
                this.totalPages,
                this.totalPages.toString(),
                `Page ${this.totalPages}`,
                false,
                false
            );
            pageNumbersContainer.appendChild(lastPageBtn);
        }

        paginationButtons.appendChild(pageNumbersContainer);

        // 下一页按钮
        const nextPageBtn = this.createPaginationButton(
            this.currentPage + 1,
            '<i class="fas fa-angle-right" aria-hidden="true"></i>',
            'Next page',
            this.currentPage === this.totalPages
        );
        paginationButtons.appendChild(nextPageBtn);

        // 移除末页按钮

        pagination.appendChild(paginationButtons);

        // 分页信息
        if (this.showInfo) {
            const paginationInfo = document.createElement('div');
            paginationInfo.className = 'text-sm text-gray-600 dark:text-gray-400';
            paginationInfo.setAttribute('aria-live', 'polite');
            paginationInfo.id = 'pagination-info';

            // 替换信息文本中的占位符
            const infoText = this.infoText
                .replace('{start}', startItem)
                .replace('{end}', endItem)
                .replace('{total}', this.totalItems);

            paginationInfo.innerHTML = infoText;
            pagination.appendChild(paginationInfo);
        }

        container.appendChild(pagination);
    }

    /**
     * 生成分页URL
     * @param {number} page - 页码
     * @returns {string} 分页URL
     */
    generatePageUrl(page) {
        if (page === 1) {
            // 第一页不需要page参数
            return this.baseUrl;
        }

        // 构建带分页参数的URL
        return UrlHandler.buildUrlWithParams(this.baseUrl, { page: page });
    }

    /**
     * 创建分页按钮或链接
     * @param {number} page - 页码
     * @param {string} content - 按钮内容（HTML）
     * @param {string} ariaLabel - 无障碍标签
     * @param {boolean} disabled - 是否禁用
     * @param {boolean} current - 是否为当前页
     * @returns {HTMLElement} 分页按钮元素
     */
    createPaginationButton(page, content, ariaLabel, disabled = false, current = false) {
        let element;

        if (disabled) {
            // 禁用状态使用span
            element = document.createElement('span');
            element.className = 'pagination-btn btn btn-sm btn-gray disabled w-10 h-10 cursor-not-allowed opacity-50';
            element.setAttribute('aria-disabled', 'true');
        } else if (current) {
            // 当前页使用span
            element = document.createElement('span');
            element.className = 'pagination-btn btn btn-sm btn-primary w-10 h-10';
            element.setAttribute('aria-current', 'page');
        } else if (this.useUrlNavigation) {
            // 使用链接
            element = document.createElement('a');
            element.href = this.generatePageUrl(page);
            element.className = 'pagination-btn btn btn-sm btn-gray w-10 h-10 no-underline';

            // 添加点击事件处理
            element.addEventListener('click', (e) => {
                e.preventDefault();
                this.goToPage(page);
            });
        } else {
            // 使用按钮
            element = document.createElement('button');
            element.className = 'pagination-btn btn btn-sm btn-gray w-10 h-10';
            element.addEventListener('click', () => this.goToPage(page));
        }

        element.setAttribute('aria-label', ariaLabel);
        element.innerHTML = content;

        return element;
    }

    /**
     * 跳转到指定页码
     * @param {number} page - 页码
     */
    goToPage(page) {
        if (page === this.currentPage || this.isLoading || page < 1 || page > this.totalPages) {
            return;
        }

        if (this.useUrlNavigation) {
            // 使用URL导航
            const pageUrl = this.generatePageUrl(page);
            UrlHandler.navigateTo(pageUrl);
        }

        // 调用页码变更回调函数
        this.onPageChange(page);
    }

    /**
     * 设置加载状态
     * @param {boolean} isLoading - 是否正在加载
     */
    setLoading(isLoading) {
        this.isLoading = isLoading;

        // 禁用或启用分页按钮
        const container = document.getElementById(this.containerId);
        if (!container) return;

        const buttons = container.querySelectorAll('.pagination-btn');
        buttons.forEach(button => {
            if (isLoading) {
                button.setAttribute('disabled', 'disabled');
                button.classList.add('disabled');
            } else {
                button.removeAttribute('disabled');
                button.classList.remove('disabled');
            }
        });
    }
}

// 导出模块
window.PaginationComponent = PaginationComponent;

} // 结束防重复声明的if语句
