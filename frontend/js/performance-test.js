/**
 * 热门模块性能测试工具
 * 用于验证优化后的跳转性能提升效果
 * 
 * @version 1.0.0
 * @date 2025-06-17
 * <AUTHOR>
 */

/**
 * 性能测试管理器
 */
class PerformanceTestManager {
    constructor() {
        this.testResults = [];
        this.isTestMode = false;
    }

    /**
     * 启动性能测试模式
     */
    startTestMode() {
        this.isTestMode = true;
        this.testResults = [];
        console.log('🧪 PerformanceTest: Test mode activated');
        
        // 添加测试UI
        this.createTestUI();
    }

    /**
     * 创建测试UI界面
     */
    createTestUI() {
        // 创建测试控制面板
        const testPanel = document.createElement('div');
        testPanel.id = 'performance-test-panel';
        testPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            max-height: 400px;
            overflow-y: auto;
        `;

        testPanel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px;">🚀 热门模块性能测试</div>
            <div id="test-stats" style="margin-bottom: 10px;"></div>
            <div id="test-log" style="max-height: 200px; overflow-y: auto; border: 1px solid #333; padding: 5px;"></div>
            <div style="margin-top: 10px;">
                <button onclick="window.PerformanceTestManager.runBenchmark()" style="margin-right: 5px;">运行基准测试</button>
                <button onclick="window.PerformanceTestManager.clearResults()">清除结果</button>
            </div>
        `;

        document.body.appendChild(testPanel);
    }

    /**
     * 记录导航性能
     * @param {string} entityType - 实体类型
     * @param {Object} entity - 实体对象
     * @param {number} startTime - 开始时间
     * @param {boolean} optimized - 是否使用优化路径
     */
    recordNavigation(entityType, entity, startTime, optimized = false) {
        const endTime = performance.now();
        const duration = endTime - startTime;

        const result = {
            timestamp: new Date().toISOString(),
            entityType,
            entityName: entity.name,
            entityId: entity.id,
            duration: Math.round(duration * 100) / 100,
            optimized,
            method: optimized ? 'Direct ID Query' : 'EntityIdMapper Query'
        };

        this.testResults.push(result);
        this.updateTestUI(result);

        console.log(`📊 PerformanceTest: ${entityType} navigation - ${duration.toFixed(2)}ms (${optimized ? 'Optimized' : 'Standard'})`);
    }

    /**
     * 更新测试UI
     * @param {Object} result - 测试结果
     */
    updateTestUI(result) {
        const statsElement = document.getElementById('test-stats');
        const logElement = document.getElementById('test-log');

        if (statsElement && logElement) {
            // 更新统计信息
            const optimizedResults = this.testResults.filter(r => r.optimized);
            const standardResults = this.testResults.filter(r => !r.optimized);
            
            const avgOptimized = optimizedResults.length > 0 ? 
                optimizedResults.reduce((sum, r) => sum + r.duration, 0) / optimizedResults.length : 0;
            const avgStandard = standardResults.length > 0 ? 
                standardResults.reduce((sum, r) => sum + r.duration, 0) / standardResults.length : 0;

            const improvement = avgStandard > 0 ? Math.round((avgStandard - avgOptimized) / avgStandard * 100) : 0;

            statsElement.innerHTML = `
                <div>总测试: ${this.testResults.length}</div>
                <div>优化路径: ${optimizedResults.length} (平均: ${avgOptimized.toFixed(2)}ms)</div>
                <div>标准路径: ${standardResults.length} (平均: ${avgStandard.toFixed(2)}ms)</div>
                <div style="color: #90EE90;">性能提升: ${improvement}%</div>
            `;

            // 添加最新日志
            const logEntry = document.createElement('div');
            logEntry.style.cssText = `
                margin-bottom: 5px;
                padding: 3px;
                background: ${result.optimized ? '#004400' : '#440000'};
                border-radius: 3px;
            `;
            logEntry.innerHTML = `
                <div>${result.entityType}: ${result.entityName}</div>
                <div>${result.duration}ms - ${result.method}</div>
            `;

            logElement.insertBefore(logEntry, logElement.firstChild);

            // 限制日志条目数量
            while (logElement.children.length > 20) {
                logElement.removeChild(logElement.lastChild);
            }
        }
    }

    /**
     * 运行基准测试
     */
    async runBenchmark() {
        console.log('🧪 PerformanceTest: Starting benchmark test...');
        
        // 模拟测试数据
        const testEntities = [
            { type: 'category', entity: { id: 71523, name: 'Life' } },
            { type: 'category', entity: { id: 142145, name: 'Writing' } },
            { type: 'author', entity: { id: 2013, name: 'Albert Einstein' } },
            { type: 'source', entity: { id: 22141, name: 'Interview' } }
        ];

        for (const testCase of testEntities) {
            // 测试优化路径
            const optimizedStart = performance.now();
            await this.simulateOptimizedNavigation(testCase.type, testCase.entity);
            this.recordNavigation(testCase.type, testCase.entity, optimizedStart, true);

            // 等待一小段时间
            await new Promise(resolve => setTimeout(resolve, 100));

            // 测试标准路径
            const standardStart = performance.now();
            await this.simulateStandardNavigation(testCase.type, testCase.entity);
            this.recordNavigation(testCase.type, testCase.entity, standardStart, false);

            // 等待一小段时间
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log('✅ PerformanceTest: Benchmark test completed');
    }

    /**
     * 模拟优化导航
     * @param {string} entityType - 实体类型
     * @param {Object} entity - 实体对象
     */
    async simulateOptimizedNavigation(entityType, entity) {
        // 模拟直接ID查询（< 5ms）
        await new Promise(resolve => setTimeout(resolve, Math.random() * 5));
        return entity;
    }

    /**
     * 模拟标准导航
     * @param {string} entityType - 实体类型
     * @param {Object} entity - 实体对象
     */
    async simulateStandardNavigation(entityType, entity) {
        // 模拟EntityIdMapper查询（50-250ms）
        const delay = 50 + Math.random() * 200;
        await new Promise(resolve => setTimeout(resolve, delay));
        return entity;
    }

    /**
     * 清除测试结果
     */
    clearResults() {
        this.testResults = [];
        const statsElement = document.getElementById('test-stats');
        const logElement = document.getElementById('test-log');
        
        if (statsElement) statsElement.innerHTML = '<div>测试结果已清除</div>';
        if (logElement) logElement.innerHTML = '';
        
        console.log('🧹 PerformanceTest: Results cleared');
    }

    /**
     * 导出测试结果
     * @returns {Object} - 测试结果统计
     */
    exportResults() {
        const optimizedResults = this.testResults.filter(r => r.optimized);
        const standardResults = this.testResults.filter(r => !r.optimized);
        
        const stats = {
            totalTests: this.testResults.length,
            optimizedTests: optimizedResults.length,
            standardTests: standardResults.length,
            avgOptimizedTime: optimizedResults.length > 0 ? 
                optimizedResults.reduce((sum, r) => sum + r.duration, 0) / optimizedResults.length : 0,
            avgStandardTime: standardResults.length > 0 ? 
                standardResults.reduce((sum, r) => sum + r.duration, 0) / standardResults.length : 0,
            results: this.testResults
        };

        stats.performanceImprovement = stats.avgStandardTime > 0 ? 
            Math.round((stats.avgStandardTime - stats.avgOptimizedTime) / stats.avgStandardTime * 100) : 0;

        return stats;
    }
}

// 创建全局实例
window.PerformanceTestManager = new PerformanceTestManager();

// 在开发环境中自动启动测试模式
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    window.addEventListener('load', () => {
        // 延迟启动，确保页面完全加载
        setTimeout(() => {
            if (window.location.search.includes('perf-test=true')) {
                window.PerformanceTestManager.startTestMode();
            }
        }, 2000);
    });
}

// 添加快捷键支持
document.addEventListener('keydown', (e) => {
    // Ctrl+Shift+P 启动性能测试
    if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        window.PerformanceTestManager.startTestMode();
    }
});

console.log('🧪 PerformanceTest: Performance testing tools loaded');
console.log('💡 Tip: Add ?perf-test=true to URL or press Ctrl+Shift+P to start testing');

/**
 * 统一性能监控系统
 * 整合EntityIdMapper和热门模块优化的性能监控
 * @version 2.0.0
 * @date 2025-06-26
 */
class UnifiedPerformanceMonitor {
    constructor() {
        this.metrics = {
            // EntityIdMapper指标
            mappingHitRate: 0,
            mappingMissRate: 0,
            apiQueryCount: 0,

            // 优化导航指标
            optimizedNavigationRate: 0,
            cacheHitRate: 0,
            avgResponseTime: 0,

            // 系统指标
            memoryUsage: 0,
            mobilePerformance: 0,
            errorRate: 0,

            // 实时统计
            totalQueries: 0,
            successfulQueries: 0,
            failedQueries: 0
        };

        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.startTime = Date.now();
        this.queryHistory = [];
        this.maxHistorySize = 100;

        console.log('🔍 UnifiedPerformanceMonitor initialized');
    }

    /**
     * 启动实时监控
     */
    startRealTimeMonitoring() {
        if (this.isMonitoring) {
            console.log('⚠️ Monitoring already active');
            return;
        }

        this.isMonitoring = true;
        this.createMonitoringUI();

        // 每5秒更新一次指标
        this.monitoringInterval = setInterval(() => {
            this.collectAllMetrics();
            this.updateMonitoringUI();
        }, 5000);

        console.log('🚀 Real-time monitoring started');
    }

    /**
     * 停止实时监控
     */
    stopRealTimeMonitoring() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }

        const panel = document.getElementById('unified-performance-monitor');
        if (panel) {
            panel.remove();
        }

        console.log('⏹️ Real-time monitoring stopped');
    }

    /**
     * 创建监控界面
     */
    createMonitoringUI() {
        // 移除现有面板
        const existingPanel = document.getElementById('unified-performance-monitor');
        if (existingPanel) {
            existingPanel.remove();
        }

        const monitorPanel = document.createElement('div');
        monitorPanel.id = 'unified-performance-monitor';
        monitorPanel.style.cssText = `
            position: fixed;
            bottom: 10px;
            right: 10px;
            width: 380px;
            background: rgba(0,0,0,0.95);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            z-index: 10000;
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #00ff00;
            box-shadow: 0 4px 20px rgba(0,255,0,0.3);
        `;

        monitorPanel.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <h4 style="margin: 0; color: #00ff00;">🔍 统一性能监控仪表板</h4>
                <div>
                    <button onclick="window.unifiedMonitor.exportData()" style="background: #333; color: white; border: 1px solid #666; padding: 2px 6px; margin-right: 5px; border-radius: 3px; font-size: 10px;">导出</button>
                    <button onclick="window.unifiedMonitor.stopRealTimeMonitoring()" style="background: #ff4444; color: white; border: none; padding: 2px 6px; border-radius: 3px; font-size: 10px;">关闭</button>
                </div>
            </div>
            <div id="performance-metrics" style="line-height: 1.4;"></div>
            <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #333; font-size: 10px; color: #888;">
                快捷键: Ctrl+Shift+M 开启/关闭监控
            </div>
        `;

        document.body.appendChild(monitorPanel);

        // 初始化显示
        this.updateMonitoringUI();
    }

    /**
     * 收集所有性能指标
     */
    collectAllMetrics() {
        // 收集EntityIdMapper指标
        this.collectEntityIdMapperMetrics();

        // 收集优化导航指标
        this.collectOptimizedNavigationMetrics();

        // 收集系统指标
        this.collectSystemMetrics();

        // 收集移动端指标
        this.collectMobileMetrics();
    }

    /**
     * 收集EntityIdMapper指标
     */
    collectEntityIdMapperMetrics() {
        if (window.EntityIdMapper && window.EntityIdMapper.getStats) {
            const stats = window.EntityIdMapper.getStats();
            this.metrics.mappingHitRate = this.calculateHitRate(stats.hits, stats.total);
            this.metrics.mappingMissRate = this.calculateMissRate(stats.misses, stats.total);
            this.metrics.apiQueryCount = stats.apiQueries || 0;
        }
    }

    /**
     * 收集优化导航指标
     */
    collectOptimizedNavigationMetrics() {
        if (window.entityCache) {
            let totalCacheSize = 0;
            let totalHits = 0;

            ['categories', 'authors', 'sources'].forEach(type => {
                const cache = window.entityCache[type];
                if (cache) {
                    totalCacheSize += cache.size;
                    // 估算缓存命中次数
                    for (const [id, data] of cache.entries()) {
                        totalHits += data.accessCount || 0;
                    }
                }
            });

            this.metrics.cacheHitRate = totalCacheSize > 0 ? (totalHits / totalCacheSize * 100) : 0;
        }

        // 计算平均响应时间
        if (this.queryHistory.length > 0) {
            const recentQueries = this.queryHistory.slice(-20); // 最近20次查询
            const totalTime = recentQueries.reduce((sum, query) => sum + (query.responseTime || 0), 0);
            this.metrics.avgResponseTime = totalTime / recentQueries.length;
        }
    }

    /**
     * 收集系统指标
     */
    collectSystemMetrics() {
        // 估算内存使用
        this.metrics.memoryUsage = this.estimateMemoryUsage();

        // 计算错误率
        if (this.metrics.totalQueries > 0) {
            this.metrics.errorRate = (this.metrics.failedQueries / this.metrics.totalQueries * 100);
        }

        // 计算优化导航使用率
        const optimizedQueries = this.queryHistory.filter(q => q.optimized).length;
        if (this.queryHistory.length > 0) {
            this.metrics.optimizedNavigationRate = (optimizedQueries / this.queryHistory.length * 100);
        }
    }

    /**
     * 收集移动端指标
     */
    collectMobileMetrics() {
        if (window.mobileOptimizer && window.mobileOptimizer.getStats) {
            const mobileStats = window.mobileOptimizer.getStats();
            this.metrics.mobilePerformance = mobileStats.optimizationCount || 0;
        }
    }

    /**
     * 估算内存使用
     */
    estimateMemoryUsage() {
        let estimatedUsage = 0;

        // 估算缓存使用的内存
        if (window.entityCache) {
            ['categories', 'authors', 'sources'].forEach(type => {
                const cache = window.entityCache[type];
                if (cache) {
                    estimatedUsage += cache.size * 2; // 每个条目约2KB
                }
            });
        }

        // 估算DOM元素使用的内存
        const elementCount = document.querySelectorAll('*').length;
        estimatedUsage += elementCount * 0.1; // 每个元素约0.1KB

        // 如果支持performance.memory，使用实际数据
        if (performance.memory) {
            estimatedUsage = performance.memory.usedJSHeapSize / (1024 * 1024); // 转换为MB
        }

        return estimatedUsage;
    }

    /**
     * 更新监控界面
     */
    updateMonitoringUI() {
        const metricsDiv = document.getElementById('performance-metrics');
        if (!metricsDiv) return;

        const uptime = Math.floor((Date.now() - this.startTime) / 1000);
        const uptimeStr = this.formatUptime(uptime);

        metricsDiv.innerHTML = `
            <div style="color: #00ff00; font-weight: bold; margin-bottom: 8px;">
                📊 系统运行时间: ${uptimeStr}
            </div>

            <div style="margin-bottom: 8px;">
                <div style="color: #ffff00; font-weight: bold;">🎯 EntityIdMapper 性能</div>
                <div>映射表命中率: <span style="color: #00ff00;">${this.metrics.mappingHitRate.toFixed(1)}%</span></div>
                <div>API查询次数: <span style="color: ${this.metrics.apiQueryCount > 10 ? '#ff4444' : '#00ff00'}">${this.metrics.apiQueryCount}</span></div>
            </div>

            <div style="margin-bottom: 8px;">
                <div style="color: #ffff00; font-weight: bold;">🚀 优化导航性能</div>
                <div>缓存命中率: <span style="color: #00ff00;">${this.metrics.cacheHitRate.toFixed(1)}%</span></div>
                <div>平均响应时间: <span style="color: ${this.metrics.avgResponseTime > 100 ? '#ff4444' : '#00ff00'}">${this.metrics.avgResponseTime.toFixed(1)}ms</span></div>
                <div>优化导航使用率: <span style="color: #00ff00;">${this.metrics.optimizedNavigationRate.toFixed(1)}%</span></div>
            </div>

            <div style="margin-bottom: 8px;">
                <div style="color: #ffff00; font-weight: bold;">💾 系统资源</div>
                <div>内存使用: <span style="color: ${this.metrics.memoryUsage > 100 ? '#ff4444' : '#00ff00'}">${this.metrics.memoryUsage.toFixed(1)} MB</span></div>
                <div>错误率: <span style="color: ${this.metrics.errorRate > 5 ? '#ff4444' : '#00ff00'}">${this.metrics.errorRate.toFixed(1)}%</span></div>
            </div>

            <div style="margin-bottom: 8px;">
                <div style="color: #ffff00; font-weight: bold;">📱 移动端优化</div>
                <div>优化次数: <span style="color: #00ff00;">${this.metrics.mobilePerformance}</span></div>
                <div>设备类型: <span style="color: #00ff00;">${window.mobileOptimizer?.isMobile ? '移动端' : '桌面端'}</span></div>
            </div>

            <div>
                <div style="color: #ffff00; font-weight: bold;">📈 实时统计</div>
                <div>总查询次数: <span style="color: #00ff00;">${this.metrics.totalQueries}</span></div>
                <div>成功查询: <span style="color: #00ff00;">${this.metrics.successfulQueries}</span></div>
                <div>失败查询: <span style="color: ${this.metrics.failedQueries > 0 ? '#ff4444' : '#00ff00'}">${this.metrics.failedQueries}</span></div>
            </div>
        `;
    }

    /**
     * 格式化运行时间
     */
    formatUptime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }

    /**
     * 计算命中率
     */
    calculateHitRate(hits, total) {
        return total > 0 ? (hits / total * 100) : 0;
    }

    /**
     * 计算失误率
     */
    calculateMissRate(misses, total) {
        return total > 0 ? (misses / total * 100) : 0;
    }

    /**
     * 记录查询
     */
    recordQuery(type, identifier, responseTime, success, optimized = false) {
        const query = {
            type,
            identifier,
            responseTime,
            success,
            optimized,
            timestamp: Date.now()
        };

        this.queryHistory.push(query);

        // 限制历史记录大小
        if (this.queryHistory.length > this.maxHistorySize) {
            this.queryHistory = this.queryHistory.slice(-this.maxHistorySize);
        }

        // 更新统计
        this.metrics.totalQueries++;
        if (success) {
            this.metrics.successfulQueries++;
        } else {
            this.metrics.failedQueries++;
        }
    }

    /**
     * 导出监控数据
     */
    exportData() {
        const data = {
            metrics: this.metrics,
            queryHistory: this.queryHistory,
            timestamp: new Date().toISOString(),
            uptime: Date.now() - this.startTime
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `performance-monitor-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log('📊 Performance data exported');
    }

    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        return {
            summary: {
                uptime: Date.now() - this.startTime,
                totalQueries: this.metrics.totalQueries,
                successRate: this.metrics.totalQueries > 0 ?
                    (this.metrics.successfulQueries / this.metrics.totalQueries * 100) : 0,
                avgResponseTime: this.metrics.avgResponseTime
            },
            metrics: this.metrics,
            recentQueries: this.queryHistory.slice(-10)
        };
    }
}

// 全局实例
window.UnifiedPerformanceMonitor = UnifiedPerformanceMonitor;

// 添加快捷键支持 (Ctrl+Shift+M)
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'M') {
        e.preventDefault();
        if (!window.unifiedMonitor) {
            window.unifiedMonitor = new UnifiedPerformanceMonitor();
        }

        if (window.unifiedMonitor.isMonitoring) {
            window.unifiedMonitor.stopRealTimeMonitoring();
        } else {
            window.unifiedMonitor.startRealTimeMonitoring();
        }
    }
});

console.log('🔍 UnifiedPerformanceMonitor loaded');
console.log('💡 Tip: Press Ctrl+Shift+M to start/stop unified monitoring');
