/**
 * ComponentLoader初始化检查器
 * 确保ComponentLoader在所有页面脚本之前正确初始化
 * 
 * @version 1.0.0
 * @date 2025-06-17
 * <AUTHOR>
 */

(function() {
    'use strict';
    
    console.log('🔧 ComponentLoader Init Checker: Starting initialization check...');
    
    // 记录开始时间
    const startTime = Date.now();
    
    /**
     * 检查ComponentLoader是否可用
     * @returns {boolean} - ComponentLoader是否可用
     */
    function checkComponentLoader() {
        const available = !!(window.ComponentLoader && typeof window.ComponentLoader.loadComponent === 'function');
        
        if (available) {
            console.log('✅ ComponentLoader Init Checker: ComponentLoader is available');
            console.log('✅ Available methods:', Object.getOwnPropertyNames(window.ComponentLoader));
        } else {
            console.log('❌ ComponentLoader Init Checker: ComponentLoader not available');
            console.log('❌ window.ComponentLoader:', typeof window.ComponentLoader);
        }
        
        return available;
    }
    
    /**
     * 等待ComponentLoader初始化
     * @param {number} maxWaitTime - 最大等待时间（毫秒）
     * @returns {Promise<boolean>} - 是否成功初始化
     */
    function waitForComponentLoader(maxWaitTime = 15000) {
        return new Promise((resolve) => {
            const checkInterval = 50; // 每50ms检查一次
            let elapsed = 0;
            
            const intervalId = setInterval(() => {
                elapsed += checkInterval;
                
                if (checkComponentLoader()) {
                    clearInterval(intervalId);
                    const totalTime = Date.now() - startTime;
                    console.log(`✅ ComponentLoader Init Checker: Success after ${totalTime}ms`);
                    resolve(true);
                    return;
                }
                
                if (elapsed >= maxWaitTime) {
                    clearInterval(intervalId);
                    console.error(`❌ ComponentLoader Init Checker: Timeout after ${maxWaitTime}ms`);
                    console.error('❌ Final window state:', {
                        ComponentLoader: typeof window.ComponentLoader,
                        availableProperties: Object.keys(window).filter(key => key.includes('Component'))
                    });
                    resolve(false);
                    return;
                }
                
                // 每秒输出一次等待日志
                if (elapsed % 1000 === 0) {
                    console.log(`⏳ ComponentLoader Init Checker: Waiting... ${elapsed}ms elapsed`);
                }
            }, checkInterval);
        });
    }
    
    /**
     * 强制初始化ComponentLoader（如果需要）
     */
    function forceInitComponentLoader() {
        console.log('🔧 ComponentLoader Init Checker: Attempting force initialization...');
        
        // 检查是否有ComponentLoader的定义但未暴露到全局
        if (typeof ComponentLoader !== 'undefined' && !window.ComponentLoader) {
            console.log('🔧 Found ComponentLoader in local scope, exposing to global...');
            window.ComponentLoader = ComponentLoader;
            return true;
        }
        
        return false;
    }
    
    // 立即检查
    if (checkComponentLoader()) {
        console.log('✅ ComponentLoader Init Checker: ComponentLoader already available');
    } else {
        console.log('⏳ ComponentLoader Init Checker: ComponentLoader not yet available, starting wait...');
        
        // 尝试强制初始化
        forceInitComponentLoader();
        
        // 等待初始化
        waitForComponentLoader().then(success => {
            if (success) {
                // 触发自定义事件通知其他脚本
                const event = new CustomEvent('componentLoaderReady', {
                    detail: { 
                        initTime: Date.now() - startTime,
                        ComponentLoader: window.ComponentLoader 
                    }
                });
                window.dispatchEvent(event);
                console.log('📢 ComponentLoader Init Checker: Dispatched componentLoaderReady event');
            } else {
                // 触发失败事件
                const event = new CustomEvent('componentLoaderFailed', {
                    detail: { 
                        initTime: Date.now() - startTime,
                        error: 'ComponentLoader initialization timeout'
                    }
                });
                window.dispatchEvent(event);
                console.error('📢 ComponentLoader Init Checker: Dispatched componentLoaderFailed event');
            }
        });
    }
    
    // 暴露检查函数到全局作用域
    window.ComponentLoaderInitChecker = {
        check: checkComponentLoader,
        wait: waitForComponentLoader,
        forceInit: forceInitComponentLoader
    };
    
    console.log('🔧 ComponentLoader Init Checker: Initialization checker ready');
})();
