/**
 * 导航状态管理
 * 处理导航链接的激活状态和样式
 */

class NavigationState {
    constructor() {
        this.currentPath = window.location.pathname;
        this.init();
    }

    /**
     * 初始化导航状态
     */
    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.updateNavigationState());
        } else {
            this.updateNavigationState();
        }

        // 监听页面变化
        window.addEventListener('popstate', () => {
            this.currentPath = window.location.pathname;
            this.updateNavigationState();
        });
    }

    /**
     * 检查链接是否为激活状态
     * @param {string} linkUrl - 链接URL
     * @returns {boolean} - 是否激活
     */
    isActive(linkUrl) {
        if (linkUrl === '/') {
            // 首页只有在完全匹配时才激活
            return this.currentPath === '/' || this.currentPath === '/index.html';
        } else {
            // 其他页面检查路径是否以链接URL开头
            return this.currentPath.startsWith(linkUrl);
        }
    }

    /**
     * 更新导航状态
     */
    updateNavigationState() {
        // 更新桌面端导航
        this.updateDesktopNavigation();
        
        // 更新移动端导航
        this.updateMobileNavigation();
        
        console.log('✅ Navigation state updated for path:', this.currentPath);
    }

    /**
     * 更新桌面端导航状态
     */
    updateDesktopNavigation() {
        const desktopNavLinks = document.querySelectorAll('.nav-link');
        
        desktopNavLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (this.isActive(href)) {
                // 激活状态样式
                link.classList.remove('text-gray-600', 'dark:text-gray-300');
                link.classList.add('text-yellow-600', 'dark:text-yellow-400', 'font-semibold');
                
                // 添加底部边框指示器
                if (!link.classList.contains('border-b-2')) {
                    link.classList.add('border-b-2', 'border-yellow-500', 'pb-1');
                }
            } else {
                // 非激活状态样式
                link.classList.remove('text-yellow-600', 'dark:text-yellow-400', 'font-semibold', 'border-b-2', 'border-yellow-500', 'pb-1');
                link.classList.add('text-gray-600', 'dark:text-gray-300');
            }
        });
    }

    /**
     * 更新移动端导航状态
     */
    updateMobileNavigation() {
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
        
        mobileNavLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (this.isActive(href)) {
                // 激活状态样式
                link.classList.remove('text-gray-600', 'dark:text-gray-300');
                link.classList.add('text-yellow-600', 'dark:text-yellow-400', 'font-semibold', 'bg-yellow-50', 'dark:bg-yellow-900/20');
            } else {
                // 非激活状态样式
                link.classList.remove('text-yellow-600', 'dark:text-yellow-400', 'font-semibold', 'bg-yellow-50', 'dark:bg-yellow-900/20');
                link.classList.add('text-gray-600', 'dark:text-gray-300');
            }
        });
    }

    /**
     * 手动设置激活链接（用于SPA导航）
     * @param {string} path - 新的路径
     */
    setActivePath(path) {
        this.currentPath = path;
        this.updateNavigationState();
    }
}

// 自动初始化导航状态管理
window.navigationState = new NavigationState();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavigationState;
}
