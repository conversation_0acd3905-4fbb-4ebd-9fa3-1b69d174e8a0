/**
 * 全局错误处理器
 * 捕获和处理各种JavaScript错误，包括认证错误
 * 
 * @version 1.0.0
 * @date 2025-06-17
 * <AUTHOR>
 */

(function() {
    'use strict';
    
    console.log('🛡️ Global Error Handler: Initializing...');
    
    /**
     * 错误统计
     */
    const errorStats = {
        total: 0,
        auth: 0,
        componentLoader: 0,
        network: 0,
        other: 0
    };
    
    /**
     * 分类错误类型
     * @param {Error|string} error - 错误对象或消息
     * @returns {string} - 错误类型
     */
    function categorizeError(error) {
        const message = typeof error === 'string' ? error : error.message || error.toString();
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('auth') || lowerMessage.includes('authentication') || lowerMessage.includes('unauthorized')) {
            return 'auth';
        }
        
        if (lowerMessage.includes('componentloader') || lowerMessage.includes('component loader')) {
            return 'componentLoader';
        }
        
        if (lowerMessage.includes('network') || lowerMessage.includes('fetch') || lowerMessage.includes('xhr')) {
            return 'network';
        }
        
        return 'other';
    }
    
    /**
     * 处理认证错误
     * @param {Error} error - 认证错误
     */
    function handleAuthError(error) {
        console.warn('🔐 Auth Error Handler: Authentication error detected:', error);
        
        // 检查是否是外部服务的认证错误（如Google Analytics、广告等）
        if (error.stack && (
            error.stack.includes('googletagmanager') ||
            error.stack.includes('google-analytics') ||
            error.stack.includes('content.bundle.js') ||
            error.stack.includes('ads') ||
            error.stack.includes('analytics')
        )) {
            console.log('🔐 Auth Error Handler: External service auth error, ignoring...');
            return true; // 表示已处理
        }
        
        // 如果是我们自己的认证错误，需要处理
        console.warn('🔐 Auth Error Handler: Internal auth error, may need attention');
        return false; // 表示未处理，需要进一步处理
    }
    
    /**
     * 处理ComponentLoader错误
     * @param {Error} error - ComponentLoader错误
     */
    function handleComponentLoaderError(error) {
        console.error('📦 ComponentLoader Error Handler:', error);
        
        // 尝试强制初始化ComponentLoader
        if (window.ComponentLoaderInitChecker) {
            console.log('📦 Attempting to force initialize ComponentLoader...');
            window.ComponentLoaderInitChecker.forceInit();
        }
        
        return true; // 表示已尝试处理
    }
    
    /**
     * 全局错误处理函数
     * @param {ErrorEvent} event - 错误事件
     */
    function handleGlobalError(event) {
        const error = event.error || event.reason || event.message || 'Unknown error';
        const category = categorizeError(error);
        
        errorStats.total++;
        errorStats[category]++;
        
        console.group(`🛡️ Global Error Handler: ${category.toUpperCase()} Error #${errorStats.total}`);
        console.error('Error details:', error);
        console.log('Error category:', category);
        console.log('Error stats:', errorStats);
        
        let handled = false;
        
        // 根据错误类型进行处理
        switch (category) {
            case 'auth':
                handled = handleAuthError(error);
                break;
            case 'componentLoader':
                handled = handleComponentLoaderError(error);
                break;
            case 'network':
                console.log('🌐 Network error detected, may be temporary');
                handled = true; // 网络错误通常是临时的
                break;
            default:
                console.log('❓ Other error type, logging for analysis');
                break;
        }
        
        if (handled) {
            console.log('✅ Error handled successfully');
            event.preventDefault && event.preventDefault();
        } else {
            console.log('⚠️ Error not handled, may propagate');
        }
        
        console.groupEnd();
        
        return handled;
    }
    
    /**
     * Promise rejection处理函数
     * @param {PromiseRejectionEvent} event - Promise rejection事件
     */
    function handleUnhandledRejection(event) {
        console.group('🛡️ Global Error Handler: Unhandled Promise Rejection');
        console.error('Rejection reason:', event.reason);
        
        const handled = handleGlobalError({
            error: event.reason,
            message: event.reason?.message || event.reason
        });
        
        if (handled) {
            event.preventDefault();
        }
        
        console.groupEnd();
    }
    
    // 注册全局错误处理器
    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    // 暴露错误统计到全局作用域
    window.GlobalErrorHandler = {
        stats: errorStats,
        categorize: categorizeError,
        handle: handleGlobalError
    };
    
    console.log('✅ Global Error Handler: Initialized successfully');
    
    // 定期输出错误统计（仅在开发环境）
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        setInterval(() => {
            if (errorStats.total > 0) {
                console.log('📊 Error Stats:', errorStats);
            }
        }, 30000); // 每30秒输出一次
    }
})();
