/**
 * 网站配置
 * 包含不同环境下的配置参数
 */

const Config = {
    // 开发环境配置 - 使用本地API端点
    development: {
        apiEndpoint: 'http://127.0.0.1:8000/api/',      // REST API端点
        graphqlEndpoint: 'http://127.0.0.1:8000/graphql/', // GraphQL API端点
        useMockData: false,  // 禁用模拟数据，使用真实API
        debug: true
    },

    // 测试环境配置
    testing: {
        apiEndpoint: 'http://************:8000/api/',
        graphqlEndpoint: 'http://************:8000/graphql/',
        useMockData: false,  // 测试环境也使用生产API数据
        debug: true
    },

    // 生产环境配置
    production: {
        apiEndpoint: 'https://api.quotese.com/api/',
        graphqlEndpoint: 'https://api.quotese.com/graphql/',
        useMockData: false,
        debug: false
    },

    // 获取当前环境配置
    // 可以根据域名或其他条件判断当前环境
    getCurrent: function() {
        // 检查URL参数是否强制使用生产API
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('use-production-api') === 'true') {
            console.log('🔄 强制使用生产API端点进行测试');
            return this.production;
        }

        // 检查localStorage是否设置了生产API模式
        if (localStorage.getItem('quotese-use-production-api') === 'true') {
            console.log('🔄 从localStorage读取：使用生产API端点');
            return this.production;
        }

        // 检查是否为本地开发环境
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            return this.development;
        }
        // 检查是否为测试环境
        if (window.location.hostname.includes('************')) {
            return this.testing;
        }
        // 默认使用生产环境
        return this.production;
    }
};

// 导出当前环境配置
window.AppConfig = Config.getCurrent();

// 添加API模式切换工具函数
window.QuoteseAPIMode = {
    // 切换到生产API模式
    useProductionAPI: function() {
        localStorage.setItem('quotese-use-production-api', 'true');
        console.log('✅ 已切换到生产API模式，刷新页面生效');
        console.log('🔗 生产API端点:', Config.production.apiEndpoint);
        console.log('🔗 生产GraphQL端点:', Config.production.graphqlEndpoint);
        return '请刷新页面以应用新配置';
    },

    // 切换到本地API模式
    useLocalAPI: function() {
        localStorage.removeItem('quotese-use-production-api');
        console.log('✅ 已切换到本地API模式，刷新页面生效');
        console.log('🔗 本地API端点:', Config.development.apiEndpoint);
        console.log('🔗 本地GraphQL端点:', Config.development.graphqlEndpoint);
        return '请刷新页面以应用新配置';
    },

    // 获取当前API模式状态
    getCurrentMode: function() {
        const isUsingProduction = localStorage.getItem('quotese-use-production-api') === 'true';
        const currentConfig = Config.getCurrent();
        console.log('📊 当前API模式状态:');
        console.log('   使用生产API:', isUsingProduction);
        console.log('   API端点:', currentConfig.apiEndpoint);
        console.log('   GraphQL端点:', currentConfig.graphqlEndpoint);
        console.log('   调试模式:', currentConfig.debug);
        return {
            isUsingProduction,
            apiEndpoint: currentConfig.apiEndpoint,
            graphqlEndpoint: currentConfig.graphqlEndpoint,
            debug: currentConfig.debug
        };
    },

    // 测试API连接
    testConnection: async function() {
        const config = Config.getCurrent();
        console.log('🔍 测试API连接...');

        try {
            // 测试REST API
            const restResponse = await fetch(config.apiEndpoint);
            console.log('✅ REST API连接正常:', restResponse.status);

            // 测试GraphQL API
            const graphqlResponse = await fetch(config.graphqlEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: '{ __schema { types { name } } }'
                })
            });
            console.log('✅ GraphQL API连接正常:', graphqlResponse.status);

            return '✅ API连接测试通过';
        } catch (error) {
            console.error('❌ API连接测试失败:', error);
            return '❌ API连接测试失败: ' + error.message;
        }
    }
};

// 在控制台显示当前配置信息
console.log('🔧 Quotese API配置已加载');
console.log('📊 当前环境:', window.location.hostname);
console.log('🔗 API端点:', window.AppConfig.apiEndpoint);
console.log('🔗 GraphQL端点:', window.AppConfig.graphqlEndpoint);
console.log('💡 使用 QuoteseAPIMode.useProductionAPI() 切换到生产API');
console.log('💡 使用 QuoteseAPIMode.useLocalAPI() 切换到本地API');
console.log('💡 使用 QuoteseAPIMode.getCurrentMode() 查看当前模式');
console.log('💡 使用 QuoteseAPIMode.testConnection() 测试API连接');
