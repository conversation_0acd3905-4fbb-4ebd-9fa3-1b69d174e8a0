/**
 * 生产API测试配置
 * 用于在本地环境测试生产API连接
 * 临时覆盖本地配置以连接生产环境
 */

const ProductionTestConfig = {
    // 强制使用生产API配置进行测试
    apiEndpoint: 'https://api.quotese.com/api/',
    graphqlEndpoint: 'https://api.quotese.com/graphql/',
    useMockData: false,
    debug: true, // 保持调试模式以便监控
    
    // 测试标识
    isProductionTest: true,
    testStartTime: new Date().toISOString(),
    
    // 超时配置（生产环境可能需要更长时间）
    timeout: 15000, // 15秒超时
    retryAttempts: 3,
    
    // 测试相关配置
    testPages: [
        '/',
        '/categories/',
        '/authors/',
        '/sources/',
        '/categories/success/',
        '/authors/richelle-e-goodrich/',
        '/sources/life-is-simply-a-game/'
    ],
    
    // 预渲染服务配置
    prerenderService: {
        enabled: true,
        port: 8082,
        endpoints: [
            '/prerender/categories/',
            '/prerender/authors/',
            '/prerender/sources/'
        ]
    }
};

// 覆盖全局配置
console.log('🔄 Switching to Production API for testing...');
console.log('📡 Production GraphQL Endpoint:', ProductionTestConfig.graphqlEndpoint);
console.log('🕐 Test started at:', ProductionTestConfig.testStartTime);

// 备份原始配置
window.OriginalAppConfig = window.AppConfig;

// 应用生产测试配置
window.AppConfig = ProductionTestConfig;

// 如果ApiClient已经初始化，需要重新配置
if (window.ApiClient) {
    console.log('🔧 Reconfiguring ApiClient for production API...');
    window.ApiClient.apiEndpoint = ProductionTestConfig.apiEndpoint;
    window.ApiClient.graphqlEndpoint = ProductionTestConfig.graphqlEndpoint;
    window.ApiClient.useMockData = ProductionTestConfig.useMockData;
    
    // 重新初始化GraphQL客户端
    if (window.ApiClient.initGraphQLClient) {
        window.ApiClient.initGraphQLClient();
    }
}

// 导出配置供其他模块使用
window.ProductionTestConfig = ProductionTestConfig;
