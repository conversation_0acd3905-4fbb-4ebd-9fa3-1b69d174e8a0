/**
 * 面包屑导航测试脚本
 * 用于验证面包屑导航功能是否正常工作
 */

class BreadcrumbTester {
    constructor() {
        this.testResults = [];
        this.init();
    }
    
    init() {
        console.log('🧪 开始面包屑导航测试...');
        this.runAllTests();
    }
    
    async runAllTests() {
        // 等待页面加载完成
        await this.waitForPageLoad();
        
        // 测试面包屑容器存在
        this.testBreadcrumbContainer();
        
        // 测试面包屑数据生成
        this.testBreadcrumbDataGeneration();
        
        // 测试面包屑渲染
        this.testBreadcrumbRendering();
        
        // 测试面包屑链接
        this.testBreadcrumbLinks();
        
        // 测试结构化数据
        this.testStructuredData();
        
        // 输出测试结果
        this.outputResults();
    }
    
    waitForPageLoad() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }
    
    testBreadcrumbContainer() {
        const container = document.getElementById('breadcrumb-container');
        const result = {
            test: '面包屑容器存在性测试',
            passed: !!container,
            message: container ? '✅ 面包屑容器存在' : '❌ 面包屑容器不存在'
        };
        
        this.testResults.push(result);
        console.log(result.message);
    }
    
    testBreadcrumbDataGeneration() {
        if (!window.UrlHandler) {
            this.testResults.push({
                test: '面包屑数据生成测试',
                passed: false,
                message: '❌ UrlHandler不可用'
            });
            return;
        }
        
        try {
            const breadcrumbData = window.UrlHandler.getBreadcrumbData();
            const result = {
                test: '面包屑数据生成测试',
                passed: Array.isArray(breadcrumbData) && breadcrumbData.length > 0,
                message: Array.isArray(breadcrumbData) && breadcrumbData.length > 0 
                    ? `✅ 面包屑数据生成成功 (${breadcrumbData.length}项)` 
                    : '❌ 面包屑数据生成失败',
                data: breadcrumbData
            };
            
            this.testResults.push(result);
            console.log(result.message, breadcrumbData);
        } catch (error) {
            this.testResults.push({
                test: '面包屑数据生成测试',
                passed: false,
                message: `❌ 面包屑数据生成出错: ${error.message}`
            });
        }
    }
    
    testBreadcrumbRendering() {
        const container = document.getElementById('breadcrumb-container');
        if (!container) {
            this.testResults.push({
                test: '面包屑渲染测试',
                passed: false,
                message: '❌ 面包屑容器不存在，无法测试渲染'
            });
            return;
        }
        
        const breadcrumbItems = container.querySelectorAll('.breadcrumb-item');
        const result = {
            test: '面包屑渲染测试',
            passed: breadcrumbItems.length > 0,
            message: breadcrumbItems.length > 0 
                ? `✅ 面包屑渲染成功 (${breadcrumbItems.length}项)` 
                : '❌ 面包屑未渲染或渲染失败'
        };
        
        this.testResults.push(result);
        console.log(result.message);
    }
    
    testBreadcrumbLinks() {
        const container = document.getElementById('breadcrumb-container');
        if (!container) {
            this.testResults.push({
                test: '面包屑链接测试',
                passed: false,
                message: '❌ 面包屑容器不存在，无法测试链接'
            });
            return;
        }
        
        const links = container.querySelectorAll('a[href]');
        const validLinks = Array.from(links).filter(link => {
            const href = link.getAttribute('href');
            return href && href !== '#' && href !== '';
        });
        
        const result = {
            test: '面包屑链接测试',
            passed: validLinks.length > 0,
            message: validLinks.length > 0 
                ? `✅ 面包屑链接正常 (${validLinks.length}个有效链接)` 
                : '❌ 面包屑链接无效或不存在',
            links: validLinks.map(link => ({
                text: link.textContent.trim(),
                href: link.getAttribute('href')
            }))
        };
        
        this.testResults.push(result);
        console.log(result.message, result.links);
    }
    
    testStructuredData() {
        const structuredDataScript = document.getElementById('breadcrumb-structured-data');
        
        if (!structuredDataScript) {
            this.testResults.push({
                test: '结构化数据测试',
                passed: false,
                message: '❌ 面包屑结构化数据不存在'
            });
            return;
        }
        
        try {
            const structuredData = JSON.parse(structuredDataScript.textContent);
            const isValid = structuredData['@type'] === 'BreadcrumbList' && 
                           Array.isArray(structuredData.itemListElement) &&
                           structuredData.itemListElement.length > 0;
            
            const result = {
                test: '结构化数据测试',
                passed: isValid,
                message: isValid 
                    ? `✅ 面包屑结构化数据有效 (${structuredData.itemListElement.length}项)` 
                    : '❌ 面包屑结构化数据无效',
                data: structuredData
            };
            
            this.testResults.push(result);
            console.log(result.message, structuredData);
        } catch (error) {
            this.testResults.push({
                test: '结构化数据测试',
                passed: false,
                message: `❌ 结构化数据解析失败: ${error.message}`
            });
        }
    }
    
    outputResults() {
        console.log('\n📊 面包屑导航测试结果汇总:');
        console.log('='.repeat(50));
        
        const passedTests = this.testResults.filter(result => result.passed);
        const failedTests = this.testResults.filter(result => !result.passed);
        
        console.log(`✅ 通过测试: ${passedTests.length}/${this.testResults.length}`);
        console.log(`❌ 失败测试: ${failedTests.length}/${this.testResults.length}`);
        
        if (failedTests.length > 0) {
            console.log('\n❌ 失败的测试:');
            failedTests.forEach(test => {
                console.log(`  - ${test.test}: ${test.message}`);
            });
        }
        
        if (passedTests.length === this.testResults.length) {
            console.log('\n🎉 所有面包屑导航测试通过！');
        } else {
            console.log('\n⚠️ 部分测试失败，需要修复。');
        }
        
        // 返回测试结果供外部使用
        return {
            total: this.testResults.length,
            passed: passedTests.length,
            failed: failedTests.length,
            results: this.testResults
        };
    }
    
    // 手动测试面包屑链接点击
    testLinkClicks() {
        console.log('\n🖱️ 开始手动测试面包屑链接点击...');
        
        const container = document.getElementById('breadcrumb-container');
        if (!container) {
            console.log('❌ 面包屑容器不存在');
            return;
        }
        
        const links = container.querySelectorAll('a[href]');
        
        links.forEach((link, index) => {
            const href = link.getAttribute('href');
            const text = link.textContent.trim();
            
            console.log(`🔗 链接 ${index + 1}: "${text}" -> ${href}`);
            
            // 添加点击事件监听器用于测试
            link.addEventListener('click', (e) => {
                console.log(`🖱️ 点击了面包屑链接: "${text}" -> ${href}`);
                
                // 如果是测试模式，阻止默认跳转
                if (window.BREADCRUMB_TEST_MODE) {
                    e.preventDefault();
                    console.log('🧪 测试模式：阻止了页面跳转');
                }
            });
        });
        
        console.log(`📝 已为 ${links.length} 个面包屑链接添加点击监听器`);
        console.log('💡 设置 window.BREADCRUMB_TEST_MODE = true 可阻止实际跳转');
    }
}

// 自动运行测试（如果页面已加载）
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            window.breadcrumbTester = new BreadcrumbTester();
        }, 2000); // 等待2秒确保所有组件加载完成
    });
} else {
    setTimeout(() => {
        window.breadcrumbTester = new BreadcrumbTester();
    }, 2000);
}

// 导出测试类供手动使用
window.BreadcrumbTester = BreadcrumbTester;
