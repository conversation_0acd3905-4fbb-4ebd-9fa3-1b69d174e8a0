/**
 * 页面加载状态监控器
 * 监控页面加载的各个阶段，帮助调试加载问题
 * 
 * @version 1.0.0
 * @date 2025-06-17
 * <AUTHOR>
 */

(function() {
    'use strict';
    
    console.log('📊 Page Load Monitor: Initializing...');
    
    /**
     * 加载状态跟踪
     */
    const loadingStates = {
        startTime: Date.now(),
        domContentLoaded: false,
        windowLoaded: false,
        componentLoaderReady: false,
        pageInitialized: false,
        errors: []
    };
    
    /**
     * 记录加载事件
     * @param {string} event - 事件名称
     * @param {Object} details - 事件详情
     */
    function recordEvent(event, details = {}) {
        const timestamp = Date.now();
        const elapsed = timestamp - loadingStates.startTime;
        
        console.log(`📊 Page Load Monitor: ${event} (${elapsed}ms)`, details);
        
        // 更新状态
        if (event === 'DOMContentLoaded') {
            loadingStates.domContentLoaded = true;
        } else if (event === 'WindowLoaded') {
            loadingStates.windowLoaded = true;
        } else if (event === 'ComponentLoaderReady') {
            loadingStates.componentLoaderReady = true;
        } else if (event === 'PageInitialized') {
            loadingStates.pageInitialized = true;
        }
        
        // 检查是否完全加载
        checkLoadingComplete();
    }
    
    /**
     * 记录错误
     * @param {string} error - 错误信息
     * @param {Object} details - 错误详情
     */
    function recordError(error, details = {}) {
        const timestamp = Date.now();
        const elapsed = timestamp - loadingStates.startTime;
        
        loadingStates.errors.push({
            error,
            details,
            timestamp,
            elapsed
        });
        
        console.error(`📊 Page Load Monitor: ERROR - ${error} (${elapsed}ms)`, details);
    }
    
    /**
     * 检查加载是否完成
     */
    function checkLoadingComplete() {
        const { domContentLoaded, windowLoaded, componentLoaderReady, pageInitialized } = loadingStates;
        
        if (domContentLoaded && windowLoaded && componentLoaderReady && pageInitialized) {
            const totalTime = Date.now() - loadingStates.startTime;
            console.log(`🎉 Page Load Monitor: Page fully loaded in ${totalTime}ms`);
            
            // 触发完成事件
            const event = new CustomEvent('pageFullyLoaded', {
                detail: { 
                    totalTime,
                    states: loadingStates
                }
            });
            window.dispatchEvent(event);
        }
    }
    
    /**
     * 获取加载状态报告
     * @returns {Object} - 加载状态报告
     */
    function getLoadingReport() {
        const currentTime = Date.now();
        const totalElapsed = currentTime - loadingStates.startTime;
        
        return {
            totalElapsed,
            states: { ...loadingStates },
            isComplete: loadingStates.domContentLoaded && 
                       loadingStates.windowLoaded && 
                       loadingStates.componentLoaderReady && 
                       loadingStates.pageInitialized,
            errorCount: loadingStates.errors.length
        };
    }
    
    /**
     * 创建加载状态UI
     */
    function createLoadingStatusUI() {
        if (window.location.search.includes('debug-loading=true')) {
            const statusPanel = document.createElement('div');
            statusPanel.id = 'loading-status-panel';
            statusPanel.style.cssText = `
                position: fixed;
                bottom: 10px;
                right: 10px;
                width: 300px;
                background: rgba(0,0,0,0.9);
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-family: monospace;
                font-size: 11px;
                z-index: 10000;
                max-height: 300px;
                overflow-y: auto;
            `;
            
            statusPanel.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 10px;">📊 页面加载状态</div>
                <div id="loading-status-content"></div>
                <div style="margin-top: 10px;">
                    <button onclick="window.PageLoadMonitor.showReport()" style="font-size: 10px;">显示报告</button>
                </div>
            `;
            
            document.body.appendChild(statusPanel);
            
            // 定期更新状态
            setInterval(updateStatusUI, 500);
        }
    }
    
    /**
     * 更新状态UI
     */
    function updateStatusUI() {
        const contentElement = document.getElementById('loading-status-content');
        if (!contentElement) return;
        
        const report = getLoadingReport();
        const states = report.states;
        
        contentElement.innerHTML = `
            <div>总耗时: ${report.totalElapsed}ms</div>
            <div style="color: ${states.domContentLoaded ? '#90EE90' : '#FF6B6B'};">
                ${states.domContentLoaded ? '✅' : '⏳'} DOM加载
            </div>
            <div style="color: ${states.windowLoaded ? '#90EE90' : '#FF6B6B'};">
                ${states.windowLoaded ? '✅' : '⏳'} Window加载
            </div>
            <div style="color: ${states.componentLoaderReady ? '#90EE90' : '#FF6B6B'};">
                ${states.componentLoaderReady ? '✅' : '⏳'} ComponentLoader
            </div>
            <div style="color: ${states.pageInitialized ? '#90EE90' : '#FF6B6B'};">
                ${states.pageInitialized ? '✅' : '⏳'} 页面初始化
            </div>
            <div style="color: ${report.errorCount > 0 ? '#FF6B6B' : '#90EE90'};">
                错误: ${report.errorCount}
            </div>
        `;
    }
    
    // 监听DOM事件
    document.addEventListener('DOMContentLoaded', () => {
        recordEvent('DOMContentLoaded');
        createLoadingStatusUI();
    });
    
    window.addEventListener('load', () => {
        recordEvent('WindowLoaded');
    });
    
    // 监听ComponentLoader事件
    window.addEventListener('componentLoaderReady', (event) => {
        recordEvent('ComponentLoaderReady', event.detail);
    });
    
    window.addEventListener('componentLoaderFailed', (event) => {
        recordError('ComponentLoader Failed', event.detail);
    });
    
    // 监听页面初始化事件（需要页面脚本触发）
    window.addEventListener('pageInitialized', (event) => {
        recordEvent('PageInitialized', event.detail);
    });
    
    // 暴露监控器到全局作用域
    window.PageLoadMonitor = {
        record: recordEvent,
        recordError: recordError,
        getReport: getLoadingReport,
        showReport: () => {
            console.group('📊 Page Load Monitor: Full Report');
            console.log(getLoadingReport());
            console.groupEnd();
        }
    };
    
    console.log('✅ Page Load Monitor: Initialized successfully');
})();
