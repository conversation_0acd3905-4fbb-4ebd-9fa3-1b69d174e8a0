/**
 * 页面路由管理器
 * 负责统一的页面路由处理、页面类型检测和初始化流程
 * 与重构后的UrlHandler完全兼容，支持新的语义化URL格式
 */

const PageRouter = {
    // 页面类型映射到初始化函数
    pageInitializers: {
        'home': 'initIndexPage',
        'authors-list': 'initAuthorsListPage',
        'author-detail': 'initAuthorPage',
        'author-quotes': 'initAuthorQuotesPage',
        'categories-list': 'initCategoriesListPage',
        'category-detail': 'initCategoryPage',
        'category-quotes': 'initCategoryQuotesPage',
        'sources-list': 'initSourcesListPage',
        'source-detail': 'initSourcePage',
        'quotes-list': 'initQuotesListPage',
        'quote-detail': 'initQuotePage',
        'search': 'initSearchPage'
    },

    // 页面参数提取器
    parameterExtractors: {
        'author-detail': () => ({
            authorSlug: UrlHandler.parseAuthorFromPath(),
            authorName: UrlHandler.parseAuthorFromPath() ? UrlHandler.deslugify(UrlHandler.parseAuthorFromPath()) : null
        }),
        'author-quotes': () => ({
            authorSlug: UrlHandler.parseAuthorFromPath(),
            authorName: UrlHandler.parseAuthorFromPath() ? UrlHandler.deslugify(UrlHandler.parseAuthorFromPath()) : null
        }),
        'category-detail': () => ({
            categorySlug: UrlHandler.parseCategoryFromPath(),
            categoryName: UrlHandler.parseCategoryFromPath() ? UrlHandler.deslugify(UrlHandler.parseCategoryFromPath()) : null
        }),
        'category-quotes': () => ({
            categorySlug: UrlHandler.parseCategoryFromPath(),
            categoryName: UrlHandler.parseCategoryFromPath() ? UrlHandler.deslugify(UrlHandler.parseCategoryFromPath()) : null
        }),
        'source-detail': () => ({
            sourceSlug: UrlHandler.parseSourceFromPath(),
            sourceName: UrlHandler.parseSourceFromPath() ? UrlHandler.deslugify(UrlHandler.parseSourceFromPath()) : null
        }),
        'quote-detail': () => ({
            quoteId: UrlHandler.parseQuoteIdFromPath()
        })
    },

    /**
     * 初始化页面路由系统
     * 这是主要的入口点，会自动检测页面类型并调用相应的初始化函数
     */
    async initializePage() {
        try {
            console.log('PageRouter: Starting page initialization...');
            
            // 获取当前页面类型
            const pageType = UrlHandler.getCurrentPageType();
            console.log('PageRouter: Detected page type:', pageType);

            // 检查页面类型是否有效
            if (pageType === 'unknown') {
                console.warn('PageRouter: Unknown page type, redirecting to 404');
                this.handle404();
                return;
            }

            // 提取页面参数
            const pageParams = this.extractPageParameters(pageType);
            console.log('PageRouter: Extracted page parameters:', pageParams);

            // 验证必需的参数
            if (!this.validatePageParameters(pageType, pageParams)) {
                console.warn('PageRouter: Invalid page parameters, redirecting to 404');
                this.handle404();
                return;
            }

            // 设置全局页面状态
            this.setGlobalPageState(pageType, pageParams);

            // 调用页面特定的初始化函数
            await this.callPageInitializer(pageType, pageParams);

            // 使用新的SEO管理器更新SEO标签
            if (window.SEOManager) {
                const seoPageData = {
                    pageType: pageType,
                    params: pageParams,
                    canonicalUrl: window.UrlHandler && window.UrlHandler.getCanonicalUrl ? window.UrlHandler.getCanonicalUrl() : window.location.href,
                    image: this.getPageImage(pageType, pageParams),
                    author: this.getPageAuthor(pageType, pageParams),
                    section: this.getPageSection(pageType)
                };
                window.SEOManager.updatePageSEO(seoPageData);
            } else {
                // 回退到原有的SEO更新方法
                this.updateSEOTags();
            }

            console.log('PageRouter: Page initialization completed successfully');

        } catch (error) {
            console.error('PageRouter: Error during page initialization:', error);
            this.handleError(error);
        }
    },

    /**
     * 提取页面参数
     * @param {string} pageType - 页面类型
     * @returns {Object} - 提取的页面参数
     */
    extractPageParameters(pageType) {
        const extractor = this.parameterExtractors[pageType];
        if (extractor) {
            return extractor();
        }

        // 对于没有特定参数的页面，返回通用参数
        return {
            page: parseInt((window.UrlHandler && window.UrlHandler.getQueryParam ? window.UrlHandler.getQueryParam('page') : null)) || 1,
            search: (window.UrlHandler && window.UrlHandler.getQueryParam ? window.UrlHandler.getQueryParam('search') : null) || null
        };
    },

    /**
     * 验证页面参数
     * @param {string} pageType - 页面类型
     * @param {Object} params - 页面参数
     * @returns {boolean} - 参数是否有效
     */
    validatePageParameters(pageType, params) {
        switch (pageType) {
            case 'author-detail':
            case 'author-quotes':
                return params.authorSlug && params.authorName;
            
            case 'category-detail':
            case 'category-quotes':
                return params.categorySlug && params.categoryName;
            
            case 'source-detail':
                return params.sourceSlug && params.sourceName;
            
            case 'quote-detail':
                return params.quoteId && params.quoteId > 0;
            
            default:
                // 对于列表页面和首页，不需要特殊验证
                return true;
        }
    },

    /**
     * 设置全局页面状态
     * @param {string} pageType - 页面类型
     * @param {Object} params - 页面参数
     */
    setGlobalPageState(pageType, params) {
        // 创建全局页面状态对象
        window.currentPageState = {
            pageType: pageType,
            params: params,
            url: window.location.href,
            pathname: window.location.pathname,
            timestamp: Date.now()
        };

        // 触发页面状态更新事件
        window.dispatchEvent(new CustomEvent('pageStateUpdated', {
            detail: window.currentPageState
        }));
    },

    /**
     * 调用页面特定的初始化函数
     * @param {string} pageType - 页面类型
     * @param {Object} params - 页面参数
     */
    async callPageInitializer(pageType, params) {
        const initializerName = this.pageInitializers[pageType];
        
        if (!initializerName) {
            console.warn(`PageRouter: No initializer found for page type: ${pageType}`);
            return;
        }

        // 检查初始化函数是否存在
        if (typeof window[initializerName] === 'function') {
            console.log(`PageRouter: Calling ${initializerName} with params:`, params);
            await window[initializerName](params);
        } else {
            console.warn(`PageRouter: Initializer function ${initializerName} not found in global scope`);
            // 尝试加载页面特定的脚本
            await this.loadPageScript(pageType);
            
            // 再次尝试调用初始化函数
            if (typeof window[initializerName] === 'function') {
                console.log(`PageRouter: Calling ${initializerName} after script load`);
                await window[initializerName](params);
            } else {
                console.error(`PageRouter: Failed to load initializer ${initializerName}`);
            }
        }
    },

    /**
     * 动态加载页面特定的脚本
     * @param {string} pageType - 页面类型
     */
    async loadPageScript(pageType) {
        const scriptMap = {
            'home': 'js/pages/index.js',
            'authors-list': 'js/pages/authors.js',
            'author-detail': 'js/pages/author.js',
            'author-quotes': 'js/pages/author.js',
            'categories-list': 'js/pages/categories.js',
            'category-detail': 'js/pages/category.js',
            'category-quotes': 'js/pages/category.js',
            'sources-list': 'js/pages/sources.js',
            'source-detail': 'js/pages/source.js',
            'quotes-list': 'js/pages/quotes.js',
            'quote-detail': 'js/pages/quote.js',
            'search': 'js/pages/search.js'
        };

        const scriptPath = scriptMap[pageType];
        if (!scriptPath) {
            console.warn(`PageRouter: No script mapping found for page type: ${pageType}`);
            return;
        }

        try {
            // 检查脚本是否已经加载
            const existingScript = document.querySelector(`script[src="${scriptPath}"]`);
            if (existingScript) {
                console.log(`PageRouter: Script ${scriptPath} already loaded`);
                return;
            }

            // 动态加载脚本
            await new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = scriptPath;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });

            console.log(`PageRouter: Successfully loaded script: ${scriptPath}`);
        } catch (error) {
            console.error(`PageRouter: Failed to load script ${scriptPath}:`, error);
        }
    },

    /**
     * 处理404错误
     */
    handle404() {
        console.log('PageRouter: Handling 404 error');
        
        // 更新页面标题
        document.title = 'Page Not Found - Quotese.com';
        
        // 显示404错误信息
        const mainContent = document.querySelector('main');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="container mx-auto px-4 py-16 text-center">
                    <div class="max-w-md mx-auto">
                        <div class="text-6xl font-bold text-yellow-500 mb-4">404</div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">Page Not Found</h1>
                        <p class="text-gray-600 dark:text-gray-400 mb-8">
                            The page you're looking for doesn't exist or has been moved.
                        </p>
                        <div class="space-y-4">
                            <a href="/" class="inline-block bg-yellow-500 text-white px-6 py-3 rounded-lg hover:bg-yellow-600 transition-colors duration-300">
                                <i class="fas fa-home mr-2"></i>
                                Go Home
                            </a>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <p>You can also try:</p>
                                <div class="mt-2 space-x-4">
                                    <a href="/authors/" class="text-yellow-500 hover:text-yellow-600">Browse Authors</a>
                                    <a href="/categories/" class="text-yellow-500 hover:text-yellow-600">Browse Categories</a>
                                    <a href="/quotes/" class="text-yellow-500 hover:text-yellow-600">Browse Quotes</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    },

    /**
     * 处理一般错误
     * @param {Error} error - 错误对象
     */
    handleError(error) {
        console.error('PageRouter: Handling general error:', error);

        // 显示错误信息
        const mainContent = document.querySelector('main');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="container mx-auto px-4 py-16 text-center">
                    <div class="max-w-md mx-auto">
                        <div class="text-4xl text-red-500 mb-4">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">Something went wrong</h1>
                        <p class="text-gray-600 dark:text-gray-400 mb-8">
                            We encountered an error while loading this page. Please try refreshing or go back to the homepage.
                        </p>
                        <div class="space-y-4">
                            <button onclick="window.location.reload()" class="inline-block bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors duration-300">
                                <i class="fas fa-refresh mr-2"></i>
                                Refresh Page
                            </button>
                            <a href="/" class="inline-block bg-yellow-500 text-white px-6 py-3 rounded-lg hover:bg-yellow-600 transition-colors duration-300 ml-4">
                                <i class="fas fa-home mr-2"></i>
                                Go Home
                            </a>
                        </div>
                    </div>
                </div>
            `;
        }
    },

    /**
     * 处理URL变化事件
     * 当用户使用浏览器前进后退按钮或URL发生变化时调用
     */
    async handleUrlChange() {
        console.log('PageRouter: URL changed, reinitializing page...');
        await this.initializePage();
    },

    /**
     * 获取页面图片URL
     * @param {string} pageType - 页面类型
     * @param {Object} params - 页面参数
     * @returns {string} - 图片URL
     */
    getPageImage(pageType, params) {
        // 根据页面类型返回特定的图片
        switch (pageType) {
            case 'author-detail':
            case 'author-quotes':
                // 可以根据作者名称生成特定的图片URL
                return `https://quotese.com/images/authors/${params.authorSlug || 'default'}.jpg`;

            case 'category-detail':
            case 'category-quotes':
                return `https://quotese.com/images/categories/${params.categorySlug || 'default'}.jpg`;

            case 'source-detail':
                return `https://quotese.com/images/sources/${params.sourceSlug || 'default'}.jpg`;

            case 'quote-detail':
                return `https://quotese.com/images/quotes/quote-${params.quoteId || 'default'}.jpg`;

            default:
                return 'https://quotese.com/images/og-default.jpg';
        }
    },

    /**
     * 获取页面作者信息
     * @param {string} pageType - 页面类型
     * @param {Object} params - 页面参数
     * @returns {string|null} - 作者信息
     */
    getPageAuthor(pageType, params) {
        switch (pageType) {
            case 'author-detail':
            case 'author-quotes':
                return params.authorName || null;

            case 'quote-detail':
                // 如果有引用的作者信息，可以在这里返回
                return params.quoteAuthor || null;

            default:
                return 'Quotese.com';
        }
    },

    /**
     * 获取页面分类信息
     * @param {string} pageType - 页面类型
     * @returns {string|null} - 分类信息
     */
    getPageSection(pageType) {
        switch (pageType) {
            case 'author-detail':
            case 'author-quotes':
            case 'authors-list':
                return 'Authors';

            case 'category-detail':
            case 'category-quotes':
            case 'categories-list':
                return 'Categories';

            case 'source-detail':
            case 'sources-list':
                return 'Sources';

            case 'quote-detail':
            case 'quotes-list':
                return 'Quotes';

            case 'search':
                return 'Search';

            default:
                return 'Home';
        }
    },

    /**
     * 导航到指定URL
     * @param {string} url - 目标URL
     * @param {boolean} replace - 是否替换当前历史记录
     */
    async navigateTo(url, replace = false) {
        console.log(`PageRouter: Navigating to ${url} (replace: ${replace})`);

        // 使用UrlHandler进行导航
        if (window.UrlHandler && window.UrlHandler.navigateTo) {
            window.UrlHandler.navigateTo(url, replace);
        } else {
            // Fallback navigation
            if (replace) {
                window.location.replace(url);
            } else {
                window.location.href = url;
            }
        }

        // 重新初始化页面
        await this.initializePage();
    },

    /**
     * 获取当前页面的面包屑数据
     * @returns {Array} - 面包屑数组
     */
    getBreadcrumbData() {
        return window.UrlHandler && window.UrlHandler.getBreadcrumbData ? window.UrlHandler.getBreadcrumbData() : [];
    },

    /**
     * 获取当前页面的SEO数据
     * @returns {Object} - SEO数据对象
     */
    getSEOData() {
        const pageType = window.UrlHandler && window.UrlHandler.getCurrentPageType ? window.UrlHandler.getCurrentPageType() : 'home';
        const params = this.extractPageParameters(pageType);

        const seoData = {
            pageType: pageType,
            canonicalUrl: window.UrlHandler && window.UrlHandler.getCanonicalUrl ? window.UrlHandler.getCanonicalUrl() : window.location.href,
            breadcrumbs: this.getBreadcrumbData()
        };

        // 根据页面类型生成特定的SEO数据
        switch (pageType) {
            case 'author-detail':
                seoData.title = `${params.authorName} Quotes | Famous Quotes Collection - Quotese.com`;
                seoData.description = `Discover inspiring quotes by ${params.authorName}. Browse famous quotes and find wisdom for life.`;
                seoData.keywords = `${params.authorName} quotes, famous sayings, wisdom, inspirational quotes`;
                break;

            case 'category-detail':
                seoData.title = `${params.categoryName} Quotes | Inspirational Quotes - Quotese.com`;
                seoData.description = `Explore ${params.categoryName} quotes. Find inspiration and motivation through carefully curated quotes.`;
                seoData.keywords = `${params.categoryName} quotes, inspirational quotes, motivation, wisdom`;
                break;

            case 'source-detail':
                seoData.title = `${params.sourceName} Quotes | Book Quotes Collection - Quotese.com`;
                seoData.description = `Discover memorable quotes from ${params.sourceName}. Explore wisdom from literature and speeches.`;
                seoData.keywords = `${params.sourceName} quotes, book quotes, literature, wisdom`;
                break;

            case 'quote-detail':
                seoData.title = `Quote #${params.quoteId} | Famous Quotes - Quotese.com`;
                seoData.description = `Read and share this inspiring quote. Discover wisdom and motivation from great minds.`;
                seoData.keywords = `famous quotes, inspirational quotes, wisdom, motivation`;
                break;

            default:
                seoData.title = 'Famous Quotes Collection | Quotese.com';
                seoData.description = 'Discover inspiring quotes from famous authors, books, and speeches. Find wisdom and motivation for life.';
                seoData.keywords = 'famous quotes, inspirational quotes, wisdom, motivation, authors';
        }

        return seoData;
    },

    /**
     * 更新页面的SEO标签
     */
    updateSEOTags() {
        const seoData = this.getSEOData();

        // 更新页面标题
        document.title = seoData.title;

        // 更新meta描述
        let metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
            metaDescription.setAttribute('content', seoData.description);
        } else {
            metaDescription = document.createElement('meta');
            metaDescription.setAttribute('name', 'description');
            metaDescription.setAttribute('content', seoData.description);
            document.head.appendChild(metaDescription);
        }

        // 更新meta关键词
        let metaKeywords = document.querySelector('meta[name="keywords"]');
        if (metaKeywords) {
            metaKeywords.setAttribute('content', seoData.keywords);
        } else {
            metaKeywords = document.createElement('meta');
            metaKeywords.setAttribute('name', 'keywords');
            metaKeywords.setAttribute('content', seoData.keywords);
            document.head.appendChild(metaKeywords);
        }

        // 更新canonical URL
        let canonicalLink = document.querySelector('link[rel="canonical"]');
        if (canonicalLink) {
            canonicalLink.setAttribute('href', seoData.canonicalUrl);
        } else {
            canonicalLink = document.createElement('link');
            canonicalLink.setAttribute('rel', 'canonical');
            canonicalLink.setAttribute('href', seoData.canonicalUrl);
            document.head.appendChild(canonicalLink);
        }

        // 更新Open Graph标签
        this.updateOpenGraphTags(seoData);

        console.log('PageRouter: SEO tags updated', seoData);
    },

    /**
     * 更新Open Graph标签
     * @param {Object} seoData - SEO数据
     */
    updateOpenGraphTags(seoData) {
        const ogTags = {
            'og:title': seoData.title,
            'og:description': seoData.description,
            'og:url': seoData.canonicalUrl,
            'og:type': 'website',
            'og:site_name': 'Quotese.com'
        };

        Object.entries(ogTags).forEach(([property, content]) => {
            let ogTag = document.querySelector(`meta[property="${property}"]`);
            if (ogTag) {
                ogTag.setAttribute('content', content);
            } else {
                ogTag = document.createElement('meta');
                ogTag.setAttribute('property', property);
                ogTag.setAttribute('content', content);
                document.head.appendChild(ogTag);
            }
        });
    }
};

// ==================== 事件监听器和初始化 ====================

// 监听URL变化事件
window.addEventListener('urlChanged', function(event) {
    console.log('PageRouter: URL changed event received', event.detail);
    PageRouter.handleUrlChange();
});

// 监听浏览器前进后退按钮
window.addEventListener('popstate', function(event) {
    console.log('PageRouter: Popstate event received');
    PageRouter.handleUrlChange();
});

// 页面加载完成后初始化路由
document.addEventListener('DOMContentLoaded', function() {
    console.log('PageRouter: DOM content loaded, initializing...');

    // 延迟一小段时间确保所有脚本都已加载
    setTimeout(() => {
        PageRouter.initializePage();
    }, 100);
});

// 如果页面已经加载完成，立即初始化
if (document.readyState === 'loading') {
    // 文档仍在加载中，等待DOMContentLoaded事件
    console.log('PageRouter: Document still loading, waiting for DOMContentLoaded');
} else {
    // 文档已经加载完成，立即初始化
    console.log('PageRouter: Document already loaded, initializing immediately');
    setTimeout(() => {
        PageRouter.initializePage();
    }, 100);
}

// 导出到全局作用域
window.PageRouter = PageRouter;

// 如果支持ES6模块，也导出为模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PageRouter;
}
