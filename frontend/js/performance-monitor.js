/**
 * Core Web Vitals Performance Monitor
 * 监控和优化页面性能指标：LCP、FID、CLS
 * 基于SEO最佳实践实施
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = {
            lcp: null,
            fid: null,
            cls: null,
            fcp: null,
            ttfb: null
        };
        
        this.thresholds = {
            lcp: { good: 2500, poor: 4000 },
            fid: { good: 100, poor: 300 },
            cls: { good: 0.1, poor: 0.25 },
            fcp: { good: 1800, poor: 3000 },
            ttfb: { good: 800, poor: 1800 }
        };
        
        this.init();
    }
    
    init() {
        console.log('🚀 Performance Monitor initialized');
        
        // 监控Core Web Vitals
        this.observeLCP();
        this.observeFID();
        this.observeCLS();
        this.observeFCP();
        this.observeTTFB();
        
        // 页面加载完成后发送报告
        if (document.readyState === 'complete') {
            this.sendReport();
        } else {
            window.addEventListener('load', () => {
                setTimeout(() => this.sendReport(), 1000);
            });
        }
    }
    
    /**
     * 监控Largest Contentful Paint (LCP)
     * 目标: < 2.5秒
     */
    observeLCP() {
        if (!('PerformanceObserver' in window)) return;
        
        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                
                this.metrics.lcp = Math.round(lastEntry.startTime);
                this.logMetric('LCP', this.metrics.lcp, 'ms');
                
                // LCP优化建议
                if (this.metrics.lcp > this.thresholds.lcp.poor) {
                    console.warn('⚠️ LCP is poor (>4s). Consider optimizing images and critical resources.');
                } else if (this.metrics.lcp > this.thresholds.lcp.good) {
                    console.warn('⚠️ LCP needs improvement (>2.5s). Consider image optimization.');
                }
            });
            
            observer.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (error) {
            console.warn('LCP observation failed:', error);
        }
    }
    
    /**
     * 监控First Input Delay (FID)
     * 目标: < 100ms
     */
    observeFID() {
        if (!('PerformanceObserver' in window)) return;
        
        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    this.metrics.fid = Math.round(entry.processingStart - entry.startTime);
                    this.logMetric('FID', this.metrics.fid, 'ms');
                    
                    // FID优化建议
                    if (this.metrics.fid > this.thresholds.fid.poor) {
                        console.warn('⚠️ FID is poor (>300ms). Consider reducing JavaScript execution time.');
                    } else if (this.metrics.fid > this.thresholds.fid.good) {
                        console.warn('⚠️ FID needs improvement (>100ms). Consider code splitting.');
                    }
                });
            });
            
            observer.observe({ entryTypes: ['first-input'] });
        } catch (error) {
            console.warn('FID observation failed:', error);
        }
    }
    
    /**
     * 监控Cumulative Layout Shift (CLS)
     * 目标: < 0.1
     */
    observeCLS() {
        if (!('PerformanceObserver' in window)) return;
        
        try {
            let clsValue = 0;
            let sessionValue = 0;
            let sessionEntries = [];
            
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                
                entries.forEach((entry) => {
                    // 只计算非用户输入引起的布局偏移
                    if (!entry.hadRecentInput) {
                        const firstSessionEntry = sessionEntries[0];
                        const lastSessionEntry = sessionEntries[sessionEntries.length - 1];
                        
                        // 如果条目与上一个条目的时间间隔小于1秒且
                        // 与会话中第一个条目的时间间隔小于5秒，则
                        // 将条目包含在当前会话中。否则，开始一个新会话。
                        if (sessionValue &&
                            entry.startTime - lastSessionEntry.startTime < 1000 &&
                            entry.startTime - firstSessionEntry.startTime < 5000) {
                            sessionValue += entry.value;
                            sessionEntries.push(entry);
                        } else {
                            sessionValue = entry.value;
                            sessionEntries = [entry];
                        }
                        
                        // 如果当前会话值大于当前CLS值，
                        // 则更新CLS及其相关条目。
                        if (sessionValue > clsValue) {
                            clsValue = sessionValue;
                            this.metrics.cls = Math.round(clsValue * 1000) / 1000;
                            this.logMetric('CLS', this.metrics.cls, '');
                            
                            // CLS优化建议
                            if (this.metrics.cls > this.thresholds.cls.poor) {
                                console.warn('⚠️ CLS is poor (>0.25). Consider setting image dimensions and avoiding dynamic content insertion.');
                            } else if (this.metrics.cls > this.thresholds.cls.good) {
                                console.warn('⚠️ CLS needs improvement (>0.1). Consider optimizing layout stability.');
                            }
                        }
                    }
                });
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
        } catch (error) {
            console.warn('CLS observation failed:', error);
        }
    }
    
    /**
     * 监控First Contentful Paint (FCP)
     * 目标: < 1.8秒
     */
    observeFCP() {
        if (!('PerformanceObserver' in window)) return;
        
        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    if (entry.name === 'first-contentful-paint') {
                        this.metrics.fcp = Math.round(entry.startTime);
                        this.logMetric('FCP', this.metrics.fcp, 'ms');
                    }
                });
            });
            
            observer.observe({ entryTypes: ['paint'] });
        } catch (error) {
            console.warn('FCP observation failed:', error);
        }
    }
    
    /**
     * 监控Time to First Byte (TTFB)
     * 目标: < 800ms
     */
    observeTTFB() {
        try {
            // 使用Navigation Timing API
            const navigationEntry = performance.getEntriesByType('navigation')[0];
            if (navigationEntry) {
                this.metrics.ttfb = Math.round(navigationEntry.responseStart - navigationEntry.requestStart);
                this.logMetric('TTFB', this.metrics.ttfb, 'ms');
                
                // TTFB优化建议
                if (this.metrics.ttfb > this.thresholds.ttfb.poor) {
                    console.warn('⚠️ TTFB is poor (>1.8s). Consider server optimization or CDN.');
                } else if (this.metrics.ttfb > this.thresholds.ttfb.good) {
                    console.warn('⚠️ TTFB needs improvement (>800ms). Consider caching strategies.');
                }
            }
        } catch (error) {
            console.warn('TTFB observation failed:', error);
        }
    }
    
    /**
     * 记录性能指标
     */
    logMetric(name, value, unit) {
        const threshold = this.thresholds[name.toLowerCase()];
        let status = '✅ Good';
        
        if (threshold) {
            if (value > threshold.poor) {
                status = '❌ Poor';
            } else if (value > threshold.good) {
                status = '⚠️ Needs Improvement';
            }
        }
        
        console.log(`📊 ${name}: ${value}${unit} ${status}`);
    }
    
    /**
     * 发送性能报告
     */
    sendReport() {
        const report = {
            url: window.location.href,
            timestamp: new Date().toISOString(),
            metrics: this.metrics,
            userAgent: navigator.userAgent,
            connection: this.getConnectionInfo(),
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        };
        
        console.log('📈 Performance Report:', report);
        
        // 发送到分析服务（如果配置了）
        this.sendToAnalytics(report);
        
        // 生成优化建议
        this.generateOptimizationSuggestions();
    }
    
    /**
     * 获取网络连接信息
     */
    getConnectionInfo() {
        if ('connection' in navigator) {
            const conn = navigator.connection;
            return {
                effectiveType: conn.effectiveType,
                downlink: conn.downlink,
                rtt: conn.rtt,
                saveData: conn.saveData
            };
        }
        return null;
    }
    
    /**
     * 发送到分析服务
     */
    sendToAnalytics(data) {
        // 这里可以集成Google Analytics、自定义分析服务等
        if (typeof gtag !== 'undefined') {
            // Google Analytics 4
            Object.keys(data.metrics).forEach(metric => {
                if (data.metrics[metric] !== null) {
                    gtag('event', 'web_vitals', {
                        metric_name: metric.toUpperCase(),
                        metric_value: data.metrics[metric],
                        page_location: data.url
                    });
                }
            });
        }
        
        // 自定义分析端点
        if (window.ANALYTICS_ENDPOINT) {
            fetch(window.ANALYTICS_ENDPOINT, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            }).catch(error => console.warn('Analytics send failed:', error));
        }
    }
    
    /**
     * 生成优化建议
     */
    generateOptimizationSuggestions() {
        const suggestions = [];
        
        if (this.metrics.lcp > this.thresholds.lcp.good) {
            suggestions.push('🖼️ Optimize images: Use WebP format, proper sizing, and lazy loading');
            suggestions.push('⚡ Preload critical resources: Add <link rel="preload"> for important assets');
        }
        
        if (this.metrics.fid > this.thresholds.fid.good) {
            suggestions.push('📦 Split JavaScript bundles: Use code splitting to reduce main thread blocking');
            suggestions.push('⏰ Defer non-critical scripts: Use defer or async attributes');
        }
        
        if (this.metrics.cls > this.thresholds.cls.good) {
            suggestions.push('📐 Set image dimensions: Always specify width and height attributes');
            suggestions.push('🎯 Reserve space for dynamic content: Use CSS to prevent layout shifts');
        }
        
        if (suggestions.length > 0) {
            console.group('💡 Performance Optimization Suggestions:');
            suggestions.forEach(suggestion => console.log(suggestion));
            console.groupEnd();
        } else {
            console.log('🎉 All Core Web Vitals are in good range!');
        }
    }
}

// 自动初始化性能监控
if (typeof window !== 'undefined') {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.performanceMonitor = new PerformanceMonitor();
        });
    } else {
        window.performanceMonitor = new PerformanceMonitor();
    }
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceMonitor;
}
