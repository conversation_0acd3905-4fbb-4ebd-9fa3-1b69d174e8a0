/**
 * URL处理器 - 重构版本
 * 负责处理SEO友好的语义化URL生成和解析
 * 支持新的路径格式：/authors/albert-einstein/ 而不是 ?name=albert-einstein&id=1
 *
 * @version 2.0.0
 * @date 2025-06-16
 * <AUTHOR>
 */

const UrlHandler = {
    // URL配置常量
    CONFIG: {
        BASE_URL: 'https://quotese.com',
        PATHS: {
            AUTHORS: 'authors',
            CATEGORIES: 'categories',
            SOURCES: 'sources',
            QUOTES: 'quotes'
        },
        SUBPATHS: {
            QUOTES: 'quotes'
        }
    },

    /**
     * 将文本转换为URL友好的slug
     * 遵循SEO最佳实践和URL架构设计规范
     * @param {string} text - 要转换的文本
     * @returns {string} - URL友好的slug
     */
    slugify(text) {
        // 处理null、undefined、空字符串等边缘情况
        if (text === null || text === undefined) {
            return '';
        }

        // 转换为字符串并检查是否为空
        const str = String(text).trim();
        if (str === '') {
            return '';
        }

        try {
            const slug = str
                .toLowerCase()           // 转换为小写
                .trim()                  // 去除首尾空格
                .replace(/\s+/g, '-')    // 空格替换为连字符
                .replace(/[^\w\-]+/g, '') // 删除非单词字符
                .replace(/\-\-+/g, '-')  // 多个连字符合并为一个
                .replace(/^-+/, '')      // 删除开头连字符
                .replace(/-+$/, '');     // 删除结尾连字符

            // 如果处理后的slug为空，返回默认值
            return slug || '';
        } catch (error) {
            console.warn('Slugify error:', error, 'Input:', text);
            return '';
        }
    },

    /**
     * 将slug转换回可读文本
     * @param {string} slug - 要转换的slug
     * @returns {string} - 可读文本
     */
    deslugify(slug) {
        if (!slug) return '';

        return slug
            .replace(/-/g, ' ')  // 将连字符替换为空格
            .replace(/\b\w/g, l => l.toUpperCase());  // 将每个单词的首字母大写
    },

    /**
     * 验证slug格式是否正确
     * @param {string} slug - 要验证的slug
     * @returns {boolean} - 是否为有效的slug
     */
    isValidSlug(slug) {
        if (!slug || typeof slug !== 'string') return false;

        // slug应该只包含小写字母、数字和连字符，不能以连字符开头或结尾
        return /^[a-z0-9]+(-[a-z0-9]+)*$/.test(slug);
    },

    // ==================== URL生成方法 ====================

    /**
     * 生成作者详情页面的URL
     * 新格式：/authors/albert-einstein/
     * @param {Object} author - 作者对象 {name: string, id?: number}
     * @returns {string} - 作者页面的URL
     */
    getAuthorUrl(author) {
        if (!author) {
            console.warn('getAuthorUrl: Author object is null or undefined');
            return `/${this.CONFIG.PATHS.AUTHORS}/unknown/`;
        }

        if (!author.name) {
            console.warn('getAuthorUrl: Author object missing name property', author);
            // 如果有ID，使用ID作为后备
            if (author.id) {
                return `/${this.CONFIG.PATHS.AUTHORS}/author-${author.id}/`;
            }
            return `/${this.CONFIG.PATHS.AUTHORS}/unknown/`;
        }

        const slug = this.slugify(author.name);
        if (!slug) {
            console.warn('getAuthorUrl: Unable to generate valid slug from author name:', author.name);
            // 使用ID作为后备，如果没有ID则使用unknown
            if (author.id) {
                return `/${this.CONFIG.PATHS.AUTHORS}/author-${author.id}/`;
            }
            return `/${this.CONFIG.PATHS.AUTHORS}/unknown/`;
        }

        return `/${this.CONFIG.PATHS.AUTHORS}/${slug}/`;
    },

    /**
     * 生成作者名言列表页面的URL
     * 新格式：/authors/albert-einstein/quotes/
     * @param {Object} author - 作者对象 {name: string, id?: number}
     * @returns {string} - 作者名言列表页面的URL
     */
    getAuthorQuotesUrl(author) {
        if (!author) {
            console.warn('getAuthorQuotesUrl: Author object is null or undefined');
            return `/${this.CONFIG.PATHS.AUTHORS}/unknown/${this.CONFIG.SUBPATHS.QUOTES}/`;
        }

        if (!author.name) {
            console.warn('getAuthorQuotesUrl: Author object missing name property', author);
            if (author.id) {
                return `/${this.CONFIG.PATHS.AUTHORS}/author-${author.id}/${this.CONFIG.SUBPATHS.QUOTES}/`;
            }
            return `/${this.CONFIG.PATHS.AUTHORS}/unknown/${this.CONFIG.SUBPATHS.QUOTES}/`;
        }

        const slug = this.slugify(author.name);
        if (!slug) {
            console.warn('getAuthorQuotesUrl: Unable to generate valid slug from author name:', author.name);
            if (author.id) {
                return `/${this.CONFIG.PATHS.AUTHORS}/author-${author.id}/${this.CONFIG.SUBPATHS.QUOTES}/`;
            }
            return `/${this.CONFIG.PATHS.AUTHORS}/unknown/${this.CONFIG.SUBPATHS.QUOTES}/`;
        }

        return `/${this.CONFIG.PATHS.AUTHORS}/${slug}/${this.CONFIG.SUBPATHS.QUOTES}/`;
    },

    /**
     * 生成类别详情页面的URL
     * 新格式：/categories/inspirational/
     * @param {Object} category - 类别对象 {name: string, id?: number}
     * @returns {string} - 类别页面的URL
     */
    getCategoryUrl(category) {
        if (!category || !category.name) {
            throw new Error('Category object must have a name property');
        }

        const slug = this.slugify(category.name);
        if (!slug) {
            throw new Error('Unable to generate valid slug from category name');
        }

        return `/${this.CONFIG.PATHS.CATEGORIES}/${slug}/`;
    },

    /**
     * 生成类别名言列表页面的URL
     * 新格式：/categories/inspirational/quotes/
     * @param {Object} category - 类别对象 {name: string, id?: number}
     * @returns {string} - 类别名言列表页面的URL
     */
    getCategoryQuotesUrl(category) {
        if (!category || !category.name) {
            throw new Error('Category object must have a name property');
        }

        const slug = this.slugify(category.name);
        if (!slug) {
            throw new Error('Unable to generate valid slug from category name');
        }

        return `/${this.CONFIG.PATHS.CATEGORIES}/${slug}/${this.CONFIG.SUBPATHS.QUOTES}/`;
    },

    /**
     * 生成来源详情页面的URL
     * 新格式：/sources/book-title/
     * @param {Object} source - 来源对象 {name: string, id?: number}
     * @returns {string} - 来源页面的URL
     */
    getSourceUrl(source) {
        if (!source || !source.name) {
            throw new Error('Source object must have a name property');
        }

        const slug = this.slugify(source.name);
        if (!slug) {
            throw new Error('Unable to generate valid slug from source name');
        }

        return `/${this.CONFIG.PATHS.SOURCES}/${slug}/`;
    },

    /**
     * 生成名言详情页面的URL
     * 新格式：/quotes/123/
     * @param {Object} quote - 名言对象 {id: number}
     * @returns {string} - 名言详情页面的URL
     */
    getQuoteUrl(quote) {
        if (!quote || !quote.id) {
            throw new Error('Quote object must have an id property');
        }

        // 验证ID是否为有效数字
        const id = parseInt(quote.id);
        if (isNaN(id) || id <= 0) {
            throw new Error('Quote ID must be a positive number');
        }

        return `/${this.CONFIG.PATHS.QUOTES}/${id}/`;
    },

    /**
     * 生成列表页面的URL
     * @param {string} type - 页面类型 ('authors', 'categories', 'sources', 'quotes')
     * @returns {string} - 列表页面的URL
     */
    getListUrl(type) {
        const validTypes = Object.values(this.CONFIG.PATHS);
        if (!validTypes.includes(type)) {
            throw new Error(`Invalid page type: ${type}. Valid types: ${validTypes.join(', ')}`);
        }

        return `/${type}/`;
    },

    /**
     * 生成首页URL
     * @returns {string} - 首页URL
     */
    getHomeUrl() {
        return '/';
    },

    // ==================== URL解析方法 ====================

    /**
     * 从当前URL路径中解析作者slug
     * 支持格式：/authors/albert-einstein/ 和 /authors/albert-einstein/quotes/
     * @returns {string|null} - 作者slug，如果不是作者页面则返回null
     */
    parseAuthorFromPath() {
        const path = window.location.pathname;

        // 匹配作者详情页：/authors/slug/ 或 /authors/slug/quotes/
        const match = path.match(/^\/authors\/([^\/]+)\/?(?:quotes\/?)?$/);

        if (match && match[1]) {
            const slug = match[1];
            // 验证slug格式
            if (this.isValidSlug(slug)) {
                return slug;
            }
        }

        return null;
    },

    /**
     * 从当前URL路径中解析类别slug
     * 支持格式：/categories/inspirational/ 和 /categories/inspirational/quotes/
     * @returns {string|null} - 类别slug，如果不是类别页面则返回null
     */
    parseCategoryFromPath() {
        const path = window.location.pathname;

        // 匹配类别详情页：/categories/slug/ 或 /categories/slug/quotes/
        const match = path.match(/^\/categories\/([^\/]+)\/?(?:quotes\/?)?$/);

        if (match && match[1]) {
            const slug = match[1];
            // 验证slug格式
            if (this.isValidSlug(slug)) {
                return slug;
            }
        }

        return null;
    },

    /**
     * 从当前URL路径中解析来源slug
     * 支持格式：/sources/book-title/
     * @returns {string|null} - 来源slug，如果不是来源页面则返回null
     */
    parseSourceFromPath() {
        const path = window.location.pathname;

        // 匹配来源详情页：/sources/slug/
        const match = path.match(/^\/sources\/([^\/]+)\/?$/);

        if (match && match[1]) {
            const slug = match[1];
            // 验证slug格式
            if (this.isValidSlug(slug)) {
                return slug;
            }
        }

        return null;
    },

    /**
     * 从当前URL路径中解析名言ID
     * 支持格式：/quotes/123/
     * @returns {number|null} - 名言ID，如果不是名言详情页面则返回null
     */
    parseQuoteIdFromPath() {
        const path = window.location.pathname;

        // 匹配名言详情页：/quotes/id/
        const match = path.match(/^\/quotes\/(\d+)\/?$/);

        if (match && match[1]) {
            const id = parseInt(match[1]);
            if (!isNaN(id) && id > 0) {
                return id;
            }
        }

        return null;
    },

    /**
     * 获取当前页面类型
     * @returns {string} - 页面类型
     */
    getCurrentPageType() {
        const path = window.location.pathname;

        // 首页
        if (path === '/' || path === '/index.html') {
            return 'home';
        }

        // 作者相关页面
        if (path.match(/^\/authors\/[^\/]+\/quotes\/?$/)) {
            return 'author-quotes';
        }
        if (path.match(/^\/authors\/[^\/]+\/?$/)) {
            return 'author-detail';
        }
        if (path.match(/^\/authors\/?$/)) {
            return 'authors-list';
        }

        // 类别相关页面
        if (path.match(/^\/categories\/[^\/]+\/quotes\/?$/)) {
            return 'category-quotes';
        }
        if (path.match(/^\/categories\/[^\/]+\/?$/)) {
            return 'category-detail';
        }
        if (path.match(/^\/categories\/?$/)) {
            return 'categories-list';
        }

        // 来源相关页面
        if (path.match(/^\/sources\/[^\/]+\/?$/)) {
            return 'source-detail';
        }
        if (path.match(/^\/sources\/?$/)) {
            return 'sources-list';
        }

        // 名言相关页面
        if (path.match(/^\/quotes\/\d+\/?$/)) {
            return 'quote-detail';
        }
        if (path.match(/^\/quotes\/?$/)) {
            return 'quotes-list';
        }

        // 搜索页面
        if (path.match(/^\/search\/?$/)) {
            return 'search';
        }

        // 404或其他页面
        return 'unknown';
    },

    /**
     * 检查当前页面是否为子页面（如名言列表页）
     * @returns {boolean} - 是否为子页面
     */
    isSubPage() {
        const pageType = this.getCurrentPageType();
        return ['author-quotes', 'category-quotes'].includes(pageType);
    },

    /**
     * 获取当前页面的面包屑数据
     * @returns {Array} - 面包屑数组
     */
    getBreadcrumbData() {
        const pageType = this.getCurrentPageType();
        const breadcrumbs = [
            { name: 'Home', url: '/', active: false }
        ];

        switch (pageType) {
            case 'authors-list':
                breadcrumbs.push({ name: 'Authors', url: '/authors/', active: true });
                break;

            case 'author-detail':
                const authorSlug = this.parseAuthorFromPath();
                if (authorSlug) {
                    const authorName = this.deslugify(authorSlug);
                    breadcrumbs.push({ name: 'Authors', url: '/authors/', active: false });
                    breadcrumbs.push({ name: authorName, url: `/authors/${authorSlug}/`, active: true });
                }
                break;

            case 'author-quotes':
                const authorQuotesSlug = this.parseAuthorFromPath();
                if (authorQuotesSlug) {
                    const authorName = this.deslugify(authorQuotesSlug);
                    breadcrumbs.push({ name: 'Authors', url: '/authors/', active: false });
                    breadcrumbs.push({ name: authorName, url: `/authors/${authorQuotesSlug}/`, active: false });
                    breadcrumbs.push({ name: 'Quotes', url: `/authors/${authorQuotesSlug}/quotes/`, active: true });
                }
                break;

            case 'categories-list':
                breadcrumbs.push({ name: 'Categories', url: '/categories/', active: true });
                break;

            case 'category-detail':
                const categorySlug = this.parseCategoryFromPath();
                if (categorySlug) {
                    const categoryName = this.deslugify(categorySlug);
                    breadcrumbs.push({ name: 'Categories', url: '/categories/', active: false });
                    breadcrumbs.push({ name: categoryName, url: `/categories/${categorySlug}/`, active: true });
                }
                break;

            case 'category-quotes':
                const categoryQuotesSlug = this.parseCategoryFromPath();
                if (categoryQuotesSlug) {
                    const categoryName = this.deslugify(categoryQuotesSlug);
                    breadcrumbs.push({ name: 'Categories', url: '/categories/', active: false });
                    breadcrumbs.push({ name: categoryName, url: `/categories/${categoryQuotesSlug}/`, active: false });
                    breadcrumbs.push({ name: 'Quotes', url: `/categories/${categoryQuotesSlug}/quotes/`, active: true });
                }
                break;

            case 'sources-list':
                breadcrumbs.push({ name: 'Sources', url: '/sources/', active: true });
                break;

            case 'source-detail':
                const sourceSlug = this.parseSourceFromPath();
                if (sourceSlug) {
                    const sourceName = this.deslugify(sourceSlug);
                    breadcrumbs.push({ name: 'Sources', url: '/sources/', active: false });
                    breadcrumbs.push({ name: sourceName, url: `/sources/${sourceSlug}/`, active: true });
                }
                break;

            case 'quotes-list':
                breadcrumbs.push({ name: 'Quotes', url: '/quotes/', active: true });
                break;

            case 'quote-detail':
                const quoteId = this.parseQuoteIdFromPath();
                if (quoteId) {
                    breadcrumbs.push({ name: 'Quotes', url: '/quotes/', active: false });
                    breadcrumbs.push({ name: `Quote #${quoteId}`, url: `/quotes/${quoteId}/`, active: true });
                }
                break;
        }

        return breadcrumbs;
    },

    // ==================== 工具方法 ====================

    /**
     * 获取URL中的查询参数
     * @param {string} name - 参数名
     * @returns {string|null} - 参数值，如果不存在则返回null
     */
    getQueryParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },

    /**
     * 获取所有查询参数
     * @returns {Object} - 包含所有查询参数的对象
     */
    getAllQueryParams() {
        const params = {};
        const urlParams = new URLSearchParams(window.location.search);

        for (const [key, value] of urlParams) {
            params[key] = value;
        }

        return params;
    },

    /**
     * 更新URL中的查询参数，不刷新页面
     * @param {string} name - 参数名
     * @param {string} value - 参数值
     */
    updateQueryParam(name, value) {
        const url = new URL(window.location.href);
        url.searchParams.set(name, value);
        window.history.pushState({}, '', url);
    },

    /**
     * 删除URL中的查询参数，不刷新页面
     * @param {string} name - 参数名
     */
    removeQueryParam(name) {
        const url = new URL(window.location.href);
        url.searchParams.delete(name);
        window.history.pushState({}, '', url);
    },

    /**
     * 生成带查询参数的URL
     * @param {string} baseUrl - 基础URL
     * @param {Object} params - 查询参数对象
     * @returns {string} - 完整的URL
     */
    buildUrlWithParams(baseUrl, params = {}) {
        const url = new URL(baseUrl, window.location.origin);

        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined && value !== '') {
                url.searchParams.set(key, value);
            }
        });

        return url.toString();
    },

    /**
     * 获取完整的规范URL
     * @param {string} path - 路径（可选，默认使用当前路径）
     * @returns {string} - 完整的规范URL
     */
    getCanonicalUrl(path = null) {
        const currentPath = path || window.location.pathname;
        return `${this.CONFIG.BASE_URL}${currentPath}`;
    },

    /**
     * 导航到指定URL
     * @param {string} url - 目标URL
     * @param {boolean} replace - 是否替换当前历史记录（默认false）
     */
    navigateTo(url, replace = false) {
        // 检查是否为外部链接
        if (!this.isInternalUrl(url)) {
            window.location.href = url;
            return;
        }

        // 对于内部链接，检查是否需要页面跳转
        const currentPath = window.location.pathname;
        const targetPath = new URL(url, window.location.origin).pathname;

        // 如果是不同的页面，直接跳转
        if (currentPath !== targetPath) {
            window.location.href = url;
            return;
        }

        // 如果是同一页面的不同部分，使用SPA导航
        if (replace) {
            window.history.replaceState({}, '', url);
        } else {
            window.history.pushState({}, '', url);
        }

        // 触发自定义事件，通知页面URL已更改
        window.dispatchEvent(new CustomEvent('urlChanged', {
            detail: { url, pageType: this.getCurrentPageType() }
        }));
    },

    /**
     * 重定向到指定URL（会刷新页面）
     * @param {string} url - 目标URL
     */
    redirectTo(url) {
        window.location.href = url;
    },

    /**
     * 检查URL是否为内部链接
     * @param {string} url - 要检查的URL
     * @returns {boolean} - 是否为内部链接
     */
    isInternalUrl(url) {
        try {
            const urlObj = new URL(url, window.location.origin);
            return urlObj.origin === window.location.origin;
        } catch (e) {
            // 相对URL被认为是内部链接
            return !url.startsWith('http://') && !url.startsWith('https://');
        }
    },

    /**
     * 格式化URL，确保正确的斜杠
     * @param {string} url - 要格式化的URL
     * @returns {string} - 格式化后的URL
     */
    formatUrl(url) {
        if (!url) return '/';

        // 确保以斜杠开头
        if (!url.startsWith('/')) {
            url = '/' + url;
        }

        // 对于目录URL，确保以斜杠结尾
        if (!url.includes('?') && !url.includes('#') && !url.match(/\.[a-z]+$/i)) {
            if (!url.endsWith('/')) {
                url += '/';
            }
        }

        return url;
    },

    // ==================== 向后兼容方法 ====================

    /**
     * 向后兼容：从URL中获取作者名称
     * @deprecated 使用 parseAuthorFromPath() 和 deslugify() 替代
     * @returns {string|null}
     */
    getAuthorNameFromUrl() {
        const slug = this.parseAuthorFromPath();
        return slug ? this.deslugify(slug) : null;
    },

    /**
     * 向后兼容：从URL中获取类别名称
     * @deprecated 使用 parseCategoryFromPath() 和 deslugify() 替代
     * @returns {string|null}
     */
    getCategoryNameFromUrl() {
        const slug = this.parseCategoryFromPath();
        return slug ? this.deslugify(slug) : null;
    },

    /**
     * 向后兼容：从URL中获取来源名称
     * @deprecated 使用 parseSourceFromPath() 和 deslugify() 替代
     * @returns {string|null}
     */
    getSourceNameFromUrl() {
        const slug = this.parseSourceFromPath();
        return slug ? this.deslugify(slug) : null;
    },

    /**
     * 向后兼容：从URL中获取名言ID
     * @deprecated 使用 parseQuoteIdFromPath() 替代
     * @returns {number|null}
     */
    getQuoteIdFromUrl() {
        return this.parseQuoteIdFromPath();
    }
};

// ==================== 初始化和事件监听 ====================

// 监听浏览器前进后退按钮
window.addEventListener('popstate', function(event) {
    // 触发自定义事件，通知页面URL已更改
    window.dispatchEvent(new CustomEvent('urlChanged', {
        detail: {
            url: window.location.href,
            pageType: UrlHandler.getCurrentPageType()
        }
    }));
});

// 导出模块
window.UrlHandler = UrlHandler;

// 如果支持ES6模块，也导出为模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UrlHandler;
}
