/**
 * 统一实体ID映射管理器
 * 为Categories、Authors、Sources提供统一的ID映射和查找服务
 *
 * @version 2.0.0 - 完整数据库映射
 * @date 2025-06-27
 * <AUTHOR>
 * @coverage 100% (17 Categories + 61 Authors + 22 Sources = 100 实体)
 */

// 统一的实体ID映射配置
// 数据来源: Django SQLite数据库 (backend/db.sqlite3)
// 更新时间: 2025-06-27 03:30
// 覆盖率: 100% (17 Categories + 61 Authors + 22 Sources = 100 实体)
const KNOWN_ENTITY_IDS = {
    categories: {
        'art': 13,             // Art
        'business': 14,        // Business
        'education': 10,       // Education
        'friendship': 17,      // Friendship
        'happiness': 12,       // Happiness
        'health': 16,          // Health
        'humor': 9,            // <PERSON>mor
        'inspirational': 1,    // Inspirational
        'leadership': 6,       // Leadership
        'life': 3,             // Life
        'love': 11,            // Love
        'motivational': 2,     // Motivational
        'philosophy': 8,       // Philosophy
        'science': 7,          // Science
        'success': 4,          // Success
        'technology': 15,      // Technology
        'wisdom': 5            // Wisdom
    },
    authors: {
        'albert-einstein': 1,                    // <PERSON>
        'albert-e<PERSON><PERSON>-49': 60,               // <PERSON> 49
        'albert-eins<PERSON>-5': 16,                // <PERSON> <PERSON> 5
        'aristotle-12': 23,                     // <PERSON> 12
        'aristotle-28': 39,                     // <PERSON> 28
        'aristotle-3': 14,                      // <PERSON> 3
        'benjamin-franklin': 9,                 // <PERSON>
        'benjamin-franklin-33': 44,             // <PERSON> <PERSON> 33
        'eleanor-roosevelt-19': 30,             // <PERSON> <PERSON> 19
        'eleanor-roosevelt-22': 33,             // Eleanor Roosevelt 22
        'eleanor-roosevelt-34': 45,             // Eleanor Roosevelt 34
        'eleanor-roosevelt-45': 56,             // Eleanor Roosevelt 45
        'gandhi-27': 38,                        // Gandhi 27
        'gandhi-32': 43,                        // Gandhi 32
        'john-lennon-41': 52,                   // John Lennon 41
        'mahatma-gandhi': 6,                    // Mahatma Gandhi
        'mark-twain': 7,                        // Mark Twain
        'martin-luther-king-jr-11': 22,         // Martin Luther King Jr. 11
        'martin-luther-king-jr-16': 27,         // Martin Luther King Jr. 16
        'martin-luther-king-jr-24': 35,         // Martin Luther King Jr. 24
        'martin-luther-king-jr-47': 58,         // Martin Luther King Jr. 47
        'maya-angelou': 3,                      // Maya Angelou
        'maya-angelou-14': 25,                  // Maya Angelou 14
        'maya-angelou-20': 31,                  // Maya Angelou 20
        'maya-angelou-8': 19,                   // Maya Angelou 8
        'nelson-mandela': 5,                    // Nelson Mandela
        'nelson-mandela-21': 32,                // Nelson Mandela 21
        'nelson-mandela-23': 34,                // Nelson Mandela 23
        'nelson-mandela-25': 36,                // Nelson Mandela 25
        'nelson-mandela-50': 61,                // Nelson Mandela 50
        'nelson-mandela-6': 17,                 // Nelson Mandela 6
        'oscar-wilde': 8,                       // Oscar Wilde
        'oscar-wilde-31': 42,                   // Oscar Wilde 31
        'oscar-wilde-42': 53,                   // Oscar Wilde 42
        'oscar-wilde-7': 18,                    // Oscar Wilde 7
        'steve-jobs': 4,                        // Steve Jobs
        'steve-jobs-30': 41,                    // Steve Jobs 30
        'steve-jobs-39': 50,                    // Steve Jobs 39
        'steve-jobs-40': 51,                    // Steve Jobs 40
        'theodore-roosevelt': 10,               // Theodore Roosevelt
        'theodore-roosevelt-17': 28,            // Theodore Roosevelt 17
        'theodore-roosevelt-26': 37,            // Theodore Roosevelt 26
        'theodore-roosevelt-35': 46,            // Theodore Roosevelt 35
        'theodore-roosevelt-38': 49,            // Theodore Roosevelt 38
        'theodore-roosevelt-46': 57,            // Theodore Roosevelt 46
        'tony-robbins-18': 29,                  // Tony Robbins 18
        'tony-robbins-4': 15,                   // Tony Robbins 4
        'tony-robbins-44': 55,                  // Tony Robbins 44
        'tony-robbins-9': 20,                   // Tony Robbins 9
        'walt-disney': 11,                      // Walt Disney
        'walt-disney-1': 12,                    // Walt Disney 1
        'walt-disney-10': 21,                   // Walt Disney 10
        'walt-disney-36': 47,                   // Walt Disney 36
        'walt-disney-37': 48,                   // Walt Disney 37
        'walt-disney-43': 54,                   // Walt Disney 43
        'winston-churchill': 2,                 // Winston Churchill
        'winston-churchill-13': 24,             // Winston Churchill 13
        'winston-churchill-15': 26,             // Winston Churchill 15
        'winston-churchill-2': 13,              // Winston Churchill 2
        'winston-churchill-29': 40,             // Winston Churchill 29
        'winston-churchill-48': 59              // Winston Churchill 48
    },
    sources: {
        'article': 21,                                      // Article
        'biography': 12,                                    // Biography
        'book': 14,                                         // Book
        'conference': 18,                                   // Conference
        'diary': 17,                                        // Diary
        'documentary': 19,                                  // Documentary
        'essay': 22,                                        // Essay
        'how-to-win-friends-and-influence-people': 4,       // How to Win Friends and Influence People
        'interview': 13,                                    // Interview
        'letter': 16,                                       // Letter
        'long-walk-to-freedom': 6,                          // Long Walk to Freedom
        'nobel-prize-speech': 10,                           // Nobel Prize Speech
        'podcast': 20,                                      // Podcast
        'speech': 15,                                       // Speech
        'stanford-commencement-address': 9,                 // Stanford Commencement Address
        'ted-talk': 11,                                     // TED Talk
        'the-7-habits-of-highly-effective-people': 3,       // The 7 Habits of Highly Effective People
        'the-art-of-war': 1,                               // The Art of War
        'the-autobiography-of-benjamin-franklin': 5,        // The Autobiography of Benjamin Franklin
        'the-picture-of-dorian-gray': 8,                    // The Picture of Dorian Gray
        'the-wit-and-wisdom-of-mark-twain': 7,              // The Wit and Wisdom of Mark Twain
        'think-and-grow-rich': 2                            // Think and Grow Rich
    }
};

/**
 * 实体ID映射管理器类
 */
class EntityIdMapper {
    constructor() {
        // 环境检测
        this.isProduction = window.location.hostname.includes('quotese.com') ||
                           window.location.hostname.includes('api.quotese.com');

        // 选择合适的映射表
        this.mappings = this.isProduction ?
            (window.PRODUCTION_ENTITY_IDS || KNOWN_ENTITY_IDS) :
            KNOWN_ENTITY_IDS;

        // 统计数据
        this.stats = {
            hits: 0,
            misses: 0,
            apiQueries: 0,
            staticHits: 0,
            cacheHits: 0,
            environment: this.isProduction ? 'production' : 'local'
        };

        // 初始化生产环境组件
        if (this.isProduction) {
            this.initProductionComponents();
        }

        console.log(`🎯 EntityIdMapper initialized (${this.stats.environment} environment)`);
        console.log(`📊 Mapping coverage: ${Object.keys(this.mappings.categories || {}).length} categories, ${Object.keys(this.mappings.authors || {}).length} authors, ${Object.keys(this.mappings.sources || {}).length} sources`);
    }

    /**
     * 初始化生产环境组件
     */
    initProductionComponents() {
        // 初始化动态缓存
        if (window.ProductionDynamicCache) {
            this.dynamicCache = new window.ProductionDynamicCache();
            console.log('🌐 Production dynamic cache initialized');
        }

        // 启动热门实体自动收集
        if (window.PRODUCTION_CONFIG?.autoCollection?.enabled) {
            this.startAutoCollection();
        }
    }

    /**
     * 获取已知实体ID
     * @param {string} entityType - 实体类型 (categories, authors, sources)
     * @param {string} slug - 实体slug
     * @returns {number|null} - 实体ID或null
     */
    getKnownId(entityType, slug) {
        const normalizedSlug = slug.toLowerCase();

        // 第一级：静态映射表查找
        const staticId = this.mappings[entityType]?.[normalizedSlug];
        if (staticId !== undefined && staticId !== null) {
            this.stats.hits++;
            this.stats.staticHits++;
            console.log(`✅ Static mapping hit: ${entityType}/${normalizedSlug} → ${staticId}`);
            return staticId;
        }

        // 第二级：动态缓存查找 (仅生产环境)
        if (this.isProduction && this.dynamicCache) {
            const cachedId = this.dynamicCache.get(entityType, normalizedSlug);
            if (cachedId !== null) {
                this.stats.hits++;
                this.stats.cacheHits++;
                console.log(`🎯 Dynamic cache hit: ${entityType}/${normalizedSlug} → ${cachedId}`);
                return cachedId;
            }
        }

        // 第三级：未找到，需要API查询
        this.stats.misses++;
        console.log(`❌ No mapping found: ${entityType}/${normalizedSlug} (will use API fallback)`);
        return null;
    }

    /**
     * 缓存API查询结果 (生产环境)
     */
    cacheApiResult(entityType, slug, id) {
        if (this.isProduction && this.dynamicCache && id) {
            const normalizedSlug = slug.toLowerCase();
            this.dynamicCache.set(entityType, normalizedSlug, id);
            console.log(`💾 Cached API result: ${entityType}/${normalizedSlug} → ${id}`);
        }
    }

    /**
     * 添加新的实体ID映射
     * @param {string} entityType - 实体类型
     * @param {string} slug - 实体slug
     * @param {number} id - 实体ID
     */
    addMapping(entityType, slug, id) {
        if (!this.mappings[entityType]) {
            this.mappings[entityType] = {};
        }
        
        const normalizedSlug = slug.toLowerCase();
        this.mappings[entityType][normalizedSlug] = id;
        
        console.log(`📝 EntityIdMapper: Added mapping ${entityType}/${normalizedSlug} → ${id}`);
    }

    /**
     * 批量更新实体ID映射
     * @param {string} entityType - 实体类型
     * @param {Object} mappings - 映射对象
     */
    updateMappings(entityType, mappings) {
        if (!this.mappings[entityType]) {
            this.mappings[entityType] = {};
        }
        
        this.mappings[entityType] = { ...this.mappings[entityType], ...mappings };
        
        console.log(`📝 EntityIdMapper: Updated ${Object.keys(mappings).length} mappings for ${entityType}`);
    }

    /**
     * 启动热门实体自动收集 (生产环境)
     */
    startAutoCollection() {
        if (!this.isProduction) return;

        console.log('🔄 Starting auto collection of popular entities...');

        // 定期收集热门实体
        setInterval(() => {
            this.collectPopularEntities();
        }, window.PRODUCTION_CONFIG?.autoCollection?.interval || 86400000); // 24小时
    }

    /**
     * 收集热门实体
     */
    async collectPopularEntities() {
        if (!this.dynamicCache) return;

        console.log('📊 Collecting popular entities from cache...');

        const popularEntities = this.analyzePopularEntities();

        // 这里可以实现将热门实体发送到服务器的逻辑
        // 或者更新本地静态映射表

        console.log('🔥 Popular entities analysis:', popularEntities);
    }

    /**
     * 分析热门实体
     */
    analyzePopularEntities() {
        if (!this.dynamicCache) return {};

        const analysis = {
            categories: [],
            authors: [],
            sources: []
        };

        // 分析缓存中的访问频率
        for (const [key, count] of this.dynamicCache.accessCount.entries()) {
            const [type, identifier] = key.split(':');
            if (count >= (window.PRODUCTION_CONFIG?.autoCollection?.minAccessCount || 10)) {
                analysis[type]?.push({ identifier, accessCount: count });
            }
        }

        // 按访问次数排序
        Object.keys(analysis).forEach(type => {
            analysis[type].sort((a, b) => b.accessCount - a.accessCount);
        });

        return analysis;
    }

    /**
     * 获取统计信息
     * @returns {Object} - 统计信息
     */
    getStats() {
        const total = this.stats.hits + this.stats.misses;
        const hitRate = total > 0 ? Math.round((this.stats.hits / total) * 100) : 0;

        const baseStats = {
            ...this.stats,
            total,
            hitRate: `${hitRate}%`
        };

        // 添加生产环境特有统计
        if (this.isProduction && this.dynamicCache) {
            baseStats.dynamicCache = this.dynamicCache.getStats();
        }

        return baseStats;
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = {
            hits: 0,
            misses: 0,
            apiQueries: 0
        };
    }

    /**
     * 获取实体类型的所有映射
     * @param {string} entityType - 实体类型
     * @returns {Object} - 映射对象
     */
    getMappings(entityType) {
        return this.mappings[entityType] || {};
    }

    /**
     * 获取实体类型的映射统计
     * @param {string} entityType - 实体类型
     * @returns {Object} - 映射统计
     */
    getMappingStats(entityType) {
        const mappings = this.getMappings(entityType);
        const total = Object.keys(mappings).length;
        const found = Object.values(mappings).filter(v => v !== null).length;
        const coverage = total > 0 ? Math.round((found / total) * 100) : 0;
        
        return {
            total,
            found,
            missing: total - found,
            coverage: `${coverage}%`
        };
    }

    /**
     * 导出映射表为代码格式
     * @param {string} entityType - 实体类型
     * @returns {string} - 代码字符串
     */
    exportMappingsAsCode(entityType) {
        const mappings = this.getMappings(entityType);
        const stats = this.getMappingStats(entityType);
        
        let code = `// ${entityType.toUpperCase()} ID映射表\n`;
        code += `// 统计: ${stats.found}/${stats.total} 个实体有ID (${stats.coverage})\n`;
        code += `const KNOWN_${entityType.toUpperCase()}_IDS = {\n`;
        
        for (const [key, value] of Object.entries(mappings)) {
            if (value !== null) {
                code += `    '${key}': ${value},\n`;
            } else {
                code += `    '${key}': null, // 待确认\n`;
            }
        }
        
        code += `};\n`;
        
        return code;
    }

    /**
     * 记录API查询统计
     */
    recordApiQuery() {
        this.stats.apiQueries++;
    }

    /**
     * 从智能缓存同步数据到映射表
     * @param {string} cacheKey - 缓存键 (categories, authors, sources)
     */
    syncFromSmartCache(cacheKey) {
        if (!window.entityCache || !window.entityCache[cacheKey]) {
            console.log(`📋 EntityIdMapper: No smart cache found for ${cacheKey}`);
            return;
        }

        const cache = window.entityCache[cacheKey];
        let syncedCount = 0;

        for (const [id, cachedEntity] of cache.entries()) {
            if (cachedEntity.slug) {
                this.addMapping(cacheKey, cachedEntity.slug, cachedEntity.id);
                syncedCount++;
            }
        }

        console.log(`🔄 EntityIdMapper: Synced ${syncedCount} entities from smart cache for ${cacheKey}`);
    }

    /**
     * 从所有智能缓存同步数据
     */
    syncAllFromSmartCache() {
        ['categories', 'authors', 'sources'].forEach(cacheKey => {
            this.syncFromSmartCache(cacheKey);
        });
    }

    /**
     * 获取缓存同步统计
     * @returns {Object} - 缓存同步统计
     */
    getCacheSyncStats() {
        if (!window.entityCache) {
            return { available: false };
        }

        const stats = {
            available: true,
            categories: window.entityCache.categories.size,
            authors: window.entityCache.authors.size,
            sources: window.entityCache.sources.size,
            lastUpdated: window.entityCache.lastUpdated
        };

        stats.total = stats.categories + stats.authors + stats.sources;
        return stats;
    }
}

/**
 * 通用的优先级实体查找函数
 * @param {string} entityType - 实体类型 (categories, authors, sources)
 * @param {string} slug - 实体slug
 * @param {string} name - 实体名称
 * @param {Function} apiMethod - API查询方法
 * @returns {Promise<Object|null>} - 实体对象或null
 */
async function findEntityWithPriority(entityType, slug, name, apiMethod) {
    // 1. 优先级1：已知ID映射表
    const knownId = window.EntityIdMapper.getKnownId(entityType, slug);
    if (knownId) {
        console.log(`🚀 EntityIdMapper: Using known ID for ${entityType} "${name}":`, knownId);
        return {
            id: knownId,
            name: name,
            fromCache: true,
            slug: slug
        };
    }

    // 2. 优先级2：智能缓存检查（来自热门模块数据）
    if (window.getCachedEntityData) {
        const cache = window.entityCache && window.entityCache[entityType];
        if (cache) {
            for (const [id, cachedEntity] of cache.entries()) {
                if (cachedEntity.name === name ||
                    cachedEntity.slug === slug ||
                    window.UrlHandler.slugify(cachedEntity.name) === slug) {

                    console.log(`🚀 EntityIdMapper: Found in smart cache for ${entityType} "${name}":`, cachedEntity);

                    // 自动添加到映射表以供将来使用
                    window.EntityIdMapper.addMapping(entityType, slug, cachedEntity.id);

                    return {
                        id: cachedEntity.id,
                        name: cachedEntity.name,
                        count: cachedEntity.count,
                        fromCache: true,
                        fromSmartCache: true,
                        slug: slug
                    };
                }
            }
        }
    }

    // 3. 优先级3-5：API查询fallback
    const queries = [slug, name, name.toLowerCase()];
    
    for (const query of queries) {
        try {
            window.EntityIdMapper.recordApiQuery();
            console.log(`🔍 EntityIdMapper: Trying API query for ${entityType} with: "${query}"`);
            
            const result = await apiMethod(query);
            if (result) {
                console.log(`✅ EntityIdMapper: Found ${entityType} via API:`, result);

                // 自动添加到映射表以供将来使用
                window.EntityIdMapper.addMapping(entityType, slug, result.id);

                // 缓存到生产环境动态缓存
                window.EntityIdMapper.cacheApiResult(entityType, slug, result.id);

                return result;
            }
        } catch (error) {
            console.warn(`❌ EntityIdMapper: API query failed for ${entityType}/${query}:`, error);
        }
    }
    
    console.error(`❌ EntityIdMapper: All queries failed for ${entityType}/${slug}`);
    return null;
}

// 创建全局实例
window.EntityIdMapper = new EntityIdMapper();

// 导出通用查找函数
window.findEntityWithPriority = findEntityWithPriority;

console.log('📋 EntityIdMapper initialized with mappings:', window.EntityIdMapper.mappings);
