/**
 * 生产环境实体ID映射系统
 * 分层混合映射架构：静态映射 + 动态缓存 + API查询
 * 
 * @version 1.0.0 - 生产环境分层策略
 * @date 2025-06-27
 * <AUTHOR>
 * @scope 生产环境 (284,247个实体)
 * @strategy 热门实体静态映射 + 智能动态缓存
 */

// 生产环境配置常量
const PRODUCTION_CONFIG = {
    // 环境检测
    isProduction: window.location.hostname.includes('quotese.com'),
    
    // 静态映射表配置
    staticMapping: {
        categories: 100,    // Top 100热门类别
        authors: 200,       // Top 200知名作者
        sources: 50         // Top 50热门来源
    },
    
    // 动态缓存配置
    dynamicCache: {
        maxSize: 2000,          // 最大缓存2000个实体
        ttl: 3600000,           // 1小时过期时间
        cleanupInterval: 300000, // 5分钟清理间隔
        hitRateTarget: 0.95     // 目标95%命中率
    },
    
    // 性能目标
    performance: {
        staticLookup: 1,        // 静态映射<1ms
        cacheLookup: 5,         // 缓存查找<5ms
        apiLookup: 100,         // API查询<100ms
        overallHitRate: 0.95    // 整体命中率>95%
    },
    
    // 热门实体自动收集
    autoCollection: {
        enabled: true,
        interval: 86400000,     // 24小时更新一次
        minAccessCount: 10,     // 最少访问10次才加入热门
        maxCollectionSize: 500  // 最多收集500个新热门实体
    }
};

// 生产环境热门实体静态映射表
// 基于实际访问数据的核心实体映射
// 策略：环境隔离 - 生产环境使用分层混合架构
const PRODUCTION_ENTITY_IDS = {
    // 核心类别 (环境隔离策略 - 依赖动态缓存)
    categories: {
        // 所有ID设为null，完全依赖动态缓存和API查询
        // 避免与本地环境的ID冲突，实现环境隔离
        'life': null,               // 依赖动态缓存
        'love': null,               // 依赖动态缓存
        'success': null,            // 依赖动态缓存
        'wisdom': null,             // 依赖动态缓存
        'happiness': null,          // 依赖动态缓存
        'motivation': null,         // 依赖动态缓存
        'inspiration': null,        // 依赖动态缓存
        'leadership': null,         // 依赖动态缓存
        'business': null,           // 依赖动态缓存
        'education': null           // 依赖动态缓存
        // 注意：环境隔离策略 - 所有ID设为null
        // 生产环境完全依赖动态缓存和API查询，避免ID冲突
    },
    
    // 知名作者 (待从生产环境收集真实数据)
    authors: {
        // 注意：所有ID设为null，依赖动态缓存和API查询
        // 这样避免了与本地环境的ID冲突问题
        'albert-einstein': null,        // 待查询真实ID
        'steve-jobs': null,             // 待查询真实ID
        'mark-twain': null,             // 待查询真实ID
        'oscar-wilde': null,            // 待查询真实ID
        'winston-churchill': null,      // 待查询真实ID
        'william-shakespeare': null,    // 待查询真实ID
        'maya-angelou': null,           // 待查询真实ID
        'mahatma-gandhi': null,         // 待查询真实ID
        'nelson-mandela': null,         // 待查询真实ID
        'benjamin-franklin': null       // 待查询真实ID
    },
    
    // 热门来源 (待从生产环境收集真实数据)
    sources: {
        // 注意：所有ID设为null，依赖动态缓存和API查询
        // 这样避免了与本地环境的ID冲突问题
        'interview': null,              // 待查询真实ID
        'speech': null,                 // 待查询真实ID
        'book': null,                   // 待查询真实ID
        'article': null,                // 待查询真实ID
        'biography': null,              // 待查询真实ID
        'essay': null,                  // 待查询真实ID
        'ted-talk': null,               // 待查询真实ID
        'podcast': null,                // 待查询真实ID
        'documentary': null,            // 待查询真实ID
        'conference': null              // 待查询真实ID
    }
};

// 动态缓存管理器
class ProductionDynamicCache {
    constructor() {
        this.cache = new Map();
        this.accessCount = new Map();
        this.lastAccess = new Map();
        this.stats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            updates: 0
        };
        
        // 启动定期清理
        this.startCleanupTimer();
        
        console.log('🌐 ProductionDynamicCache initialized');
    }
    
    /**
     * 获取缓存实体
     */
    get(type, identifier) {
        const key = `${type}:${identifier}`;
        const cached = this.cache.get(key);
        
        if (cached && !this.isExpired(cached)) {
            // 更新访问统计
            this.accessCount.set(key, (this.accessCount.get(key) || 0) + 1);
            this.lastAccess.set(key, Date.now());
            this.stats.hits++;
            
            console.log(`🎯 Cache hit: ${key}`);
            return cached.data;
        }
        
        this.stats.misses++;
        console.log(`❌ Cache miss: ${key}`);
        return null;
    }
    
    /**
     * 设置缓存实体
     */
    set(type, identifier, data) {
        const key = `${type}:${identifier}`;
        
        // 检查缓存大小限制
        if (this.cache.size >= PRODUCTION_CONFIG.dynamicCache.maxSize) {
            this.evictLeastUsed();
        }
        
        this.cache.set(key, {
            data: data,
            timestamp: Date.now(),
            type: type
        });
        
        this.accessCount.set(key, 1);
        this.lastAccess.set(key, Date.now());
        this.stats.updates++;
        
        console.log(`💾 Cache set: ${key}`);
    }
    
    /**
     * 检查是否过期
     */
    isExpired(cached) {
        return (Date.now() - cached.timestamp) > PRODUCTION_CONFIG.dynamicCache.ttl;
    }
    
    /**
     * 驱逐最少使用的实体
     */
    evictLeastUsed() {
        let leastUsedKey = null;
        let leastUsedCount = Infinity;
        let oldestAccess = Infinity;
        
        for (const [key, count] of this.accessCount.entries()) {
            const lastAccessTime = this.lastAccess.get(key) || 0;
            
            if (count < leastUsedCount || 
                (count === leastUsedCount && lastAccessTime < oldestAccess)) {
                leastUsedKey = key;
                leastUsedCount = count;
                oldestAccess = lastAccessTime;
            }
        }
        
        if (leastUsedKey) {
            this.cache.delete(leastUsedKey);
            this.accessCount.delete(leastUsedKey);
            this.lastAccess.delete(leastUsedKey);
            this.stats.evictions++;
            
            console.log(`🗑️ Cache evicted: ${leastUsedKey} (access count: ${leastUsedCount})`);
        }
    }
    
    /**
     * 启动定期清理
     */
    startCleanupTimer() {
        setInterval(() => {
            this.cleanup();
        }, PRODUCTION_CONFIG.dynamicCache.cleanupInterval);
    }
    
    /**
     * 清理过期缓存
     */
    cleanup() {
        let cleanedCount = 0;
        
        for (const [key, cached] of this.cache.entries()) {
            if (this.isExpired(cached)) {
                this.cache.delete(key);
                this.accessCount.delete(key);
                this.lastAccess.delete(key);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            console.log(`🧹 Cache cleanup: removed ${cleanedCount} expired entries`);
        }
    }
    
    /**
     * 获取缓存统计
     */
    getStats() {
        const hitRate = this.stats.hits / (this.stats.hits + this.stats.misses) * 100;
        
        return {
            size: this.cache.size,
            maxSize: PRODUCTION_CONFIG.dynamicCache.maxSize,
            hitRate: hitRate.toFixed(2) + '%',
            ...this.stats
        };
    }
}

// 全局实例
window.ProductionDynamicCache = ProductionDynamicCache;
window.PRODUCTION_CONFIG = PRODUCTION_CONFIG;
window.PRODUCTION_ENTITY_IDS = PRODUCTION_ENTITY_IDS;

console.log('🌐 Production Entity ID Mapper loaded');
console.log('📊 Production config:', PRODUCTION_CONFIG);
console.log('🎯 Static mapping coverage:', {
    categories: Object.keys(PRODUCTION_ENTITY_IDS.categories).length,
    authors: Object.keys(PRODUCTION_ENTITY_IDS.authors).length,
    sources: Object.keys(PRODUCTION_ENTITY_IDS.sources).length
});
