/**
 * URL处理器单元测试
 * 测试重构后的url-handler.js的所有功能
 * 
 * @version 2.0.0
 * @date 2025-06-16
 * <AUTHOR>
 */

// 模拟浏览器环境
const mockWindow = {
    location: {
        pathname: '/',
        search: '',
        href: 'https://quotese.com/',
        origin: 'https://quotese.com'
    },
    history: {
        pushState: jest.fn(),
        replaceState: jest.fn()
    },
    addEventListener: jest.fn(),
    dispatchEvent: jest.fn()
};

// 设置全局window对象
global.window = mockWindow;
global.URL = require('url').URL;
global.URLSearchParams = require('url').URLSearchParams;

// 导入被测试的模块
const UrlHandler = require('./url-handler.js');

describe('UrlHandler', () => {
    
    beforeEach(() => {
        // 重置mock
        jest.clearAllMocks();
        
        // 重置window.location
        mockWindow.location.pathname = '/';
        mockWindow.location.search = '';
        mockWindow.location.href = 'https://quotese.com/';
    });

    // ==================== Slug处理测试 ====================
    
    describe('slugify', () => {
        test('应该正确转换基本文本', () => {
            expect(UrlHandler.slugify('Albert Einstein')).toBe('albert-einstein');
            expect(UrlHandler.slugify('Self-Improvement')).toBe('self-improvement');
            expect(UrlHandler.slugify('Life & Success')).toBe('life-success');
        });

        test('应该处理特殊字符', () => {
            expect(UrlHandler.slugify('100 Best Quotes')).toBe('100-best-quotes');
            expect(UrlHandler.slugify('Hello, World!')).toBe('hello-world');
            expect(UrlHandler.slugify('Test@#$%^&*()')).toBe('test');
        });

        test('应该处理边界情况', () => {
            expect(UrlHandler.slugify('')).toBe('');
            expect(UrlHandler.slugify('   ')).toBe('');
            expect(UrlHandler.slugify(null)).toBe('');
            expect(UrlHandler.slugify(undefined)).toBe('');
        });

        test('应该处理多个空格和连字符', () => {
            expect(UrlHandler.slugify('  multiple   spaces  ')).toBe('multiple-spaces');
            expect(UrlHandler.slugify('--multiple--dashes--')).toBe('multiple-dashes');
        });
    });

    describe('deslugify', () => {
        test('应该正确转换slug回文本', () => {
            expect(UrlHandler.deslugify('albert-einstein')).toBe('Albert Einstein');
            expect(UrlHandler.deslugify('self-improvement')).toBe('Self Improvement');
            expect(UrlHandler.deslugify('100-best-quotes')).toBe('100 Best Quotes');
        });

        test('应该处理边界情况', () => {
            expect(UrlHandler.deslugify('')).toBe('');
            expect(UrlHandler.deslugify(null)).toBe('');
            expect(UrlHandler.deslugify(undefined)).toBe('');
        });
    });

    describe('isValidSlug', () => {
        test('应该验证有效的slug', () => {
            expect(UrlHandler.isValidSlug('albert-einstein')).toBe(true);
            expect(UrlHandler.isValidSlug('self-improvement')).toBe(true);
            expect(UrlHandler.isValidSlug('100-best-quotes')).toBe(true);
            expect(UrlHandler.isValidSlug('single')).toBe(true);
        });

        test('应该拒绝无效的slug', () => {
            expect(UrlHandler.isValidSlug('-invalid')).toBe(false);
            expect(UrlHandler.isValidSlug('invalid-')).toBe(false);
            expect(UrlHandler.isValidSlug('invalid--double')).toBe(false);
            expect(UrlHandler.isValidSlug('Invalid-Case')).toBe(false);
            expect(UrlHandler.isValidSlug('invalid space')).toBe(false);
            expect(UrlHandler.isValidSlug('')).toBe(false);
            expect(UrlHandler.isValidSlug(null)).toBe(false);
        });
    });

    // ==================== URL生成测试 ====================

    describe('URL生成方法', () => {
        test('getAuthorUrl应该生成正确的作者URL', () => {
            const author = { name: 'Albert Einstein', id: 1 };
            expect(UrlHandler.getAuthorUrl(author)).toBe('/authors/albert-einstein/');
        });

        test('getAuthorQuotesUrl应该生成正确的作者名言URL', () => {
            const author = { name: 'Albert Einstein', id: 1 };
            expect(UrlHandler.getAuthorQuotesUrl(author)).toBe('/authors/albert-einstein/quotes/');
        });

        test('getCategoryUrl应该生成正确的类别URL', () => {
            const category = { name: 'Inspirational', id: 1 };
            expect(UrlHandler.getCategoryUrl(category)).toBe('/categories/inspirational/');
        });

        test('getCategoryQuotesUrl应该生成正确的类别名言URL', () => {
            const category = { name: 'Inspirational', id: 1 };
            expect(UrlHandler.getCategoryQuotesUrl(category)).toBe('/categories/inspirational/quotes/');
        });

        test('getSourceUrl应该生成正确的来源URL', () => {
            const source = { name: 'Book Title', id: 1 };
            expect(UrlHandler.getSourceUrl(source)).toBe('/sources/book-title/');
        });

        test('getQuoteUrl应该生成正确的名言URL', () => {
            const quote = { id: 123 };
            expect(UrlHandler.getQuoteUrl(quote)).toBe('/quotes/123/');
        });

        test('getListUrl应该生成正确的列表URL', () => {
            expect(UrlHandler.getListUrl('authors')).toBe('/authors/');
            expect(UrlHandler.getListUrl('categories')).toBe('/categories/');
            expect(UrlHandler.getListUrl('sources')).toBe('/sources/');
            expect(UrlHandler.getListUrl('quotes')).toBe('/quotes/');
        });

        test('getHomeUrl应该返回首页URL', () => {
            expect(UrlHandler.getHomeUrl()).toBe('/');
        });
    });

    describe('URL生成错误处理', () => {
        test('应该在缺少必需参数时抛出错误', () => {
            expect(() => UrlHandler.getAuthorUrl({})).toThrow('Author object must have a name property');
            expect(() => UrlHandler.getAuthorUrl(null)).toThrow('Author object must have a name property');
            expect(() => UrlHandler.getQuoteUrl({})).toThrow('Quote object must have an id property');
            expect(() => UrlHandler.getQuoteUrl({ id: 'invalid' })).toThrow('Quote ID must be a positive number');
        });

        test('应该在无效页面类型时抛出错误', () => {
            expect(() => UrlHandler.getListUrl('invalid')).toThrow('Invalid page type');
        });
    });

    // ==================== URL解析测试 ====================

    describe('URL解析方法', () => {
        test('parseAuthorFromPath应该正确解析作者slug', () => {
            mockWindow.location.pathname = '/authors/albert-einstein/';
            expect(UrlHandler.parseAuthorFromPath()).toBe('albert-einstein');

            mockWindow.location.pathname = '/authors/albert-einstein/quotes/';
            expect(UrlHandler.parseAuthorFromPath()).toBe('albert-einstein');

            mockWindow.location.pathname = '/categories/inspirational/';
            expect(UrlHandler.parseAuthorFromPath()).toBe(null);
        });

        test('parseCategoryFromPath应该正确解析类别slug', () => {
            mockWindow.location.pathname = '/categories/inspirational/';
            expect(UrlHandler.parseCategoryFromPath()).toBe('inspirational');

            mockWindow.location.pathname = '/categories/inspirational/quotes/';
            expect(UrlHandler.parseCategoryFromPath()).toBe('inspirational');

            mockWindow.location.pathname = '/authors/albert-einstein/';
            expect(UrlHandler.parseCategoryFromPath()).toBe(null);
        });

        test('parseSourceFromPath应该正确解析来源slug', () => {
            mockWindow.location.pathname = '/sources/book-title/';
            expect(UrlHandler.parseSourceFromPath()).toBe('book-title');

            mockWindow.location.pathname = '/authors/albert-einstein/';
            expect(UrlHandler.parseSourceFromPath()).toBe(null);
        });

        test('parseQuoteIdFromPath应该正确解析名言ID', () => {
            mockWindow.location.pathname = '/quotes/123/';
            expect(UrlHandler.parseQuoteIdFromPath()).toBe(123);

            mockWindow.location.pathname = '/quotes/invalid/';
            expect(UrlHandler.parseQuoteIdFromPath()).toBe(null);

            mockWindow.location.pathname = '/authors/albert-einstein/';
            expect(UrlHandler.parseQuoteIdFromPath()).toBe(null);
        });
    });

    // ==================== 页面类型检测测试 ====================

    describe('getCurrentPageType', () => {
        test('应该正确识别各种页面类型', () => {
            const testCases = [
                { path: '/', expected: 'home' },
                { path: '/index.html', expected: 'home' },
                { path: '/authors/', expected: 'authors-list' },
                { path: '/authors/albert-einstein/', expected: 'author-detail' },
                { path: '/authors/albert-einstein/quotes/', expected: 'author-quotes' },
                { path: '/categories/', expected: 'categories-list' },
                { path: '/categories/inspirational/', expected: 'category-detail' },
                { path: '/categories/inspirational/quotes/', expected: 'category-quotes' },
                { path: '/sources/', expected: 'sources-list' },
                { path: '/sources/book-title/', expected: 'source-detail' },
                { path: '/quotes/', expected: 'quotes-list' },
                { path: '/quotes/123/', expected: 'quote-detail' },
                { path: '/search/', expected: 'search' },
                { path: '/invalid-path/', expected: 'unknown' }
            ];

            testCases.forEach(({ path, expected }) => {
                mockWindow.location.pathname = path;
                expect(UrlHandler.getCurrentPageType()).toBe(expected);
            });
        });
    });

    // ==================== 面包屑测试 ====================

    describe('getBreadcrumbData', () => {
        test('应该为作者详情页生成正确的面包屑', () => {
            mockWindow.location.pathname = '/authors/albert-einstein/';
            const breadcrumbs = UrlHandler.getBreadcrumbData();
            
            expect(breadcrumbs).toHaveLength(3);
            expect(breadcrumbs[0]).toEqual({ name: 'Home', url: '/', active: false });
            expect(breadcrumbs[1]).toEqual({ name: 'Authors', url: '/authors/', active: false });
            expect(breadcrumbs[2]).toEqual({ name: 'Albert Einstein', url: '/authors/albert-einstein/', active: true });
        });

        test('应该为作者名言列表页生成正确的面包屑', () => {
            mockWindow.location.pathname = '/authors/albert-einstein/quotes/';
            const breadcrumbs = UrlHandler.getBreadcrumbData();
            
            expect(breadcrumbs).toHaveLength(4);
            expect(breadcrumbs[3]).toEqual({ name: 'Quotes', url: '/authors/albert-einstein/quotes/', active: true });
        });
    });

    // ==================== 工具方法测试 ====================

    describe('工具方法', () => {
        test('formatUrl应该正确格式化URL', () => {
            expect(UrlHandler.formatUrl('authors')).toBe('/authors/');
            expect(UrlHandler.formatUrl('/authors')).toBe('/authors/');
            expect(UrlHandler.formatUrl('/authors/')).toBe('/authors/');
            expect(UrlHandler.formatUrl('')).toBe('/');
        });

        test('isInternalUrl应该正确识别内部链接', () => {
            expect(UrlHandler.isInternalUrl('/authors/')).toBe(true);
            expect(UrlHandler.isInternalUrl('https://quotese.com/authors/')).toBe(true);
            expect(UrlHandler.isInternalUrl('https://external.com/page')).toBe(false);
            expect(UrlHandler.isInternalUrl('http://external.com/page')).toBe(false);
        });

        test('getCanonicalUrl应该生成正确的规范URL', () => {
            expect(UrlHandler.getCanonicalUrl('/authors/')).toBe('https://quotese.com/authors/');
            expect(UrlHandler.getCanonicalUrl()).toBe('https://quotese.com/');
        });
    });
});
