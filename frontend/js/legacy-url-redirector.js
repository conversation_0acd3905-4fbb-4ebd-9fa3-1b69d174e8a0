/**
 * Legacy URL Redirector
 * 处理旧URL格式到新语义化URL格式的客户端重定向
 * 作为服务器端重定向的备用方案
 */

class LegacyUrlRedirector {
    constructor() {
        this.redirectRules = {
            // HTML文件重定向规则
            htmlFiles: {
                'index.html': '/',
                'authors.html': '/authors/',
                'categories.html': '/categories/',
                'sources.html': '/sources/',
                'quotes.html': '/quotes/',
                'search.html': '/search/'
            },
            
            // 查询参数重定向规则
            queryParams: {
                'author.html': this.redirectAuthorPage.bind(this),
                'category.html': this.redirectCategoryPage.bind(this),
                'source.html': this.redirectSourcePage.bind(this),
                'quote.html': this.redirectQuotePage.bind(this),
                'search.html': this.redirectSearchPage.bind(this)
            }
        };
        
        this.init();
    }
    
    /**
     * 初始化重定向检查
     */
    init() {
        // 只在页面加载时执行一次
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.checkAndRedirect());
        } else {
            this.checkAndRedirect();
        }
    }
    
    /**
     * 检查当前URL并执行重定向
     */
    checkAndRedirect() {
        const currentUrl = window.location;
        const pathname = currentUrl.pathname;
        const search = currentUrl.search;
        const filename = pathname.split('/').pop();
        
        // 记录重定向尝试
        console.log('[LegacyUrlRedirector] Checking URL:', currentUrl.href);
        
        try {
            // 检查HTML文件重定向
            if (this.shouldRedirectHtmlFile(filename)) {
                this.performRedirect(this.redirectRules.htmlFiles[filename], 'HTML file redirect');
                return;
            }
            
            // 检查查询参数重定向
            if (search && this.redirectRules.queryParams[filename]) {
                const newUrl = this.redirectRules.queryParams[filename](search);
                if (newUrl) {
                    this.performRedirect(newUrl, 'Query parameter redirect');
                    return;
                }
            }
            
            // 检查旧URL模式
            const legacyPatternRedirect = this.checkLegacyPatterns(pathname);
            if (legacyPatternRedirect) {
                this.performRedirect(legacyPatternRedirect, 'Legacy pattern redirect');
                return;
            }
            
            console.log('[LegacyUrlRedirector] No redirect needed');
            
        } catch (error) {
            console.error('[LegacyUrlRedirector] Error during redirect check:', error);
        }
    }
    
    /**
     * 检查是否需要重定向HTML文件
     */
    shouldRedirectHtmlFile(filename) {
        return this.redirectRules.htmlFiles.hasOwnProperty(filename);
    }
    
    /**
     * 重定向作者页面
     */
    redirectAuthorPage(search) {
        const params = new URLSearchParams(search);
        const name = params.get('name');
        const id = params.get('id');
        
        if (name) {
            // 使用UrlHandler的slugify函数（如果可用）
            const slug = window.UrlHandler ? 
                window.UrlHandler.slugify(name) : 
                this.slugify(name);
            return `/authors/${slug}/`;
        } else if (id) {
            // 只有ID时重定向到列表页
            return '/authors/';
        }
        
        return null;
    }
    
    /**
     * 重定向类别页面
     */
    redirectCategoryPage(search) {
        const params = new URLSearchParams(search);
        const name = params.get('name');
        const id = params.get('id');
        
        if (name) {
            const slug = window.UrlHandler ? 
                window.UrlHandler.slugify(name) : 
                this.slugify(name);
            return `/categories/${slug}/`;
        } else if (id) {
            return '/categories/';
        }
        
        return null;
    }
    
    /**
     * 重定向来源页面
     */
    redirectSourcePage(search) {
        const params = new URLSearchParams(search);
        const name = params.get('name');
        const id = params.get('id');
        
        if (name) {
            const slug = window.UrlHandler ? 
                window.UrlHandler.slugify(name) : 
                this.slugify(name);
            return `/sources/${slug}/`;
        } else if (id) {
            return '/sources/';
        }
        
        return null;
    }
    
    /**
     * 重定向名言页面
     */
    redirectQuotePage(search) {
        const params = new URLSearchParams(search);
        const id = params.get('id');
        
        if (id && /^\d+$/.test(id)) {
            return `/quotes/${id}/`;
        }
        
        return '/quotes/';
    }
    
    /**
     * 重定向搜索页面
     */
    redirectSearchPage(search) {
        const params = new URLSearchParams(search);
        const query = params.get('q');
        
        if (query) {
            return `/search/?q=${encodeURIComponent(query)}`;
        }
        
        return '/search/';
    }
    
    /**
     * 检查旧URL模式
     */
    checkLegacyPatterns(pathname) {
        // 检查ID-name格式：/authors/albert-einstein-1/ → /authors/albert-einstein/
        const idNamePattern = /^\/(?:authors|categories|sources)\/([^\/]+)-(\d+)\/?$/;
        const idNameMatch = pathname.match(idNamePattern);
        if (idNameMatch) {
            const [, name] = idNameMatch;
            const basePath = pathname.split('/')[1];
            return `/${basePath}/${name}/`;
        }
        
        // 检查名言旧格式：/quotes/quote-123/ → /quotes/123/
        const quotePattern = /^\/quotes\/quote-(\d+)\/?$/;
        const quoteMatch = pathname.match(quotePattern);
        if (quoteMatch) {
            const [, id] = quoteMatch;
            return `/quotes/${id}/`;
        }
        
        // 检查缺少尾部斜杠的目录
        const directoryPattern = /^\/(?:authors|categories|sources|quotes)$/;
        if (directoryPattern.test(pathname)) {
            return pathname + '/';
        }
        
        return null;
    }
    
    /**
     * 执行重定向
     */
    performRedirect(newUrl, reason) {
        console.log(`[LegacyUrlRedirector] Redirecting to: ${newUrl} (${reason})`);
        
        // 使用replace而不是assign，避免在浏览器历史中留下旧URL
        window.location.replace(newUrl);
    }
    
    /**
     * 简单的slugify函数（备用）
     */
    slugify(text) {
        return text
            .toLowerCase()
            .trim()
            .replace(/[^\w\s-]/g, '') // 移除特殊字符
            .replace(/[\s_-]+/g, '-') // 替换空格和下划线为连字符
            .replace(/^-+|-+$/g, ''); // 移除开头和结尾的连字符
    }
    
    /**
     * 静态方法：检查是否需要重定向
     */
    static needsRedirect() {
        const pathname = window.location.pathname;
        const search = window.location.search;
        const filename = pathname.split('/').pop();
        
        // 检查HTML文件
        const htmlFiles = ['index.html', 'authors.html', 'categories.html', 'sources.html', 'quotes.html', 'search.html'];
        if (htmlFiles.includes(filename)) {
            return true;
        }
        
        // 检查查询参数页面
        const queryParamPages = ['author.html', 'category.html', 'source.html', 'quote.html'];
        if (search && queryParamPages.includes(filename)) {
            return true;
        }
        
        // 检查旧URL模式
        const legacyPatterns = [
            /^\/(?:authors|categories|sources)\/[^\/]+-\d+\/?$/,
            /^\/quotes\/quote-\d+\/?$/,
            /^\/(?:authors|categories|sources|quotes)$/
        ];
        
        return legacyPatterns.some(pattern => pattern.test(pathname));
    }
}

// 自动初始化（如果在浏览器环境中）
if (typeof window !== 'undefined') {
    // 创建全局实例
    window.LegacyUrlRedirector = new LegacyUrlRedirector();
    
    // 提供静态检查方法
    window.LegacyUrlRedirector.needsRedirect = LegacyUrlRedirector.needsRedirect;
}

// 导出类（用于模块化环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LegacyUrlRedirector;
}
