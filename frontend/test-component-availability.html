<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Availability Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Component Availability Test</h1>
    <div id="results"></div>

    <!-- Include the same scripts as quote.html -->
    <script src="/js/debug.js?v=20250626"></script>
    <script src="/js/component-loader.js?v=20250626"></script>
    <script src="/js/mock-data.js?v=20250626"></script>
    <script src="/js/api-client.js?v=20250626"></script>
    <script src="/js/theme.js?v=20250626"></script>
    <script src="/js/url-handler.js?v=20250626"></script>
    <script src="/js/seo-manager.js?v=20250626"></script>
    <script src="/js/page-router.js?v=20250626"></script>
    <script src="/js/mobile-menu.js?v=20250626"></script>
    <script src="/js/components/breadcrumb.js?v=20250626"></script>
    <script src="/js/components/quote-card.js?v=20250626"></script>
    <script src="/js/social-meta.js?v=20250626"></script>
    <script src="/js/global-fix.js?v=20250626"></script>

    <script>
        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            results.appendChild(div);
        }

        function testComponents() {
            addResult('info', 'Testing component availability...');
            
            // Test QuoteCardComponent
            if (typeof QuoteCardComponent !== 'undefined') {
                addResult('success', 'QuoteCardComponent is defined');
                
                if (typeof QuoteCardComponent.render === 'function') {
                    addResult('success', 'QuoteCardComponent.render is a function');
                    
                    // Test rendering with sample data
                    try {
                        const sampleQuote = {
                            id: '1',
                            content: 'Test quote content',
                            author: { id: '1', name: 'Test Author' },
                            categories: [{ id: '1', name: 'Test' }],
                            sources: [{ id: '1', name: 'Test Source' }],
                            createdAt: new Date().toISOString()
                        };
                        
                        const card = QuoteCardComponent.render(sampleQuote, 0, {
                            isDetailPage: true,
                            showActions: true
                        });
                        
                        if (card) {
                            addResult('success', 'QuoteCardComponent.render works correctly');
                            
                            // Add the card to the page
                            const container = document.createElement('div');
                            container.innerHTML = '<h3>Sample Rendered Card:</h3>';
                            container.appendChild(card);
                            document.body.appendChild(container);
                        } else {
                            addResult('error', 'QuoteCardComponent.render returned null/undefined');
                        }
                    } catch (error) {
                        addResult('error', `QuoteCardComponent.render threw error: ${error.message}`);
                    }
                } else {
                    addResult('error', 'QuoteCardComponent.render is not a function');
                }
            } else {
                addResult('error', 'QuoteCardComponent is undefined');
            }
            
            // Test other components
            const components = ['UrlHandler', 'ApiClient', 'QuoteseAPIMode'];
            components.forEach(name => {
                if (typeof window[name] !== 'undefined') {
                    addResult('success', `${name} is available`);
                } else {
                    addResult('error', `${name} is undefined`);
                }
            });
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testComponents, 1000); // Wait for all scripts to load
        });
    </script>
</body>
</html>
