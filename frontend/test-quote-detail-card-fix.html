<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Detail Card Fix Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .page-link {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .page-link:hover { background-color: #218838; }
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Quote Detail Card Fix Verification</h1>
    <p>This tool verifies that the quote card component now works correctly on quote detail pages.</p>

    <div class="test-section">
        <h2>Fix Summary</h2>
        <div class="info test-result">
            <strong>Issues Fixed:</strong>
            <ul>
                <li>✅ Replaced static HTML structure with dynamic QuoteCardComponent</li>
                <li>✅ Added isDetailPage configuration to QuoteCardComponent</li>
                <li>✅ Implemented larger font sizes for detail page display</li>
                <li>✅ Added fallback rendering method for error handling</li>
                <li>✅ Disabled click navigation on detail page cards</li>
                <li>✅ Enhanced styling for detail page presentation</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>Page Comparison Test</h2>
        <div class="comparison-container">
            <div class="comparison-item">
                <h3>Quote Listing Page</h3>
                <p>Quote cards with standard styling and click navigation</p>
                <a href="/quotes/?use-production-api=true" target="_blank" class="page-link">Open Quotes List</a>
            </div>
            <div class="comparison-item">
                <h3>Quote Detail Page</h3>
                <p>Quote card with enhanced styling for individual display</p>
                <a href="/quotes/499276/?use-production-api=true" target="_blank" class="page-link">Open Quote 499276</a>
            </div>
        </div>
        <button onclick="comparePageTypes()">🔍 Compare Page Types</button>
        <div id="comparison-results"></div>
    </div>

    <div class="test-section">
        <h2>Quote Detail Card Component Test</h2>
        <button onclick="testQuoteDetailCard()">🧪 Test Quote Detail Card</button>
        <div id="quote-detail-card-results"></div>
    </div>

    <div class="test-section">
        <h2>Multiple Quote IDs Test</h2>
        <div>
            <h3>Test Different Quote IDs</h3>
            <a href="/quotes/499276/?use-production-api=true" target="_blank" class="page-link">Quote 499276</a>
            <a href="/quotes/499001/?use-production-api=true" target="_blank" class="page-link">Quote 499001</a>
            <a href="/quotes/499002/?use-production-api=true" target="_blank" class="page-link">Quote 499002</a>
            <a href="/quotes/499003/?use-production-api=true" target="_blank" class="page-link">Quote 499003</a>
        </div>
        <button onclick="testMultipleQuoteIds()">🔄 Test Multiple Quote IDs</button>
        <div id="multiple-quotes-results"></div>
    </div>

    <div class="test-section">
        <h2>Component Configuration Test</h2>
        <button onclick="testComponentConfigurations()">⚙️ Test Component Configurations</button>
        <div id="component-config-results"></div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/components/quote-card.js"></script>

    <script>
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
        }

        async function comparePageTypes() {
            const container = 'comparison-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Comparing quote card functionality between page types...');
            
            try {
                // Switch to production API
                if (window.QuoteseAPIMode && typeof window.QuoteseAPIMode.useProductionAPI === 'function') {
                    window.QuoteseAPIMode.useProductionAPI();
                }
                
                // Test quotes listing page structure
                const listingResponse = await fetch('/quotes/?use-production-api=true');
                if (listingResponse.ok) {
                    const listingHtml = await listingResponse.text();
                    const hasQuotesContainer = listingHtml.includes('id="quotes-list-container"');
                    const hasQuotesScript = listingHtml.includes('quotes.js');
                    
                    if (hasQuotesContainer && hasQuotesScript) {
                        addResult(container, 'success', 'Quotes listing page: Structure correct');
                    } else {
                        addResult(container, 'error', 'Quotes listing page: Missing required elements');
                    }
                } else {
                    addResult(container, 'error', `Quotes listing page: HTTP ${listingResponse.status}`);
                }
                
                // Test quote detail page structure
                const detailResponse = await fetch('/quotes/499276/?use-production-api=true');
                if (detailResponse.ok) {
                    const detailHtml = await detailResponse.text();
                    const hasQuoteCardContent = detailHtml.includes('id="quote-card-content"');
                    const hasQuoteScript = detailHtml.includes('quote.js');
                    
                    if (hasQuoteCardContent && hasQuoteScript) {
                        addResult(container, 'success', 'Quote detail page: Structure updated correctly');
                    } else {
                        addResult(container, 'error', 'Quote detail page: Missing required elements');
                    }
                } else {
                    addResult(container, 'error', `Quote detail page: HTTP ${detailResponse.status}`);
                }
                
            } catch (error) {
                addResult(container, 'error', `Comparison test failed: ${error.message}`);
            }
        }

        async function testQuoteDetailCard() {
            const container = 'quote-detail-card-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing quote detail card component...');
            
            try {
                // Switch to production API
                if (window.QuoteseAPIMode && typeof window.QuoteseAPIMode.useProductionAPI === 'function') {
                    window.QuoteseAPIMode.useProductionAPI();
                }
                
                // Get test quote data
                const quote = await window.ApiClient.getQuoteById('499276');
                if (!quote) {
                    addResult(container, 'error', 'Failed to load test quote data');
                    return;
                }
                
                addResult(container, 'success', `Test quote loaded: "${quote.content.substring(0, 50)}..."`);
                
                // Test standard quote card
                const standardCard = QuoteCardComponent.render(quote, 0, {
                    showActions: true,
                    showAuthorAvatar: true
                });
                
                if (standardCard) {
                    addResult(container, 'success', 'Standard quote card rendered successfully');
                } else {
                    addResult(container, 'error', 'Standard quote card rendering failed');
                }
                
                // Test detail page quote card
                const detailCard = QuoteCardComponent.render(quote, 0, {
                    showActions: true,
                    showAuthorAvatar: true,
                    showCategories: true,
                    showSources: true,
                    showDate: true,
                    isDetailPage: true,
                    containerClass: 'quote-detail-card'
                });
                
                if (detailCard) {
                    addResult(container, 'success', 'Detail page quote card rendered successfully');
                    
                    // Check for detail page specific features
                    const hasLargeText = detailCard.innerHTML.includes('text-xl sm:text-2xl md:text-3xl lg:text-4xl');
                    const hasH1Tag = detailCard.innerHTML.includes('<h1');
                    const hasDetailStyling = detailCard.classList.contains('quote-detail-card');
                    
                    if (hasLargeText) {
                        addResult(container, 'success', 'Detail page: Large text styling applied');
                    } else {
                        addResult(container, 'warning', 'Detail page: Large text styling not found');
                    }
                    
                    if (hasH1Tag) {
                        addResult(container, 'success', 'Detail page: H1 tag used for content');
                    } else {
                        addResult(container, 'warning', 'Detail page: H1 tag not found');
                    }
                    
                    if (hasDetailStyling) {
                        addResult(container, 'success', 'Detail page: Custom styling applied');
                    } else {
                        addResult(container, 'warning', 'Detail page: Custom styling not found');
                    }
                    
                    // Add the card to the page for visual inspection
                    const testContainer = document.createElement('div');
                    testContainer.innerHTML = '<h4>Generated Detail Page Quote Card (for visual inspection):</h4>';
                    testContainer.appendChild(detailCard);
                    document.getElementById(container).appendChild(testContainer);
                    
                } else {
                    addResult(container, 'error', 'Detail page quote card rendering failed');
                }
                
            } catch (error) {
                addResult(container, 'error', `Quote detail card test failed: ${error.message}`);
            }
        }

        async function testMultipleQuoteIds() {
            const container = 'multiple-quotes-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing multiple quote IDs...');
            
            const testIds = ['499276', '499001', '499002', '499003'];
            
            for (const id of testIds) {
                try {
                    // Test page accessibility
                    const response = await fetch(`/quotes/${id}/?use-production-api=true`);
                    if (response.ok) {
                        addResult(container, 'success', `Quote ${id}: Page accessible`);
                        
                        // Test API data
                        const quote = await window.ApiClient.getQuoteById(id);
                        if (quote) {
                            addResult(container, 'success', 
                                `Quote ${id}: Data loaded - "${quote.content.substring(0, 30)}..."`);
                            
                            // Test component rendering
                            const card = QuoteCardComponent.render(quote, 0, { isDetailPage: true });
                            if (card) {
                                addResult(container, 'success', `Quote ${id}: Component rendered successfully`);
                            } else {
                                addResult(container, 'error', `Quote ${id}: Component rendering failed`);
                            }
                        } else {
                            addResult(container, 'warning', `Quote ${id}: No data returned from API`);
                        }
                    } else {
                        addResult(container, 'error', `Quote ${id}: HTTP ${response.status}`);
                    }
                } catch (error) {
                    addResult(container, 'error', `Quote ${id}: ${error.message}`);
                }
            }
        }

        function testComponentConfigurations() {
            const container = 'component-config-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing component configurations...');
            
            // Test sample quote data
            const sampleQuote = {
                id: '499276',
                content: 'This is a test quote for configuration testing.',
                author: { id: '1', name: 'Test Author' },
                categories: [{ id: '1', name: 'Test Category' }],
                sources: [{ id: '1', name: 'Test Source' }],
                createdAt: new Date().toISOString()
            };
            
            const configurations = [
                { name: 'Standard Card', config: { showActions: false } },
                { name: 'Card with Actions', config: { showActions: true } },
                { name: 'Card with Avatar', config: { showAuthorAvatar: true } },
                { name: 'Detail Page Card', config: { isDetailPage: true, showActions: true } },
                { name: 'Full Featured Card', config: { 
                    isDetailPage: true, 
                    showActions: true, 
                    showAuthorAvatar: true,
                    showCategories: true,
                    showSources: true,
                    showDate: true
                }}
            ];
            
            configurations.forEach(test => {
                try {
                    const card = QuoteCardComponent.render(sampleQuote, 0, test.config);
                    if (card) {
                        addResult(container, 'success', `${test.name}: Rendered successfully`);
                        
                        // Check specific features
                        if (test.config.isDetailPage) {
                            const hasLargeText = card.innerHTML.includes('text-xl sm:text-2xl');
                            if (hasLargeText) {
                                addResult(container, 'success', `${test.name}: Detail page styling applied`);
                            }
                        }
                        
                        if (test.config.showActions) {
                            const hasActions = card.innerHTML.includes('quote-share-btn');
                            if (hasActions) {
                                addResult(container, 'success', `${test.name}: Actions displayed`);
                            }
                        }
                    } else {
                        addResult(container, 'error', `${test.name}: Rendering failed`);
                    }
                } catch (error) {
                    addResult(container, 'error', `${test.name}: ${error.message}`);
                }
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                addResult('quote-detail-card-results', 'info', 
                    'Quote detail card fix verification tool ready. Click the test buttons to begin.');
            }, 1000);
        });
    </script>
</body>
</html>
