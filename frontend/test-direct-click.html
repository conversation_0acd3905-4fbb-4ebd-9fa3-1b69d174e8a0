<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Click Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-card { 
            border: 1px solid #ccc; 
            padding: 20px; 
            margin: 10px 0; 
            cursor: pointer;
            background: #f9f9f9;
        }
        .test-card:hover { background: #e9e9e9; }
        .result { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Direct Click Test</h1>
    
    <div class="result info">
        <strong>Test Purpose:</strong> Verify quote card click functionality without external dependencies
    </div>

    <div id="test-results"></div>

    <h2>Test Cards</h2>
    <div id="test-cards-container"></div>

    <!-- Include minimal scripts -->
    <script src="js/config.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/components/quote-card.js"></script>

    <script>
        function addResult(type, message) {
            const container = document.getElementById('test-results');
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
        }

        // Switch to production API
        if (typeof window.QuoteseAPIMode !== 'undefined') {
            window.QuoteseAPIMode.useProductionAPI();
            addResult('info', 'Switched to production API');
        }

        async function runDirectTest() {
            try {
                addResult('info', 'Starting direct click test...');

                // Create a simple test quote
                const testQuote = {
                    id: '499001',
                    content: 'This is a test quote for click functionality.',
                    author: { id: '1', name: 'Test Author' },
                    categories: [{ id: '1', name: 'Test Category' }],
                    sources: []
                };

                // Test 1: Create quote card with QuoteCardComponent
                addResult('info', 'Test 1: Creating quote card with QuoteCardComponent...');
                
                const quoteCard = QuoteCardComponent.render(testQuote, 0, {
                    showActions: false,
                    showAuthorAvatar: false
                });

                // Add click tracking
                let clickCount = 0;
                quoteCard.addEventListener('click', (e) => {
                    clickCount++;
                    addResult('success', `Quote card clicked! Count: ${clickCount}`);
                    addResult('info', `Click target: ${e.target.tagName} - ${e.target.className}`);
                    
                    // Test URL generation
                    try {
                        const url = UrlHandler.getQuoteUrl({ id: testQuote.id });
                        addResult('success', `Generated URL: ${url}`);
                    } catch (error) {
                        addResult('error', `URL generation failed: ${error.message}`);
                    }
                });

                document.getElementById('test-cards-container').appendChild(quoteCard);
                addResult('success', 'Quote card created and added to page');

                // Test 2: Check card properties
                addResult('info', 'Test 2: Checking card properties...');
                
                const hasPointer = quoteCard.classList.contains('cursor-pointer');
                const quoteId = quoteCard.getAttribute('data-quote-id');
                
                addResult(hasPointer ? 'success' : 'error', 
                    `Cursor pointer class: ${hasPointer ? 'Present' : 'Missing'}`);
                addResult(quoteId ? 'success' : 'error', 
                    `Quote ID attribute: ${quoteId || 'Missing'}`);

                // Test 3: Create manual test card
                addResult('info', 'Test 3: Creating manual test card...');
                
                const manualCard = document.createElement('div');
                manualCard.className = 'test-card';
                manualCard.innerHTML = `
                    <h3>Manual Test Card</h3>
                    <p>Click this card to test basic click functionality</p>
                    <p>Quote ID: ${testQuote.id}</p>
                `;
                
                manualCard.addEventListener('click', (e) => {
                    addResult('success', 'Manual test card clicked!');
                    const url = `/quotes/${testQuote.id}/`;
                    addResult('info', `Would navigate to: ${url}`);
                });

                document.getElementById('test-cards-container').appendChild(manualCard);
                addResult('success', 'Manual test card created');

                // Test 4: Test API call
                addResult('info', 'Test 4: Testing API call...');
                
                try {
                    const quote = await window.ApiClient.getQuoteById(testQuote.id);
                    if (quote) {
                        addResult('success', `API call successful: "${quote.content.substring(0, 50)}..."`);
                    } else {
                        addResult('error', 'API call returned null');
                    }
                } catch (error) {
                    addResult('error', `API call failed: ${error.message}`);
                }

                addResult('success', 'All tests completed. Click the cards above to test functionality.');

            } catch (error) {
                addResult('error', `Test failed: ${error.message}`);
            }
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runDirectTest, 1000);
        });

        // Global click tracker
        document.addEventListener('click', (e) => {
            console.log('Global click detected:', e.target);
        });
    </script>
</body>
</html>
