<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspirational Quote | Wisdom Collection - quotese.com</title>
    <meta name="description" content="Explore this profound quote and its context. Discover wisdom, inspiration, and insights from history's most influential thinkers.">
    <meta name="keywords" content="inspirational quote, wisdom quote, famous saying, thought-provoking quote, life quote">
    <link rel="canonical" href="https://quotese.com/quotes/">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Inspirational Quote | Wisdom Collection - quotese.com">
    <meta property="og:description" content="Explore this profound quote and its context. Discover wisdom, inspiration, and insights from history's most influential thinkers.">
    <meta property="og:image" content="https://quotese.com/images/og-image-quote.jpg">
    <meta property="og:url" content="https://quotese.com/quotes/">
    <meta property="og:type" content="article">
    <meta property="og:site_name" content="quotese.com">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@quotesecom">
    <meta name="twitter:title" content="Inspirational Quote | Wisdom Collection">
    <meta name="twitter:description" content="Explore this profound quote and its context. Discover wisdom, inspiration, and insights from history's most influential thinkers.">
    <meta name="twitter:image" content="https://quotese.com/images/og-image-quote.jpg">
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif:wght@400;500;600;700&family=Noto+Sans:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
    <!-- Google Analytics -->
    <script src="/js/analytics.js?v=20250626"></script>
</head>
<body class="light-mode">
    <!-- 导航栏 (将由组件加载器加载) -->
    <header id="navigation-container" role="banner"></header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-8" role="main">
        <!-- 面包屑导航 -->
        <div id="breadcrumb-container"></div>
        <!-- Quote Card -->
        <section class="mb-12 fade-in" id="quote-card-container">
            <div class="max-w-4xl mx-auto">
                <!-- Quote card will be dynamically generated here -->
                <div id="quote-card-content">
                    <!-- Loading state -->
                    <div class="relative p-6 sm:p-8 md:p-10 bg-gradient-to-br from-yellow-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700" id="quote-loading">
                        <div class="flex justify-center items-center py-8">
                            <div class="loading-spinner"></div>
                            <span class="ml-3 text-gray-600 dark:text-gray-300">Loading quote...</span>
                        </div>
                    </div>

                    <!-- Error state (hidden by default) -->
                    <div class="hidden relative p-6 sm:p-8 md:p-10 bg-gradient-to-br from-red-50 to-white dark:from-red-900 dark:to-gray-900 rounded-xl shadow-lg border border-red-200 dark:border-red-700" id="quote-error">
                        <div class="text-center py-8">
                            <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-4"></i>
                            <h2 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2">Quote Not Found</h2>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">The quote you're looking for doesn't exist or has been removed.</p>
                            <div class="space-x-4">
                                <a href="/" class="inline-block px-6 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors">
                                    Go Home
                                </a>
                                <button class="px-6 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors" onclick="window.history.back()">
                                    Go Back
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Content Grid (Left-Right Layout) -->
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Left Column (Related Quotes) -->
            <section class="lg:w-2/3">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fas fa-quote-right text-yellow-500 mr-2" aria-hidden="true"></i>
                    More Quotes by <span id="author-name-heading">this Author</span>
                </h2>

                <div id="related-quotes-container">
                    <!-- Related quotes will be loaded here -->
                    <div class="flex justify-center py-12" id="related-quotes-loading">
                        <div class="loading-spinner" role="status">
                            <span class="sr-only">Loading quotes...</span>
                        </div>
                    </div>

                    <!-- Error state (hidden by default) -->
                    <div class="hidden text-center py-12" id="related-quotes-error">
                        <div class="text-gray-500 dark:text-gray-400">
                            <i class="fas fa-exclamation-triangle text-yellow-500 text-2xl mb-2"></i>
                            <p>Unable to load related quotes.</p>
                            <button class="mt-2 px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors" onclick="window.location.reload()">
                                Try Again
                            </button>
                        </div>
                    </div>

                    <!-- Empty state (hidden by default) -->
                    <div class="hidden text-center py-12" id="related-quotes-empty">
                        <div class="text-gray-500 dark:text-gray-400">
                            <i class="fas fa-quote-right text-yellow-500 text-2xl mb-2"></i>
                            <p>No other quotes found by this author.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Right Column (Sidebar) -->
            <aside class="lg:w-1/3" role="complementary" aria-label="Sidebar">
                <!-- Popular Categories Section -->
                <section class="card-container mb-8 p-6" aria-labelledby="popular-categories-heading">
                    <h3 id="popular-categories-heading" class="text-xl font-bold mb-4 flex items-center">
                        <i class="fas fa-tags text-yellow-500 mr-2" aria-hidden="true"></i>
                        Popular Categories
                    </h3>
                    <div class="flex flex-wrap gap-2" id="categories-container" role="list" aria-busy="true" aria-label="Popular categories list">
                        <!-- Loading spinner (will be replaced by categories) -->
                        <div class="w-full flex justify-center py-4">
                            <div class="loading-spinner" role="status">
                                <span class="sr-only">Loading categories...</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Popular Authors Section -->
                <section class="card-container mb-8 p-6" aria-labelledby="popular-authors-heading">
                    <h3 id="popular-authors-heading" class="text-xl font-bold mb-4 flex items-center">
                        <i class="fas fa-user-pen text-yellow-500 mr-2" aria-hidden="true"></i>
                        Popular Authors
                    </h3>
                    <ul class="space-y-2" id="authors-container" role="list" aria-busy="true" aria-label="Popular authors list">
                        <!-- Loading spinner (will be replaced by authors) -->
                        <div class="w-full flex justify-center py-4">
                            <div class="loading-spinner" role="status">
                                <span class="sr-only">Loading authors...</span>
                            </div>
                        </div>
                    </ul>
                </section>

                <!-- Popular Sources Section -->
                <section class="card-container p-6" aria-labelledby="popular-sources-heading">
                    <h3 id="popular-sources-heading" class="text-xl font-bold mb-4 flex items-center">
                        <i class="fas fa-book text-yellow-500 mr-2" aria-hidden="true"></i>
                        Popular Sources
                    </h3>
                    <ul class="space-y-2" id="sources-container" role="list" aria-busy="true" aria-label="Popular sources list">
                        <!-- Loading spinner (will be replaced by sources) -->
                        <div class="w-full flex justify-center py-4">
                            <div class="loading-spinner" role="status">
                                <span class="sr-only">Loading sources...</span>
                            </div>
                        </div>
                    </ul>
                </section>

                <!-- Popular Topics Container (for additional components) -->
                <div id="popular-topics-container">
                    <!-- Additional popular topics component will be loaded here -->
                </div>
            </aside>
        </div>
    </main>

    <!-- Footer (will be loaded by component loader) -->
    <footer id="footer-container" role="contentinfo"></footer>

    <!-- JavaScript -->
    <!-- Debug Script -->
    <script src="/js/debug.js?v=20250629"></script>

    <!-- Component Loader -->
    <script src="/js/component-loader.js?v=20250629"></script>

    <!-- Mock Data -->
    <script src="/js/mock-data.js?v=20250629"></script>

    <!-- API Client -->
    <script src="/js/api-client.js?v=20250629"></script>

    <!-- Core Modules -->
    <script src="/js/theme.js?v=20250629"></script>
    <script src="/js/url-handler.js?v=20250629"></script>
    <script src="/js/entity-id-mapper-production.js?v=20250629"></script>
    <script src="/js/entity-id-mapper.js?v=20250629"></script>
    <script src="/js/optimized-navigation.js?v=20250629"></script>
    <script src="/js/mobile-performance-optimizer.js?v=20250629"></script>
    <script src="/js/performance-test.js?v=20250629"></script>
    <script src="/js/seo-manager.js?v=20250629"></script>
    <script src="/js/page-router.js?v=20250629"></script>
    <script src="/js/mobile-menu.js?v=20250629"></script>
    <script src="/js/components/pagination.js?v=20250629"></script>
    <script src="/js/components/quote-card.js?v=20250629"></script>
    <script src="/js/components/breadcrumb.js?v=20250629"></script>
    <script src="/js/navigation-state.js?v=20250629"></script>
    <script src="/js/social-meta.js?v=20250629"></script>

    <!-- Global Fix Script -->
    <script src="/js/global-fix.js?v=20250629"></script>

    <!-- Page Specific Script -->
    <script src="/js/pages/quote.js?v=20250629"></script>
</body>
</html>
