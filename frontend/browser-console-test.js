/**
 * 浏览器控制台测试脚本
 * 在任意页面的浏览器控制台中运行此脚本来测试名言卡片点击功能
 * 
 * 使用方法：
 * 1. 打开任意包含名言卡片的页面
 * 2. 按F12打开开发者工具
 * 3. 切换到Console标签
 * 4. 复制并粘贴此脚本内容
 * 5. 按Enter执行
 */

(function() {
    'use strict';
    
    console.log('🧪 开始名言卡片点击功能测试...');
    console.log('📍 当前页面:', window.location.href);
    
    // 测试结果统计
    const testResults = {
        totalCards: 0,
        clickableCards: 0,
        cardsWithPointer: 0,
        cardsWithId: 0,
        errors: []
    };
    
    // 1. 检查修复脚本是否加载
    console.log('\n🔍 1. 检查修复脚本状态...');
    if (typeof window.QuoteCardClickFix !== 'undefined') {
        console.log('✅ 修复脚本已加载');
        console.log('📊 修复脚本配置:', window.QuoteCardClickFix.config);
        console.log('🔄 检查次数:', window.QuoteCardClickFix.checkCount());
    } else {
        console.log('⚠️  修复脚本未检测到');
    }
    
    // 2. 查找页面上的名言卡片
    console.log('\n🔍 2. 查找名言卡片...');
    const quoteCards = document.querySelectorAll('.quote-card-component');
    testResults.totalCards = quoteCards.length;
    console.log(`📊 找到 ${quoteCards.length} 个名言卡片`);
    
    if (quoteCards.length === 0) {
        console.log('❌ 页面上没有找到名言卡片');
        console.log('💡 可能的原因:');
        console.log('   - 页面还在加载中');
        console.log('   - API调用失败');
        console.log('   - 名言卡片使用了不同的CSS类名');
        return;
    }
    
    // 3. 检查每个名言卡片的状态
    console.log('\n🔍 3. 检查名言卡片状态...');
    quoteCards.forEach((card, index) => {
        console.log(`\n📋 卡片 ${index + 1}:`);
        
        // 检查quote ID
        const quoteId = card.getAttribute('data-quote-id');
        if (quoteId) {
            testResults.cardsWithId++;
            console.log(`   ✅ Quote ID: ${quoteId}`);
        } else {
            testResults.errors.push(`卡片 ${index + 1} 缺少 data-quote-id 属性`);
            console.log(`   ❌ 缺少 data-quote-id 属性`);
        }
        
        // 检查cursor-pointer样式
        const hasPointer = card.classList.contains('cursor-pointer');
        if (hasPointer) {
            testResults.cardsWithPointer++;
            console.log(`   ✅ 具有 cursor-pointer 样式`);
        } else {
            testResults.errors.push(`卡片 ${index + 1} 缺少 cursor-pointer 样式`);
            console.log(`   ❌ 缺少 cursor-pointer 样式`);
        }
        
        // 检查计算样式
        const computedStyle = getComputedStyle(card);
        console.log(`   🎨 计算样式 cursor: ${computedStyle.cursor}`);
        console.log(`   🎨 计算样式 pointer-events: ${computedStyle.pointerEvents}`);
        
        // 检查是否有点击事件监听器
        const hasClickFixed = card.hasAttribute('data-click-fixed');
        if (hasClickFixed) {
            testResults.clickableCards++;
            console.log(`   ✅ 已修复点击事件`);
        } else {
            console.log(`   ⚠️  未检测到修复标记`);
        }
        
        // 检查卡片内容
        const content = card.textContent.trim();
        if (content.length > 0) {
            console.log(`   📝 内容预览: "${content.substring(0, 50)}..."`);
        }
    });
    
    // 4. 测试URL生成功能
    console.log('\n🔍 4. 测试URL生成功能...');
    if (typeof UrlHandler !== 'undefined' && typeof UrlHandler.getQuoteUrl === 'function') {
        console.log('✅ UrlHandler 可用');
        
        // 测试URL生成
        const testQuote = { id: '499001' };
        try {
            const testUrl = UrlHandler.getQuoteUrl(testQuote);
            console.log(`✅ URL生成测试: ${testUrl}`);
        } catch (error) {
            testResults.errors.push(`URL生成失败: ${error.message}`);
            console.log(`❌ URL生成失败: ${error.message}`);
        }
    } else {
        testResults.errors.push('UrlHandler 不可用');
        console.log('❌ UrlHandler 不可用');
    }
    
    // 5. 添加测试点击事件
    console.log('\n🔍 5. 添加测试点击事件...');
    if (quoteCards.length > 0) {
        const firstCard = quoteCards[0];
        const quoteId = firstCard.getAttribute('data-quote-id');
        
        // 添加测试点击事件
        const testClickHandler = (e) => {
            console.log('🖱️  测试点击事件触发!');
            console.log('📍 点击目标:', e.target);
            console.log('📍 当前目标:', e.currentTarget);
            console.log('📋 Quote ID:', quoteId);
            
            if (quoteId) {
                const targetUrl = `/quotes/${quoteId}/`;
                console.log(`🔗 应该跳转到: ${targetUrl}`);
                
                // 询问是否真的要跳转
                if (confirm(`测试成功！是否跳转到名言详情页？\\n目标URL: ${targetUrl}`)) {
                    window.location.href = targetUrl;
                }
            }
            
            // 移除测试事件监听器
            firstCard.removeEventListener('click', testClickHandler);
            console.log('🗑️  测试事件监听器已移除');
        };
        
        firstCard.addEventListener('click', testClickHandler);
        console.log('✅ 测试点击事件已添加到第一个名言卡片');
        console.log('💡 请点击第一个名言卡片进行测试');
    }
    
    // 6. 显示测试总结
    console.log('\n📊 测试总结:');
    console.log(`   总卡片数: ${testResults.totalCards}`);
    console.log(`   有ID的卡片: ${testResults.cardsWithId}`);
    console.log(`   有pointer样式的卡片: ${testResults.cardsWithPointer}`);
    console.log(`   已修复点击的卡片: ${testResults.clickableCards}`);
    console.log(`   错误数量: ${testResults.errors.length}`);
    
    if (testResults.errors.length > 0) {
        console.log('\n❌ 发现的问题:');
        testResults.errors.forEach((error, index) => {
            console.log(`   ${index + 1}. ${error}`);
        });
    }
    
    // 7. 给出建议
    console.log('\n💡 建议:');
    if (testResults.totalCards === 0) {
        console.log('   - 等待页面完全加载后重新运行测试');
        console.log('   - 检查API调用是否成功');
    } else if (testResults.cardsWithPointer < testResults.totalCards) {
        console.log('   - 某些卡片缺少cursor-pointer样式');
        console.log('   - 运行 window.QuoteCardClickFix.fixAll() 尝试修复');
    } else if (testResults.clickableCards < testResults.totalCards) {
        console.log('   - 某些卡片可能没有正确的点击事件');
        console.log('   - 检查修复脚本是否正常工作');
    } else {
        console.log('   - 所有检查都通过了！');
        console.log('   - 请手动点击名言卡片测试实际功能');
    }
    
    console.log('\n🎯 下一步:');
    console.log('   1. 点击第一个名言卡片测试跳转功能');
    console.log('   2. 如果有问题，运行 window.QuoteCardClickFix.fixAll() 进行修复');
    console.log('   3. 在其他页面重复此测试');
    
    // 返回测试结果供进一步使用
    window.quoteCardTestResults = testResults;
    console.log('\n📋 测试结果已保存到 window.quoteCardTestResults');
    
})();
