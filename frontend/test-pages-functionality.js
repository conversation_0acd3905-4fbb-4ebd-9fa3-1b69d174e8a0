/**
 * 页面功能测试脚本
 * 用于验证所有页面的URL功能、路由处理和SEO标签生成
 * 版本：v1.0 - SEO重启实施测试
 * 更新日期：2025年6月16日
 */

class PageFunctionalityTester {
    constructor() {
        this.testResults = [];
        this.baseUrl = window.location.origin;
        this.testPages = [
            { name: '首页', url: '/', type: 'home', file: 'index.html' },
            { name: '作者列表页', url: '/authors/', type: 'authors-list', file: 'authors.html' },
            { name: '作者详情页', url: '/authors/albert-einstein/', type: 'author-detail', file: 'author.html' },
            { name: '作者名言页', url: '/authors/albert-einstein/quotes/', type: 'author-quotes', file: 'author.html' },
            { name: '类别列表页', url: '/categories/', type: 'categories-list', file: 'categories.html' },
            { name: '类别详情页', url: '/categories/inspirational/', type: 'category-detail', file: 'category.html' },
            { name: '类别名言页', url: '/categories/inspirational/quotes/', type: 'category-quotes', file: 'category.html' },
            { name: '来源列表页', url: '/sources/', type: 'sources-list', file: 'sources.html' },
            { name: '来源详情页', url: '/sources/the-art-of-war/', type: 'source-detail', file: 'source.html' },
            { name: '名言列表页', url: '/quotes/', type: 'quotes-list', file: 'quotes.html' },
            { name: '名言详情页', url: '/quotes/123/', type: 'quote-detail', file: 'quote.html' },
            { name: '搜索页面', url: '/search/', type: 'search', file: 'search.html' }
        ];
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('开始运行页面功能测试...');
        this.testResults = [];

        // 1. 测试UrlHandler功能
        await this.testUrlHandler();

        // 2. 测试PageRouter功能
        await this.testPageRouter();

        // 3. 测试SEOManager功能
        await this.testSEOManager();

        // 4. 测试页面访问功能
        await this.testPageAccess();

        // 5. 测试URL解析功能
        await this.testUrlParsing();

        // 6. 测试SEO标签生成
        await this.testSEOGeneration();

        // 输出测试结果
        this.outputResults();

        return this.getTestSummary();
    }

    /**
     * 测试UrlHandler功能
     */
    async testUrlHandler() {
        console.log('测试UrlHandler功能...');

        // 测试slugify函数
        const slugifyTests = [
            { input: 'Albert Einstein', expected: 'albert-einstein' },
            { input: 'Self-Improvement', expected: 'self-improvement' },
            { input: 'Life & Success', expected: 'life-success' },
            { input: '100 Best Quotes', expected: '100-best-quotes' },
            { input: '  Spaced  Text  ', expected: 'spaced-text' },
            { input: 'Special@#$%Characters', expected: 'specialcharacters' }
        ];

        if (typeof UrlHandler !== 'undefined' && UrlHandler.slugify) {
            for (const test of slugifyTests) {
                try {
                    const result = UrlHandler.slugify(test.input);
                    this.addTestResult(
                        'UrlHandler.slugify',
                        `输入: "${test.input}"`,
                        result === test.expected,
                        `期望: "${test.expected}", 实际: "${result}"`
                    );
                } catch (error) {
                    this.addTestResult(
                        'UrlHandler.slugify',
                        `输入: "${test.input}"`,
                        false,
                        `错误: ${error.message}`
                    );
                }
            }
        } else {
            this.addTestResult(
                'UrlHandler',
                '可用性检查',
                false,
                'UrlHandler未定义或slugify函数不存在'
            );
        }

        // 测试deslugify函数
        if (typeof UrlHandler !== 'undefined' && UrlHandler.deslugify) {
            const deslugifyTests = [
                { input: 'albert-einstein', expected: 'Albert Einstein' },
                { input: 'self-improvement', expected: 'Self Improvement' },
                { input: 'life-success', expected: 'Life Success' }
            ];

            for (const test of deslugifyTests) {
                try {
                    const result = UrlHandler.deslugify(test.input);
                    this.addTestResult(
                        'UrlHandler.deslugify',
                        `输入: "${test.input}"`,
                        result === test.expected,
                        `期望: "${test.expected}", 实际: "${result}"`
                    );
                } catch (error) {
                    this.addTestResult(
                        'UrlHandler.deslugify',
                        `输入: "${test.input}"`,
                        false,
                        `错误: ${error.message}`
                    );
                }
            }
        }

        // 测试页面类型检测
        if (typeof UrlHandler !== 'undefined' && UrlHandler.getCurrentPageType) {
            const pageTypeTests = [
                { url: '/', expected: 'home' },
                { url: '/authors/', expected: 'authors-list' },
                { url: '/authors/test/', expected: 'author-detail' },
                { url: '/categories/test/', expected: 'category-detail' },
                { url: '/quotes/123/', expected: 'quote-detail' }
            ];

            for (const test of pageTypeTests) {
                try {
                    // 模拟URL变化
                    const originalUrl = window.location.href;
                    history.pushState({}, '', test.url);
                    
                    const result = UrlHandler.getCurrentPageType();
                    
                    // 恢复原URL
                    history.pushState({}, '', originalUrl);
                    
                    this.addTestResult(
                        'UrlHandler.getCurrentPageType',
                        `URL: "${test.url}"`,
                        result === test.expected,
                        `期望: "${test.expected}", 实际: "${result}"`
                    );
                } catch (error) {
                    this.addTestResult(
                        'UrlHandler.getCurrentPageType',
                        `URL: "${test.url}"`,
                        false,
                        `错误: ${error.message}`
                    );
                }
            }
        }
    }

    /**
     * 测试PageRouter功能
     */
    async testPageRouter() {
        console.log('测试PageRouter功能...');

        if (typeof PageRouter !== 'undefined') {
            // 测试PageRouter基本功能
            const routerTests = [
                { name: '页面初始化器配置', test: () => PageRouter.pageInitializers && Object.keys(PageRouter.pageInitializers).length > 0 },
                { name: '参数提取器配置', test: () => PageRouter.parameterExtractors && Object.keys(PageRouter.parameterExtractors).length > 0 },
                { name: '页面初始化函数', test: () => typeof PageRouter.initializePage === 'function' },
                { name: '参数提取函数', test: () => typeof PageRouter.extractPageParameters === 'function' },
                { name: '参数验证函数', test: () => typeof PageRouter.validatePageParameters === 'function' }
            ];

            for (const test of routerTests) {
                try {
                    const result = test.test();
                    this.addTestResult(
                        'PageRouter',
                        test.name,
                        result,
                        result ? '功能正常' : '功能缺失或异常'
                    );
                } catch (error) {
                    this.addTestResult(
                        'PageRouter',
                        test.name,
                        false,
                        `错误: ${error.message}`
                    );
                }
            }

            // 测试参数提取功能
            for (const page of this.testPages) {
                if (PageRouter.parameterExtractors[page.type]) {
                    try {
                        // 模拟URL变化
                        const originalUrl = window.location.href;
                        history.pushState({}, '', page.url);
                        
                        const params = PageRouter.extractPageParameters(page.type);
                        
                        // 恢复原URL
                        history.pushState({}, '', originalUrl);
                        
                        this.addTestResult(
                            'PageRouter.extractPageParameters',
                            `${page.name} (${page.type})`,
                            params !== null,
                            `参数: ${JSON.stringify(params)}`
                        );
                    } catch (error) {
                        this.addTestResult(
                            'PageRouter.extractPageParameters',
                            `${page.name} (${page.type})`,
                            false,
                            `错误: ${error.message}`
                        );
                    }
                }
            }
        } else {
            this.addTestResult(
                'PageRouter',
                '可用性检查',
                false,
                'PageRouter未定义'
            );
        }
    }

    /**
     * 测试SEOManager功能
     */
    async testSEOManager() {
        console.log('测试SEOManager功能...');

        if (typeof SEOManager !== 'undefined') {
            // 测试SEOManager基本功能
            const seoTests = [
                { name: '配置对象', test: () => SEOManager.CONFIG && typeof SEOManager.CONFIG === 'object' },
                { name: '模板配置', test: () => SEOManager.TEMPLATES && Object.keys(SEOManager.TEMPLATES).length > 0 },
                { name: 'updatePageSEO函数', test: () => typeof SEOManager.updatePageSEO === 'function' },
                { name: 'generateSEOData函数', test: () => typeof SEOManager.generateSEOData === 'function' },
                { name: 'validateSEO函数', test: () => typeof SEOManager.validateSEO === 'function' },
                { name: 'getCurrentSEOData函数', test: () => typeof SEOManager.getCurrentSEOData === 'function' }
            ];

            for (const test of seoTests) {
                try {
                    const result = test.test();
                    this.addTestResult(
                        'SEOManager',
                        test.name,
                        result,
                        result ? '功能正常' : '功能缺失或异常'
                    );
                } catch (error) {
                    this.addTestResult(
                        'SEOManager',
                        test.name,
                        false,
                        `错误: ${error.message}`
                    );
                }
            }

            // 测试SEO验证功能
            try {
                const validation = SEOManager.validateSEO();
                this.addTestResult(
                    'SEOManager.validateSEO',
                    '当前页面SEO验证',
                    validation && typeof validation === 'object',
                    `验证结果: ${JSON.stringify(validation)}`
                );
            } catch (error) {
                this.addTestResult(
                    'SEOManager.validateSEO',
                    '当前页面SEO验证',
                    false,
                    `错误: ${error.message}`
                );
            }
        } else {
            this.addTestResult(
                'SEOManager',
                '可用性检查',
                false,
                'SEOManager未定义'
            );
        }
    }

    /**
     * 测试页面访问功能
     */
    async testPageAccess() {
        console.log('测试页面访问功能...');

        for (const page of this.testPages) {
            try {
                // 检查对应的HTML文件是否存在
                const response = await fetch(page.file);
                const exists = response.ok;
                
                this.addTestResult(
                    '页面文件访问',
                    `${page.name} (${page.file})`,
                    exists,
                    exists ? '文件存在且可访问' : `HTTP状态: ${response.status}`
                );
            } catch (error) {
                this.addTestResult(
                    '页面文件访问',
                    `${page.name} (${page.file})`,
                    false,
                    `错误: ${error.message}`
                );
            }
        }
    }

    /**
     * 测试URL解析功能
     */
    async testUrlParsing() {
        console.log('测试URL解析功能...');

        const urlTests = [
            { url: '/authors/albert-einstein/', expectedSlug: 'albert-einstein' },
            { url: '/categories/inspirational/', expectedSlug: 'inspirational' },
            { url: '/sources/the-art-of-war/', expectedSlug: 'the-art-of-war' },
            { url: '/quotes/123/', expectedId: '123' }
        ];

        for (const test of urlTests) {
            if (typeof UrlHandler !== 'undefined') {
                try {
                    // 模拟URL变化
                    const originalUrl = window.location.href;
                    history.pushState({}, '', test.url);
                    
                    let result;
                    if (test.url.includes('/authors/')) {
                        result = UrlHandler.parseAuthorFromPath && UrlHandler.parseAuthorFromPath();
                    } else if (test.url.includes('/categories/')) {
                        result = UrlHandler.parseCategoryFromPath && UrlHandler.parseCategoryFromPath();
                    } else if (test.url.includes('/sources/')) {
                        result = UrlHandler.parseSourceFromPath && UrlHandler.parseSourceFromPath();
                    } else if (test.url.includes('/quotes/')) {
                        result = UrlHandler.parseQuoteIdFromPath && UrlHandler.parseQuoteIdFromPath();
                    }
                    
                    // 恢复原URL
                    history.pushState({}, '', originalUrl);
                    
                    const expected = test.expectedSlug || test.expectedId;
                    this.addTestResult(
                        'URL解析',
                        `解析 ${test.url}`,
                        result === expected,
                        `期望: "${expected}", 实际: "${result}"`
                    );
                } catch (error) {
                    this.addTestResult(
                        'URL解析',
                        `解析 ${test.url}`,
                        false,
                        `错误: ${error.message}`
                    );
                }
            }
        }
    }

    /**
     * 测试SEO标签生成
     */
    async testSEOGeneration() {
        console.log('测试SEO标签生成...');

        if (typeof SEOManager !== 'undefined') {
            for (const page of this.testPages.slice(0, 5)) { // 测试前5个页面
                try {
                    const pageData = {
                        pageType: page.type,
                        params: this.getTestParams(page.type),
                        canonicalUrl: `${this.baseUrl}${page.url}`
                    };

                    const seoData = SEOManager.generateSEOData(pageData);
                    
                    this.addTestResult(
                        'SEO标签生成',
                        `${page.name} (${page.type})`,
                        seoData && seoData.title && seoData.description,
                        `标题: "${seoData.title}", 描述长度: ${seoData.description ? seoData.description.length : 0}`
                    );
                } catch (error) {
                    this.addTestResult(
                        'SEO标签生成',
                        `${page.name} (${page.type})`,
                        false,
                        `错误: ${error.message}`
                    );
                }
            }
        }
    }

    /**
     * 获取测试参数
     */
    getTestParams(pageType) {
        switch (pageType) {
            case 'author-detail':
            case 'author-quotes':
                return { authorSlug: 'albert-einstein', authorName: 'Albert Einstein' };
            case 'category-detail':
            case 'category-quotes':
                return { categorySlug: 'inspirational', categoryName: 'Inspirational' };
            case 'source-detail':
                return { sourceSlug: 'the-art-of-war', sourceName: 'The Art of War' };
            case 'quote-detail':
                return { quoteId: 123 };
            default:
                return {};
        }
    }

    /**
     * 添加测试结果
     */
    addTestResult(category, testName, passed, details) {
        this.testResults.push({
            category,
            testName,
            passed,
            details,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 输出测试结果
     */
    outputResults() {
        console.log('\n=== 页面功能测试结果 ===');
        
        const summary = this.getTestSummary();
        console.log(`总测试数: ${summary.total}`);
        console.log(`通过: ${summary.passed}`);
        console.log(`失败: ${summary.failed}`);
        console.log(`成功率: ${summary.successRate}%`);
        
        console.log('\n详细结果:');
        this.testResults.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} [${result.category}] ${result.testName}: ${result.details}`);
        });
    }

    /**
     * 获取测试摘要
     */
    getTestSummary() {
        const total = this.testResults.length;
        const passed = this.testResults.filter(r => r.passed).length;
        const failed = total - passed;
        const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;

        return { total, passed, failed, successRate };
    }
}

// 导出测试类
window.PageFunctionalityTester = PageFunctionalityTester;
