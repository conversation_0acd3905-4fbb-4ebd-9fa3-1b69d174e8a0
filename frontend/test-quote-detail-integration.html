<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Detail Integration Test - quotese.com</title>
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
</head>
<body class="light-mode bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">Quote Detail Page Integration Test</h1>
        
        <!-- Test Controls -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Test Controls</h2>
            
            <!-- Test Quote IDs -->
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Test Quote ID:</label>
                <div class="flex flex-wrap gap-2 mb-4">
                    <button onclick="testQuoteDetail(1)" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        Test ID: 1
                    </button>
                    <button onclick="testQuoteDetail(100)" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        Test ID: 100
                    </button>
                    <button onclick="testQuoteDetail(500)" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        Test ID: 500
                    </button>
                    <button onclick="testQuoteDetail(999999)" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                        Test Invalid ID: 999999
                    </button>
                </div>
                
                <div class="flex gap-2">
                    <input type="number" id="customQuoteId" placeholder="Enter custom quote ID" 
                           class="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button onclick="testCustomQuoteId()" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        Test Custom ID
                    </button>
                </div>
            </div>
            
            <!-- Test Results -->
            <div class="mb-4">
                <h3 class="text-lg font-semibold mb-2">Test Results:</h3>
                <div id="test-results" class="bg-gray-100 dark:bg-gray-700 p-4 rounded min-h-[100px] font-mono text-sm">
                    Ready for testing...
                </div>
            </div>
            
            <!-- Clear Results -->
            <button onclick="clearResults()" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                Clear Results
            </button>
        </div>
        
        <!-- Quote Detail Page Simulation -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold mb-4">Quote Detail Page Simulation</h2>
            
            <!-- Breadcrumb Container -->
            <div id="breadcrumb-container" class="mb-4"></div>
            
            <!-- Quote Card Container -->
            <section class="mb-12 fade-in" id="quote-card-container">
                <div class="max-w-4xl mx-auto">
                    <div id="quote-card-content">
                        <!-- Loading state -->
                        <div class="relative p-6 sm:p-8 md:p-10 bg-gradient-to-br from-yellow-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700" id="quote-loading">
                            <div class="flex justify-center items-center py-8">
                                <div class="loading-spinner"></div>
                                <span class="ml-3 text-gray-600 dark:text-gray-300">Ready to test...</span>
                            </div>
                        </div>
                        
                        <!-- Error state (hidden by default) -->
                        <div class="hidden relative p-6 sm:p-8 md:p-10 bg-gradient-to-br from-red-50 to-white dark:from-red-900 dark:to-gray-900 rounded-xl shadow-lg border border-red-200 dark:border-red-700" id="quote-error">
                            <div class="text-center py-8">
                                <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-4"></i>
                                <h2 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2">Quote Not Found</h2>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">The quote you're looking for doesn't exist or has been removed.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Content Grid -->
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Left Column (Related Quotes) -->
                <section class="lg:w-2/3">
                    <h2 class="text-2xl font-bold mb-6 flex items-center">
                        <i class="fas fa-quote-right text-yellow-500 mr-2"></i>
                        More Quotes by <span id="author-name-heading">this Author</span>
                    </h2>
                    
                    <div id="related-quotes-container">
                        <div class="flex justify-center py-12" id="related-quotes-loading">
                            <div class="loading-spinner"></div>
                        </div>
                        
                        <div class="hidden text-center py-12" id="related-quotes-error">
                            <div class="text-gray-500 dark:text-gray-400">
                                <i class="fas fa-exclamation-triangle text-yellow-500 text-2xl mb-2"></i>
                                <p>Unable to load related quotes.</p>
                            </div>
                        </div>
                        
                        <div class="hidden text-center py-12" id="related-quotes-empty">
                            <div class="text-gray-500 dark:text-gray-400">
                                <i class="fas fa-quote-right text-yellow-500 text-2xl mb-2"></i>
                                <p>No other quotes found by this author.</p>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Right Column (Sidebar) -->
                <aside class="lg:w-1/3">
                    <!-- Popular Categories -->
                    <section class="card-container mb-8 p-6">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-tags text-yellow-500 mr-2"></i>
                            Popular Categories
                        </h3>
                        <div id="categories-container">
                            <div class="w-full flex justify-center py-4">
                                <div class="loading-spinner"></div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Popular Authors -->
                    <section class="card-container mb-8 p-6">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-user-pen text-yellow-500 mr-2"></i>
                            Popular Authors
                        </h3>
                        <div id="authors-container">
                            <div class="w-full flex justify-center py-4">
                                <div class="loading-spinner"></div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Popular Sources -->
                    <section class="card-container p-6">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-book text-yellow-500 mr-2"></i>
                            Popular Sources
                        </h3>
                        <div id="sources-container">
                            <div class="w-full flex justify-center py-4">
                                <div class="loading-spinner"></div>
                            </div>
                        </div>
                    </section>
                </aside>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/debug.js?v=20250629"></script>
    <script src="/js/component-loader.js?v=20250629"></script>
    <script src="/js/mock-data.js?v=20250629"></script>
    <script src="/js/api-client.js?v=20250629"></script>
    <script src="/js/theme.js?v=20250629"></script>
    <script src="/js/url-handler.js?v=20250629"></script>
    <script src="/js/components/quote-card.js?v=20250629"></script>
    <script src="/js/components/breadcrumb.js?v=20250629"></script>
    
    <script>
        // Test functions
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const typeColor = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-blue-600';
            resultsDiv.innerHTML += `<div class="${typeColor}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = 'Ready for testing...';
        }
        
        async function testQuoteDetail(quoteId) {
            logResult(`Starting test for Quote ID: ${quoteId}`, 'info');
            
            try {
                // Test API Client
                if (typeof window.ApiClient === 'undefined') {
                    logResult('ERROR: ApiClient not available', 'error');
                    return;
                }
                
                logResult('ApiClient available, testing getQuoteById...', 'info');
                
                // Test quote data loading
                const quote = await window.ApiClient.getQuoteById(quoteId);
                
                if (quote) {
                    logResult(`SUCCESS: Quote loaded - "${quote.content.substring(0, 50)}..."`, 'success');
                    logResult(`Author: ${quote.author?.name || 'Unknown'}`, 'info');
                    logResult(`Categories: ${quote.categories?.map(c => c.name).join(', ') || 'None'}`, 'info');
                    logResult(`Sources: ${quote.sources?.map(s => s.name).join(', ') || 'None'}`, 'info');
                    
                    // Test QuoteCardComponent
                    if (typeof QuoteCardComponent !== 'undefined') {
                        logResult('Testing QuoteCardComponent rendering...', 'info');
                        try {
                            const quoteCard = QuoteCardComponent.render(quote, 0, {
                                showActions: true,
                                showAuthorAvatar: true,
                                showCategories: true,
                                showSources: true,
                                showDate: true,
                                isDetailPage: true,
                                containerClass: 'quote-detail-card'
                            });
                            
                            if (quoteCard) {
                                const container = document.getElementById('quote-card-content');
                                container.innerHTML = '';
                                container.appendChild(quoteCard);
                                logResult('SUCCESS: QuoteCardComponent rendered successfully', 'success');
                            } else {
                                logResult('ERROR: QuoteCardComponent returned null', 'error');
                            }
                        } catch (error) {
                            logResult(`ERROR: QuoteCardComponent failed - ${error.message}`, 'error');
                        }
                    } else {
                        logResult('WARNING: QuoteCardComponent not available', 'error');
                    }
                    
                    // Test related quotes
                    if (quote.author?.id) {
                        logResult('Testing related quotes loading...', 'info');
                        try {
                            const relatedQuotes = await window.ApiClient.getRelatedQuotesByAuthor(quote.author.id, quoteId, 5);
                            logResult(`SUCCESS: Found ${relatedQuotes?.length || 0} related quotes`, 'success');
                        } catch (error) {
                            logResult(`ERROR: Related quotes failed - ${error.message}`, 'error');
                        }
                    }
                    
                } else {
                    logResult(`ERROR: Quote with ID ${quoteId} not found`, 'error');
                    // Show error state
                    document.getElementById('quote-loading').classList.add('hidden');
                    document.getElementById('quote-error').classList.remove('hidden');
                }
                
            } catch (error) {
                logResult(`ERROR: Test failed - ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }
        
        function testCustomQuoteId() {
            const customId = document.getElementById('customQuoteId').value;
            if (customId) {
                testQuoteDetail(parseInt(customId));
            } else {
                logResult('ERROR: Please enter a quote ID', 'error');
            }
        }
        
        // Initialize test environment
        window.addEventListener('load', () => {
            logResult('Test environment initialized', 'success');
            logResult('Available components:', 'info');
            logResult(`- ApiClient: ${typeof window.ApiClient !== 'undefined' ? 'Available' : 'Not Available'}`, 'info');
            logResult(`- QuoteCardComponent: ${typeof QuoteCardComponent !== 'undefined' ? 'Available' : 'Not Available'}`, 'info');
            logResult(`- UrlHandler: ${typeof window.UrlHandler !== 'undefined' ? 'Available' : 'Not Available'}`, 'info');
        });
    </script>
</body>
</html>
