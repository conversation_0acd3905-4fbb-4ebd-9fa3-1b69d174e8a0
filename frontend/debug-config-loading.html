<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置加载调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .cache-info { background: #fff3cd; padding: 10px; border-radius: 3px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 配置加载调试页面</h1>
    
    <div class="cache-info">
        <strong>⚠️ 缓存清除提示：</strong>
        <p>如果看到配置问题，请按 <kbd>Ctrl+Shift+R</kbd> (Windows/Linux) 或 <kbd>Cmd+Shift+R</kbd> (Mac) 强制刷新页面清除缓存。</p>
        <p>或者打开开发者工具 (F12)，右键刷新按钮选择"清空缓存并硬性重新加载"。</p>
    </div>
    
    <div class="debug-section">
        <h2>📋 环境检测</h2>
        <div id="environment-info"></div>
    </div>
    
    <div class="debug-section">
        <h2>🔍 配置对象调试</h2>
        <div id="config-debug"></div>
        <button onclick="debugConfig()">重新检测配置</button>
    </div>
    
    <div class="debug-section">
        <h2>🌐 API客户端调试</h2>
        <div id="api-client-debug"></div>
        <button onclick="debugApiClient()">重新检测API客户端</button>
    </div>
    
    <div class="debug-section">
        <h2>🧪 实时测试</h2>
        <div id="live-test-result"></div>
        <button onclick="testLiveEndpoints()">测试端点连接</button>
    </div>

    <!-- 强制无缓存加载JavaScript文件 -->
    <script>
        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = `${src}?v=${timestamp}`;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        async function loadScripts() {
            try {
                await loadScript('js/config.js');
                await loadScript('js/api-client.js');
                console.log('✅ Scripts loaded successfully');
                initializeDebug();
            } catch (error) {
                console.error('❌ Script loading failed:', error);
                document.getElementById('config-debug').innerHTML = '<div class="error">❌ 脚本加载失败: ' + error.message + '</div>';
            }
        }
        
        function addResult(container, message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            container.appendChild(div);
        }
        
        function initializeDebug() {
            // 环境检测
            const envContainer = document.getElementById('environment-info');
            envContainer.innerHTML = '';
            
            addResult(envContainer, `🌍 当前域名: ${window.location.hostname}`, 'info');
            addResult(envContainer, `🔗 当前URL: ${window.location.href}`, 'info');
            addResult(envContainer, `⏰ 页面加载时间: ${new Date().toLocaleString()}`, 'info');
            addResult(envContainer, `🔄 缓存时间戳: ${timestamp}`, 'info');
            
            // 自动运行调试
            setTimeout(() => {
                debugConfig();
                debugApiClient();
            }, 100);
        }
        
        function debugConfig() {
            const container = document.getElementById('config-debug');
            container.innerHTML = '';
            
            addResult(container, '🔍 开始配置调试...', 'info');
            
            // 检查Config对象
            if (typeof Config !== 'undefined') {
                addResult(container, '✅ Config对象存在', 'success');
                
                // 检查development配置
                if (Config.development) {
                    addResult(container, '✅ development配置存在', 'success');
                    addResult(container, `📡 REST API: ${Config.development.apiEndpoint}`, 'info');
                    addResult(container, `🔍 GraphQL: ${Config.development.graphqlEndpoint || '❌ 未配置'}`, Config.development.graphqlEndpoint ? 'success' : 'error');
                } else {
                    addResult(container, '❌ development配置不存在', 'error');
                }
                
                // 检查getCurrent方法
                if (typeof Config.getCurrent === 'function') {
                    addResult(container, '✅ getCurrent方法存在', 'success');
                    const currentConfig = Config.getCurrent();
                    addResult(container, `🎯 当前环境配置:`, 'info');
                    
                    const pre = document.createElement('pre');
                    pre.textContent = JSON.stringify(currentConfig, null, 2);
                    container.appendChild(pre);
                } else {
                    addResult(container, '❌ getCurrent方法不存在', 'error');
                }
            } else {
                addResult(container, '❌ Config对象不存在', 'error');
            }
            
            // 检查window.AppConfig
            if (window.AppConfig) {
                addResult(container, '✅ window.AppConfig存在', 'success');
                addResult(container, `📡 REST API: ${window.AppConfig.apiEndpoint}`, 'info');
                addResult(container, `🔍 GraphQL: ${window.AppConfig.graphqlEndpoint || '❌ 未配置'}`, window.AppConfig.graphqlEndpoint ? 'success' : 'error');
                addResult(container, `🎭 模拟数据: ${window.AppConfig.useMockData}`, 'info');
                addResult(container, `🐛 调试模式: ${window.AppConfig.debug}`, 'info');
                
                const pre2 = document.createElement('pre');
                pre2.textContent = JSON.stringify(window.AppConfig, null, 2);
                container.appendChild(pre2);
            } else {
                addResult(container, '❌ window.AppConfig不存在', 'error');
            }
        }
        
        function debugApiClient() {
            const container = document.getElementById('api-client-debug');
            container.innerHTML = '';
            
            addResult(container, '🔍 开始API客户端调试...', 'info');
            
            if (window.ApiClient) {
                addResult(container, '✅ window.ApiClient存在', 'success');
                addResult(container, `📡 REST端点: ${window.ApiClient.apiEndpoint}`, 'info');
                addResult(container, `🔍 GraphQL端点: ${window.ApiClient.graphqlEndpoint || '❌ 未配置'}`, window.ApiClient.graphqlEndpoint ? 'success' : 'error');
                addResult(container, `🎭 模拟数据: ${window.ApiClient.useMockData}`, 'info');
                
                // 检查构造函数参数
                addResult(container, '🔧 构造函数信息:', 'info');
                addResult(container, `   - apiEndpoint: ${window.ApiClient.apiEndpoint}`, 'info');
                addResult(container, `   - graphqlEndpoint: ${window.ApiClient.graphqlEndpoint}`, window.ApiClient.graphqlEndpoint ? 'info' : 'error');
                addResult(container, `   - useMockData: ${window.ApiClient.useMockData}`, 'info');
                
                // 检查方法
                if (typeof window.ApiClient.query === 'function') {
                    addResult(container, '✅ query方法存在', 'success');
                } else {
                    addResult(container, '❌ query方法不存在', 'error');
                }
                
                if (typeof window.ApiClient.getTopQuotes === 'function') {
                    addResult(container, '✅ getTopQuotes方法存在', 'success');
                } else {
                    addResult(container, '❌ getTopQuotes方法不存在', 'error');
                }
            } else {
                addResult(container, '❌ window.ApiClient不存在', 'error');
            }
        }
        
        async function testLiveEndpoints() {
            const container = document.getElementById('live-test-result');
            container.innerHTML = '';
            
            addResult(container, '🧪 开始实时端点测试...', 'info');
            
            // 测试REST API
            try {
                addResult(container, '📡 测试REST API...', 'info');
                const restResponse = await fetch('http://127.0.0.1:8000/api/', {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' }
                });
                
                if (restResponse.ok) {
                    const restData = await restResponse.json();
                    addResult(container, `✅ REST API连接成功: ${restData.message}`, 'success');
                } else {
                    addResult(container, `❌ REST API失败: ${restResponse.status}`, 'error');
                }
            } catch (error) {
                addResult(container, `❌ REST API错误: ${error.message}`, 'error');
            }
            
            // 测试GraphQL API
            try {
                addResult(container, '🔍 测试GraphQL API...', 'info');
                const graphqlResponse = await fetch('http://127.0.0.1:8000/graphql/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        query: 'query { quotesCount }'
                    })
                });
                
                if (graphqlResponse.ok) {
                    const graphqlData = await graphqlResponse.json();
                    if (graphqlData.errors) {
                        addResult(container, `❌ GraphQL查询错误: ${graphqlData.errors[0].message}`, 'error');
                    } else {
                        addResult(container, `✅ GraphQL API连接成功: ${graphqlData.data.quotesCount} 条名言`, 'success');
                    }
                } else {
                    addResult(container, `❌ GraphQL API失败: ${graphqlResponse.status}`, 'error');
                }
            } catch (error) {
                addResult(container, `❌ GraphQL API错误: ${error.message}`, 'error');
            }
            
            // 测试API客户端
            if (window.ApiClient && window.ApiClient.graphqlEndpoint) {
                try {
                    addResult(container, '🎯 测试API客户端getTopQuotes...', 'info');
                    const quotes = await window.ApiClient.getTopQuotes(1, 2);
                    
                    if (quotes && quotes.quotes && quotes.quotes.length > 0) {
                        addResult(container, `✅ API客户端成功: 获取到 ${quotes.quotes.length} 条名言`, 'success');
                        addResult(container, `📊 总数: ${quotes.totalCount}`, 'info');
                    } else {
                        addResult(container, '❌ API客户端返回空数据', 'error');
                    }
                } catch (error) {
                    addResult(container, `❌ API客户端错误: ${error.message}`, 'error');
                }
            } else {
                addResult(container, '❌ API客户端未正确初始化', 'error');
            }
        }
        
        // 页面加载时开始
        window.addEventListener('load', loadScripts);
    </script>
</body>
</html>
