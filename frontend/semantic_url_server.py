#!/usr/bin/env python3
"""
语义化URL服务器
支持前端路由重写的自定义HTTP服务器
用于处理Quotese项目的语义化URL
"""

import http.server
import socketserver
import os
import re
import urllib.parse
from pathlib import Path

class SemanticURLHandler(http.server.SimpleHTTPRequestHandler):
    """
    自定义HTTP请求处理器，支持语义化URL路由
    """
    
    def __init__(self, *args, **kwargs):
        # 移除directory参数，让它使用默认行为
        super().__init__(*args, **kwargs)
    
    def do_HEAD(self):
        """处理HEAD请求"""
        print(f"🔍 收到HEAD请求: {self.path}")
        self.do_GET()

    def do_GET(self):
        """处理GET请求，支持语义化URL重写"""

        # URL解码处理
        try:
            decoded_path = urllib.parse.unquote(self.path)
            if decoded_path != self.path:
                print(f"🔧 URL解码: {self.path} → {decoded_path}")
                self.path = decoded_path
        except Exception as e:
            print(f"⚠️ URL解码失败: {e}")

        # 解析URL路径
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path

        print(f"🔍 收到GET请求: {self.path}")
        print(f"🔍 解析路径: {path}")

        # 处理缺少尾斜杠的情况
        if path in ['/categories', '/authors', '/sources']:
            redirect_path = path + '/'
            print(f"🔄 重定向到: {redirect_path}")
            self.send_response(301)
            self.send_header('Location', redirect_path)
            self.end_headers()
            return
        
        # 定义语义化URL模式和对应的HTML文件
        url_patterns = [
            # 作者相关URL
            (r'^/authors/$', 'authors.html'),
            (r'^/authors/([a-zA-Z0-9\-_]+)/$', 'author.html'),
            (r'^/authors/([a-zA-Z0-9\-_]+)/quotes/$', 'author.html'),

            # 类别相关URL
            (r'^/categories/$', 'categories.html'),
            (r'^/categories/([a-zA-Z0-9\-_]+)/$', 'category.html'),
            (r'^/categories/([a-zA-Z0-9\-_]+)/quotes/$', 'category.html'),

            # 来源相关URL
            (r'^/sources/$', 'sources.html'),
            (r'^/sources/([a-zA-Z0-9\-_]+)/$', 'source.html'),

            # 名言相关URL
            (r'^/quotes/$', 'quotes.html'),
            (r'^/quotes/(\d+)/$', 'quote.html'),

            # 搜索页面
            (r'^/search/$', 'search.html'),
        ]
        
        # 检查是否为语义化URL
        matched = False
        for pattern, html_file in url_patterns:
            match = re.match(pattern, path)
            if match:
                print(f"✅ 匹配到语义化URL模式: {pattern} -> {html_file}")
                print(f"   匹配组: {match.groups()}")

                # 检查HTML文件是否存在
                full_path = os.path.join(os.getcwd(), html_file)
                print(f"   检查文件: {full_path}")

                if os.path.exists(html_file):
                    # 重写路径到对应的HTML文件
                    self.path = '/' + html_file + (('?' + parsed_path.query) if parsed_path.query else '')
                    print(f"   ✅ 重写路径为: {self.path}")
                    matched = True
                    break
                else:
                    print(f"   ❌ HTML文件不存在: {html_file}")
                    print(f"   当前目录文件: {os.listdir('.')}")
                    self.send_error(404, f"HTML file not found: {html_file}")
                    return

        if not matched:
            print(f"❌ 未匹配到任何语义化URL模式: {path}")
        
        # 处理根路径
        if path == '/':
            if os.path.exists('index.html'):
                self.path = '/index.html'
            
        # 处理静态文件（CSS, JS, 图片等）
        elif path.startswith('/css/') or path.startswith('/js/') or path.startswith('/images/') or path.startswith('/components/'):
            # 直接处理静态文件
            print(f"✅ 静态文件请求: {path}")
            pass

        # 处理错误的静态文件路径（从语义化URL中来的）
        elif ('/css/' in path or '/js/' in path or '/images/' in path or '/components/' in path):
            # 提取正确的静态文件路径
            if '/css/' in path:
                correct_path = '/css/' + path.split('/css/')[-1]
            elif '/js/' in path:
                correct_path = '/js/' + path.split('/js/')[-1]
            elif '/images/' in path:
                correct_path = '/images/' + path.split('/images/')[-1]
            elif '/components/' in path:
                correct_path = '/components/' + path.split('/components/')[-1]
            else:
                correct_path = path

            print(f"🔧 修复静态文件路径: {path} → {correct_path}")
            self.path = correct_path + (('?' + parsed_path.query) if parsed_path.query else '')
            pass
        
        # 处理测试页面
        elif path.endswith('.html'):
            # 直接处理HTML文件
            pass
        
        # 如果没有匹配到任何模式，检查是否为现有文件
        elif not os.path.exists(path.lstrip('/')):
            # 如果文件不存在，可能是语义化URL但没有匹配到模式
            print(f"未匹配的路径，可能需要添加新的URL模式: {path}")
            
            # 尝试猜测应该使用的HTML文件
            if path == '/authors/':
                # 作者列表页面
                if os.path.exists('authors.html'):
                    self.path = '/authors.html'
                    print("重定向到 authors.html")
                else:
                    self.send_error(404, "Authors page not found")
                    return
            elif '/authors/' in path:
                # 单个作者页面
                if os.path.exists('author.html'):
                    self.path = '/author.html'
                    print("重定向到 author.html")
                else:
                    self.send_error(404, "Author page not found")
                    return
            elif path == '/categories/':
                # 分类列表页面
                if os.path.exists('categories.html'):
                    self.path = '/categories.html'
                    print("重定向到 categories.html")
                else:
                    self.send_error(404, "Categories page not found")
                    return
            elif '/categories/' in path:
                # 单个分类页面
                if os.path.exists('category.html'):
                    self.path = '/category.html'
                    print("重定向到 category.html")
                else:
                    self.send_error(404, "Category page not found")
                    return
            elif path == '/sources/':
                # 来源列表页面
                if os.path.exists('sources.html'):
                    self.path = '/sources.html'
                    print("重定向到 sources.html")
                else:
                    self.send_error(404, "Sources page not found")
                    return
            elif '/sources/' in path:
                # 单个来源页面
                if os.path.exists('source.html'):
                    self.path = '/source.html'
                    print("重定向到 source.html")
                else:
                    self.send_error(404, "Source page not found")
                    return
            elif '/quotes/' in path:
                if os.path.exists('quote.html'):
                    self.path = '/quote.html'
                    print("重定向到 quote.html")
                else:
                    self.send_error(404, "Quote page not found")
                    return
        
        # 调用父类方法处理请求
        try:
            super().do_GET()
        except Exception as e:
            print(f"处理请求时出错: {e}")
            self.send_error(500, f"Internal server error: {e}")
    
    def end_headers(self):
        """添加CORS头部"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.address_string()}] {format % args}")

def run_server(port=8081):
    """启动语义化URL服务器"""
    
    # 确保在正确的目录中
    frontend_dir = Path(__file__).parent
    os.chdir(frontend_dir)
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"启动语义化URL服务器在端口 {port}...")
    print(f"服务器地址: http://localhost:{port}")
    print("\n支持的语义化URL模式:")
    print("- /authors/ -> authors.html")
    print("- /authors/{slug}/ -> author.html")
    print("- /authors/{slug}/quotes/ -> author.html")
    print("- /categories/ -> categories.html")
    print("- /categories/{slug}/ -> category.html")
    print("- /categories/{slug}/quotes/ -> category.html")
    print("- /sources/ -> sources.html")
    print("- /sources/{slug}/ -> source.html")
    print("- /quotes/ -> quotes.html")
    print("- /quotes/{id}/ -> quote.html")
    print("- /search/ -> search.html")
    print("\n按 Ctrl+C 停止服务器")
    
    try:
        with socketserver.TCPServer(("", port), SemanticURLHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"端口 {port} 已被占用，请先停止现有服务器或使用其他端口")
        else:
            print(f"启动服务器时出错: {e}")

if __name__ == "__main__":
    import sys
    
    # 允许通过命令行参数指定端口
    port = 8081
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("无效的端口号，使用默认端口 8081")
    
    run_server(port)
