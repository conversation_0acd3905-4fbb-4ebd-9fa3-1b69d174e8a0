<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Quotes | Find Your Inspiration - Quotese.com</title>
    <meta name="description" content="Search our vast collection of quotes. Find the perfect quote for any occasion or mood.">
    <meta name="keywords" content="search quotes, find quotes, quote search, inspirational quotes, famous sayings">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link href="/css/variables.css" rel="stylesheet">
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/responsive.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <div id="navigation-container"></div>
    
    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-search mr-3 text-green-500"></i>
                    Search Quotes
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Find the perfect quote for any occasion. Search through thousands of inspiring quotes.
                </p>
            </div>
            
            <!-- Advanced Search Form -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Search Query -->
                    <div class="lg:col-span-2">
                        <label for="search-query" class="block text-sm font-medium text-gray-700 mb-2">Search Query</label>
                        <div class="relative">
                            <input type="text" id="search-query" placeholder="Enter keywords, phrases, or topics..." 
                                   class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- Search Type -->
                    <div>
                        <label for="search-type" class="block text-sm font-medium text-gray-700 mb-2">Search In</label>
                        <select id="search-type" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <option value="all">All Content</option>
                            <option value="quotes">Quotes Only</option>
                            <option value="authors">Authors Only</option>
                            <option value="categories">Categories Only</option>
                            <option value="sources">Sources Only</option>
                        </select>
                    </div>
                    
                    <!-- Category Filter -->
                    <div>
                        <label for="category-filter" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select id="category-filter" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <option value="">All Categories</option>
                            <option value="inspirational">Inspirational</option>
                            <option value="motivational">Motivational</option>
                            <option value="life">Life</option>
                            <option value="success">Success</option>
                            <option value="wisdom">Wisdom</option>
                            <option value="leadership">Leadership</option>
                            <option value="science">Science</option>
                            <option value="philosophy">Philosophy</option>
                        </select>
                    </div>
                    
                    <!-- Author Filter -->
                    <div>
                        <label for="author-filter" class="block text-sm font-medium text-gray-700 mb-2">Author</label>
                        <select id="author-filter" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <option value="">All Authors</option>
                            <!-- Authors will be loaded dynamically -->
                        </select>
                    </div>
                    
                    <!-- Search Button -->
                    <div class="flex items-end">
                        <button id="search-button" class="w-full bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                            <i class="fas fa-search mr-2"></i>Search
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Search Results -->
            <div id="search-results" class="hidden">
                <!-- Results Header -->
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-semibold text-gray-800">
                        Search Results
                        <span id="results-count" class="text-lg font-normal text-gray-600"></span>
                    </h2>
                    
                    <!-- Sort Options -->
                    <div class="flex items-center space-x-4">
                        <label for="sort-by" class="text-sm font-medium text-gray-700">Sort by:</label>
                        <select id="sort-by" class="px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <option value="relevance">Relevance</option>
                            <option value="date">Date</option>
                            <option value="author">Author</option>
                            <option value="length">Length</option>
                        </select>
                    </div>
                </div>
                
                <!-- Results Grid -->
                <div id="results-grid" class="space-y-6">
                    <!-- Search results will be loaded here -->
                </div>
                
                <!-- Pagination -->
                <div id="search-pagination" class="mt-12"></div>
            </div>
            
            <!-- No Results -->
            <div id="no-results" class="hidden text-center py-12">
                <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">No results found</h3>
                <p class="text-gray-500">Try adjusting your search terms or filters.</p>
            </div>
            
            <!-- Popular Searches -->
            <div id="popular-searches" class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Popular Searches</h3>
                <div class="flex flex-wrap gap-2">
                    <button class="search-tag px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" data-query="love">love</button>
                    <button class="search-tag px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" data-query="success">success</button>
                    <button class="search-tag px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" data-query="life">life</button>
                    <button class="search-tag px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" data-query="wisdom">wisdom</button>
                    <button class="search-tag px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" data-query="motivation">motivation</button>
                    <button class="search-tag px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" data-query="happiness">happiness</button>
                    <button class="search-tag px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" data-query="leadership">leadership</button>
                    <button class="search-tag px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" data-query="education">education</button>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <div id="footer-container"></div>
    
    <!-- Core Scripts -->
    <script src="js/theme.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/seo-manager.js"></script>
    <script src="js/page-router.js"></script>
    <script src="js/mobile-menu.js"></script>
    <script src="js/components/pagination.js"></script>
    <script src="js/components/quote-card.js"></script>
    <script src="js/components/breadcrumb.js"></script>
    <script src="js/social-meta.js"></script>
    
    <!-- Page Scripts -->
    <script src="js/pages/search.js"></script>
    
    <!-- Component Loader -->
    <script src="js/component-loader.js"></script>
</body>
</html>
