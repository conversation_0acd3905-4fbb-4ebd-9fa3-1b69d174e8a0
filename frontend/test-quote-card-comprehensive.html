<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Card Click Comprehensive Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .page-link {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .page-link:hover { background-color: #218838; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .test-status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            margin: 5px;
        }
        .status-pending { background-color: #ffc107; color: #212529; }
        .status-testing { background-color: #17a2b8; color: white; }
        .status-passed { background-color: #28a745; color: white; }
        .status-failed { background-color: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>Quote Card Click Comprehensive Test</h1>
    <p>This page provides comprehensive testing for quote card click functionality across all page types.</p>

    <div class="test-section">
        <h2>Test Environment Status</h2>
        <button onclick="checkEnvironment()">Check Environment</button>
        <div id="environment-status"></div>
    </div>

    <div class="test-section">
        <h2>Page Navigation Tests</h2>
        <p>Click the links below to test each page type. Each link opens in a new tab.</p>
        
        <div class="page-test" data-page="homepage">
            <h3>1. Homepage Test</h3>
            <span class="test-status status-pending" id="status-homepage">Pending</span>
            <a href="/?use-production-api=true" target="_blank" class="page-link">Open Homepage</a>
            <button onclick="testPageQuoteCards('homepage', '/')">Test Quote Cards</button>
        </div>

        <div class="page-test" data-page="category">
            <h3>2. Category Page Test</h3>
            <span class="test-status status-pending" id="status-category">Pending</span>
            <a href="/categories/intelligence/?use-production-api=true" target="_blank" class="page-link">Open Category Page</a>
            <button onclick="testPageQuoteCards('category', '/categories/intelligence/')">Test Quote Cards</button>
        </div>

        <div class="page-test" data-page="author">
            <h3>3. Author Page Test</h3>
            <span class="test-status status-pending" id="status-author">Pending</span>
            <a href="/authors/pearl-zhu/?use-production-api=true" target="_blank" class="page-link">Open Author Page</a>
            <button onclick="testPageQuoteCards('author', '/authors/pearl-zhu/')">Test Quote Cards</button>
        </div>

        <div class="page-test" data-page="source">
            <h3>4. Source Page Test</h3>
            <span class="test-status status-pending" id="status-source">Pending</span>
            <a href="/sources/1984/?use-production-api=true" target="_blank" class="page-link">Open Source Page</a>
            <button onclick="testPageQuoteCards('source', '/sources/1984/')">Test Quote Cards</button>
        </div>

        <div id="page-test-results"></div>
    </div>

    <div class="test-section">
        <h2>Automated Test Suite</h2>
        <button onclick="runAllTests()" style="background-color: #28a745; font-size: 16px; padding: 15px 30px;">🚀 Run All Tests</button>
        <button onclick="generateTestReport()">📊 Generate Test Report</button>
        <div id="automated-test-results"></div>
    </div>

    <div class="test-section">
        <h2>Manual Testing Instructions</h2>
        <div class="info test-result">
            <strong>Manual Testing Steps:</strong>
            <ol>
                <li>Click each page link above to open the page in a new tab</li>
                <li>On each page, look for quote cards (rectangular boxes with quotes)</li>
                <li>Hover over quote cards - cursor should change to pointer (hand icon)</li>
                <li>Click on any quote card</li>
                <li>Verify it navigates to a quote detail page (/quotes/{id}/)</li>
                <li>Check that the detail page loads correctly with quote content</li>
                <li>Return to this tab and mark the test result</li>
            </ol>
        </div>
        
        <div class="warning test-result">
            <strong>What to Look For:</strong>
            <ul>
                <li>✅ Quote cards have cursor-pointer style (hand cursor on hover)</li>
                <li>✅ Clicking quote cards navigates to /quotes/{id}/ URLs</li>
                <li>✅ Detail pages load without errors</li>
                <li>✅ Quote content, author, categories are displayed</li>
                <li>❌ JavaScript errors in browser console</li>
                <li>❌ Quote cards not clickable or missing cursor-pointer</li>
                <li>❌ Navigation fails or goes to wrong URL</li>
            </ul>
        </div>
    </div>

    <script>
        // Test results storage
        const testResults = {
            environment: null,
            pages: {
                homepage: { status: 'pending', errors: [] },
                category: { status: 'pending', errors: [] },
                author: { status: 'pending', errors: [] },
                source: { status: 'pending', errors: [] }
            },
            startTime: new Date(),
            endTime: null
        };

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
        }

        function updatePageStatus(pageType, status) {
            const statusElement = document.getElementById(`status-${pageType}`);
            statusElement.className = `test-status status-${status}`;
            statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            testResults.pages[pageType].status = status;
        }

        async function checkEnvironment() {
            const container = 'environment-status';
            document.getElementById(container).innerHTML = '';
            
            try {
                addResult(container, 'info', 'Checking environment...');
                
                // Check if servers are running
                const frontendCheck = await fetch('/').then(r => r.ok).catch(() => false);
                const backendCheck = await fetch('http://localhost:8000/api/').then(r => r.ok).catch(() => false);
                
                if (frontendCheck) {
                    addResult(container, 'success', 'Frontend server (port 8083) is running');
                } else {
                    addResult(container, 'error', 'Frontend server (port 8083) is not accessible');
                }
                
                if (backendCheck) {
                    addResult(container, 'success', 'Backend server (port 8000) is running');
                } else {
                    addResult(container, 'error', 'Backend server (port 8000) is not accessible');
                }
                
                // Check if production API is accessible
                try {
                    const prodApiCheck = await fetch('https://api.quotese.com/graphql/', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ query: '{ __typename }' })
                    }).then(r => r.ok);
                    
                    if (prodApiCheck) {
                        addResult(container, 'success', 'Production API is accessible');
                    } else {
                        addResult(container, 'warning', 'Production API check failed');
                    }
                } catch (error) {
                    addResult(container, 'warning', `Production API error: ${error.message}`);
                }
                
                testResults.environment = frontendCheck && backendCheck ? 'ready' : 'issues';
                
            } catch (error) {
                addResult(container, 'error', `Environment check failed: ${error.message}`);
                testResults.environment = 'error';
            }
        }

        async function testPageQuoteCards(pageType, url) {
            updatePageStatus(pageType, 'testing');
            
            try {
                addResult('page-test-results', 'info', `Testing ${pageType} page quote cards...`);
                
                // This is a placeholder for actual testing
                // In a real scenario, you would need to load the page content and test
                addResult('page-test-results', 'warning', 
                    `${pageType} page test requires manual verification. Please:
                    1. Click the "${pageType}" page link above
                    2. Test quote card clicking manually
                    3. Return here to mark the result`);
                
                // For now, mark as pending manual verification
                updatePageStatus(pageType, 'pending');
                
            } catch (error) {
                addResult('page-test-results', 'error', `${pageType} test failed: ${error.message}`);
                testResults.pages[pageType].errors.push(error.message);
                updatePageStatus(pageType, 'failed');
            }
        }

        async function runAllTests() {
            const container = 'automated-test-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Starting comprehensive test suite...');
            
            // Check environment first
            await checkEnvironment();
            
            if (testResults.environment !== 'ready') {
                addResult(container, 'error', 'Environment not ready. Please fix issues before running tests.');
                return;
            }
            
            // Test each page type
            const pages = ['homepage', 'category', 'author', 'source'];
            const urls = ['/', '/categories/intelligence/', '/authors/pearl-zhu/', '/sources/1984/'];
            
            for (let i = 0; i < pages.length; i++) {
                await testPageQuoteCards(pages[i], urls[i]);
                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait between tests
            }
            
            addResult(container, 'success', 'Automated tests completed. Manual verification required for quote card clicking.');
        }

        function generateTestReport() {
            testResults.endTime = new Date();
            const duration = testResults.endTime - testResults.startTime;
            
            const report = `
# Quote Card Click Test Report

**Test Date:** ${testResults.startTime.toLocaleString()}
**Duration:** ${Math.round(duration / 1000)} seconds
**Environment:** ${testResults.environment || 'Unknown'}

## Page Test Results

${Object.entries(testResults.pages).map(([page, result]) => `
### ${page.charAt(0).toUpperCase() + page.slice(1)} Page
- **Status:** ${result.status}
- **Errors:** ${result.errors.length > 0 ? result.errors.join(', ') : 'None'}
`).join('')}

## Manual Verification Required

This test requires manual verification of quote card clicking functionality.
Please test each page manually and update the results accordingly.

---
Generated at: ${new Date().toLocaleString()}
            `;
            
            // Create and download report
            const blob = new Blob([report], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `quote-card-test-report-${Date.now()}.md`;
            a.click();
            URL.revokeObjectURL(url);
            
            addResult('automated-test-results', 'success', 'Test report generated and downloaded');
        }

        // Auto-check environment on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(checkEnvironment, 1000);
        });
    </script>
</body>
</html>
