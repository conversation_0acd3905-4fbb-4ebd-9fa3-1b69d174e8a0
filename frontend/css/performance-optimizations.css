/**
 * Performance Optimizations CSS
 * Core Web Vitals优化样式
 * 减少CLS、优化LCP、提升整体性能
 */

/* 图片懒加载优化 */
img[loading="lazy"] {
    /* 为懒加载图片设置占位符，防止布局偏移 */
    background-color: #f3f4f6;
    background-image: linear-gradient(45deg, #f9fafb 25%, transparent 25%),
                      linear-gradient(-45deg, #f9fafb 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #f9fafb 75%),
                      linear-gradient(-45deg, transparent 75%, #f9fafb 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    min-height: 200px; /* 设置最小高度防止CLS */
    transition: opacity 0.3s ease;
}

/* 图片加载完成后的样式 */
img[loading="lazy"].loaded {
    background: none;
    opacity: 1;
}

/* 预加载关键资源的样式 */
.critical-image {
    /* 关键图片不使用懒加载 */
    loading: eager;
}

/* 布局稳定性优化 */
.layout-stable {
    /* 为动态内容预留空间 */
    min-height: 100px;
    transition: min-height 0.3s ease;
}

.layout-stable.loaded {
    min-height: auto;
}

/* 字体加载优化 */
@font-face {
    font-family: 'Inter';
    font-display: swap; /* 优化字体加载，减少FOIT */
    src: url('/fonts/inter.woff2') format('woff2');
}

/* 减少动画对性能的影响 */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 移动端性能优化 */
@media (max-width: 768px) {
    /* 减少移动端的动画和过渡效果 */
    .mobile-optimized * {
        animation-duration: 0.2s !important;
        transition-duration: 0.2s !important;
    }
    
    /* 优化移动端图片显示 */
    img {
        max-width: 100%;
        height: auto;
        object-fit: cover;
    }
}

/* 加载状态优化 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    /* 为加载动画设置固定尺寸，防止CLS */
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 内容容器优化 */
.content-container {
    /* 使用contain属性优化渲染性能 */
    contain: layout style paint;
}

/* 网格布局优化 */
.optimized-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    /* 使用CSS Grid减少JavaScript布局计算 */
}

/* 卡片组件优化 */
.card-optimized {
    /* 为卡片设置固定的宽高比，防止布局偏移 */
    aspect-ratio: 1 / 1.2;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    /* 使用transform而不是改变尺寸，避免重排 */
}

.card-optimized:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* 文本渲染优化 */
.optimized-text {
    text-rendering: optimizeSpeed; /* 优化文本渲染速度 */
    font-smooth: auto;
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
}

/* 滚动性能优化 */
.scroll-optimized {
    /* 启用硬件加速 */
    transform: translateZ(0);
    will-change: scroll-position;
}

/* 关键CSS内联样式 */
.critical-css {
    /* 首屏关键样式，应该内联到HTML中 */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
}

/* 非关键CSS延迟加载 */
.non-critical {
    /* 非首屏样式，可以延迟加载 */
    opacity: 0;
    transition: opacity 0.3s ease;
}

.non-critical.loaded {
    opacity: 1;
}

/* 预连接优化 */
.preconnect-hint {
    /* 为外部资源添加预连接提示 */
    /* 在HTML中使用: <link rel="preconnect" href="https://fonts.googleapis.com"> */
}

/* 资源提示优化 */
.preload-hint {
    /* 为关键资源添加预加载提示 */
    /* 在HTML中使用: <link rel="preload" href="/critical.css" as="style"> */
}

/* 响应式图片优化 */
.responsive-image {
    width: 100%;
    height: auto;
    /* 使用object-fit保持宽高比 */
    object-fit: cover;
    /* 设置固定的宽高比防止CLS */
    aspect-ratio: 16 / 9;
}

/* 懒加载占位符动画 */
.lazy-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 性能监控样式 */
.performance-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 9999;
    font-family: monospace;
}

.performance-indicator.good { background: rgba(34, 197, 94, 0.8); }
.performance-indicator.needs-improvement { background: rgba(251, 191, 36, 0.8); }
.performance-indicator.poor { background: rgba(239, 68, 68, 0.8); }

/* 打印样式优化 */
@media print {
    .no-print {
        display: none !important;
    }
    
    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .card-optimized {
        border: 2px solid #000;
    }
    
    .optimized-text {
        font-weight: bold;
    }
}

/* 暗色模式性能优化 */
@media (prefers-color-scheme: dark) {
    .dark-optimized {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .loading-spinner {
        border-color: #374151;
        border-top-color: #60a5fa;
    }
}
