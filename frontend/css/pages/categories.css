/* Categories List Page Styles */

/* Main container styles */
.categories-display {
    min-height: 400px;
    transition: all 0.3s ease;
}

/* Grid view styles */
.categories-display.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
}

/* List view styles */
.categories-display.list-view {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Category card styles */
.category-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background: white;
    padding: 1rem;
    text-decoration: none;
    color: inherit;
}

.dark .category-card {
    background: #1f2937;
    border-color: #374151;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.dark .category-card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Category card accent line */
.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.category-card:hover::before {
    transform: translateX(0);
}

/* Category icon styles */
.category-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    color: white;
    font-size: 1.25rem;
}

/* Category title styles */
.category-title {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    text-align: center;
    transition: color 0.3s ease;
}

.category-card:hover .category-title {
    color: #3b82f6;
}

.dark .category-card:hover .category-title {
    color: #60a5fa;
}

/* Category count styles */
.category-count {
    font-size: 0.75rem;
    color: #6b7280;
    text-align: center;
}

.dark .category-count {
    color: #9ca3af;
}

/* List view specific styles */
.categories-display.list-view .category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background: white;
    text-decoration: none;
    color: inherit;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.dark .categories-display.list-view .category-item {
    background: #1f2937;
    border-color: #374151;
}

.categories-display.list-view .category-item:hover {
    border-left-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateX(4px);
}

.dark .categories-display.list-view .category-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.list-view .category-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.list-view .category-icon {
    width: 2rem;
    height: 2rem;
    margin: 0;
    font-size: 0.875rem;
}

.list-view .category-title {
    font-size: 1rem;
    text-align: left;
    margin: 0;
}

.list-view .category-count {
    font-size: 0.875rem;
    color: #6b7280;
}

.dark .list-view .category-count {
    color: #9ca3af;
}

/* Search container styles */
.search-container {
    position: relative;
}

.search-container .fas.fa-search {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    pointer-events: none;
    z-index: 10;
}

/* Toolbar styles */
.toolbar {
    margin-bottom: 1.5rem;
}

.toolbar .controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

/* Loading spinner styles */
.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button styles */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Stats section styles */
.stats {
    margin-bottom: 1.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar .controls {
        justify-content: space-between;
        width: 100%;
    }
    
    .categories-display.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 0.75rem;
    }
    
    .category-card {
        padding: 0.75rem;
    }
    
    .category-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
    
    .category-title {
        font-size: 0.8rem;
    }
    
    .category-count {
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .categories-display.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.5rem;
    }
    
    .toolbar .controls {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .toolbar .controls > div {
        width: 100%;
        justify-content: space-between;
    }
}

/* Dark mode adjustments */
.dark .loading-spinner {
    border-color: #374151;
    border-top-color: #60a5fa;
}

/* Animation for category cards */
.category-card {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stagger animation for multiple cards */
.category-card:nth-child(1) { animation-delay: 0.1s; }
.category-card:nth-child(2) { animation-delay: 0.2s; }
.category-card:nth-child(3) { animation-delay: 0.3s; }
.category-card:nth-child(4) { animation-delay: 0.4s; }
.category-card:nth-child(5) { animation-delay: 0.5s; }
.category-card:nth-child(6) { animation-delay: 0.6s; }

/* Focus styles for accessibility */
.category-card:focus,
.category-item:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

#categories-search:focus,
#sort-select:focus,
#view-select:focus {
    outline: none;
    ring: 2px;
    ring-color: #3b82f6;
}
