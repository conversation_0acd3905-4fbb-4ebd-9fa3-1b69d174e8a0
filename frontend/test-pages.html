<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .loading { background-color: #fff3cd; color: #856404; }
        button { margin: 5px; padding: 8px 12px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .item { margin: 5px 0; padding: 8px; background: #f8f9fa; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>页面功能测试</h1>
    
    <div class="section">
        <h2>配置测试</h2>
        <button onclick="testConfig()">测试配置</button>
        <div id="config-results"></div>
    </div>
    
    <div class="section">
        <h2>Categories页面测试</h2>
        <button onclick="testCategoriesAPI()">测试Categories API</button>
        <div id="categories-results"></div>
    </div>
    
    <div class="section">
        <h2>Authors页面测试</h2>
        <button onclick="testAuthorsAPI()">测试Authors API</button>
        <div id="authors-results"></div>
    </div>
    
    <div class="section">
        <h2>Sources页面测试</h2>
        <button onclick="testSourcesAPI()">测试Sources API</button>
        <div id="sources-results"></div>
    </div>
    
    <!-- Load scripts in correct order -->
    <script src="/js/config.js?v=20250626"></script>
    <script src="/js/api-client.js?v=20250626"></script>
    
    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        function testConfig() {
            clearResults('config-results');
            addResult('config-results', '测试配置...', 'loading');
            
            if (window.AppConfig) {
                addResult('config-results', '✅ 配置已加载', 'success');
                addResult('config-results', `API端点: ${window.AppConfig.apiEndpoint}`, 'info');
                addResult('config-results', `GraphQL端点: ${window.AppConfig.graphqlEndpoint}`, 'info');
                addResult('config-results', `调试模式: ${window.AppConfig.debug}`, 'info');
            } else {
                addResult('config-results', '❌ 配置未加载', 'error');
            }
            
            if (window.ApiClient) {
                addResult('config-results', '✅ API客户端已初始化', 'success');
            } else {
                addResult('config-results', '❌ API客户端未初始化', 'error');
            }
        }
        
        async function testCategoriesAPI() {
            clearResults('categories-results');
            addResult('categories-results', '测试Categories API...', 'loading');
            
            if (!window.ApiClient) {
                addResult('categories-results', '❌ API客户端未初始化', 'error');
                return;
            }
            
            try {
                // 直接使用GraphQL查询
                const query = `
                    query {
                        categories(first: 10) {
                            id
                            name
                            quotesCount
                        }
                    }
                `;
                
                const result = await window.ApiClient.query(query);
                
                if (result && result.categories) {
                    addResult('categories-results', `✅ 成功获取 ${result.categories.length} 个类别`, 'success');
                    
                    result.categories.slice(0, 5).forEach(category => {
                        addResult('categories-results', 
                            `<div class="item">${category.name} (${category.quotesCount} quotes)</div>`, 
                            'info'
                        );
                    });
                } else {
                    addResult('categories-results', '❌ 返回数据格式错误', 'error');
                }
            } catch (error) {
                addResult('categories-results', `❌ API调用失败: ${error.message}`, 'error');
                console.error('Categories API error:', error);
            }
        }
        
        async function testAuthorsAPI() {
            clearResults('authors-results');
            addResult('authors-results', '测试Authors API...', 'loading');
            
            if (!window.ApiClient) {
                addResult('authors-results', '❌ API客户端未初始化', 'error');
                return;
            }
            
            try {
                const query = `
                    query {
                        authors(first: 10) {
                            id
                            name
                            quotesCount
                        }
                    }
                `;
                
                const result = await window.ApiClient.query(query);
                
                if (result && result.authors) {
                    addResult('authors-results', `✅ 成功获取 ${result.authors.length} 个作者`, 'success');
                    
                    result.authors.slice(0, 5).forEach(author => {
                        addResult('authors-results', 
                            `<div class="item">${author.name} (${author.quotesCount} quotes)</div>`, 
                            'info'
                        );
                    });
                } else {
                    addResult('authors-results', '❌ 返回数据格式错误', 'error');
                }
            } catch (error) {
                addResult('authors-results', `❌ API调用失败: ${error.message}`, 'error');
                console.error('Authors API error:', error);
            }
        }
        
        async function testSourcesAPI() {
            clearResults('sources-results');
            addResult('sources-results', '测试Sources API...', 'loading');
            
            if (!window.ApiClient) {
                addResult('sources-results', '❌ API客户端未初始化', 'error');
                return;
            }
            
            try {
                const query = `
                    query {
                        sources(first: 10) {
                            id
                            name
                            quotesCount
                        }
                    }
                `;
                
                const result = await window.ApiClient.query(query);
                
                if (result && result.sources) {
                    addResult('sources-results', `✅ 成功获取 ${result.sources.length} 个来源`, 'success');
                    
                    result.sources.slice(0, 5).forEach(source => {
                        addResult('sources-results', 
                            `<div class="item">${source.name} (${source.quotesCount} quotes)</div>`, 
                            'info'
                        );
                    });
                } else {
                    addResult('sources-results', '❌ 返回数据格式错误', 'error');
                }
            } catch (error) {
                addResult('sources-results', `❌ API调用失败: ${error.message}`, 'error');
                console.error('Sources API error:', error);
            }
        }
        
        // 页面加载后自动测试配置
        window.addEventListener('load', function() {
            setTimeout(() => {
                testConfig();
            }, 100);
        });
    </script>
</body>
</html>
