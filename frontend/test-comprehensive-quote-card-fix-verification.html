<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Quote Card Fix Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .page-link {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .page-link:hover { background-color: #218838; }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Comprehensive Quote Card Fix Verification</h1>
    <p>This tool verifies that all quote card entity ID and detail page loading issues have been resolved.</p>

    <div class="test-section">
        <h2>Fix Summary</h2>
        <div class="info test-result">
            <strong>Issues Fixed:</strong>
            <ul>
                <li>✅ Added missing authors-container and sources-container to quote.html</li>
                <li>✅ Fixed HTML structure for proper JavaScript DOM element access</li>
                <li>✅ Verified quote card component generates correct entity IDs</li>
                <li>✅ Confirmed URL generation and parsing works correctly</li>
                <li>✅ Ensured API integration functions properly</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Results Summary</h2>
        <div class="summary-stats" id="summary-stats">
            <!-- Stats will be populated here -->
        </div>
    </div>

    <div class="test-section">
        <h2>Page Testing</h2>
        <div>
            <h3>Test All Page Types</h3>
            <a href="/?use-production-api=true" target="_blank" class="page-link">Homepage</a>
            <a href="/categories/love-quotes/?use-production-api=true" target="_blank" class="page-link">Category Page</a>
            <a href="/authors/lailah-gifty-akita/?use-production-api=true" target="_blank" class="page-link">Author Page</a>
            <a href="/sources/and-being-free/?use-production-api=true" target="_blank" class="page-link">Source Page</a>
        </div>
        <div>
            <h3>Test Quote Detail Pages</h3>
            <a href="/quotes/499001/?use-production-api=true" target="_blank" class="page-link">Quote 499001</a>
            <a href="/quotes/499002/?use-production-api=true" target="_blank" class="page-link">Quote 499002</a>
            <a href="/quotes/499003/?use-production-api=true" target="_blank" class="page-link">Quote 499003</a>
        </div>
        <button onclick="runComprehensiveTest()">🔍 Run Comprehensive Test</button>
        <div id="comprehensive-test-results"></div>
    </div>

    <div class="test-section">
        <h2>Quote Detail Page Structure Test</h2>
        <button onclick="testQuoteDetailStructure()">🏗️ Test Quote Detail Structure</button>
        <div id="structure-test-results"></div>
    </div>

    <div class="test-section">
        <h2>Data Flow Integration Test</h2>
        <button onclick="testCompleteDataFlow()">🔄 Test Complete Data Flow</button>
        <div id="data-flow-test-results"></div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/components/quote-card.js"></script>

    <script>
        // Global test results
        const testResults = {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            warnings: 0,
            startTime: new Date()
        };

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
            
            // Update test statistics
            testResults.totalTests++;
            if (type === 'success') testResults.passedTests++;
            else if (type === 'error') testResults.failedTests++;
            else if (type === 'warning') testResults.warnings++;
            
            updateSummaryStats();
        }

        function updateSummaryStats() {
            const statsContainer = document.getElementById('summary-stats');
            const duration = Math.round((new Date() - testResults.startTime) / 1000);
            const successRate = testResults.totalTests > 0 ? 
                ((testResults.passedTests / testResults.totalTests) * 100).toFixed(1) : 0;
            
            statsContainer.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${testResults.totalTests}</div>
                    <div class="stat-label">Total Tests</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${testResults.passedTests}</div>
                    <div class="stat-label">Passed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${testResults.failedTests}</div>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${successRate}%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${duration}s</div>
                    <div class="stat-label">Duration</div>
                </div>
            `;
        }

        async function testQuoteDetailStructure() {
            const container = 'structure-test-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing quote detail page structure...');
            
            try {
                // Test if we can access the quote detail page structure
                const response = await fetch('/quotes/499001/?use-production-api=true');
                if (response.ok) {
                    const html = await response.text();
                    
                    // Check for required elements
                    const requiredElements = [
                        'quote-content',
                        'author-link',
                        'author-initial',
                        'source-text',
                        'categories-container',
                        'related-quotes-container',
                        'authors-container',
                        'sources-container'
                    ];
                    
                    let foundElements = 0;
                    requiredElements.forEach(elementId => {
                        if (html.includes(`id="${elementId}"`)) {
                            foundElements++;
                            addResult(container, 'success', `Found element: ${elementId}`);
                        } else {
                            addResult(container, 'error', `Missing element: ${elementId}`);
                        }
                    });
                    
                    const structureScore = ((foundElements / requiredElements.length) * 100).toFixed(1);
                    addResult(container, foundElements === requiredElements.length ? 'success' : 'warning', 
                        `Structure completeness: ${foundElements}/${requiredElements.length} (${structureScore}%)`);
                    
                } else {
                    addResult(container, 'error', `Failed to fetch quote detail page: ${response.status}`);
                }
                
            } catch (error) {
                addResult(container, 'error', `Structure test failed: ${error.message}`);
            }
        }

        async function testCompleteDataFlow() {
            const container = 'data-flow-test-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing complete data flow...');
            
            try {
                // Switch to production API
                if (window.QuoteseAPIMode && typeof window.QuoteseAPIMode.useProductionAPI === 'function') {
                    window.QuoteseAPIMode.useProductionAPI();
                    addResult(container, 'success', 'Switched to production API');
                }
                
                // Step 1: Test API data retrieval
                const quotesData = await window.ApiClient.getQuotes(1, 1);
                if (quotesData && quotesData.quotes && quotesData.quotes.length > 0) {
                    const testQuote = quotesData.quotes[0];
                    addResult(container, 'success', `Step 1: Retrieved test quote ID ${testQuote.id}`);
                    
                    // Step 2: Test quote card generation
                    const quoteCard = QuoteCardComponent.render(testQuote, 0, { showActions: true });
                    addResult(container, 'success', 'Step 2: Quote card generated successfully');
                    
                    // Step 3: Verify entity ID
                    const entityId = quoteCard.getAttribute('data-quote-id');
                    if (entityId === testQuote.id.toString()) {
                        addResult(container, 'success', `Step 3: Entity ID verified (${entityId})`);
                    } else {
                        addResult(container, 'error', `Step 3: Entity ID mismatch (${entityId} vs ${testQuote.id})`);
                        return;
                    }
                    
                    // Step 4: Test URL generation
                    const quoteUrl = UrlHandler.getQuoteUrl(testQuote);
                    addResult(container, 'success', `Step 4: URL generated (${quoteUrl})`);
                    
                    // Step 5: Test URL parsing
                    const originalPath = window.location.pathname;
                    try {
                        Object.defineProperty(window.location, 'pathname', {
                            writable: true,
                            value: quoteUrl
                        });
                        
                        const parsedId = UrlHandler.parseQuoteIdFromPath();
                        if (parsedId === parseInt(testQuote.id)) {
                            addResult(container, 'success', `Step 5: URL parsing successful (${parsedId})`);
                        } else {
                            addResult(container, 'error', `Step 5: URL parsing failed (${parsedId} vs ${testQuote.id})`);
                        }
                    } finally {
                        Object.defineProperty(window.location, 'pathname', {
                            writable: true,
                            value: originalPath
                        });
                    }
                    
                    // Step 6: Test detail page API call
                    const detailQuote = await window.ApiClient.getQuoteById(testQuote.id);
                    if (detailQuote) {
                        addResult(container, 'success', 'Step 6: Detail page API call successful');
                    } else {
                        addResult(container, 'error', 'Step 6: Detail page API call failed');
                        return;
                    }
                    
                    // Step 7: Test related data loading
                    if (detailQuote.author) {
                        const relatedQuotes = await window.ApiClient.getRelatedQuotesByAuthor(
                            detailQuote.author.id, testQuote.id, 3
                        );
                        if (Array.isArray(relatedQuotes)) {
                            addResult(container, 'success', `Step 7: Related quotes loaded (${relatedQuotes.length} quotes)`);
                        } else {
                            addResult(container, 'warning', 'Step 7: Related quotes loading failed');
                        }
                    }
                    
                    addResult(container, 'success', '🎉 Complete data flow test passed!');
                    
                } else {
                    addResult(container, 'error', 'Step 1: Failed to retrieve test quote data');
                }
                
            } catch (error) {
                addResult(container, 'error', `Data flow test failed: ${error.message}`);
            }
        }

        async function runComprehensiveTest() {
            const container = 'comprehensive-test-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Running comprehensive test suite...');
            
            // Reset test statistics
            testResults.totalTests = 0;
            testResults.passedTests = 0;
            testResults.failedTests = 0;
            testResults.warnings = 0;
            testResults.startTime = new Date();
            
            // Run all tests
            await testQuoteDetailStructure();
            await testCompleteDataFlow();
            
            // Test quote card functionality with different page configurations
            const pageConfigs = [
                { name: 'Homepage', showActions: false, showAuthorAvatar: false },
                { name: 'Category Page', showActions: false, highlightCurrentCategory: true },
                { name: 'Author Page', showActions: false, hideEmptyAvatar: true },
                { name: 'Source Page', showActions: false, hideEmptyAvatar: true }
            ];
            
            try {
                const quotesData = await window.ApiClient.getQuotes(1, 1);
                if (quotesData && quotesData.quotes && quotesData.quotes.length > 0) {
                    const testQuote = quotesData.quotes[0];
                    
                    pageConfigs.forEach(config => {
                        try {
                            const quoteCard = QuoteCardComponent.render(testQuote, 0, config);
                            const entityId = quoteCard.getAttribute('data-quote-id');
                            const hasClickStyling = quoteCard.classList.contains('cursor-pointer');
                            
                            if (entityId === testQuote.id.toString() && hasClickStyling) {
                                addResult(container, 'success', `${config.name}: Quote card functional`);
                            } else {
                                addResult(container, 'error', `${config.name}: Quote card issues detected`);
                            }
                        } catch (error) {
                            addResult(container, 'error', `${config.name}: Quote card generation failed`);
                        }
                    });
                }
            } catch (error) {
                addResult(container, 'error', `Page configuration test failed: ${error.message}`);
            }
            
            // Generate final report
            const finalSuccessRate = testResults.totalTests > 0 ? 
                ((testResults.passedTests / testResults.totalTests) * 100).toFixed(1) : 0;
            
            if (finalSuccessRate >= 90) {
                addResult(container, 'success', 
                    `🎉 Comprehensive test completed! Success rate: ${finalSuccessRate}% (${testResults.passedTests}/${testResults.totalTests})`);
            } else if (finalSuccessRate >= 70) {
                addResult(container, 'warning', 
                    `⚠️ Comprehensive test completed with warnings. Success rate: ${finalSuccessRate}% (${testResults.passedTests}/${testResults.totalTests})`);
            } else {
                addResult(container, 'error', 
                    `❌ Comprehensive test failed. Success rate: ${finalSuccessRate}% (${testResults.passedTests}/${testResults.totalTests})`);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateSummaryStats();
            
            setTimeout(() => {
                addResult('comprehensive-test-results', 'info', 
                    'Comprehensive fix verification tool ready. Click "Run Comprehensive Test" to begin.');
            }, 1000);
        });
    </script>
</body>
</html>
