#!/usr/bin/env python3
"""
简单的测试服务器
用于验证自定义HTTP处理器是否工作
"""

import http.server
import socketserver
import os
import re
import urllib.parse

class TestHandler(http.server.SimpleHTTPRequestHandler):
    """测试HTTP请求处理器"""

    def do_HEAD(self):
        """处理HEAD请求"""
        print(f"🔍 收到HEAD请求: {self.path}")
        self.do_GET()

    def do_GET(self):
        """处理GET请求"""
        print(f"🔍 收到GET请求: {self.path}")

        # 解析URL路径
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path

        print(f"🔍 解析路径: {path}")

        # 检查是否为语义化URL
        if path == '/categories/words/':
            print("✅ 匹配到 /categories/words/")
            # 重写路径到category.html
            self.path = '/category.html'
            print(f"✅ 重写路径为: {self.path}")
        elif path == '/authors/criss-jami/':
            print("✅ 匹配到 /authors/criss-jami/")
            # 重写路径到author.html
            self.path = '/author.html'
            print(f"✅ 重写路径为: {self.path}")
        elif path == '/sources/life/':
            print("✅ 匹配到 /sources/life/")
            # 重写路径到source.html
            self.path = '/source.html'
            print(f"✅ 重写路径为: {self.path}")
        else:
            print(f"❌ 未匹配的路径: {path}")

        # 调用父类方法处理请求
        try:
            super().do_GET()
        except Exception as e:
            print(f"❌ 处理请求时出错: {e}")
            self.send_error(500, f"Internal server error: {e}")
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"📝 [{self.address_string()}] {format % args}")

def run_test_server(port=8082):
    """启动测试服务器"""
    
    # 确保在正确的目录中
    os.chdir('/Users/<USER>/Documents/quotese_0503_online/frontend')
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"启动测试服务器在端口 {port}...")
    print(f"服务器地址: http://localhost:{port}")
    print("\n测试URL:")
    print("- http://localhost:8082/categories/words/")
    print("- http://localhost:8082/authors/criss-jami/")
    print("- http://localhost:8082/sources/life/")
    print("\n按 Ctrl+C 停止服务器")
    
    try:
        with socketserver.TCPServer(("", port), TestHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"端口 {port} 已被占用，请先停止现有服务器或使用其他端口")
        else:
            print(f"启动服务器时出错: {e}")

if __name__ == "__main__":
    run_test_server()
