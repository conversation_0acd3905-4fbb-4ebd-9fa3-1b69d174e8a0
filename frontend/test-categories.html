<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Categories Page - Quotese.com</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/dist/combined.css">
    <link rel="stylesheet" href="css/pages/categories.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Categories Page Test Suite</h1>
        
        <!-- Test Section 1: Basic Dependencies -->
        <div class="test-section">
            <h2>1. 基础依赖测试</h2>
            <button class="test-button" onclick="testDependencies()">测试依赖</button>
            <div id="dependencies-result" class="test-result"></div>
        </div>
        
        <!-- Test Section 2: DOM Elements -->
        <div class="test-section">
            <h2>2. DOM元素测试</h2>
            <button class="test-button" onclick="testDOMElements()">测试DOM元素</button>
            <div id="dom-result" class="test-result"></div>
            
            <!-- Add required containers for testing -->
            <div style="margin-top: 20px;">
                <h3>测试容器:</h3>
                <div id="navbar-container" style="border: 1px dashed #ccc; padding: 10px; margin: 5px;">navbar-container</div>
                <div id="breadcrumb-container" style="border: 1px dashed #ccc; padding: 10px; margin: 5px;">breadcrumb-container</div>
                <div id="loading-container" style="border: 1px dashed #ccc; padding: 10px; margin: 5px;">loading-container</div>
                <div id="categories-container" style="border: 1px dashed #ccc; padding: 10px; margin: 5px;">categories-container</div>
                <div id="error-container" style="border: 1px dashed #ccc; padding: 10px; margin: 5px;">error-container</div>
                <input id="categories-search" placeholder="Search..." style="margin: 5px; padding: 5px;">
                <select id="sort-select" style="margin: 5px; padding: 5px;">
                    <option value="popularity">Most Popular</option>
                    <option value="alphabetical">A-Z</option>
                </select>
                <select id="view-select" style="margin: 5px; padding: 5px;">
                    <option value="grid">Grid</option>
                    <option value="list">List</option>
                </select>
                <div id="footer-container" style="border: 1px dashed #ccc; padding: 10px; margin: 5px;">footer-container</div>
            </div>
        </div>
        
        <!-- Test Section 3: API Testing -->
        <div class="test-section">
            <h2>3. API测试</h2>
            <button class="test-button" onclick="testAPI()">测试API调用</button>
            <div id="api-result" class="test-result"></div>
        </div>
        
        <!-- Test Section 4: Page Initialization -->
        <div class="test-section">
            <h2>4. 页面初始化测试</h2>
            <button class="test-button" onclick="testPageInit()">测试页面初始化</button>
            <div id="init-result" class="test-result"></div>
        </div>
        
        <!-- Test Section 5: Component Loading -->
        <div class="test-section">
            <h2>5. 组件加载测试</h2>
            <button class="test-button" onclick="testComponentLoading()">测试组件加载</button>
            <div id="component-result" class="test-result"></div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="js/dist/core.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/page-router.js"></script>
    <script src="js/seo-manager.js"></script>
    <script src="js/component-loader.js"></script>
    <script src="js/components/breadcrumb.js"></script>
    <script src="js/components/navigation.js"></script>
    <script src="js/components/footer.js"></script>
    <script src="js/pages/categories.js"></script>
    
    <script>
        // Test functions
        function testDependencies() {
            const result = document.getElementById('dependencies-result');
            let output = '依赖检查结果:\n';
            
            const dependencies = {
                'ApiClient': window.ApiClient,
                'ComponentLoader': window.ComponentLoader,
                'PageRouter': window.PageRouter,
                'UrlHandler': window.UrlHandler,
                'initCategoriesListPage': window.initCategoriesListPage,
                'testCategoriesPage': window.testCategoriesPage
            };
            
            let allGood = true;
            for (const [name, obj] of Object.entries(dependencies)) {
                const status = obj ? '✅' : '❌';
                output += `${status} ${name}: ${!!obj}\n`;
                if (!obj) allGood = false;
            }
            
            result.textContent = output;
            result.className = `test-result ${allGood ? 'success' : 'error'}`;
        }
        
        function testDOMElements() {
            const result = document.getElementById('dom-result');
            let output = 'DOM元素检查结果:\n';
            
            const elements = [
                'navbar-container',
                'breadcrumb-container',
                'loading-container',
                'categories-container',
                'error-container',
                'categories-search',
                'sort-select',
                'view-select',
                'footer-container'
            ];
            
            let allGood = true;
            elements.forEach(id => {
                const element = document.getElementById(id);
                const status = element ? '✅' : '❌';
                output += `${status} ${id}: ${!!element}\n`;
                if (!element) allGood = false;
            });
            
            result.textContent = output;
            result.className = `test-result ${allGood ? 'success' : 'error'}`;
        }
        
        async function testAPI() {
            const result = document.getElementById('api-result');
            result.textContent = '正在测试API调用...';
            result.className = 'test-result info';
            
            try {
                if (!window.ApiClient) {
                    throw new Error('ApiClient不可用');
                }
                
                const categories = await window.ApiClient.getPopularCategories(5);
                let output = `API调用成功!\n`;
                output += `返回了 ${categories.length} 个分类:\n`;
                categories.forEach((cat, index) => {
                    output += `${index + 1}. ${cat.name} (${cat.count || 0} quotes)\n`;
                });
                
                result.textContent = output;
                result.className = 'test-result success';
            } catch (error) {
                result.textContent = `API调用失败: ${error.message}`;
                result.className = 'test-result error';
            }
        }
        
        async function testPageInit() {
            const result = document.getElementById('init-result');
            result.textContent = '正在测试页面初始化...';
            result.className = 'test-result info';
            
            try {
                if (!window.initCategoriesListPage) {
                    throw new Error('initCategoriesListPage函数不可用');
                }
                
                await window.initCategoriesListPage({});
                result.textContent = '页面初始化成功!';
                result.className = 'test-result success';
            } catch (error) {
                result.textContent = `页面初始化失败: ${error.message}`;
                result.className = 'test-result error';
            }
        }
        
        async function testComponentLoading() {
            const result = document.getElementById('component-result');
            result.textContent = '正在测试组件加载...';
            result.className = 'test-result info';
            
            try {
                if (!window.ComponentLoader) {
                    throw new Error('ComponentLoader不可用');
                }
                
                // Test loading a component
                await window.ComponentLoader.loadComponent('navbar-container', 'navigation');
                result.textContent = '组件加载测试成功!';
                result.className = 'test-result success';
            } catch (error) {
                result.textContent = `组件加载测试失败: ${error.message}`;
                result.className = 'test-result error';
            }
        }
        
        // Auto-run basic tests on load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded, running basic tests...');
            setTimeout(() => {
                testDependencies();
                testDOMElements();
            }, 1000);
        });
    </script>
</body>
</html>
