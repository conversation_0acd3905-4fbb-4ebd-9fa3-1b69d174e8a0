<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台错误检测工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error-item { margin: 5px 0; padding: 8px; background: #f8f9fa; border-left: 4px solid #dc3545; }
        .extension-error { border-left-color: #ffc107; }
        .app-error { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <h1>🔍 控制台错误检测工具</h1>
    <p>检测和分析页面中的JavaScript错误，区分应用错误和浏览器扩展错误</p>

    <div class="test-section">
        <h2>📊 错误监控状态</h2>
        <div id="monitoring-status"></div>
        <button onclick="startMonitoring()">开始监控</button>
        <button onclick="stopMonitoring()">停止监控</button>
        <button onclick="clearErrors()">清空错误</button>
    </div>

    <div class="test-section">
        <h2>🚨 检测到的错误</h2>
        <div id="detected-errors"></div>
    </div>

    <div class="test-section">
        <h2>📋 错误分类统计</h2>
        <div id="error-statistics"></div>
    </div>

    <div class="test-section">
        <h2>🔧 应用功能测试</h2>
        <button onclick="testApplicationFunctions()">测试应用功能</button>
        <div id="function-test-results"></div>
    </div>

    <script>
        let isMonitoring = false;
        let detectedErrors = [];
        let originalConsoleError = console.error;
        let originalWindowError = window.onerror;
        let originalUnhandledRejection = window.onunhandledrejection;

        function addResult(containerId, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById(containerId).appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 错误分类函数
        function classifyError(error, source) {
            const errorText = error.toString().toLowerCase();
            const sourceText = (source || '').toLowerCase();
            
            // 浏览器扩展相关的错误特征
            const extensionIndicators = [
                'runtime.lasterror',
                'message port closed',
                'content.bundle.js',
                'extension',
                'chrome-extension',
                'moz-extension',
                'auth required',
                'content script',
                'background script'
            ];
            
            // 应用相关的错误特征
            const appIndicators = [
                'source.js',
                'category.js',
                'author.js',
                'api-client.js',
                'url-handler.js',
                'page-router.js',
                'entity-id-mapper.js'
            ];
            
            // 检查是否为扩展错误
            for (const indicator of extensionIndicators) {
                if (errorText.includes(indicator) || sourceText.includes(indicator)) {
                    return 'extension';
                }
            }
            
            // 检查是否为应用错误
            for (const indicator of appIndicators) {
                if (sourceText.includes(indicator)) {
                    return 'application';
                }
            }
            
            // 默认分类为未知
            return 'unknown';
        }

        // 开始监控错误
        function startMonitoring() {
            if (isMonitoring) return;
            
            isMonitoring = true;
            detectedErrors = [];
            
            // 监控console.error
            console.error = function(...args) {
                const error = {
                    type: 'console.error',
                    message: args.join(' '),
                    timestamp: new Date().toISOString(),
                    source: 'console',
                    classification: classifyError(args.join(' '), 'console')
                };
                detectedErrors.push(error);
                updateErrorDisplay();
                originalConsoleError.apply(console, args);
            };
            
            // 监控window.onerror
            window.onerror = function(message, source, lineno, colno, error) {
                const errorObj = {
                    type: 'window.onerror',
                    message: message,
                    source: source,
                    line: lineno,
                    column: colno,
                    timestamp: new Date().toISOString(),
                    classification: classifyError(message, source)
                };
                detectedErrors.push(errorObj);
                updateErrorDisplay();
                
                if (originalWindowError) {
                    return originalWindowError(message, source, lineno, colno, error);
                }
                return false;
            };
            
            // 监控unhandled promise rejections
            window.onunhandledrejection = function(event) {
                const error = {
                    type: 'unhandledrejection',
                    message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
                    timestamp: new Date().toISOString(),
                    source: 'promise',
                    classification: classifyError(event.reason ? event.reason.toString() : '', 'promise')
                };
                detectedErrors.push(error);
                updateErrorDisplay();
                
                if (originalUnhandledRejection) {
                    return originalUnhandledRejection(event);
                }
            };
            
            updateMonitoringStatus();
            addResult('monitoring-status', '✅ 错误监控已启动', 'success');
        }

        // 停止监控错误
        function stopMonitoring() {
            if (!isMonitoring) return;
            
            isMonitoring = false;
            
            // 恢复原始函数
            console.error = originalConsoleError;
            window.onerror = originalWindowError;
            window.onunhandledrejection = originalUnhandledRejection;
            
            updateMonitoringStatus();
            addResult('monitoring-status', '⏹️ 错误监控已停止', 'warning');
        }

        // 清空错误记录
        function clearErrors() {
            detectedErrors = [];
            clearResults('detected-errors');
            clearResults('error-statistics');
            addResult('monitoring-status', '🧹 错误记录已清空', 'info');
        }

        // 更新监控状态显示
        function updateMonitoringStatus() {
            const status = isMonitoring ? '🟢 监控中' : '🔴 已停止';
            document.getElementById('monitoring-status').innerHTML = `
                <div class="test-result info">
                    <strong>监控状态:</strong> ${status}<br>
                    <strong>检测到的错误数量:</strong> ${detectedErrors.length}
                </div>
            `;
        }

        // 更新错误显示
        function updateErrorDisplay() {
            const container = document.getElementById('detected-errors');
            container.innerHTML = '';
            
            if (detectedErrors.length === 0) {
                container.innerHTML = '<div class="test-result success">✅ 暂无检测到错误</div>';
                return;
            }
            
            detectedErrors.forEach((error, index) => {
                const errorClass = error.classification === 'extension' ? 'extension-error' : 
                                 error.classification === 'application' ? 'app-error' : '';
                
                const errorDiv = document.createElement('div');
                errorDiv.className = `error-item ${errorClass}`;
                errorDiv.innerHTML = `
                    <strong>错误 #${index + 1}</strong> [${error.classification.toUpperCase()}]<br>
                    <strong>类型:</strong> ${error.type}<br>
                    <strong>消息:</strong> ${error.message}<br>
                    <strong>来源:</strong> ${error.source || 'Unknown'}<br>
                    <strong>时间:</strong> ${error.timestamp}<br>
                    ${error.line ? `<strong>位置:</strong> 行${error.line}:列${error.column}<br>` : ''}
                `;
                container.appendChild(errorDiv);
            });
            
            updateErrorStatistics();
        }

        // 更新错误统计
        function updateErrorStatistics() {
            const stats = {
                total: detectedErrors.length,
                extension: detectedErrors.filter(e => e.classification === 'extension').length,
                application: detectedErrors.filter(e => e.classification === 'application').length,
                unknown: detectedErrors.filter(e => e.classification === 'unknown').length
            };
            
            const container = document.getElementById('error-statistics');
            container.innerHTML = `
                <div class="test-result info">
                    <h4>📊 错误分类统计</h4>
                    <strong>总错误数:</strong> ${stats.total}<br>
                    <strong>🔌 浏览器扩展错误:</strong> ${stats.extension} (${Math.round(stats.extension/stats.total*100) || 0}%)<br>
                    <strong>🚨 应用程序错误:</strong> ${stats.application} (${Math.round(stats.application/stats.total*100) || 0}%)<br>
                    <strong>❓ 未知来源错误:</strong> ${stats.unknown} (${Math.round(stats.unknown/stats.total*100) || 0}%)<br>
                </div>
            `;
            
            if (stats.application > 0) {
                container.innerHTML += '<div class="test-result error">⚠️ 检测到应用程序错误，需要修复！</div>';
            } else if (stats.extension > 0) {
                container.innerHTML += '<div class="test-result warning">ℹ️ 检测到的错误来自浏览器扩展，不影响应用功能</div>';
            } else if (stats.total === 0) {
                container.innerHTML += '<div class="test-result success">✅ 未检测到任何错误</div>';
            }
        }

        // 测试应用功能
        async function testApplicationFunctions() {
            const container = 'function-test-results';
            clearResults(container);
            
            addResult(container, '🧪 开始测试应用功能...', 'info');
            
            // 测试1: 检查关键对象是否存在
            const criticalObjects = [
                'window.UrlHandler',
                'window.ApiClient', 
                'window.EntityIdMapper',
                'window.SEOManager',
                'window.PageRouter'
            ];
            
            let objectsOk = true;
            for (const objPath of criticalObjects) {
                const obj = objPath.split('.').reduce((o, p) => o && o[p], window);
                if (obj) {
                    addResult(container, `✅ ${objPath} 存在`, 'success');
                } else {
                    addResult(container, `❌ ${objPath} 不存在`, 'error');
                    objectsOk = false;
                }
            }
            
            // 测试2: 测试URL处理功能
            if (window.UrlHandler) {
                try {
                    const testSlug = 'his-final-gift';
                    const sourceName = window.UrlHandler.getSourceNameFromUrl();
                    addResult(container, `✅ UrlHandler.getSourceNameFromUrl() 正常工作`, 'success');
                } catch (error) {
                    addResult(container, `❌ UrlHandler 功能异常: ${error.message}`, 'error');
                    objectsOk = false;
                }
            }
            
            // 测试3: 测试EntityIdMapper功能
            if (window.EntityIdMapper) {
                try {
                    const stats = window.EntityIdMapper.getStats();
                    addResult(container, `✅ EntityIdMapper 正常工作，统计: ${JSON.stringify(stats)}`, 'success');
                } catch (error) {
                    addResult(container, `❌ EntityIdMapper 功能异常: ${error.message}`, 'error');
                    objectsOk = false;
                }
            }
            
            // 测试4: 测试API客户端
            if (window.ApiClient) {
                try {
                    // 只测试配置，不实际发送请求
                    const useMockData = window.ApiClient.useMockData;
                    addResult(container, `✅ ApiClient 配置正常，useMockData: ${useMockData}`, 'success');
                } catch (error) {
                    addResult(container, `❌ ApiClient 配置异常: ${error.message}`, 'error');
                    objectsOk = false;
                }
            }
            
            // 总结
            if (objectsOk) {
                addResult(container, '🎉 所有应用功能测试通过！', 'success');
            } else {
                addResult(container, '⚠️ 部分应用功能存在问题', 'warning');
            }
        }

        // 页面加载时自动开始监控
        window.addEventListener('load', () => {
            updateMonitoringStatus();
            addResult('monitoring-status', '📋 错误检测工具已加载，点击"开始监控"开始检测', 'info');
        });
    </script>
</body>
</html>
