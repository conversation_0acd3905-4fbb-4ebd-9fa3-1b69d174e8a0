<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EntityIdMapper调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .query-test { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .api-result { margin: 5px 0; padding: 8px; background: #fff; border: 1px solid #ddd; border-radius: 3px; }
        .timing { color: #666; font-size: 0.9em; }
        .debug-info { background: #f1f3f4; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 EntityIdMapper调试工具</h1>
    <p>专门调试Sources页面EntityIdMapper查询失败问题</p>

    <div class="test-section">
        <h2>🚨 失败的查询案例</h2>
        <div id="failed-cases"></div>
        <button onclick="testFailedCases()">测试失败案例</button>
        <button onclick="testWithMockData()">使用模拟数据测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="test-section">
        <h2>🔧 API连接测试</h2>
        <div id="api-connection-results"></div>
        <button onclick="testApiConnection()">测试API连接</button>
    </div>

    <div class="test-section">
        <h2>🔍 Slug转换测试</h2>
        <div id="slug-conversion-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 EntityIdMapper状态</h2>
        <div id="mapper-status"></div>
    </div>

    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/entity-id-mapper.js"></script>
    <script>
        // 失败的测试案例
        const failedCases = [
            {
                name: "长slug案例1",
                slug: "rise-up-and-salute-the-sun-the-writings-of-suzy-kassem",
                expectedName: "Rise Up And Salute The Sun The Writings Of Suzy Kassem",
                url: "/sources/rise-up-and-salute-the-sun-the-writings-of-suzy-kassem/"
            },
            {
                name: "短slug案例",
                slug: "enders-game",
                expectedName: "Enders Game",
                url: "/sources/enders-game/"
            },
            {
                name: "已知工作案例",
                slug: "meditations",
                expectedName: "Meditations",
                url: "/sources/meditations/"
            }
        ];

        function addResult(containerId, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById(containerId).appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults() {
            ['failed-cases', 'api-connection-results', 'slug-conversion-results', 'mapper-status'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
        }

        // 显示失败案例
        function displayFailedCases() {
            const container = document.getElementById('failed-cases');
            container.innerHTML = '';
            
            failedCases.forEach((testCase, index) => {
                const div = document.createElement('div');
                div.className = 'query-test';
                div.innerHTML = `
                    <strong>${testCase.name}</strong><br>
                    <strong>Slug:</strong> ${testCase.slug}<br>
                    <strong>预期名称:</strong> ${testCase.expectedName}<br>
                    <strong>URL:</strong> ${testCase.url}<br>
                    <strong>Slug长度:</strong> ${testCase.slug.length} 字符
                `;
                container.appendChild(div);
            });
        }

        // 测试API连接
        async function testApiConnection() {
            addResult('api-connection-results', '🔗 开始测试API连接...', 'info');
            
            // 检查API客户端配置
            if (window.ApiClient) {
                addResult('api-connection-results', `
                    <strong>API客户端配置:</strong><br>
                    <strong>端点:</strong> ${window.ApiClient.apiEndpoint}<br>
                    <strong>使用模拟数据:</strong> ${window.ApiClient.useMockData}
                `, 'info');
            } else {
                addResult('api-connection-results', '❌ API客户端未初始化', 'error');
                return;
            }

            // 测试基本API连接
            try {
                const startTime = performance.now();
                const stats = await window.ApiClient.getStats();
                const endTime = performance.now();
                const duration = (endTime - startTime).toFixed(2);
                
                addResult('api-connection-results', `
                    ✅ <strong>API连接成功</strong><br>
                    <strong>响应时间:</strong> ${duration}ms<br>
                    <strong>统计数据:</strong> ${JSON.stringify(stats, null, 2)}
                `, 'success');
            } catch (error) {
                addResult('api-connection-results', `
                    ❌ <strong>API连接失败</strong><br>
                    <strong>错误:</strong> ${error.message}<br>
                    <strong>详情:</strong> ${error.stack}
                `, 'error');
            }
        }

        // 测试失败案例
        async function testFailedCases() {
            addResult('failed-cases', '🧪 开始测试失败案例...', 'info');
            
            for (const testCase of failedCases) {
                addResult('failed-cases', `<strong>测试 ${testCase.name}</strong>`, 'info');
                
                // 1. 测试slug转换
                const convertedName = window.UrlHandler.deslugify(testCase.slug);
                const conversionCorrect = convertedName === testCase.expectedName;
                
                addResult('failed-cases', `
                    <strong>Slug转换:</strong><br>
                    <strong>输入:</strong> ${testCase.slug}<br>
                    <strong>输出:</strong> ${convertedName}<br>
                    <strong>预期:</strong> ${testCase.expectedName}<br>
                    <strong>正确:</strong> ${conversionCorrect ? '✅ 是' : '❌ 否'}
                `, conversionCorrect ? 'success' : 'warning');
                
                // 2. 测试EntityIdMapper查询
                try {
                    const startTime = performance.now();
                    const result = await window.findEntityWithPriority(
                        'sources',
                        testCase.slug,
                        convertedName,
                        window.ApiClient.getSourceByName.bind(window.ApiClient)
                    );
                    const endTime = performance.now();
                    const duration = (endTime - startTime).toFixed(2);
                    
                    if (result) {
                        addResult('failed-cases', `
                            ✅ <strong>EntityIdMapper查询成功</strong><br>
                            <strong>结果:</strong> ${result.name} (ID: ${result.id})<br>
                            <strong>来源:</strong> ${result.fromCache ? '映射表' : 'API查询'}<br>
                            <strong>查询时间:</strong> ${duration}ms
                        `, 'success');
                    } else {
                        addResult('failed-cases', `
                            ❌ <strong>EntityIdMapper查询失败</strong><br>
                            <strong>查询时间:</strong> ${duration}ms<br>
                            <strong>所有查询都失败了</strong>
                        `, 'error');
                    }
                } catch (error) {
                    addResult('failed-cases', `
                        ❌ <strong>EntityIdMapper查询异常</strong><br>
                        <strong>错误:</strong> ${error.message}
                    `, 'error');
                }
                
                // 3. 测试直接API查询
                await testDirectApiQuery(testCase, convertedName);
            }
        }

        // 测试直接API查询
        async function testDirectApiQuery(testCase, convertedName) {
            const queries = [testCase.slug, convertedName, convertedName.toLowerCase()];
            
            for (const query of queries) {
                try {
                    const startTime = performance.now();
                    const result = await window.ApiClient.getSourceByName(query);
                    const endTime = performance.now();
                    const duration = (endTime - startTime).toFixed(2);
                    
                    if (result) {
                        addResult('failed-cases', `
                            ✅ <strong>直接API查询成功</strong><br>
                            <strong>查询值:</strong> "${query}"<br>
                            <strong>结果:</strong> ${result.name} (ID: ${result.id})<br>
                            <strong>查询时间:</strong> ${duration}ms
                        `, 'success');
                        return; // 找到结果就停止
                    } else {
                        addResult('failed-cases', `
                            ❌ <strong>直接API查询失败</strong><br>
                            <strong>查询值:</strong> "${query}"<br>
                            <strong>结果:</strong> null<br>
                            <strong>查询时间:</strong> ${duration}ms
                        `, 'warning');
                    }
                } catch (error) {
                    addResult('failed-cases', `
                        ❌ <strong>直接API查询异常</strong><br>
                        <strong>查询值:</strong> "${query}"<br>
                        <strong>错误:</strong> ${error.message}
                    `, 'error');
                }
            }
        }

        // 使用模拟数据测试
        async function testWithMockData() {
            addResult('failed-cases', '🎭 开始使用模拟数据测试...', 'info');
            
            // 临时启用模拟数据
            const originalUseMockData = window.ApiClient.useMockData;
            window.ApiClient.useMockData = true;
            
            try {
                for (const testCase of failedCases) {
                    addResult('failed-cases', `<strong>模拟数据测试 ${testCase.name}</strong>`, 'info');
                    
                    const convertedName = window.UrlHandler.deslugify(testCase.slug);
                    
                    // 测试模拟数据查询
                    try {
                        const result = await window.ApiClient.getSourceByName(convertedName);
                        if (result) {
                            addResult('failed-cases', `
                                ✅ <strong>模拟数据查询成功</strong><br>
                                <strong>查询名称:</strong> ${convertedName}<br>
                                <strong>结果:</strong> ${result.name} (ID: ${result.id})
                            `, 'success');
                        } else {
                            addResult('failed-cases', `
                                ❌ <strong>模拟数据查询失败</strong><br>
                                <strong>查询名称:</strong> ${convertedName}<br>
                                <strong>结果:</strong> null
                            `, 'warning');
                        }
                    } catch (error) {
                        addResult('failed-cases', `
                            ❌ <strong>模拟数据查询异常</strong><br>
                            <strong>错误:</strong> ${error.message}
                        `, 'error');
                    }
                }
            } finally {
                // 恢复原始设置
                window.ApiClient.useMockData = originalUseMockData;
                addResult('failed-cases', `🔄 已恢复API设置 (useMockData: ${originalUseMockData})`, 'info');
            }
        }

        // 显示EntityIdMapper状态
        function showMapperStatus() {
            if (window.EntityIdMapper) {
                const stats = window.EntityIdMapper.getStats();
                const mappings = window.EntityIdMapper.mappings;
                
                addResult('mapper-status', `
                    <strong>EntityIdMapper状态:</strong><br>
                    <strong>统计:</strong> ${JSON.stringify(stats, null, 2)}<br>
                    <strong>Sources映射数量:</strong> ${Object.keys(mappings.sources || {}).length}<br>
                    <strong>Sources映射:</strong> ${JSON.stringify(mappings.sources, null, 2)}
                `, 'info');
            } else {
                addResult('mapper-status', '❌ EntityIdMapper未初始化', 'error');
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            displayFailedCases();
            showMapperStatus();
            addResult('mapper-status', '📋 EntityIdMapper调试工具已加载', 'info');
        });
    </script>
</body>
</html>
