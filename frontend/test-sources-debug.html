<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sources Debug Test - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .test-result {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #1f2937;
            color: #f3f4f6;
            padding: 12px;
            border-radius: 6px;
            margin: 8px 0;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-success {
            background: #065f46;
            color: #d1fae5;
        }
        .test-error {
            background: #7f1d1d;
            color: #fecaca;
        }
        .test-warning {
            background: #92400e;
            color: #fef3c7;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-bug mr-3 text-red-500"></i>
                Sources功能全面调试
            </h1>
            
            <!-- 配置检查 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">1. 配置检查</h2>
                <div id="config-test" class="test-result">检测中...</div>
            </div>
            
            <!-- API客户端检查 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">2. API客户端检查</h2>
                <div id="api-test" class="test-result">检测中...</div>
            </div>
            
            <!-- MockData检查 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">3. MockData检查</h2>
                <div id="mockdata-test" class="test-result">检测中...</div>
            </div>
            
            <!-- Sources数据流测试 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">4. Sources数据流测试</h2>
                <div id="dataflow-test" class="test-result">检测中...</div>
            </div>
            
            <!-- 手动测试 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">5. 手动测试</h2>
                <div class="flex flex-wrap gap-2 mb-4">
                    <button onclick="testSource('healology')" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        测试 Healology
                    </button>
                    <button onclick="testSource('atlas-shrugged')" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        测试 Atlas Shrugged
                    </button>
                    <button onclick="testSource('long-walk-to-freedom')" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        测试 Long Walk to Freedom
                    </button>
                    <button onclick="testAllSources()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        测试所有来源
                    </button>
                </div>
                <div id="manual-test" class="test-result">点击按钮开始手动测试...</div>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/config.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/url-handler.js"></script>

    <script>
        // 自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runAllTests, 500);
        });
        
        function runAllTests() {
            testConfig();
            testApiClient();
            testMockData();
            testDataFlow();
        }
        
        function testConfig() {
            const configTest = document.getElementById('config-test');
            try {
                let results = '配置检查结果:\n';
                
                results += `AppConfig存在: ${!!window.AppConfig}\n`;
                if (window.AppConfig) {
                    results += `API端点: ${window.AppConfig.apiEndpoint}\n`;
                    results += `使用模拟数据: ${window.AppConfig.useMockData}\n`;
                    results += `调试模式: ${window.AppConfig.debug}\n`;
                }
                
                if (window.AppConfig && !window.AppConfig.useMockData) {
                    results += '\n⚠️  警告: 当前配置不使用模拟数据，这可能导致source页面失败！\n';
                    configTest.className = 'test-result test-warning';
                } else {
                    configTest.className = 'test-result test-success';
                }
                
                configTest.textContent = results;
            } catch (error) {
                configTest.textContent = `配置检查错误: ${error.message}`;
                configTest.className = 'test-result test-error';
            }
        }
        
        function testApiClient() {
            const apiTest = document.getElementById('api-test');
            try {
                let results = 'API客户端检查结果:\n';
                
                results += `ApiClient存在: ${!!window.ApiClient}\n`;
                if (window.ApiClient) {
                    results += `使用模拟数据: ${window.ApiClient.useMockData}\n`;
                    results += `API端点: ${window.ApiClient.apiEndpoint}\n`;
                    results += `getSourceByName方法存在: ${typeof window.ApiClient.getSourceByName === 'function'}\n`;
                    results += `getQuotes方法存在: ${typeof window.ApiClient.getQuotes === 'function'}\n`;
                }
                
                if (window.ApiClient && !window.ApiClient.useMockData) {
                    results += '\n⚠️  警告: ApiClient不使用模拟数据，将尝试连接真实API！\n';
                    apiTest.className = 'test-result test-warning';
                } else {
                    apiTest.className = 'test-result test-success';
                }
                
                apiTest.textContent = results;
            } catch (error) {
                apiTest.textContent = `API客户端检查错误: ${error.message}`;
                apiTest.className = 'test-result test-error';
            }
        }
        
        function testMockData() {
            const mockdataTest = document.getElementById('mockdata-test');
            try {
                let results = 'MockData检查结果:\n';
                
                results += `MockData存在: ${!!window.MockData}\n`;
                if (window.MockData) {
                    results += `来源总数: ${window.MockData.sources.length}\n`;
                    results += `名言总数: ${window.MockData.quotes.length}\n`;
                    results += `getSourceByName方法存在: ${typeof window.MockData.getSourceByName === 'function'}\n`;
                    results += `getQuotes方法存在: ${typeof window.MockData.getQuotes === 'function'}\n`;
                    
                    // 检查Healology
                    const healologySource = window.MockData.sources.find(s => s.name === 'Healology');
                    results += `\nHealology来源存在: ${!!healologySource}\n`;
                    if (healologySource) {
                        results += `Healology ID: ${healologySource.id}\n`;
                        results += `Healology 名言数量: ${healologySource.count}\n`;
                        
                        // 检查相关名言
                        const healologyQuotes = window.MockData.quotes.filter(q => 
                            q.sources && q.sources.some(s => s.id === healologySource.id)
                        );
                        results += `实际Healology名言数量: ${healologyQuotes.length}\n`;
                    }
                }
                
                mockdataTest.textContent = results;
                mockdataTest.className = 'test-result test-success';
            } catch (error) {
                mockdataTest.textContent = `MockData检查错误: ${error.message}`;
                mockdataTest.className = 'test-result test-error';
            }
        }
        
        async function testDataFlow() {
            const dataflowTest = document.getElementById('dataflow-test');
            try {
                let results = '数据流测试结果:\n';
                
                // 测试getSourceByName
                results += '\n1. 测试getSourceByName("healology"):\n';
                const source = await window.ApiClient.getSourceByName('healology');
                results += `   结果: ${source ? source.name : 'null'}\n`;
                results += `   ID: ${source ? source.id : 'null'}\n`;
                
                if (source) {
                    // 测试getQuotes with sourceId
                    results += '\n2. 测试getQuotes with sourceId:\n';
                    const quotesData = await window.ApiClient.getQuotes(1, 10, { sourceId: source.id });
                    results += `   名言数量: ${quotesData.quotes ? quotesData.quotes.length : 0}\n`;
                    results += `   总数: ${quotesData.totalCount || 0}\n`;
                    
                    if (quotesData.quotes && quotesData.quotes.length > 0) {
                        results += `   第一条名言: "${quotesData.quotes[0].content.substring(0, 50)}..."\n`;
                        results += `   作者: ${quotesData.quotes[0].author.name}\n`;
                    }
                }
                
                dataflowTest.textContent = results;
                dataflowTest.className = 'test-result test-success';
            } catch (error) {
                dataflowTest.textContent = `数据流测试错误: ${error.message}\n${error.stack}`;
                dataflowTest.className = 'test-result test-error';
            }
        }
        
        async function testSource(sourceName) {
            const manualTest = document.getElementById('manual-test');
            try {
                manualTest.textContent = `测试来源: ${sourceName}\n`;
                manualTest.className = 'test-result';
                
                // 1. 获取来源信息
                manualTest.textContent += '1. 获取来源信息...\n';
                const source = await window.ApiClient.getSourceByName(sourceName);
                
                if (!source) {
                    throw new Error(`来源 "${sourceName}" 未找到`);
                }
                
                manualTest.textContent += `   ✅ 来源: ${source.name} (ID: ${source.id})\n`;
                
                // 2. 获取名言
                manualTest.textContent += '2. 获取名言...\n';
                const quotesData = await window.ApiClient.getQuotes(1, 5, { sourceId: source.id });
                
                manualTest.textContent += `   ✅ 找到 ${quotesData.totalCount} 条名言\n`;
                manualTest.textContent += `   ✅ 当前页显示 ${quotesData.quotes.length} 条\n`;
                
                if (quotesData.quotes.length > 0) {
                    manualTest.textContent += '\n前3条名言:\n';
                    quotesData.quotes.slice(0, 3).forEach((quote, index) => {
                        manualTest.textContent += `   ${index + 1}. "${quote.content.substring(0, 60)}..." - ${quote.author.name}\n`;
                    });
                }
                
                manualTest.textContent += `\n🎉 测试 ${sourceName} 成功！\n`;
                manualTest.className = 'test-result test-success';
                
            } catch (error) {
                manualTest.textContent += `\n❌ 测试 ${sourceName} 失败: ${error.message}\n`;
                manualTest.className = 'test-result test-error';
            }
        }
        
        async function testAllSources() {
            const manualTest = document.getElementById('manual-test');
            try {
                manualTest.textContent = '测试所有来源...\n';
                manualTest.className = 'test-result';
                
                const sources = window.MockData.sources;
                let successCount = 0;
                let failCount = 0;
                
                for (const source of sources.slice(0, 10)) { // 只测试前10个
                    try {
                        const slug = window.UrlHandler.slugify(source.name);
                        const sourceData = await window.ApiClient.getSourceByName(source.name);
                        const quotesData = await window.ApiClient.getQuotes(1, 1, { sourceId: sourceData.id });
                        
                        manualTest.textContent += `✅ ${source.name} -> /sources/${slug}/ (${quotesData.totalCount} 条名言)\n`;
                        successCount++;
                    } catch (error) {
                        manualTest.textContent += `❌ ${source.name} -> 失败: ${error.message}\n`;
                        failCount++;
                    }
                }
                
                manualTest.textContent += `\n总结: ${successCount} 成功, ${failCount} 失败\n`;
                
                if (failCount === 0) {
                    manualTest.className = 'test-result test-success';
                } else {
                    manualTest.className = 'test-result test-warning';
                }
                
            } catch (error) {
                manualTest.textContent += `\n❌ 批量测试失败: ${error.message}\n`;
                manualTest.className = 'test-result test-error';
            }
        }
    </script>
</body>
</html>
