<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sources最终验证测试 - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .test-result {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #1f2937;
            color: #f3f4f6;
            padding: 12px;
            border-radius: 6px;
            margin: 8px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-success {
            background: #065f46;
            color: #d1fae5;
        }
        .test-error {
            background: #7f1d1d;
            color: #fecaca;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-check-circle mr-3 text-green-500"></i>
                Sources功能最终验证
            </h1>
            
            <!-- 测试结果 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">测试结果</h2>
                <div id="test-results" class="test-result">运行测试中...</div>
            </div>
            
            <!-- 快速测试按钮 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">快速测试</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
                    <a href="/sources/healology/" target="_blank" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 text-center">
                        Healology
                    </a>
                    <a href="/sources/atlas-shrugged/" target="_blank" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 text-center">
                        Atlas Shrugged
                    </a>
                    <a href="/sources/long-walk-to-freedom/" target="_blank" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 text-center">
                        Long Walk to Freedom
                    </a>
                    <a href="/sources/the-alchemist/" target="_blank" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 text-center">
                        The Alchemist
                    </a>
                </div>
                <p class="text-sm text-gray-600">点击上面的链接在新标签页中测试各个source页面</p>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/config.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/url-handler.js"></script>

    <script>
        // 自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runFinalVerification, 500);
        });
        
        async function runFinalVerification() {
            const testResults = document.getElementById('test-results');
            let results = '🎯 Sources功能最终验证测试\n';
            results += '=' * 50 + '\n\n';
            
            try {
                // 1. 配置验证
                results += '1. 配置验证:\n';
                results += `   ✅ AppConfig存在: ${!!window.AppConfig}\n`;
                results += `   ✅ 使用模拟数据: ${window.AppConfig?.useMockData}\n`;
                results += `   ✅ ApiClient使用模拟数据: ${window.ApiClient?.useMockData}\n\n`;
                
                // 2. 数据验证
                results += '2. 数据验证:\n';
                results += `   ✅ MockData存在: ${!!window.MockData}\n`;
                results += `   ✅ 来源总数: ${window.MockData?.sources?.length || 0}\n`;
                results += `   ✅ 名言总数: ${window.MockData?.quotes?.length || 0}\n\n`;
                
                // 3. 关键来源测试
                results += '3. 关键来源测试:\n';
                const testSources = ['healology', 'atlas-shrugged', 'long-walk-to-freedom', 'the-alchemist'];
                let successCount = 0;
                
                for (const sourceName of testSources) {
                    try {
                        const source = await window.ApiClient.getSourceByName(sourceName);
                        if (source) {
                            const quotesData = await window.ApiClient.getQuotes(1, 5, { sourceId: source.id });
                            results += `   ✅ ${sourceName}: ${source.name} (${quotesData.totalCount} 条名言)\n`;
                            successCount++;
                        } else {
                            results += `   ❌ ${sourceName}: 未找到来源\n`;
                        }
                    } catch (error) {
                        results += `   ❌ ${sourceName}: 错误 - ${error.message}\n`;
                    }
                }
                
                // 4. URL处理测试
                results += '\n4. URL处理测试:\n';
                const urlTests = [
                    { input: 'healology', expected: 'healology' },
                    { input: 'atlas-shrugged', expected: 'Atlas Shrugged' },
                    { input: 'long-walk-to-freedom', expected: 'Long Walk to Freedom' },
                    { input: 'the-alchemist', expected: 'The Alchemist' }
                ];
                
                for (const test of urlTests) {
                    try {
                        const source = await window.ApiClient.getSourceByName(test.input);
                        if (source && source.name.toLowerCase().includes(test.expected.toLowerCase().split('-')[0])) {
                            results += `   ✅ ${test.input} -> ${source.name}\n`;
                        } else {
                            results += `   ❌ ${test.input} -> 未匹配到 ${test.expected}\n`;
                        }
                    } catch (error) {
                        results += `   ❌ ${test.input} -> 错误: ${error.message}\n`;
                    }
                }
                
                // 5. 总结
                results += '\n' + '=' * 50 + '\n';
                if (successCount === testSources.length) {
                    results += '🎉 所有测试通过！Sources功能已完全修复！\n\n';
                    results += '✅ 配置正确：使用模拟数据\n';
                    results += '✅ 数据完整：包含所有必要的来源和名言\n';
                    results += '✅ API正常：所有来源都能正确获取数据\n';
                    results += '✅ URL路由：所有语义化URL都能正常工作\n\n';
                    results += '现在所有 /sources/ 路径下的URL都应该能正常显示内容，包括：\n';
                    results += '- /sources/healology/\n';
                    results += '- /sources/atlas-shrugged/\n';
                    results += '- /sources/long-walk-to-freedom/\n';
                    results += '- 以及其他所有来源页面\n';
                    
                    testResults.className = 'test-result test-success';
                } else {
                    results += `⚠️  部分测试失败 (${successCount}/${testSources.length})\n`;
                    results += '需要进一步检查失败的来源\n';
                    testResults.className = 'test-result test-error';
                }
                
            } catch (error) {
                results += `\n❌ 测试过程中发生错误: ${error.message}\n${error.stack}`;
                testResults.className = 'test-result test-error';
            }
            
            testResults.textContent = results;
        }
    </script>
</body>
</html>
