<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance & Compatibility Test - Quote Detail Page</title>
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
</head>
<body class="light-mode bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">Performance & Compatibility Test</h1>
        
        <!-- Performance Metrics Dashboard -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="font-bold text-lg mb-2">Page Load Time</h3>
                <div id="load-time" class="text-2xl font-bold text-blue-600">-</div>
                <div class="text-sm text-gray-500">milliseconds</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="font-bold text-lg mb-2">DOM Ready</h3>
                <div id="dom-ready-time" class="text-2xl font-bold text-green-600">-</div>
                <div class="text-sm text-gray-500">milliseconds</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="font-bold text-lg mb-2">Scripts Loaded</h3>
                <div id="scripts-loaded" class="text-2xl font-bold text-purple-600">-</div>
                <div class="text-sm text-gray-500">count</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="font-bold text-lg mb-2">Memory Usage</h3>
                <div id="memory-usage" class="text-2xl font-bold text-orange-600">-</div>
                <div class="text-sm text-gray-500">MB (approx)</div>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Performance Tests</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="testPageLoadPerformance()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    Test Page Load Performance
                </button>
                <button onclick="testAPIPerformance()" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    Test API Performance
                </button>
                <button onclick="testComponentRenderPerformance()" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    Test Component Render Performance
                </button>
                <button onclick="testMemoryUsage()" class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600">
                    Test Memory Usage
                </button>
                <button onclick="testRouterCompatibility()" class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600">
                    Test Router Compatibility
                </button>
                <button onclick="testSEOMetadataUpdate()" class="px-4 py-2 bg-pink-500 text-white rounded hover:bg-pink-600">
                    Test SEO Metadata Update
                </button>
            </div>
        </div>
        
        <!-- Browser Compatibility -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Browser Compatibility</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3">Browser Information</h3>
                    <div class="space-y-2 text-sm">
                        <div><strong>User Agent:</strong> <span id="user-agent" class="text-gray-600"></span></div>
                        <div><strong>Browser:</strong> <span id="browser-name" class="text-gray-600"></span></div>
                        <div><strong>Version:</strong> <span id="browser-version" class="text-gray-600"></span></div>
                        <div><strong>Platform:</strong> <span id="platform" class="text-gray-600"></span></div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-3">Feature Support</h3>
                    <div id="feature-support" class="space-y-2 text-sm">
                        <!-- Feature support will be populated here -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Performance Monitoring -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Real-time Performance Monitoring</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3">Network Performance</h3>
                    <div id="network-performance" class="space-y-2 text-sm">
                        <div>Connection Type: <span id="connection-type" class="font-mono">-</span></div>
                        <div>Effective Type: <span id="effective-type" class="font-mono">-</span></div>
                        <div>Downlink: <span id="downlink" class="font-mono">-</span> Mbps</div>
                        <div>RTT: <span id="rtt" class="font-mono">-</span> ms</div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-3">Resource Timing</h3>
                    <div id="resource-timing" class="space-y-2 text-sm">
                        <!-- Resource timing will be populated here -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold mb-4">Test Results</h2>
            <div id="test-results" class="bg-gray-100 dark:bg-gray-700 p-4 rounded min-h-[300px] font-mono text-sm overflow-y-auto max-h-96">
                Ready for performance and compatibility tests...
            </div>
            <div class="mt-4 flex gap-2">
                <button onclick="clearResults()" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    Clear Results
                </button>
                <button onclick="generateReport()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    Generate Report
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/debug.js?v=20250629"></script>
    <script src="/js/api-client.js?v=20250629"></script>
    <script src="/js/url-handler.js?v=20250629"></script>
    <script src="/js/page-router.js?v=20250629"></script>
    <script src="/js/components/quote-card.js?v=20250629"></script>
    <script src="/js/seo-manager.js?v=20250629"></script>
    <script src="/js/social-meta.js?v=20250629"></script>
    
    <script>
        // Performance tracking
        let performanceData = {
            loadTime: 0,
            domReadyTime: 0,
            scriptsLoaded: 0,
            memoryUsage: 0
        };
        
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const typeColor = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : type === 'warning' ? 'text-yellow-600' : 'text-blue-600';
            resultsDiv.innerHTML += `<div class="${typeColor}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = 'Ready for performance and compatibility tests...';
        }
        
        function updateMetric(id, value, unit = '') {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value + unit;
            }
        }
        
        function testPageLoadPerformance() {
            logResult('Testing page load performance...', 'info');
            
            try {
                if (performance && performance.timing) {
                    const timing = performance.timing;
                    const loadTime = timing.loadEventEnd - timing.navigationStart;
                    const domReadyTime = timing.domContentLoadedEventEnd - timing.navigationStart;
                    
                    performanceData.loadTime = loadTime;
                    performanceData.domReadyTime = domReadyTime;
                    
                    updateMetric('load-time', loadTime);
                    updateMetric('dom-ready-time', domReadyTime);
                    
                    logResult(`✓ Page load time: ${loadTime}ms`, 'success');
                    logResult(`✓ DOM ready time: ${domReadyTime}ms`, 'success');
                    
                    // Performance evaluation
                    if (loadTime < 2000) {
                        logResult('✓ Page load performance: Excellent (<2s)', 'success');
                    } else if (loadTime < 5000) {
                        logResult('⚠ Page load performance: Good (2-5s)', 'warning');
                    } else {
                        logResult('✗ Page load performance: Poor (>5s)', 'error');
                    }
                    
                } else {
                    logResult('⚠ Performance timing API not available', 'warning');
                }
                
                // Test script loading
                const scripts = document.querySelectorAll('script[src]');
                performanceData.scriptsLoaded = scripts.length;
                updateMetric('scripts-loaded', scripts.length);
                logResult(`✓ Scripts loaded: ${scripts.length}`, 'success');
                
            } catch (error) {
                logResult(`✗ Page load performance test failed: ${error.message}`, 'error');
            }
        }
        
        async function testAPIPerformance() {
            logResult('Testing API performance...', 'info');
            
            try {
                if (typeof window.ApiClient === 'undefined') {
                    logResult('✗ ApiClient not available', 'error');
                    return;
                }
                
                const startTime = performance.now();
                
                // Test quote loading
                const quote = await window.ApiClient.getQuoteById(1);
                const quoteLoadTime = performance.now() - startTime;
                
                logResult(`✓ Quote API call completed in ${quoteLoadTime.toFixed(2)}ms`, 'success');
                
                if (quoteLoadTime < 500) {
                    logResult('✓ API performance: Excellent (<500ms)', 'success');
                } else if (quoteLoadTime < 1000) {
                    logResult('⚠ API performance: Good (500-1000ms)', 'warning');
                } else {
                    logResult('✗ API performance: Poor (>1000ms)', 'error');
                }
                
                // Test parallel API calls
                const parallelStartTime = performance.now();
                await Promise.all([
                    window.ApiClient.getCategories(5),
                    window.ApiClient.getAuthors(5),
                    window.ApiClient.getSources(5)
                ]);
                const parallelLoadTime = performance.now() - parallelStartTime;
                
                logResult(`✓ Parallel API calls completed in ${parallelLoadTime.toFixed(2)}ms`, 'success');
                
            } catch (error) {
                logResult(`✗ API performance test failed: ${error.message}`, 'error');
            }
        }
        
        function testComponentRenderPerformance() {
            logResult('Testing component render performance...', 'info');
            
            try {
                if (typeof QuoteCardComponent === 'undefined') {
                    logResult('✗ QuoteCardComponent not available', 'error');
                    return;
                }
                
                const testQuote = {
                    id: 1,
                    content: 'This is a test quote for performance testing.',
                    author: { id: 1, name: 'Test Author' },
                    categories: [{ id: 1, name: 'Test Category' }],
                    sources: [{ id: 1, name: 'Test Source' }]
                };
                
                const startTime = performance.now();
                
                // Render multiple quote cards
                for (let i = 0; i < 10; i++) {
                    const card = QuoteCardComponent.render(testQuote, i, {
                        showActions: true,
                        showAuthorAvatar: true,
                        showCategories: true,
                        showSources: true,
                        isDetailPage: false
                    });
                }
                
                const renderTime = performance.now() - startTime;
                logResult(`✓ 10 quote cards rendered in ${renderTime.toFixed(2)}ms`, 'success');
                logResult(`✓ Average render time: ${(renderTime / 10).toFixed(2)}ms per card`, 'success');
                
                if (renderTime < 100) {
                    logResult('✓ Component render performance: Excellent', 'success');
                } else if (renderTime < 500) {
                    logResult('⚠ Component render performance: Good', 'warning');
                } else {
                    logResult('✗ Component render performance: Poor', 'error');
                }
                
            } catch (error) {
                logResult(`✗ Component render performance test failed: ${error.message}`, 'error');
            }
        }
        
        function testMemoryUsage() {
            logResult('Testing memory usage...', 'info');
            
            try {
                if (performance && performance.memory) {
                    const memory = performance.memory;
                    const usedMB = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
                    const totalMB = (memory.totalJSHeapSize / 1024 / 1024).toFixed(2);
                    const limitMB = (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2);
                    
                    performanceData.memoryUsage = usedMB;
                    updateMetric('memory-usage', usedMB);
                    
                    logResult(`✓ Memory used: ${usedMB}MB`, 'success');
                    logResult(`✓ Memory total: ${totalMB}MB`, 'success');
                    logResult(`✓ Memory limit: ${limitMB}MB`, 'success');
                    
                    const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
                    if (usagePercent < 50) {
                        logResult('✓ Memory usage: Excellent (<50%)', 'success');
                    } else if (usagePercent < 80) {
                        logResult('⚠ Memory usage: Good (50-80%)', 'warning');
                    } else {
                        logResult('✗ Memory usage: High (>80%)', 'error');
                    }
                    
                } else {
                    logResult('⚠ Memory API not available', 'warning');
                }
            } catch (error) {
                logResult(`✗ Memory usage test failed: ${error.message}`, 'error');
            }
        }
        
        function testRouterCompatibility() {
            logResult('Testing router compatibility...', 'info');
            
            try {
                // Test PageRouter availability
                if (typeof window.PageRouter !== 'undefined') {
                    logResult('✓ PageRouter is available', 'success');
                } else {
                    logResult('⚠ PageRouter not available', 'warning');
                }
                
                // Test UrlHandler compatibility
                if (typeof window.UrlHandler !== 'undefined') {
                    logResult('✓ UrlHandler is available', 'success');
                    
                    // Test URL parsing methods
                    const methods = ['getCurrentPageType', 'parseQuoteIdFromPath', 'getQuoteUrl'];
                    let compatibleMethods = 0;
                    
                    for (const method of methods) {
                        if (typeof window.UrlHandler[method] === 'function') {
                            logResult(`✓ UrlHandler.${method} is compatible`, 'success');
                            compatibleMethods++;
                        } else {
                            logResult(`✗ UrlHandler.${method} not available`, 'error');
                        }
                    }
                    
                    if (compatibleMethods === methods.length) {
                        logResult('✓ Full router compatibility confirmed', 'success');
                    } else {
                        logResult(`⚠ Partial router compatibility: ${compatibleMethods}/${methods.length}`, 'warning');
                    }
                    
                } else {
                    logResult('✗ UrlHandler not available', 'error');
                }
                
            } catch (error) {
                logResult(`✗ Router compatibility test failed: ${error.message}`, 'error');
            }
        }
        
        function testSEOMetadataUpdate() {
            logResult('Testing SEO metadata update functionality...', 'info');
            
            try {
                // Test SocialMetaUtil availability
                if (typeof window.SocialMetaUtil !== 'undefined') {
                    logResult('✓ SocialMetaUtil is available', 'success');
                    
                    // Test metadata update with sample quote
                    const testQuote = {
                        id: 1,
                        content: 'This is a test quote for SEO metadata testing.',
                        author: { name: 'Test Author' }
                    };
                    
                    if (typeof window.SocialMetaUtil.updateQuoteMetaTags === 'function') {
                        try {
                            window.SocialMetaUtil.updateQuoteMetaTags(testQuote);
                            logResult('✓ SEO metadata update function executed', 'success');
                            
                            // Check if title was updated
                            const title = document.title;
                            if (title.includes('Test Author') || title.includes('test quote')) {
                                logResult('✓ Page title updated successfully', 'success');
                            } else {
                                logResult('⚠ Page title may not have been updated', 'warning');
                            }
                            
                        } catch (error) {
                            logResult(`✗ SEO metadata update failed: ${error.message}`, 'error');
                        }
                    } else {
                        logResult('✗ updateQuoteMetaTags method not available', 'error');
                    }
                    
                } else {
                    logResult('⚠ SocialMetaUtil not available', 'warning');
                }
                
                // Test SEOManager availability
                if (typeof window.SEOManager !== 'undefined') {
                    logResult('✓ SEOManager is available', 'success');
                } else {
                    logResult('⚠ SEOManager not available', 'warning');
                }
                
            } catch (error) {
                logResult(`✗ SEO metadata test failed: ${error.message}`, 'error');
            }
        }
        
        function detectBrowserInfo() {
            const userAgent = navigator.userAgent;
            document.getElementById('user-agent').textContent = userAgent;
            
            // Simple browser detection
            let browserName = 'Unknown';
            let browserVersion = 'Unknown';
            
            if (userAgent.includes('Chrome')) {
                browserName = 'Chrome';
                const match = userAgent.match(/Chrome\/([0-9.]+)/);
                if (match) browserVersion = match[1];
            } else if (userAgent.includes('Firefox')) {
                browserName = 'Firefox';
                const match = userAgent.match(/Firefox\/([0-9.]+)/);
                if (match) browserVersion = match[1];
            } else if (userAgent.includes('Safari')) {
                browserName = 'Safari';
                const match = userAgent.match(/Version\/([0-9.]+)/);
                if (match) browserVersion = match[1];
            } else if (userAgent.includes('Edge')) {
                browserName = 'Edge';
                const match = userAgent.match(/Edge\/([0-9.]+)/);
                if (match) browserVersion = match[1];
            }
            
            document.getElementById('browser-name').textContent = browserName;
            document.getElementById('browser-version').textContent = browserVersion;
            document.getElementById('platform').textContent = navigator.platform;
        }
        
        function checkFeatureSupport() {
            const features = [
                { name: 'Fetch API', supported: typeof fetch !== 'undefined' },
                { name: 'Promise', supported: typeof Promise !== 'undefined' },
                { name: 'Arrow Functions', supported: true }, // If this script runs, arrow functions are supported
                { name: 'Local Storage', supported: typeof localStorage !== 'undefined' },
                { name: 'Session Storage', supported: typeof sessionStorage !== 'undefined' },
                { name: 'Performance API', supported: typeof performance !== 'undefined' },
                { name: 'Intersection Observer', supported: typeof IntersectionObserver !== 'undefined' },
                { name: 'CSS Grid', supported: CSS.supports('display', 'grid') }
            ];
            
            const container = document.getElementById('feature-support');
            container.innerHTML = '';
            
            features.forEach(feature => {
                const div = document.createElement('div');
                div.innerHTML = `
                    <span class="${feature.supported ? 'text-green-600' : 'text-red-600'}">
                        ${feature.supported ? '✓' : '✗'} ${feature.name}
                    </span>
                `;
                container.appendChild(div);
            });
        }
        
        function checkNetworkInfo() {
            if (navigator.connection) {
                const connection = navigator.connection;
                document.getElementById('connection-type').textContent = connection.type || 'unknown';
                document.getElementById('effective-type').textContent = connection.effectiveType || 'unknown';
                document.getElementById('downlink').textContent = connection.downlink || 'unknown';
                document.getElementById('rtt').textContent = connection.rtt || 'unknown';
            } else {
                document.getElementById('connection-type').textContent = 'not available';
                document.getElementById('effective-type').textContent = 'not available';
                document.getElementById('downlink').textContent = 'not available';
                document.getElementById('rtt').textContent = 'not available';
            }
        }
        
        function generateReport() {
            logResult('=== PERFORMANCE & COMPATIBILITY REPORT ===', 'info');
            logResult(`Generated at: ${new Date().toISOString()}`, 'info');
            logResult('', 'info');
            
            logResult('Performance Metrics:', 'info');
            logResult(`- Page Load Time: ${performanceData.loadTime}ms`, 'info');
            logResult(`- DOM Ready Time: ${performanceData.domReadyTime}ms`, 'info');
            logResult(`- Scripts Loaded: ${performanceData.scriptsLoaded}`, 'info');
            logResult(`- Memory Usage: ${performanceData.memoryUsage}MB`, 'info');
            logResult('', 'info');
            
            logResult('Browser Information:', 'info');
            logResult(`- Browser: ${document.getElementById('browser-name').textContent}`, 'info');
            logResult(`- Version: ${document.getElementById('browser-version').textContent}`, 'info');
            logResult(`- Platform: ${document.getElementById('platform').textContent}`, 'info');
            logResult('', 'info');
            
            logResult('=== END OF REPORT ===', 'info');
        }
        
        // Initialize
        window.addEventListener('load', () => {
            logResult('Performance & compatibility test environment initialized', 'success');
            
            detectBrowserInfo();
            checkFeatureSupport();
            checkNetworkInfo();
            
            // Auto-run basic performance test
            setTimeout(() => {
                testPageLoadPerformance();
            }, 1000);
        });
    </script>
</body>
</html>
