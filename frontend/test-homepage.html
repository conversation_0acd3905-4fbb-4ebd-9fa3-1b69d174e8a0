<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页诊断测试 - Quotese.com</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; background-color: #f5f5f5; }
        .test-section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .loading { color: #007bff; }
        .warning { color: #ffc107; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 200px; overflow-y: auto; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
        .status-item { padding: 15px; border-radius: 8px; border-left: 4px solid #ddd; }
        .status-success { border-left-color: #28a745; background-color: #f8fff9; }
        .status-error { border-left-color: #dc3545; background-color: #fff8f8; }
        .status-warning { border-left-color: #ffc107; background-color: #fffdf5; }
    </style>
</head>
<body>
    <h1>🏠 Quotese.com 首页诊断测试</h1>
    
    <div class="test-section">
        <h2>系统状态检查</h2>
        <button onclick="checkSystemStatus()">检查系统状态</button>
        <div id="system-status"></div>
    </div>

    <div class="test-section">
        <h2>API连接测试</h2>
        <button onclick="testAPIConnections()">测试API连接</button>
        <div id="api-status"></div>
    </div>

    <div class="test-section">
        <h2>首页功能测试</h2>
        <button onclick="testHomepageFunctions()">测试首页功能</button>
        <div id="homepage-status"></div>
    </div>

    <div class="test-section">
        <h2>优化导航测试</h2>
        <button onclick="testOptimizedNavigation()">测试优化导航</button>
        <div id="optimization-status"></div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/entity-id-mapper.js"></script>
    <script src="js/optimized-navigation.js"></script>

    <script>
        // 检查系统状态
        async function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            statusDiv.innerHTML = '<p class="loading">🔄 检查系统状态...</p>';

            const checks = [
                { name: 'AppConfig配置', test: () => typeof window.AppConfig === 'object' },
                { name: 'API客户端', test: () => typeof window.ApiClient === 'object' },
                { name: 'EntityIdMapper', test: () => typeof window.EntityIdMapper === 'object' },
                { name: '优化导航系统', test: () => typeof window.navigateToEntityWithId === 'function' },
                { name: '实体缓存系统', test: () => typeof window.entityCache === 'object' },
                { name: '缓存统计函数', test: () => typeof window.getCacheStats === 'function' }
            ];

            let results = '<div class="status-grid">';
            checks.forEach(check => {
                const passed = check.test();
                const statusClass = passed ? 'status-success' : 'status-error';
                const icon = passed ? '✅' : '❌';
                results += `
                    <div class="status-item ${statusClass}">
                        <strong>${icon} ${check.name}</strong><br>
                        <small>${passed ? '正常' : '未加载'}</small>
                    </div>
                `;
            });
            results += '</div>';

            // 显示配置信息
            results += `
                <h4>当前配置:</h4>
                <pre>${JSON.stringify(window.AppConfig, null, 2)}</pre>
            `;

            statusDiv.innerHTML = results;
        }

        // 测试API连接
        async function testAPIConnections() {
            const statusDiv = document.getElementById('api-status');
            statusDiv.innerHTML = '<p class="loading">🔄 测试API连接...</p>';

            const tests = [
                {
                    name: '基础连接测试',
                    test: async () => {
                        const response = await fetch(window.AppConfig.apiEndpoint, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ query: '{ quotesCount }' })
                        });
                        return response.ok ? await response.json() : null;
                    }
                },
                {
                    name: '统计数据查询',
                    test: async () => await window.ApiClient.getStats()
                },
                {
                    name: '热门类别查询',
                    test: async () => await window.ApiClient.getPopularCategories(5)
                },
                {
                    name: '热门作者查询',
                    test: async () => await window.ApiClient.getPopularAuthors(5)
                },
                {
                    name: '热门来源查询',
                    test: async () => await window.ApiClient.getPopularSources(5)
                },
                {
                    name: '名言列表查询',
                    test: async () => await window.ApiClient.getTopQuotes(1, 3)
                }
            ];

            let results = '<div class="status-grid">';
            for (const test of tests) {
                try {
                    const result = await test.test();
                    const success = result && (Array.isArray(result) ? result.length > 0 : Object.keys(result).length > 0);
                    results += `
                        <div class="status-item ${success ? 'status-success' : 'status-warning'}">
                            <strong>${success ? '✅' : '⚠️'} ${test.name}</strong><br>
                            <small>${success ? '成功' : '无数据'}</small>
                        </div>
                    `;
                } catch (error) {
                    results += `
                        <div class="status-item status-error">
                            <strong>❌ ${test.name}</strong><br>
                            <small>错误: ${error.message}</small>
                        </div>
                    `;
                }
            }
            results += '</div>';

            statusDiv.innerHTML = results;
        }

        // 测试首页功能
        async function testHomepageFunctions() {
            const statusDiv = document.getElementById('homepage-status');
            statusDiv.innerHTML = '<p class="loading">🔄 测试首页功能...</p>';

            const tests = [
                {
                    name: '每日名言加载',
                    test: async () => {
                        const { totalCount } = await window.ApiClient.getTopQuotes(1, 1, true);
                        return totalCount > 0;
                    }
                },
                {
                    name: '名言列表加载',
                    test: async () => {
                        const result = await window.ApiClient.getTopQuotes(1, 5);
                        return result.quotes && result.quotes.length > 0;
                    }
                },
                {
                    name: '热门模块数据',
                    test: async () => {
                        const [categories, authors, sources] = await Promise.all([
                            window.ApiClient.getPopularCategories(5),
                            window.ApiClient.getPopularAuthors(5),
                            window.ApiClient.getPopularSources(5)
                        ]);
                        return categories.length > 0 && authors.length > 0 && sources.length > 0;
                    }
                }
            ];

            let results = '<div class="status-grid">';
            for (const test of tests) {
                try {
                    const success = await test.test();
                    results += `
                        <div class="status-item ${success ? 'status-success' : 'status-warning'}">
                            <strong>${success ? '✅' : '⚠️'} ${test.name}</strong><br>
                            <small>${success ? '正常' : '无数据'}</small>
                        </div>
                    `;
                } catch (error) {
                    results += `
                        <div class="status-item status-error">
                            <strong>❌ ${test.name}</strong><br>
                            <small>错误: ${error.message}</small>
                        </div>
                    `;
                }
            }
            results += '</div>';

            statusDiv.innerHTML = results;
        }

        // 测试优化导航
        async function testOptimizedNavigation() {
            const statusDiv = document.getElementById('optimization-status');
            statusDiv.innerHTML = '<p class="loading">🔄 测试优化导航...</p>';

            try {
                // 获取测试数据
                const [categories, authors, sources] = await Promise.all([
                    window.ApiClient.getPopularCategories(3),
                    window.ApiClient.getPopularAuthors(3),
                    window.ApiClient.getPopularSources(3)
                ]);

                let results = '<h4>优化导航测试结果:</h4><div class="status-grid">';

                // 测试类别导航
                if (categories.length > 0) {
                    const category = categories[0];
                    const hasOptimization = typeof window.navigateToEntityWithId === 'function';
                    results += `
                        <div class="status-item ${hasOptimization ? 'status-success' : 'status-error'}">
                            <strong>${hasOptimization ? '✅' : '❌'} 类别导航优化</strong><br>
                            <small>测试实体: ${category.name} (ID: ${category.id})</small>
                        </div>
                    `;
                }

                // 测试作者导航
                if (authors.length > 0) {
                    const author = authors[0];
                    const hasCache = window.entityCache && window.entityCache.author;
                    results += `
                        <div class="status-item ${hasCache ? 'status-success' : 'status-warning'}">
                            <strong>${hasCache ? '✅' : '⚠️'} 作者缓存系统</strong><br>
                            <small>测试实体: ${author.name} (ID: ${author.id})</small>
                        </div>
                    `;
                }

                // 测试来源导航
                if (sources.length > 0) {
                    const source = sources[0];
                    const hasEntityMapper = typeof window.EntityIdMapper === 'object';
                    results += `
                        <div class="status-item ${hasEntityMapper ? 'status-success' : 'status-error'}">
                            <strong>${hasEntityMapper ? '✅' : '❌'} EntityIdMapper</strong><br>
                            <small>测试实体: ${source.name} (ID: ${source.id})</small>
                        </div>
                    `;
                }

                results += '</div>';

                // 显示缓存统计
                if (typeof window.getCacheStats === 'function') {
                    const cacheStats = window.getCacheStats();
                    results += `
                        <h4>缓存统计:</h4>
                        <pre>${JSON.stringify(cacheStats, null, 2)}</pre>
                    `;
                }

                statusDiv.innerHTML = results;
            } catch (error) {
                statusDiv.innerHTML = `<p class="error">❌ 优化导航测试失败: ${error.message}</p>`;
            }
        }

        // 页面加载时自动检查系统状态
        window.addEventListener('load', function() {
            console.log('🧪 首页诊断测试页面已加载');
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
