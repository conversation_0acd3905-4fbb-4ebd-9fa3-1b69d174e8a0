<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Switch to Production API - Quotese</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        <div class="text-center mb-6">
            <div class="text-4xl mb-4">🚀</div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">Switch to Production API</h1>
            <p class="text-gray-600">Configure the frontend to use production API endpoints</p>
        </div>
        
        <div class="space-y-4">
            <div class="bg-gray-50 p-4 rounded">
                <h3 class="font-semibold mb-2">Current Configuration:</h3>
                <div id="current-config" class="text-sm font-mono text-gray-700">
                    Loading...
                </div>
            </div>
            
            <div class="space-y-3">
                <button onclick="switchToProduction()" class="w-full px-4 py-3 bg-red-500 text-white rounded hover:bg-red-600 transition-colors font-semibold">
                    🚀 Switch to Production API
                </button>
                
                <button onclick="switchToLocal()" class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                    🏠 Switch to Local API
                </button>
                
                <button onclick="testConnection()" class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                    🔍 Test Current API
                </button>
            </div>
            
            <div class="border-t pt-4">
                <h3 class="font-semibold mb-2">Quick Actions:</h3>
                <div class="space-y-2">
                    <a href="/test-production-api-comprehensive.html" class="block w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors text-center">
                        🧪 Run Comprehensive Tests
                    </a>
                    <a href="/quotes/1/" class="block w-full px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition-colors text-center">
                        📄 Test Quote Detail Page
                    </a>
                </div>
            </div>
        </div>
        
        <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
            <div class="flex items-start">
                <div class="text-yellow-600 mr-2">⚠️</div>
                <div class="text-sm text-yellow-800">
                    <strong>Note:</strong> After switching API modes, you need to refresh any open pages to apply the new configuration.
                </div>
            </div>
        </div>
        
        <div id="status-message" class="mt-4 p-3 rounded hidden">
            <!-- Status messages will appear here -->
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/config.js?v=20250629"></script>
    
    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-message');
            const colors = {
                'success': 'bg-green-100 border border-green-200 text-green-800',
                'error': 'bg-red-100 border border-red-200 text-red-800',
                'warning': 'bg-yellow-100 border border-yellow-200 text-yellow-800',
                'info': 'bg-blue-100 border border-blue-200 text-blue-800'
            };
            
            statusDiv.className = `mt-4 p-3 rounded ${colors[type] || colors.info}`;
            statusDiv.textContent = message;
            statusDiv.classList.remove('hidden');
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                statusDiv.classList.add('hidden');
            }, 5000);
        }
        
        function updateCurrentConfig() {
            const configDiv = document.getElementById('current-config');
            
            if (typeof window.QuoteseAPIMode !== 'undefined') {
                const mode = window.QuoteseAPIMode.getCurrentMode();
                configDiv.innerHTML = `
                    <div><strong>Mode:</strong> <span class="${mode.isUsingProduction ? 'text-red-600' : 'text-blue-600'}">${mode.isUsingProduction ? 'Production' : 'Local'}</span></div>
                    <div><strong>API:</strong> ${mode.apiEndpoint}</div>
                    <div><strong>GraphQL:</strong> ${mode.graphqlEndpoint}</div>
                `;
            } else {
                configDiv.innerHTML = '<div class="text-red-600">Configuration not available</div>';
            }
        }
        
        function switchToProduction() {
            if (typeof window.QuoteseAPIMode !== 'undefined') {
                const result = window.QuoteseAPIMode.useProductionAPI();
                showStatus('✅ Switched to production API. Please refresh any open pages.', 'success');
                updateCurrentConfig();
                
                // Auto-refresh after 2 seconds
                setTimeout(() => {
                    if (confirm('Configuration changed. Refresh this page now?')) {
                        window.location.reload();
                    }
                }, 2000);
            } else {
                showStatus('❌ API mode switcher not available', 'error');
            }
        }
        
        function switchToLocal() {
            if (typeof window.QuoteseAPIMode !== 'undefined') {
                const result = window.QuoteseAPIMode.useLocalAPI();
                showStatus('✅ Switched to local API. Please refresh any open pages.', 'success');
                updateCurrentConfig();
                
                // Auto-refresh after 2 seconds
                setTimeout(() => {
                    if (confirm('Configuration changed. Refresh this page now?')) {
                        window.location.reload();
                    }
                }, 2000);
            } else {
                showStatus('❌ API mode switcher not available', 'error');
            }
        }
        
        async function testConnection() {
            if (typeof window.QuoteseAPIMode !== 'undefined') {
                try {
                    showStatus('🔍 Testing API connection...', 'info');
                    const result = await window.QuoteseAPIMode.testConnection();
                    showStatus('✅ API connection test successful', 'success');
                } catch (error) {
                    showStatus(`❌ API connection test failed: ${error.message}`, 'error');
                }
            } else {
                showStatus('❌ API tester not available', 'error');
            }
        }
        
        // Initialize
        window.addEventListener('load', () => {
            updateCurrentConfig();
            
            // Check current mode and show appropriate message
            const currentMode = window.QuoteseAPIMode?.getCurrentMode();
            if (currentMode?.isUsingProduction) {
                showStatus('✅ Currently using production API', 'success');
            } else {
                showStatus('ℹ️ Currently using local API', 'info');
            }
        });
    </script>
</body>
</html>
