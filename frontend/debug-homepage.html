<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Homepage Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-left: 3px solid #ccc; }
        .error { border-left-color: #f44336; background-color: #ffebee; }
        .success { border-left-color: #4caf50; background-color: #e8f5e8; }
        .info { border-left-color: #2196f3; background-color: #e3f2fd; }
        .warning { border-left-color: #ff9800; background-color: #fff3e0; }
        button { margin: 5px; padding: 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Homepage Debug Tool</h1>
    
    <button onclick="testConfig()">Test Config</button>
    <button onclick="testApiClient()">Test API Client</button>
    <button onclick="testQuotesAPI()">Test Quotes API</button>
    <button onclick="testCategoriesAPI()">Test Categories API</button>
    <button onclick="clearLogs()">Clear Logs</button>
    
    <div id="logs"></div>

    <!-- Load the same scripts as homepage -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `
                <strong>[${new Date().toLocaleTimeString()}]</strong> ${message}
            `;
            logsDiv.appendChild(logDiv);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        // 测试配置
        function testConfig() {
            log('Testing configuration...', 'info');
            
            if (typeof window.AppConfig === 'undefined') {
                log('❌ AppConfig is not defined', 'error');
                return;
            }
            
            log(`✅ AppConfig loaded: ${JSON.stringify(window.AppConfig)}`, 'success');
            log(`API Endpoint: ${window.AppConfig.apiEndpoint}`, 'info');
            log(`Use Mock Data: ${window.AppConfig.useMockData}`, 'info');
            log(`Debug Mode: ${window.AppConfig.debug}`, 'info');
        }

        // 测试API客户端
        function testApiClient() {
            log('Testing API Client...', 'info');
            
            if (typeof window.ApiClient === 'undefined') {
                log('❌ ApiClient is not defined', 'error');
                return;
            }
            
            log('✅ ApiClient loaded', 'success');
            log(`API Endpoint: ${window.ApiClient.apiEndpoint}`, 'info');
            log(`Use Mock Data: ${window.ApiClient.useMockData}`, 'info');
        }

        // 测试名言API
        async function testQuotesAPI() {
            log('Testing Quotes API...', 'info');
            
            if (!window.ApiClient) {
                log('❌ ApiClient not available', 'error');
                return;
            }

            try {
                log('Calling getQuotes(1, 5)...', 'info');
                const result = await window.ApiClient.getQuotes(1, 5);
                log(`✅ Quotes API success: ${result.quotes.length} quotes loaded`, 'success');
                log(`Total count: ${result.totalCount}`, 'info');
                log(`Sample quote: "${result.quotes[0]?.content?.substring(0, 50)}..."`, 'info');
                
                // 显示详细结果
                const pre = document.createElement('pre');
                pre.textContent = JSON.stringify(result, null, 2);
                document.getElementById('logs').appendChild(pre);
                
            } catch (error) {
                log(`❌ Quotes API error: ${error.message}`, 'error');
                console.error('Quotes API error details:', error);
            }
        }

        // 测试类别API
        async function testCategoriesAPI() {
            log('Testing Categories API...', 'info');
            
            if (!window.ApiClient) {
                log('❌ ApiClient not available', 'error');
                return;
            }

            try {
                log('Calling getPopularCategories(10)...', 'info');
                const result = await window.ApiClient.getPopularCategories(10);
                log(`✅ Categories API success: ${result.length} categories loaded`, 'success');
                
                if (result.length > 0) {
                    log(`Sample category: ${result[0].name} (${result[0].count} quotes)`, 'info');
                }
                
                // 显示详细结果
                const pre = document.createElement('pre');
                pre.textContent = JSON.stringify(result, null, 2);
                document.getElementById('logs').appendChild(pre);
                
            } catch (error) {
                log(`❌ Categories API error: ${error.message}`, 'error');
                console.error('Categories API error details:', error);
            }
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            log('🚀 Starting homepage debug tests...', 'info');
            
            setTimeout(() => {
                testConfig();
                testApiClient();
            }, 500);
            
            setTimeout(() => {
                testQuotesAPI();
            }, 1000);
            
            setTimeout(() => {
                testCategoriesAPI();
            }, 1500);
        });

        // 捕获所有错误
        window.addEventListener('error', (event) => {
            log(`❌ JavaScript Error: ${event.error.message}`, 'error');
            log(`File: ${event.filename}:${event.lineno}`, 'error');
        });

        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            log(`❌ Unhandled Promise Rejection: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
