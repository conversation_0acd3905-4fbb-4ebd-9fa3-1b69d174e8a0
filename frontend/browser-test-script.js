
// 在浏览器控制台中运行此脚本来测试名言卡片点击功能

console.log('🧪 开始测试名言卡片点击功能...');

// 1. 切换到生产API
if (typeof window.QuoteseAPIMode !== 'undefined') {
    window.QuoteseAPIMode.useProductionAPI();
    console.log('✅ 已切换到生产API');
} else {
    console.log('❌ QuoteseAPIMode 不可用');
}

// 2. 等待页面加载后测试
setTimeout(async () => {
    try {
        // 查找页面上的名言卡片
        const quoteCards = document.querySelectorAll('.quote-card-component');
        console.log(`📊 找到 ${quoteCards.length} 个名言卡片`);
        
        if (quoteCards.length === 0) {
            console.log('❌ 页面上没有找到名言卡片');
            return;
        }
        
        // 检查第一个卡片的属性
        const firstCard = quoteCards[0];
        const quoteId = firstCard.getAttribute('data-quote-id');
        const hasCursorPointer = firstCard.classList.contains('cursor-pointer');
        
        console.log(`📋 第一个卡片信息:`);
        console.log(`   Quote ID: ${quoteId}`);
        console.log(`   Has cursor-pointer: ${hasCursorPointer}`);
        console.log(`   Classes: ${firstCard.className}`);
        
        // 测试URL生成
        if (quoteId && typeof UrlHandler !== 'undefined') {
            const quoteUrl = UrlHandler.getQuoteUrl({ id: quoteId });
            console.log(`🔗 生成的URL: ${quoteUrl}`);
        }
        
        // 添加测试点击事件
        firstCard.addEventListener('click', (e) => {
            console.log('🖱️  名言卡片被点击了!', {
                quoteId: quoteId,
                target: e.target,
                currentTarget: e.currentTarget
            });
        });
        
        console.log('✅ 测试设置完成，请点击第一个名言卡片进行测试');
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    }
}, 2000);
