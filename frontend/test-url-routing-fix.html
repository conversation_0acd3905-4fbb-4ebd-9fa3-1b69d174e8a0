<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL路由修复验证测试 - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .test-result {
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .test-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .url-link {
            color: #007bff;
            text-decoration: underline;
            cursor: pointer;
        }
        .url-link:hover {
            color: #0056b3;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-route mr-3 text-blue-500"></i>
                URL路由修复验证测试
            </h1>
            
            <!-- 修复状态概览 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">修复状态概览</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-gray-800 mb-3">已修复的问题</h3>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                创建了自定义语义化URL服务器
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                实现了URL模式匹配和重写
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                修复了静态资源路径问题
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                添加了CORS支持
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                创建了缺失的HTML页面
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800 mb-3">技术实现</h3>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center">
                                <i class="fas fa-cog text-blue-500 mr-2"></i>
                                Python自定义HTTP服务器
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-cog text-blue-500 mr-2"></i>
                                正则表达式URL匹配
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-cog text-blue-500 mr-2"></i>
                                动态路径重写
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-cog text-blue-500 mr-2"></i>
                                静态资源路径修复
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 问题URL测试 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">问题URL测试</h2>
                <p class="text-gray-600 mb-6">测试原本返回404错误的语义化URL现在是否正常工作：</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="border rounded-lg p-4">
                        <h3 class="font-medium text-gray-800 mb-2">类别页面</h3>
                        <p class="text-sm text-gray-600 mb-3">测试类别语义化URL</p>
                        <a href="http://localhost:8083/categories/words/" target="_blank" class="url-link">
                            /categories/words/
                        </a>
                        <div id="category-status" class="mt-2 text-sm">
                            <span class="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                            检测中...
                        </div>
                    </div>
                    
                    <div class="border rounded-lg p-4">
                        <h3 class="font-medium text-gray-800 mb-2">作者页面</h3>
                        <p class="text-sm text-gray-600 mb-3">测试作者语义化URL</p>
                        <a href="http://localhost:8083/authors/criss-jami/" target="_blank" class="url-link">
                            /authors/criss-jami/
                        </a>
                        <div id="author-status" class="mt-2 text-sm">
                            <span class="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                            检测中...
                        </div>
                    </div>
                    
                    <div class="border rounded-lg p-4">
                        <h3 class="font-medium text-gray-800 mb-2">来源页面</h3>
                        <p class="text-sm text-gray-600 mb-3">测试来源语义化URL</p>
                        <a href="http://localhost:8083/sources/life/" target="_blank" class="url-link">
                            /sources/life/
                        </a>
                        <div id="source-status" class="mt-2 text-sm">
                            <span class="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                            检测中...
                        </div>
                    </div>
                </div>
                
                <button onclick="testProblemUrls()" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                    <i class="fas fa-play mr-2"></i>测试问题URL
                </button>
            </div>
            
            <!-- 全面URL测试 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">全面URL路由测试</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    <button onclick="testSemanticUrls()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        <i class="fas fa-link mr-2"></i>测试语义化URL
                    </button>
                    <button onclick="testStaticResources()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        <i class="fas fa-file mr-2"></i>测试静态资源
                    </button>
                    <button onclick="testUrlPatterns()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                        <i class="fas fa-search mr-2"></i>测试URL模式
                    </button>
                    <button onclick="testErrorHandling()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        <i class="fas fa-exclamation-triangle mr-2"></i>测试错误处理
                    </button>
                    <button onclick="testCorsHeaders()" class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                        <i class="fas fa-globe mr-2"></i>测试CORS头部
                    </button>
                    <button onclick="testPageLoading()" class="bg-pink-500 text-white px-4 py-2 rounded hover:bg-pink-600">
                        <i class="fas fa-spinner mr-2"></i>测试页面加载
                    </button>
                </div>
                
                <div class="flex gap-4">
                    <button onclick="runAllTests()" class="bg-gray-800 text-white px-6 py-2 rounded hover:bg-gray-900">
                        <i class="fas fa-play mr-2"></i>运行所有测试
                    </button>
                    <button onclick="clearResults()" class="bg-gray-400 text-white px-4 py-2 rounded hover:bg-gray-500">
                        <i class="fas fa-trash mr-2"></i>清除结果
                    </button>
                </div>
            </div>
            
            <!-- 测试结果显示区域 -->
            <div id="test-results" class="space-y-6">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        const SERVER_BASE_URL = 'http://localhost:8083';
        
        // 页面加载时检查问题URL状态
        document.addEventListener('DOMContentLoaded', function() {
            checkProblemUrlsStatus();
        });
        
        // 检查问题URL状态
        async function checkProblemUrlsStatus() {
            const problemUrls = [
                { url: '/categories/words/', statusId: 'category-status', name: '类别页面' },
                { url: '/authors/criss-jami/', statusId: 'author-status', name: '作者页面' },
                { url: '/sources/life/', statusId: 'source-status', name: '来源页面' }
            ];
            
            for (const urlTest of problemUrls) {
                try {
                    const response = await fetch(`${SERVER_BASE_URL}${urlTest.url}`, { mode: 'cors' });
                    const statusElement = document.getElementById(urlTest.statusId);
                    
                    if (response.ok) {
                        statusElement.innerHTML = `
                            <span class="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                            ✅ 正常 (${response.status})
                        `;
                    } else {
                        statusElement.innerHTML = `
                            <span class="inline-block w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                            ❌ 错误 ${response.status}
                        `;
                    }
                } catch (error) {
                    const statusElement = document.getElementById(urlTest.statusId);
                    statusElement.innerHTML = `
                        <span class="inline-block w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                        ❌ 连接失败
                    `;
                }
            }
        }
        
        // 测试问题URL
        async function testProblemUrls() {
            addTestSection('问题URL修复验证测试');
            
            const problemUrls = [
                { url: '/categories/words/', name: '类别页面 - words', expected: 'category.html' },
                { url: '/authors/criss-jami/', name: '作者页面 - criss-jami', expected: 'author.html' },
                { url: '/sources/life/', name: '来源页面 - life', expected: 'source.html' }
            ];
            
            for (const urlTest of problemUrls) {
                try {
                    const response = await fetch(`${SERVER_BASE_URL}${urlTest.url}`, { mode: 'cors' });
                    
                    if (response.ok) {
                        const contentType = response.headers.get('content-type');
                        addTestResult(urlTest.name, 'success', 
                                    `状态码: ${response.status}, 内容类型: ${contentType}`);
                    } else {
                        addTestResult(urlTest.name, 'error', 
                                    `HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    addTestResult(urlTest.name, 'error', `请求失败: ${error.message}`);
                }
            }
        }
        
        // 测试语义化URL
        async function testSemanticUrls() {
            addTestSection('语义化URL路由测试');
            
            const semanticUrls = [
                { url: '/authors/', name: '作者列表页' },
                { url: '/authors/albert-einstein/', name: '作者详情页' },
                { url: '/categories/', name: '类别列表页' },
                { url: '/categories/motivation/', name: '类别详情页' },
                { url: '/sources/', name: '来源列表页' },
                { url: '/sources/book/', name: '来源详情页' },
                { url: '/quotes/', name: '名言列表页' },
                { url: '/search/', name: '搜索页面' }
            ];
            
            for (const urlTest of semanticUrls) {
                try {
                    const response = await fetch(`${SERVER_BASE_URL}${urlTest.url}`, { mode: 'cors' });
                    
                    if (response.ok) {
                        addTestResult(urlTest.name, 'success', `状态码: ${response.status}`);
                    } else {
                        addTestResult(urlTest.name, 'error', `HTTP ${response.status}`);
                    }
                } catch (error) {
                    addTestResult(urlTest.name, 'error', `请求失败: ${error.message}`);
                }
            }
        }
        
        // 测试静态资源
        async function testStaticResources() {
            addTestSection('静态资源路径测试');
            
            const staticResources = [
                { url: '/css/styles.css', name: 'CSS样式文件' },
                { url: '/js/url-handler.js', name: 'URL处理脚本' },
                { url: '/js/page-router.js', name: '页面路由脚本' },
                { url: '/components/navigation.html', name: '导航组件' },
                { url: '/components/footer.html', name: '页脚组件' }
            ];
            
            for (const resource of staticResources) {
                try {
                    const response = await fetch(`${SERVER_BASE_URL}${resource.url}`, { mode: 'cors' });
                    
                    if (response.ok) {
                        addTestResult(resource.name, 'success', `状态码: ${response.status}`);
                    } else {
                        addTestResult(resource.name, 'error', `HTTP ${response.status}`);
                    }
                } catch (error) {
                    addTestResult(resource.name, 'error', `请求失败: ${error.message}`);
                }
            }
        }
        
        // 测试URL模式
        async function testUrlPatterns() {
            addTestSection('URL模式匹配测试');
            
            const urlPatterns = [
                { url: '/authors/test-author-123/', name: '作者slug模式' },
                { url: '/categories/test-category/', name: '类别slug模式' },
                { url: '/sources/test-source/', name: '来源slug模式' },
                { url: '/quotes/123/', name: '名言ID模式' }
            ];
            
            for (const pattern of urlPatterns) {
                try {
                    const response = await fetch(`${SERVER_BASE_URL}${pattern.url}`, { mode: 'cors' });
                    
                    if (response.ok) {
                        addTestResult(pattern.name, 'success', `模式匹配成功: ${response.status}`);
                    } else {
                        addTestResult(pattern.name, 'error', `模式匹配失败: ${response.status}`);
                    }
                } catch (error) {
                    addTestResult(pattern.name, 'error', `请求失败: ${error.message}`);
                }
            }
        }
        
        // 测试错误处理
        async function testErrorHandling() {
            addTestSection('错误处理测试');
            
            const errorUrls = [
                { url: '/nonexistent-page/', name: '不存在的页面' },
                { url: '/invalid/path/structure/', name: '无效路径结构' }
            ];
            
            for (const errorTest of errorUrls) {
                try {
                    const response = await fetch(`${SERVER_BASE_URL}${errorTest.url}`, { mode: 'cors' });
                    
                    if (response.status === 404) {
                        addTestResult(errorTest.name, 'success', `正确返回404错误`);
                    } else {
                        addTestResult(errorTest.name, 'error', `意外状态码: ${response.status}`);
                    }
                } catch (error) {
                    addTestResult(errorTest.name, 'error', `请求失败: ${error.message}`);
                }
            }
        }
        
        // 测试CORS头部
        async function testCorsHeaders() {
            addTestSection('CORS头部测试');
            
            try {
                const response = await fetch(`${SERVER_BASE_URL}/categories/words/`, { mode: 'cors' });
                
                const corsHeaders = [
                    'Access-Control-Allow-Origin',
                    'Access-Control-Allow-Methods',
                    'Access-Control-Allow-Headers'
                ];
                
                corsHeaders.forEach(header => {
                    const headerValue = response.headers.get(header);
                    addTestResult(`CORS头: ${header}`, headerValue ? 'success' : 'error', 
                                headerValue || '未设置');
                });
                
            } catch (error) {
                addTestResult('CORS测试', 'error', `测试失败: ${error.message}`);
            }
        }
        
        // 测试页面加载
        async function testPageLoading() {
            addTestSection('页面加载性能测试');
            
            const testUrls = [
                '/categories/words/',
                '/authors/criss-jami/',
                '/sources/life/'
            ];
            
            for (const url of testUrls) {
                try {
                    const startTime = performance.now();
                    const response = await fetch(`${SERVER_BASE_URL}${url}`, { mode: 'cors' });
                    const endTime = performance.now();
                    const loadTime = Math.round(endTime - startTime);
                    
                    if (response.ok) {
                        addTestResult(`页面加载: ${url}`, 'success', 
                                    `加载时间: ${loadTime}ms, 大小: ${response.headers.get('content-length')} bytes`);
                    } else {
                        addTestResult(`页面加载: ${url}`, 'error', `HTTP ${response.status}`);
                    }
                } catch (error) {
                    addTestResult(`页面加载: ${url}`, 'error', `加载失败: ${error.message}`);
                }
            }
        }
        
        // 运行所有测试
        async function runAllTests() {
            clearResults();
            addTestSection('开始运行URL路由修复验证测试');
            
            await testProblemUrls();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testSemanticUrls();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testStaticResources();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testUrlPatterns();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testErrorHandling();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testCorsHeaders();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPageLoading();
            
            addTestSection('所有URL路由修复验证测试完成 ✅');
        }
        
        // 工具函数
        function addTestSection(title) {
            const resultsDiv = document.getElementById('test-results');
            const sectionHTML = `
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        ${title}
                    </h3>
                    <div id="section-${Date.now()}" class="space-y-2">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
            `;
            resultsDiv.insertAdjacentHTML('beforeend', sectionHTML);
        }
        
        function addTestResult(testName, status, message) {
            const lastSection = document.querySelector('#test-results > div:last-child > div:last-child');
            if (!lastSection) return;
            
            const statusClass = status === 'success' ? 'test-success' : 'test-error';
            const iconClass = status === 'success' ? 'fa-check text-green-600' : 'fa-times text-red-600';
            
            const resultHTML = `
                <div class="test-result border rounded p-3 ${statusClass}">
                    <div class="flex items-center">
                        <i class="fas ${iconClass} mr-2"></i>
                        <span class="font-medium">${testName}</span>
                    </div>
                    <div class="mt-1 text-sm">${message}</div>
                </div>
            `;
            
            lastSection.insertAdjacentHTML('beforeend', resultHTML);
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
    </script>
</body>
</html>
