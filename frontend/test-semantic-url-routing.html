<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语义化URL路由测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .url-link { display: inline-block; margin: 5px; padding: 8px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .url-link:hover { background-color: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #218838; }
    </style>
</head>
<body>
    <h1>语义化URL路由测试</h1>
    <p>测试本地开发环境中的语义化URL路由功能</p>
    
    <div class="test-section">
        <h2>🔧 服务器状态</h2>
        <div id="server-status">
            <div class="info">当前使用语义化URL服务器 (semantic_url_server.py) 在端口 8083</div>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 URL路由测试</h2>
        <p>点击以下链接测试语义化URL是否正常工作：</p>
        
        <h3>Categories (分类页面)</h3>
        <a href="/categories/friendship/" class="url-link" target="_blank">/categories/friendship/</a>
        <a href="/categories/life/" class="url-link" target="_blank">/categories/life/</a>
        <a href="/categories/wisdom/" class="url-link" target="_blank">/categories/wisdom/</a>
        
        <h3>Authors (作者页面)</h3>
        <a href="/authors/pearl-zhu/" class="url-link" target="_blank">/authors/pearl-zhu/</a>
        <a href="/authors/albert-einstein/" class="url-link" target="_blank">/authors/albert-einstein/</a>
        <a href="/authors/steve-jobs/" class="url-link" target="_blank">/authors/steve-jobs/</a>
        
        <h3>Sources (来源页面)</h3>
        <a href="/sources/meditations/" class="url-link" target="_blank">/sources/meditations/</a>
        <a href="/sources/healology/" class="url-link" target="_blank">/sources/healology/</a>
        <a href="/sources/interview/" class="url-link" target="_blank">/sources/interview/</a>
        
        <h3>List Pages (列表页面)</h3>
        <a href="/authors/" class="url-link" target="_blank">/authors/</a>
        <a href="/categories/" class="url-link" target="_blank">/categories/</a>
        <a href="/sources/" class="url-link" target="_blank">/sources/</a>
    </div>

    <div class="test-section">
        <h2>🔍 自动化测试</h2>
        <button onclick="runAutomatedTests()">运行自动化测试</button>
        <button onclick="clearResults()">清除结果</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 测试统计</h2>
        <div id="test-stats">
            <div class="info">点击"运行自动化测试"查看详细统计</div>
        </div>
    </div>

    <script>
        let testResults = { total: 0, passed: 0, failed: 0 };

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById('test-results').appendChild(div);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            testResults = { total: 0, passed: 0, failed: 0 };
            updateStats();
        }

        function updateStats() {
            const statsDiv = document.getElementById('test-stats');
            if (testResults.total === 0) {
                statsDiv.innerHTML = '<div class="info">点击"运行自动化测试"查看详细统计</div>';
            } else {
                const passRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
                statsDiv.innerHTML = `
                    <div class="success">
                        <strong>测试统计：</strong><br>
                        总计: ${testResults.total} | 
                        通过: ${testResults.passed} | 
                        失败: ${testResults.failed} | 
                        通过率: ${passRate}%
                    </div>
                `;
            }
        }

        async function testUrl(url, expectedContent = null) {
            testResults.total++;
            
            try {
                const response = await fetch(url);
                
                if (response.ok) {
                    const text = await response.text();
                    
                    // 检查是否包含预期内容
                    if (expectedContent && !text.includes(expectedContent)) {
                        addResult(`❌ ${url} - 响应成功但内容不符合预期`, 'error');
                        testResults.failed++;
                        return false;
                    }
                    
                    // 检查是否是HTML页面
                    if (text.includes('<!DOCTYPE html>') || text.includes('<html')) {
                        addResult(`✅ ${url} - 路由成功，返回HTML页面`, 'success');
                        testResults.passed++;
                        return true;
                    } else {
                        addResult(`⚠️ ${url} - 响应成功但不是HTML页面`, 'warning');
                        testResults.failed++;
                        return false;
                    }
                } else {
                    addResult(`❌ ${url} - HTTP ${response.status} ${response.statusText}`, 'error');
                    testResults.failed++;
                    return false;
                }
            } catch (error) {
                addResult(`❌ ${url} - 请求失败: ${error.message}`, 'error');
                testResults.failed++;
                return false;
            }
        }

        async function runAutomatedTests() {
            clearResults();
            addResult('🚀 开始自动化URL路由测试...', 'info');
            
            // 测试URL列表
            const testUrls = [
                // Categories
                '/categories/friendship/',
                '/categories/life/',
                '/categories/wisdom/',
                
                // Authors  
                '/authors/pearl-zhu/',
                '/authors/albert-einstein/',
                '/authors/steve-jobs/',
                
                // Sources
                '/sources/meditations/',
                '/sources/healology/',
                '/sources/interview/',
                
                // List pages
                '/authors/',
                '/categories/',
                '/sources/'
            ];

            addResult(`📋 准备测试 ${testUrls.length} 个URL...`, 'info');
            
            // 逐个测试URL
            for (const url of testUrls) {
                await testUrl(url);
                // 添加小延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            updateStats();
            
            if (testResults.failed === 0) {
                addResult('🎉 所有URL路由测试通过！语义化URL功能正常工作。', 'success');
            } else {
                addResult(`⚠️ 发现 ${testResults.failed} 个问题，请检查服务器配置。`, 'warning');
            }
        }

        // 页面加载时显示当前状态
        window.addEventListener('load', () => {
            // 检查当前URL是否使用语义化路由
            const currentPath = window.location.pathname;
            if (currentPath.includes('/categories/') || currentPath.includes('/authors/') || currentPath.includes('/sources/')) {
                addResult(`✅ 当前页面使用语义化URL: ${currentPath}`, 'success');
            }
            
            updateStats();
        });
    </script>
</body>
</html>
