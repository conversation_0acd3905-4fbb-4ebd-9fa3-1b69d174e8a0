<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实体ID收集工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .mapping-output { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>实体ID收集工具</h1>
    <p>收集Categories、Authors、Sources的ID信息，用于构建映射表</p>
    
    <!-- 加载必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/url-handler.js"></script>

    <div class="test-section">
        <h2>🏷️ Categories ID收集</h2>
        <button onclick="collectCategoryIds()">收集类别ID</button>
        <div id="category-results"></div>
        <div id="category-mapping" class="mapping-output" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>👤 Authors ID收集</h2>
        <button onclick="collectAuthorIds()">收集作者ID</button>
        <div id="author-results"></div>
        <div id="author-mapping" class="mapping-output" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📚 Sources ID收集</h2>
        <button onclick="collectSourceIds()">收集来源ID</button>
        <div id="source-results"></div>
        <div id="source-mapping" class="mapping-output" style="display: none;"></div>
    </div>

    <script>
        function addResult(containerId, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById(containerId).appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 收集类别ID
        async function collectCategoryIds() {
            const container = 'category-results';
            clearResults(container);
            
            addResult(container, '🔍 开始收集类别ID...', 'info');
            
            // 确保使用生产API
            window.ApiClient.useMockData = false;
            
            const categoryNames = [
                'life', 'writing', 'friendship', 'wisdom', 'love', 
                'success', 'motivation', 'happiness', 'inspiration',
                'leadership', 'business', 'education', 'family',
                'health', 'creativity', 'courage', 'perseverance'
            ];
            
            const mappings = {};
            
            for (const categoryName of categoryNames) {
                try {
                    addResult(container, `🔍 查询类别: "${categoryName}"...`, 'info');
                    
                    // 尝试多种查询方式
                    let category = null;
                    
                    // 1. 尝试小写
                    category = await window.ApiClient.getCategoryByName(categoryName);
                    if (!category) {
                        // 2. 尝试首字母大写
                        const capitalized = categoryName.charAt(0).toUpperCase() + categoryName.slice(1);
                        category = await window.ApiClient.getCategoryByName(capitalized);
                    }
                    
                    if (category) {
                        mappings[categoryName] = category.id;
                        addResult(container, `✅ "${categoryName}" → ID: ${category.id}, Name: "${category.name}", Count: ${category.count}`, 'success');
                    } else {
                        mappings[categoryName] = null;
                        addResult(container, `❌ "${categoryName}" 未找到`, 'warning');
                    }
                    
                    // 添加延迟避免请求过快
                    await new Promise(resolve => setTimeout(resolve, 200));
                    
                } catch (error) {
                    mappings[categoryName] = null;
                    addResult(container, `❌ "${categoryName}" 查询失败: ${error.message}`, 'error');
                }
            }
            
            // 生成映射表代码
            generateMappingCode('category-mapping', 'categories', mappings);
        }

        // 收集作者ID
        async function collectAuthorIds() {
            const container = 'author-results';
            clearResults(container);
            
            addResult(container, '🔍 开始收集作者ID...', 'info');
            
            // 确保使用生产API
            window.ApiClient.useMockData = false;
            
            const authorSlugs = [
                'albert-einstein', 'steve-jobs', 'pearl-zhu', 'mark-twain',
                'oscar-wilde', 'winston-churchill', 'maya-angelou',
                'nelson-mandela', 'martin-luther-king', 'gandhi',
                'aristotle', 'plato', 'confucius', 'buddha'
            ];
            
            const mappings = {};
            
            for (const authorSlug of authorSlugs) {
                try {
                    addResult(container, `🔍 查询作者: "${authorSlug}"...`, 'info');
                    
                    // 转换slug为名称
                    const authorName = window.UrlHandler.deslugify(authorSlug);
                    
                    // 尝试查询
                    const author = await window.ApiClient.getAuthorByName(authorName);
                    
                    if (author) {
                        mappings[authorSlug] = author.id;
                        addResult(container, `✅ "${authorSlug}" (${authorName}) → ID: ${author.id}, Name: "${author.name}", Count: ${author.count}`, 'success');
                    } else {
                        mappings[authorSlug] = null;
                        addResult(container, `❌ "${authorSlug}" (${authorName}) 未找到`, 'warning');
                    }
                    
                    // 添加延迟避免请求过快
                    await new Promise(resolve => setTimeout(resolve, 200));
                    
                } catch (error) {
                    mappings[authorSlug] = null;
                    addResult(container, `❌ "${authorSlug}" 查询失败: ${error.message}`, 'error');
                }
            }
            
            // 生成映射表代码
            generateMappingCode('author-mapping', 'authors', mappings);
        }

        // 收集来源ID
        async function collectSourceIds() {
            const container = 'source-results';
            clearResults(container);
            
            addResult(container, '🔍 开始收集来源ID...', 'info');
            
            // 确保使用生产API
            window.ApiClient.useMockData = false;
            
            const sourceSlugs = [
                'meditations', 'healology', 'interview', 'speech',
                'letter', 'book', 'article', 'essay', 'diary',
                'autobiography', 'biography', 'novel', 'poem'
            ];
            
            const mappings = {};
            
            for (const sourceSlug of sourceSlugs) {
                try {
                    addResult(container, `🔍 查询来源: "${sourceSlug}"...`, 'info');
                    
                    // 转换slug为名称
                    const sourceName = window.UrlHandler.deslugify(sourceSlug);
                    
                    // 尝试多种查询方式
                    let source = null;
                    
                    // 1. 尝试原始slug
                    source = await window.ApiClient.getSourceByName(sourceSlug);
                    if (!source) {
                        // 2. 尝试转换后的名称
                        source = await window.ApiClient.getSourceByName(sourceName);
                    }
                    if (!source) {
                        // 3. 尝试小写
                        source = await window.ApiClient.getSourceByName(sourceName.toLowerCase());
                    }
                    
                    if (source) {
                        mappings[sourceSlug] = source.id;
                        addResult(container, `✅ "${sourceSlug}" (${sourceName}) → ID: ${source.id}, Name: "${source.name}", Count: ${source.count}`, 'success');
                    } else {
                        mappings[sourceSlug] = null;
                        addResult(container, `❌ "${sourceSlug}" (${sourceName}) 未找到`, 'warning');
                    }
                    
                    // 添加延迟避免请求过快
                    await new Promise(resolve => setTimeout(resolve, 200));
                    
                } catch (error) {
                    mappings[sourceSlug] = null;
                    addResult(container, `❌ "${sourceSlug}" 查询失败: ${error.message}`, 'error');
                }
            }
            
            // 生成映射表代码
            generateMappingCode('source-mapping', 'sources', mappings);
        }

        // 生成映射表代码
        function generateMappingCode(containerId, entityType, mappings) {
            const container = document.getElementById(containerId);
            container.style.display = 'block';
            
            let code = `// ${entityType.toUpperCase()} ID映射表\nconst KNOWN_${entityType.toUpperCase()}_IDS = {\n`;
            
            for (const [key, value] of Object.entries(mappings)) {
                if (value !== null) {
                    code += `    '${key}': ${value},\n`;
                } else {
                    code += `    '${key}': null, // 未找到\n`;
                }
            }
            
            code += `};\n\n`;
            
            // 添加统计信息
            const found = Object.values(mappings).filter(v => v !== null).length;
            const total = Object.keys(mappings).length;
            
            code += `// 统计: ${found}/${total} 个${entityType}找到ID (${Math.round(found/total*100)}%)`;
            
            container.innerHTML = `
                <h3>📋 ${entityType.toUpperCase()} 映射表代码</h3>
                <pre>${code}</pre>
                <button onclick="copyToClipboard('${containerId}')">复制代码</button>
            `;
        }

        // 复制到剪贴板
        function copyToClipboard(containerId) {
            const container = document.getElementById(containerId);
            const pre = container.querySelector('pre');
            const text = pre.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                alert('代码已复制到剪贴板！');
            }).catch(err => {
                console.error('复制失败:', err);
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('代码已复制到剪贴板！');
            });
        }

        // 页面加载时显示说明
        window.addEventListener('load', () => {
            console.log('实体ID收集工具已加载');
        });
    </script>
</body>
</html>
