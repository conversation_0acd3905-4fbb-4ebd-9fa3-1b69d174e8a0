<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面包屑导航修复测试 - Quotese.com</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        #console-output {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>面包屑导航修复测试</h1>
    
    <div class="test-section">
        <h2>问题描述</h2>
        <p>修复前：点击面包屑导航的 home 时，页面报错 "Identifier 'pageState' has already been declared"</p>
        <p>修复方案：将每个页面的 pageState 变量改为使用不同的命名空间</p>
        <ul>
            <li>index.js: <code>pageState</code> → <code>indexPageState</code></li>
            <li>category.js: <code>pageState</code> → <code>categoryPageState</code></li>
            <li>author.js: <code>pageState</code> → <code>authorPageState</code></li>
            <li>source.js: <code>pageState</code> → <code>sourcePageState</code></li>
            <li>quote.js: <code>pageState</code> → <code>quotePageState</code></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>点击下面的按钮导航到不同页面</li>
            <li>在每个页面点击面包屑导航的 "Home" 链接</li>
            <li>观察浏览器控制台是否还有 JavaScript 错误</li>
        </ol>
        
        <button class="test-button" onclick="navigateToCategory()">导航到 Life 类别页面</button>
        <button class="test-button" onclick="navigateToAuthor()">导航到作者页面</button>
        <button class="test-button" onclick="navigateToSource()">导航到来源页面</button>
        <button class="test-button" onclick="navigateToHome()">导航到首页</button>
    </div>

    <div class="test-section">
        <h2>控制台输出</h2>
        <div id="console-output"></div>
        <button class="test-button" onclick="clearConsole()">清除输出</button>
    </div>

    <script>
        // 捕获控制台输出
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.innerHTML = `<span style="color: #666;">[${timestamp}]</span> <span style="color: ${type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black'};">[${type.toUpperCase()}]</span> ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole('log', args.join(' '));
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole('error', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole('warn', args.join(' '));
        };
        
        // 捕获未处理的错误
        window.addEventListener('error', function(event) {
            addToConsole('error', `Uncaught Error: ${event.message} at ${event.filename}:${event.lineno}:${event.colno}`);
        });
        
        // 导航函数
        function navigateToCategory() {
            console.log('导航到 Life 类别页面...');
            window.location.href = '/categories/life/';
        }
        
        function navigateToAuthor() {
            console.log('导航到作者页面...');
            window.location.href = '/authors/albert-einstein/';
        }
        
        function navigateToSource() {
            console.log('导航到来源页面...');
            window.location.href = '/sources/the-art-of-war/';
        }
        
        function navigateToHome() {
            console.log('导航到首页...');
            window.location.href = '/';
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
        }
        
        console.log('面包屑导航修复测试页面已加载');
        console.log('请按照测试步骤进行测试');
    </script>
</body>
</html>
