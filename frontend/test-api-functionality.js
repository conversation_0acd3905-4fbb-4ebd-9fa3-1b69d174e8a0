/**
 * API功能测试脚本
 * 在浏览器控制台中运行此脚本来测试API功能
 */

// 测试结果存储
const testResults = {
    passed: 0,
    failed: 0,
    results: []
};

// 测试辅助函数
function logTest(testName, passed, message) {
    const result = {
        test: testName,
        passed: passed,
        message: message,
        timestamp: new Date().toISOString()
    };
    
    testResults.results.push(result);
    if (passed) {
        testResults.passed++;
        console.log(`✅ ${testName}: ${message}`);
    } else {
        testResults.failed++;
        console.error(`❌ ${testName}: ${message}`);
    }
}

// 测试1: getQuoteById API
async function testGetQuoteById() {
    try {
        console.log('🧪 Testing getQuoteById API...');
        
        const quoteId = 21;
        const quote = await window.ApiClient.getQuoteById(quoteId);
        
        if (!quote) {
            logTest('getQuoteById', false, 'API returned null or undefined');
            return;
        }
        
        if (!quote.id || !quote.content || !quote.author) {
            logTest('getQuoteById', false, 'Quote object missing required fields');
            return;
        }
        
        if (quote.id !== quoteId.toString()) {
            logTest('getQuoteById', false, `Expected ID ${quoteId}, got ${quote.id}`);
            return;
        }
        
        logTest('getQuoteById', true, `Successfully loaded quote: "${quote.content.substring(0, 50)}..." by ${quote.author.name}`);
        
        // 测试数据结构
        if (quote.categories && Array.isArray(quote.categories)) {
            logTest('getQuoteById-categories', true, `Quote has ${quote.categories.length} categories`);
        } else {
            logTest('getQuoteById-categories', false, 'Categories field missing or not an array');
        }
        
        if (quote.sources && Array.isArray(quote.sources)) {
            logTest('getQuoteById-sources', true, `Quote has ${quote.sources.length} sources`);
        } else {
            logTest('getQuoteById-sources', false, 'Sources field missing or not an array');
        }
        
    } catch (error) {
        logTest('getQuoteById', false, `API error: ${error.message}`);
    }
}

// 测试2: getRelatedQuotesByAuthor API
async function testGetRelatedQuotesByAuthor() {
    try {
        console.log('🧪 Testing getRelatedQuotesByAuthor API...');
        
        const authorId = 10; // Theodore Roosevelt
        const excludeQuoteId = 21;
        const limit = 5;
        
        const relatedQuotes = await window.ApiClient.getRelatedQuotesByAuthor(authorId, excludeQuoteId, limit);
        
        if (!Array.isArray(relatedQuotes)) {
            logTest('getRelatedQuotesByAuthor', false, 'API did not return an array');
            return;
        }
        
        logTest('getRelatedQuotesByAuthor', true, `Found ${relatedQuotes.length} related quotes`);
        
        // 验证排除逻辑
        const hasExcludedQuote = relatedQuotes.some(quote => parseInt(quote.id) === excludeQuoteId);
        if (hasExcludedQuote) {
            logTest('getRelatedQuotesByAuthor-exclude', false, 'Excluded quote ID found in results');
        } else {
            logTest('getRelatedQuotesByAuthor-exclude', true, 'Excluded quote ID properly filtered out');
        }
        
        // 验证作者一致性
        const wrongAuthor = relatedQuotes.find(quote => parseInt(quote.author.id) !== authorId);
        if (wrongAuthor) {
            logTest('getRelatedQuotesByAuthor-author', false, `Found quote by wrong author: ${wrongAuthor.author.name}`);
        } else {
            logTest('getRelatedQuotesByAuthor-author', true, 'All quotes are by the correct author');
        }
        
    } catch (error) {
        logTest('getRelatedQuotesByAuthor', false, `API error: ${error.message}`);
    }
}

// 测试3: Quote Card Component
async function testQuoteCardComponent() {
    try {
        console.log('🧪 Testing QuoteCardComponent...');
        
        // 获取测试数据
        const quote = await window.ApiClient.getQuoteById(21);
        
        if (!quote) {
            logTest('QuoteCardComponent', false, 'Could not get test quote data');
            return;
        }
        
        // 创建测试容器
        const testContainer = document.createElement('div');
        testContainer.id = 'test-quote-card-container';
        testContainer.style.display = 'none';
        document.body.appendChild(testContainer);
        
        // 测试卡片创建
        const quoteCard = QuoteCardComponent.render(quote, 0, {
            showActions: true,
            showAuthorAvatar: true
        });
        
        if (!quoteCard) {
            logTest('QuoteCardComponent', false, 'Component did not return a card element');
            return;
        }
        
        testContainer.appendChild(quoteCard);
        
        // 验证卡片结构
        const hasContent = quoteCard.textContent.includes(quote.content);
        const hasAuthor = quoteCard.textContent.includes(quote.author.name);
        const hasClickable = quoteCard.classList.contains('cursor-pointer');
        
        if (!hasContent) {
            logTest('QuoteCardComponent-content', false, 'Quote content not found in card');
        } else {
            logTest('QuoteCardComponent-content', true, 'Quote content properly displayed');
        }
        
        if (!hasAuthor) {
            logTest('QuoteCardComponent-author', false, 'Author name not found in card');
        } else {
            logTest('QuoteCardComponent-author', true, 'Author name properly displayed');
        }
        
        if (!hasClickable) {
            logTest('QuoteCardComponent-clickable', false, 'Card does not have cursor-pointer class');
        } else {
            logTest('QuoteCardComponent-clickable', true, 'Card is properly marked as clickable');
        }
        
        // 验证操作按钮
        const shareBtn = quoteCard.querySelector('.quote-share-btn');
        const detailBtn = quoteCard.querySelector('.quote-detail-btn');
        
        if (!shareBtn) {
            logTest('QuoteCardComponent-share-btn', false, 'Share button not found');
        } else {
            logTest('QuoteCardComponent-share-btn', true, 'Share button properly created');
        }
        
        if (!detailBtn) {
            logTest('QuoteCardComponent-detail-btn', false, 'Detail button not found');
        } else {
            logTest('QuoteCardComponent-detail-btn', true, 'Detail button properly created');
        }
        
        // 清理测试容器
        document.body.removeChild(testContainer);
        
        logTest('QuoteCardComponent', true, 'Component tests completed');
        
    } catch (error) {
        logTest('QuoteCardComponent', false, `Component error: ${error.message}`);
    }
}

// 测试4: URL Handler
function testUrlHandler() {
    try {
        console.log('🧪 Testing UrlHandler...');
        
        // 测试getQuoteUrl
        const testQuote = { id: 123 };
        const quoteUrl = UrlHandler.getQuoteUrl(testQuote);
        
        if (quoteUrl !== '/quotes/123/') {
            logTest('UrlHandler-getQuoteUrl', false, `Expected '/quotes/123/', got '${quoteUrl}'`);
        } else {
            logTest('UrlHandler-getQuoteUrl', true, 'Quote URL generation working correctly');
        }
        
        // 测试parseQuoteIdFromPath (模拟)
        const originalPathname = window.location.pathname;
        
        // 模拟不同的路径
        Object.defineProperty(window.location, 'pathname', {
            writable: true,
            value: '/quotes/456/'
        });
        
        const parsedId = UrlHandler.parseQuoteIdFromPath();
        
        if (parsedId !== 456) {
            logTest('UrlHandler-parseQuoteIdFromPath', false, `Expected 456, got ${parsedId}`);
        } else {
            logTest('UrlHandler-parseQuoteIdFromPath', true, 'Quote ID parsing working correctly');
        }
        
        // 恢复原始路径
        Object.defineProperty(window.location, 'pathname', {
            writable: true,
            value: originalPathname
        });
        
        logTest('UrlHandler', true, 'URL Handler tests completed');
        
    } catch (error) {
        logTest('UrlHandler', false, `URL Handler error: ${error.message}`);
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('🚀 Starting Quote Detail Functionality Tests...');
    console.log('================================================');
    
    // 重置测试结果
    testResults.passed = 0;
    testResults.failed = 0;
    testResults.results = [];
    
    // 运行测试
    await testGetQuoteById();
    await testGetRelatedQuotesByAuthor();
    await testQuoteCardComponent();
    testUrlHandler();
    
    // 显示总结
    console.log('================================================');
    console.log('📊 Test Results Summary:');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
    
    if (testResults.failed === 0) {
        console.log('🎉 All tests passed! Quote detail functionality is working correctly.');
    } else {
        console.log('⚠️  Some tests failed. Please check the detailed results above.');
    }
    
    return testResults;
}

// 导出测试函数
window.QuoteDetailTests = {
    runAllTests,
    testGetQuoteById,
    testGetRelatedQuotesByAuthor,
    testQuoteCardComponent,
    testUrlHandler,
    testResults
};

// 自动运行测试（如果在测试环境中）
if (window.location.pathname.includes('test-')) {
    console.log('🔍 Test environment detected. Running tests automatically...');
    setTimeout(runAllTests, 1000); // 等待1秒让页面完全加载
}
