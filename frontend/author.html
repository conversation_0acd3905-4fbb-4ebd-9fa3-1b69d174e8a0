<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Author's Classic Quotes | Wisdom Collection - quotese.com</title>
    <meta name="description" content="Discover profound quotes and wisdom from influential authors throughout history. Find inspiration and insights from the world's greatest minds.">
    <meta name="keywords" content="author quotes, famous sayings, wisdom collection, inspirational quotes, author wisdom">
    <link rel="canonical" href="https://quotese.com/authors/">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Author's Classic Quotes | Wisdom Collection - quotese.com">
    <meta property="og:description" content="Discover profound quotes and wisdom from influential authors throughout history. Find inspiration and insights from the world's greatest minds.">
    <meta property="og:image" content="https://quotese.com/images/og-image-author.jpg">
    <meta property="og:url" content="https://quotese.com/authors/">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="quotese.com">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@quotesecom">
    <meta name="twitter:title" content="Author's Classic Quotes | Wisdom Collection">
    <meta name="twitter:description" content="Discover profound quotes and wisdom from influential authors throughout history. Find inspiration and insights from the world's greatest minds.">
    <meta name="twitter:image" content="https://quotese.com/images/og-image-author.jpg">
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif:wght@400;500;600;700&family=Noto+Sans:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
    <!-- Google Analytics -->
    <script src="/js/analytics.js?v=20250626"></script>
</head>
<body class="light-mode">
    <!-- 导航栏 (将由组件加载器加载) -->
    <header id="navigation-container" role="banner"></header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-8" role="main">
        <!-- 面包屑导航 -->
        <div id="breadcrumb-container"></div>
        <!-- Author Header - 隐藏 -->
        <section class="mb-8 text-center py-4 hidden" id="author-header">
            <div class="flex justify-center mb-4 fade-in">
                <div class="w-24 h-24 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-600 dark:text-yellow-300 border-4 border-yellow-400 dark:border-yellow-600">
                    <span id="author-initial" class="text-3xl font-bold">A</span>
                </div>
            </div>
            <h1 id="author-name" class="text-4xl md:text-5xl font-bold mb-4 fade-in fade-in-delay-1">
                Author Name
            </h1>
            <p id="quote-count-container" class="text-gray-500 dark:text-gray-400 fade-in fade-in-delay-3">
                <span id="quote-count">0</span> quotes
            </p>
        </section>

        <!-- Content Grid (Left-Right Layout) -->
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Left Column (Quotes List) -->
            <section class="lg:w-2/3">
                <div id="quotes-container">
                    <!-- Quotes list component will be loaded here -->
                </div>

                <!-- Pagination -->
                <div id="pagination-container">
                    <!-- Pagination component will be loaded here -->
                </div>
            </section>

            <!-- Right Column (Sidebar) -->
            <aside class="lg:w-1/3" role="complementary" aria-label="Sidebar">
                <div id="popular-topics-container">
                    <!-- Popular topics component will be loaded here -->
                </div>
            </aside>
        </div>
    </main>

    <!-- Footer (will be loaded by component loader) -->
    <footer id="footer-container" role="contentinfo"></footer>

    <!-- JavaScript -->
    <!-- Debug Script -->
    <script src="/js/debug.js?v=20250626"></script>

    <!-- Component Loader -->
    <script src="/js/component-loader.js?v=20250626"></script>

    <!-- Mock Data -->
    <script src="/js/mock-data.js?v=20250626"></script>

    <!-- API Client -->
    <script src="/js/api-client.js?v=20250626"></script>

    <!-- Core Modules -->
    <script src="/js/theme.js?v=20250626"></script>
    <script src="/js/url-handler.js?v=20250626"></script>
    <script src="/js/entity-id-mapper-production.js?v=20250627"></script>
    <script src="/js/entity-id-mapper.js?v=20250627"></script>
    <script src="/js/optimized-navigation.js?v=20250626"></script>
    <script src="/js/mobile-performance-optimizer.js?v=20250626"></script>
    <script src="/js/performance-test.js?v=20250626"></script>
    <script src="/js/seo-manager.js?v=20250626"></script>
    <script src="/js/page-router.js?v=20250626"></script>
    <script src="/js/mobile-menu.js?v=20250626"></script>
    <script src="/js/components/pagination.js?v=20250626"></script>
    <script src="/js/components/quote-card.js?v=20250626"></script>
    <script src="/js/components/breadcrumb.js?v=20250626"></script>
    <script src="/js/navigation-state.js?v=20250627"></script>
    <script src="/js/social-meta.js?v=20250626"></script>

    <!-- Global Fix Script -->
    <script src="/js/global-fix.js?v=20250626"></script>

    <!-- Quote Card Click Fix -->
    <script src="/js/quote-card-click-fix.js?v=20250628"></script>

    <!-- Page Specific Script -->
    <script src="/js/pages/author.js?v=20250626"></script>
</body>
</html>
