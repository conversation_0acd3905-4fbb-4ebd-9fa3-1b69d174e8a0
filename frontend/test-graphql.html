<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphQL测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>GraphQL API测试</h1>
    <div id="results"></div>
    
    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }
        
        async function testGraphQL() {
            addResult('开始GraphQL测试...', 'info');
            
            const endpoint = 'http://localhost:8000/graphql/';
            
            // 测试类别查询
            const categoriesQuery = `
                query {
                    categories(first: 10) {
                        id
                        name
                        quotesCount
                    }
                }
            `;
            
            try {
                addResult('测试类别查询...', 'info');
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: categoriesQuery
                    })
                });
                
                const data = await response.json();
                
                if (data.errors) {
                    addResult(`❌ GraphQL错误: <pre>${JSON.stringify(data.errors, null, 2)}</pre>`, 'error');
                } else if (data.data && data.data.categories) {
                    addResult(`✅ 类别查询成功 - 获取到 ${data.data.categories.length} 个类别`, 'success');
                    addResult(`<pre>${JSON.stringify(data.data.categories.slice(0, 3), null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ 意外的响应格式: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ 请求失败: ${error.message}`, 'error');
            }
            
            // 测试作者查询
            const authorsQuery = `
                query {
                    authors(first: 10) {
                        id
                        name
                        quotesCount
                    }
                }
            `;
            
            try {
                addResult('测试作者查询...', 'info');
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: authorsQuery
                    })
                });
                
                const data = await response.json();
                
                if (data.errors) {
                    addResult(`❌ GraphQL错误: <pre>${JSON.stringify(data.errors, null, 2)}</pre>`, 'error');
                } else if (data.data && data.data.authors) {
                    addResult(`✅ 作者查询成功 - 获取到 ${data.data.authors.length} 个作者`, 'success');
                    addResult(`<pre>${JSON.stringify(data.data.authors.slice(0, 3), null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ 意外的响应格式: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ 请求失败: ${error.message}`, 'error');
            }
            
            // 测试来源查询
            const sourcesQuery = `
                query {
                    sources(first: 10) {
                        id
                        name
                        quotesCount
                    }
                }
            `;
            
            try {
                addResult('测试来源查询...', 'info');
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: sourcesQuery
                    })
                });
                
                const data = await response.json();
                
                if (data.errors) {
                    addResult(`❌ GraphQL错误: <pre>${JSON.stringify(data.errors, null, 2)}</pre>`, 'error');
                } else if (data.data && data.data.sources) {
                    addResult(`✅ 来源查询成功 - 获取到 ${data.data.sources.length} 个来源`, 'success');
                    addResult(`<pre>${JSON.stringify(data.data.sources.slice(0, 3), null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ 意外的响应格式: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ 请求失败: ${error.message}`, 'error');
            }
            
            addResult('GraphQL测试完成', 'info');
        }
        
        // 页面加载后自动开始测试
        window.addEventListener('load', testGraphQL);
    </script>
</body>
</html>
