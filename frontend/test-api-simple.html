<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .error { background-color: #ffebee; }
        .success { background-color: #e8f5e8; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>Simple API Test</h1>
    
    <button onclick="testQuotes()">Test Quotes</button>
    <button onclick="testCategories()">Test Categories</button>
    <button onclick="testAuthors()">Test Authors</button>
    <button onclick="testSources()">Test Sources</button>
    
    <div id="results"></div>

    <script>
        // 配置
        const API_ENDPOINT = 'http://localhost:8000/graphql/';
        
        // 通用GraphQL查询函数
        async function query(graphqlQuery) {
            try {
                const response = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: graphqlQuery
                    }),
                });

                const result = await response.json();
                
                if (result.errors) {
                    throw new Error(result.errors[0].message);
                }

                return result.data;
            } catch (error) {
                console.error('GraphQL query error:', error);
                throw error;
            }
        }

        // 显示结果
        function showResult(title, data, isError = false) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        // 测试名言
        async function testQuotes() {
            try {
                const data = await query(`
                    query {
                        quotes(first: 5) {
                            id
                            content
                            author {
                                id
                                name
                            }
                            categories {
                                id
                                name
                            }
                            sources {
                                id
                                name
                            }
                        }
                        filteredQuotesCount
                    }
                `);
                showResult('Quotes Test', data);
            } catch (error) {
                showResult('Quotes Test Error', error.message, true);
            }
        }

        // 测试类别
        async function testCategories() {
            try {
                const data = await query(`
                    query {
                        categories(first: 10, orderBy: "quotes_count", orderDirection: "desc") {
                            id
                            name
                            quotesCount
                        }
                    }
                `);
                showResult('Categories Test', data);
            } catch (error) {
                showResult('Categories Test Error', error.message, true);
            }
        }

        // 测试作者
        async function testAuthors() {
            try {
                const data = await query(`
                    query {
                        authors(first: 10, orderBy: "quotes_count", orderDirection: "desc") {
                            id
                            name
                            quotesCount
                        }
                    }
                `);
                showResult('Authors Test', data);
            } catch (error) {
                showResult('Authors Test Error', error.message, true);
            }
        }

        // 测试来源
        async function testSources() {
            try {
                const data = await query(`
                    query {
                        sources(first: 10, orderBy: "quotes_count", orderDirection: "desc") {
                            id
                            name
                            quotesCount
                        }
                    }
                `);
                showResult('Sources Test', data);
            } catch (error) {
                showResult('Sources Test Error', error.message, true);
            }
        }

        // 页面加载时自动测试
        window.addEventListener('load', () => {
            console.log('Starting automatic API tests...');
            setTimeout(testQuotes, 500);
            setTimeout(testCategories, 1000);
            setTimeout(testAuthors, 1500);
            setTimeout(testSources, 2000);
        });
    </script>
</body>
</html>
