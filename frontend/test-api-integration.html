<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API集成测试 - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .api-result {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            margin: 8px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-plug mr-3 text-blue-500"></i>
                API集成测试
            </h1>
            
            <!-- 服务状态 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">服务状态</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="p-4 border rounded">
                        <h3 class="font-medium text-gray-800">前端服务</h3>
                        <p class="text-sm text-gray-600">http://localhost:8081</p>
                        <div id="frontend-status" class="mt-2">
                            <span class="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                            <span class="text-green-600">运行中</span>
                        </div>
                    </div>
                    <div class="p-4 border rounded">
                        <h3 class="font-medium text-gray-800">后端服务</h3>
                        <p class="text-sm text-gray-600">http://localhost:8001</p>
                        <div id="backend-status" class="mt-2">
                            <span class="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                            <span class="text-yellow-600">检测中...</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 测试控制面板 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">API测试控制面板</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    <button onclick="testBackendConnection()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        <i class="fas fa-link mr-2"></i>测试后端连接
                    </button>
                    <button onclick="testAuthorsAPI()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        <i class="fas fa-user mr-2"></i>测试作者API
                    </button>
                    <button onclick="testCategoriesAPI()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        <i class="fas fa-tags mr-2"></i>测试类别API
                    </button>
                    <button onclick="testSourcesAPI()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                        <i class="fas fa-book mr-2"></i>测试来源API
                    </button>
                    <button onclick="testQuotesAPI()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        <i class="fas fa-quote-left mr-2"></i>测试名言API
                    </button>
                    <button onclick="testGraphQLAPI()" class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                        <i class="fas fa-code mr-2"></i>测试GraphQL
                    </button>
                </div>
                
                <div class="flex gap-4">
                    <button onclick="runAllAPITests()" class="bg-gray-800 text-white px-6 py-2 rounded hover:bg-gray-900">
                        <i class="fas fa-play mr-2"></i>运行所有测试
                    </button>
                    <button onclick="clearResults()" class="bg-gray-400 text-white px-4 py-2 rounded hover:bg-gray-500">
                        <i class="fas fa-trash mr-2"></i>清除结果
                    </button>
                </div>
            </div>
            
            <!-- 测试结果显示区域 -->
            <div id="test-results" class="space-y-6">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8001';
        
        // 页面加载时检查后端状态
        document.addEventListener('DOMContentLoaded', function() {
            checkBackendStatus();
        });
        
        // 检查后端服务状态
        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/admin/`, { mode: 'no-cors' });
                updateBackendStatus(true);
            } catch (error) {
                updateBackendStatus(false);
            }
        }
        
        // 更新后端状态显示
        function updateBackendStatus(isOnline) {
            const statusDiv = document.getElementById('backend-status');
            if (isOnline) {
                statusDiv.innerHTML = `
                    <span class="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                    <span class="text-green-600">运行中</span>
                `;
            } else {
                statusDiv.innerHTML = `
                    <span class="inline-block w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                    <span class="text-red-600">离线</span>
                `;
            }
        }
        
        // 测试后端连接
        async function testBackendConnection() {
            addTestSection('后端连接测试');
            
            try {
                // 测试Django admin页面
                const adminResponse = await fetch(`${API_BASE_URL}/admin/`, { mode: 'no-cors' });
                addTestResult('Django Admin', 'success', '后端服务正常运行');
                
                // 测试API根路径
                try {
                    const apiResponse = await fetch(`${API_BASE_URL}/api/`, { mode: 'cors' });
                    if (apiResponse.ok) {
                        addTestResult('API根路径', 'success', 'API服务可访问');
                    } else {
                        addTestResult('API根路径', 'error', `HTTP ${apiResponse.status}`);
                    }
                } catch (apiError) {
                    addTestResult('API根路径', 'error', `CORS或网络错误: ${apiError.message}`);
                }
                
            } catch (error) {
                addTestResult('后端连接', 'error', `连接失败: ${error.message}`);
            }
        }
        
        // 测试作者API
        async function testAuthorsAPI() {
            addTestSection('作者API测试');
            
            try {
                // 测试获取作者列表
                const response = await fetch(`${API_BASE_URL}/api/authors/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addTestResult('获取作者列表', 'success', `成功获取 ${data.length || 0} 个作者`);
                    addAPIResult('作者数据', data);
                } else {
                    addTestResult('获取作者列表', 'error', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addTestResult('作者API', 'error', `请求失败: ${error.message}`);
            }
        }
        
        // 测试类别API
        async function testCategoriesAPI() {
            addTestSection('类别API测试');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/categories/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addTestResult('获取类别列表', 'success', `成功获取 ${data.length || 0} 个类别`);
                    addAPIResult('类别数据', data);
                } else {
                    addTestResult('获取类别列表', 'error', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addTestResult('类别API', 'error', `请求失败: ${error.message}`);
            }
        }
        
        // 测试来源API
        async function testSourcesAPI() {
            addTestSection('来源API测试');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/sources/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addTestResult('获取来源列表', 'success', `成功获取 ${data.length || 0} 个来源`);
                    addAPIResult('来源数据', data);
                } else {
                    addTestResult('获取来源列表', 'error', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addTestResult('来源API', 'error', `请求失败: ${error.message}`);
            }
        }
        
        // 测试名言API
        async function testQuotesAPI() {
            addTestSection('名言API测试');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/quotes/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addTestResult('获取名言列表', 'success', `成功获取 ${data.length || 0} 个名言`);
                    addAPIResult('名言数据', data);
                } else {
                    addTestResult('获取名言列表', 'error', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addTestResult('名言API', 'error', `请求失败: ${error.message}`);
            }
        }
        
        // 测试GraphQL API
        async function testGraphQLAPI() {
            addTestSection('GraphQL API测试');
            
            const query = `
                query {
                    allAuthors {
                        id
                        name
                    }
                }
            `;
            
            try {
                const response = await fetch(`${API_BASE_URL}/graphql/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors',
                    body: JSON.stringify({ query })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addTestResult('GraphQL查询', 'success', 'GraphQL API正常工作');
                    addAPIResult('GraphQL响应', data);
                } else {
                    addTestResult('GraphQL查询', 'error', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addTestResult('GraphQL API', 'error', `请求失败: ${error.message}`);
            }
        }
        
        // 运行所有API测试
        async function runAllAPITests() {
            clearResults();
            addTestSection('开始运行完整的API集成测试');
            
            await testBackendConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAuthorsAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testCategoriesAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testSourcesAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testQuotesAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testGraphQLAPI();
            
            addTestSection('所有API测试完成');
        }
        
        // 添加测试部分标题
        function addTestSection(title) {
            const resultsDiv = document.getElementById('test-results');
            const sectionHTML = `
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        ${title}
                    </h3>
                    <div id="section-${Date.now()}" class="space-y-2">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
            `;
            resultsDiv.insertAdjacentHTML('beforeend', sectionHTML);
        }
        
        // 添加测试结果
        function addTestResult(testName, status, message) {
            const lastSection = document.querySelector('#test-results > div:last-child > div:last-child');
            if (!lastSection) return;
            
            const statusClass = status === 'success' ? 'test-success' : 'test-error';
            const iconClass = status === 'success' ? 'fa-check text-green-600' : 'fa-times text-red-600';
            
            const resultHTML = `
                <div class="border rounded p-3 ${statusClass}">
                    <div class="flex items-center">
                        <i class="fas ${iconClass} mr-2"></i>
                        <span class="font-medium">${testName}</span>
                    </div>
                    <div class="mt-1 text-sm">${message}</div>
                </div>
            `;
            
            lastSection.insertAdjacentHTML('beforeend', resultHTML);
        }
        
        // 添加API结果
        function addAPIResult(title, data) {
            const lastSection = document.querySelector('#test-results > div:last-child > div:last-child');
            if (!lastSection) return;
            
            const resultHTML = `
                <div class="mt-4">
                    <h4 class="font-medium text-gray-700 mb-2">${title}:</h4>
                    <div class="api-result">${JSON.stringify(data, null, 2)}</div>
                </div>
            `;
            
            lastSection.insertAdjacentHTML('beforeend', resultHTML);
        }
        
        // 清除测试结果
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
    </script>
</body>
</html>
