<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试Categories方法</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .loading { background-color: #fff3cd; color: #856404; }
        button { margin: 5px; padding: 8px 12px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>测试Categories方法</h1>
    
    <div>
        <button onclick="testGetPopularCategories()">测试getPopularCategories</button>
        <button onclick="testDirectQuery()">测试直接GraphQL查询</button>
        <button onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="results"></div>
    
    <!-- Load scripts in correct order -->
    <script src="/js/config.js?v=20250626"></script>
    <script src="/js/api-client.js?v=20250626"></script>
    
    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        async function testGetPopularCategories() {
            addResult('测试getPopularCategories方法...', 'loading');
            
            if (!window.ApiClient) {
                addResult('❌ API客户端未初始化', 'error');
                return;
            }
            
            try {
                addResult('调用 getPopularCategories(10)...', 'info');
                const categories = await window.ApiClient.getPopularCategories(10);
                
                addResult(`✅ 成功获取 ${categories.length} 个类别`, 'success');
                addResult(`<pre>${JSON.stringify(categories.slice(0, 3), null, 2)}</pre>`, 'info');
                
                // 检查数据结构
                if (categories.length > 0) {
                    const sample = categories[0];
                    addResult(`数据结构检查: id=${sample.id}, name=${sample.name}, count=${sample.count}, quotesCount=${sample.quotesCount}`, 'info');
                }
                
            } catch (error) {
                addResult(`❌ getPopularCategories失败: ${error.message}`, 'error');
                console.error('getPopularCategories error:', error);
            }
        }
        
        async function testDirectQuery() {
            addResult('测试直接GraphQL查询...', 'loading');
            
            if (!window.ApiClient) {
                addResult('❌ API客户端未初始化', 'error');
                return;
            }
            
            try {
                const query = `
                    query {
                        categories(first: 5, orderBy: "quotes_count", orderDirection: "desc") {
                            id
                            name
                            quotesCount
                        }
                    }
                `;
                
                addResult('执行GraphQL查询...', 'info');
                const result = await window.ApiClient.query(query);
                
                addResult(`✅ 直接查询成功`, 'success');
                addResult(`<pre>${JSON.stringify(result, null, 2)}</pre>`, 'info');
                
            } catch (error) {
                addResult(`❌ 直接查询失败: ${error.message}`, 'error');
                console.error('Direct query error:', error);
            }
        }
        
        // 页面加载后显示配置信息
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (window.AppConfig) {
                    addResult(`配置已加载: ${window.AppConfig.graphqlEndpoint}`, 'success');
                } else {
                    addResult('配置未加载', 'error');
                }
                
                if (window.ApiClient) {
                    addResult('API客户端已初始化', 'success');
                } else {
                    addResult('API客户端未初始化', 'error');
                }
            }, 100);
        });
    </script>
</body>
</html>
