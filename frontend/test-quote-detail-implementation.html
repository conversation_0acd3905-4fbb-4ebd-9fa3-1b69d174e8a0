<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>名言详情页实现状态测试 - Quotese</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success { background-color: #d4edda; border-color: #28a745; color: #155724; }
        .error { background-color: #f8d7da; border-color: #dc3545; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffc107; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .status-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📋 名言详情页实现状态测试</h1>
        <p>检查Quotese项目中名言详情页功能的当前实现状态和缺失组件</p>
    </div>

    <div class="test-section">
        <h3>🔍 快速检测</h3>
        <button onclick="runFullAnalysis()">运行完整分析</button>
        <button onclick="testQuotePageAccess()">测试名言页面访问</button>
        <button onclick="testAPISupport()">测试API支持</button>
        <button onclick="testQuoteCardClicks()">测试名言卡片点击</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div id="test-results"></div>

    <!-- 加载必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>
    
    <script>
        // 页面加载时初始化
        window.addEventListener('load', function() {
            addTestResult('页面加载', 'info', '名言详情页实现状态测试页面已加载');
            checkBasicInfrastructure();
        });

        // 检查基础设施
        function checkBasicInfrastructure() {
            addTestSection('基础设施检查');
            
            // 检查必要的全局对象
            const checks = [
                { name: 'UrlHandler', object: window.UrlHandler, required: true },
                { name: 'AppConfig', object: window.AppConfig, required: true },
                { name: 'ApiClient', object: window.ApiClient, required: false },
                { name: 'QuoteCardComponent', object: window.QuoteCardComponent, required: false }
            ];

            checks.forEach(check => {
                if (check.object) {
                    addTestResult(`${check.name} 对象`, 'success', '已加载');
                } else {
                    const type = check.required ? 'error' : 'warning';
                    addTestResult(`${check.name} 对象`, type, '未找到');
                }
            });
        }

        // 运行完整分析
        async function runFullAnalysis() {
            addTestSection('完整实现状态分析');
            
            // 1. 检查URL处理器功能
            await testUrlHandler();
            
            // 2. 检查页面模板
            await testPageTemplate();
            
            // 3. 检查API客户端
            await testApiClient();
            
            // 4. 检查路由系统
            await testRouterSupport();
            
            // 5. 检查名言卡片组件
            await testQuoteCardComponent();
            
            addTestResult('完整分析', 'success', '所有检查已完成');
        }

        // 测试URL处理器
        async function testUrlHandler() {
            addTestSection('URL处理器测试');
            
            if (!window.UrlHandler) {
                addTestResult('URL处理器', 'error', 'UrlHandler对象未找到');
                return;
            }

            // 测试getQuoteUrl方法
            try {
                const testQuote = { id: 12345 };
                const url = window.UrlHandler.getQuoteUrl(testQuote);
                addTestResult('getQuoteUrl方法', 'success', `生成URL: ${url}`);
            } catch (error) {
                addTestResult('getQuoteUrl方法', 'error', `错误: ${error.message}`);
            }

            // 测试parseQuoteIdFromPath方法
            try {
                // 模拟URL
                const originalPath = window.location.pathname;
                window.history.pushState({}, '', '/quotes/12345/');
                
                const quoteId = window.UrlHandler.parseQuoteIdFromPath();
                if (quoteId === 12345) {
                    addTestResult('parseQuoteIdFromPath方法', 'success', `解析ID: ${quoteId}`);
                } else {
                    addTestResult('parseQuoteIdFromPath方法', 'warning', `解析结果: ${quoteId}`);
                }
                
                // 恢复原始URL
                window.history.pushState({}, '', originalPath);
            } catch (error) {
                addTestResult('parseQuoteIdFromPath方法', 'error', `错误: ${error.message}`);
            }
        }

        // 测试页面模板
        async function testPageTemplate() {
            addTestSection('页面模板测试');
            
            try {
                const response = await fetch('/quote.html');
                if (response.ok) {
                    addTestResult('quote.html模板', 'success', `状态码: ${response.status}`);
                    
                    const html = await response.text();
                    
                    // 检查关键元素
                    const keyElements = [
                        'quote-content',
                        'author-link', 
                        'categories-container',
                        'related-quotes-container'
                    ];
                    
                    keyElements.forEach(elementId => {
                        if (html.includes(`id="${elementId}"`)) {
                            addTestResult(`模板元素 #${elementId}`, 'success', '已找到');
                        } else {
                            addTestResult(`模板元素 #${elementId}`, 'warning', '未找到');
                        }
                    });
                } else {
                    addTestResult('quote.html模板', 'error', `HTTP ${response.status}`);
                }
            } catch (error) {
                addTestResult('页面模板测试', 'error', `请求失败: ${error.message}`);
            }
        }

        // 测试API客户端
        async function testApiClient() {
            addTestSection('API客户端测试');
            
            if (!window.AppConfig) {
                addTestResult('API配置', 'error', 'AppConfig未找到');
                return;
            }

            // 创建API客户端实例
            const apiClient = new ApiClient(
                window.AppConfig.apiEndpoint,
                window.AppConfig.graphqlEndpoint
            );

            // 检查getQuoteById方法
            if (typeof apiClient.getQuoteById === 'function') {
                addTestResult('getQuoteById方法', 'success', '方法已存在');
                
                // 尝试调用（可能会失败，这是预期的）
                try {
                    await apiClient.getQuoteById(1);
                    addTestResult('getQuoteById调用', 'success', '调用成功');
                } catch (error) {
                    addTestResult('getQuoteById调用', 'warning', `调用失败（预期）: ${error.message}`);
                }
            } else {
                addTestResult('getQuoteById方法', 'error', '方法不存在 - 需要实现');
            }

            // 检查其他相关方法
            const methods = ['getQuotes', 'query'];
            methods.forEach(method => {
                if (typeof apiClient[method] === 'function') {
                    addTestResult(`${method}方法`, 'success', '已存在');
                } else {
                    addTestResult(`${method}方法`, 'error', '不存在');
                }
            });
        }

        // 测试路由系统支持
        async function testRouterSupport() {
            addTestSection('路由系统测试');
            
            try {
                const response = await fetch('/js/page-router.js');
                if (response.ok) {
                    const routerCode = await response.text();
                    
                    // 检查关键配置
                    const checks = [
                        { name: 'quote-detail页面类型', pattern: /['"]quote-detail['"]/ },
                        { name: 'initQuotePage函数', pattern: /initQuotePage/ },
                        { name: 'quote.js脚本映射', pattern: /quote\.js/ }
                    ];
                    
                    checks.forEach(check => {
                        if (check.pattern.test(routerCode)) {
                            addTestResult(check.name, 'success', '已配置');
                        } else {
                            addTestResult(check.name, 'error', '未配置');
                        }
                    });
                } else {
                    addTestResult('page-router.js', 'error', `无法加载: HTTP ${response.status}`);
                }
            } catch (error) {
                addTestResult('路由系统测试', 'error', `测试失败: ${error.message}`);
            }
        }

        // 测试名言卡片组件
        async function testQuoteCardComponent() {
            addTestSection('名言卡片组件测试');
            
            if (!window.QuoteCardComponent) {
                addTestResult('QuoteCardComponent', 'error', '组件未加载');
                return;
            }

            // 检查组件方法
            const methods = ['render', 'renderList'];
            methods.forEach(method => {
                if (typeof window.QuoteCardComponent[method] === 'function') {
                    addTestResult(`${method}方法`, 'success', '已存在');
                } else {
                    addTestResult(`${method}方法`, 'error', '不存在');
                }
            });

            // 检查点击事件状态
            try {
                const response = await fetch('/js/components/quote-card.js');
                if (response.ok) {
                    const componentCode = await response.text();
                    
                    if (componentCode.includes('禁用名言卡片点击')) {
                        addTestResult('名言卡片点击', 'warning', '当前被禁用');
                    } else {
                        addTestResult('名言卡片点击', 'success', '可能已启用');
                    }
                }
            } catch (error) {
                addTestResult('组件代码检查', 'error', `检查失败: ${error.message}`);
            }
        }

        // 测试名言页面访问
        async function testQuotePageAccess() {
            addTestSection('名言页面访问测试');
            
            // 测试不同的名言ID
            const testIds = [1, 12345, 999999];
            
            for (const id of testIds) {
                try {
                    const url = `/quotes/${id}/`;
                    const response = await fetch(url);
                    
                    if (response.ok) {
                        addTestResult(`访问 ${url}`, 'success', `状态码: ${response.status}`);
                    } else if (response.status === 404) {
                        addTestResult(`访问 ${url}`, 'info', '返回404（正常）');
                    } else {
                        addTestResult(`访问 ${url}`, 'warning', `状态码: ${response.status}`);
                    }
                } catch (error) {
                    addTestResult(`访问测试 ID:${id}`, 'error', `请求失败: ${error.message}`);
                }
            }
        }

        // 测试API支持
        async function testAPISupport() {
            addTestSection('API支持测试');
            
            if (!window.AppConfig) {
                addTestResult('API配置', 'error', 'AppConfig未找到');
                return;
            }

            // 测试GraphQL单个名言查询
            try {
                const query = `
                    query {
                        quote(id: "1") {
                            id
                            content
                            author {
                                id
                                name
                            }
                        }
                    }
                `;

                const response = await fetch(window.AppConfig.graphqlEndpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.errors) {
                        addTestResult('GraphQL单个查询', 'warning', `GraphQL错误: ${result.errors[0]?.message}`);
                    } else {
                        addTestResult('GraphQL单个查询', 'success', '查询成功');
                    }
                } else {
                    addTestResult('GraphQL单个查询', 'error', `HTTP ${response.status}`);
                }
            } catch (error) {
                addTestResult('GraphQL API测试', 'error', `请求失败: ${error.message}`);
            }
        }

        // 测试名言卡片点击
        function testQuoteCardClicks() {
            addTestSection('名言卡片点击测试');
            
            // 查找页面中的名言卡片
            const quoteCards = document.querySelectorAll('.quote-card-component, [data-quote-id]');
            
            if (quoteCards.length === 0) {
                addTestResult('名言卡片', 'info', '当前页面没有名言卡片');
                return;
            }

            addTestResult('名言卡片数量', 'info', `找到 ${quoteCards.length} 个名言卡片`);

            // 检查点击事件
            quoteCards.forEach((card, index) => {
                const hasClickEvent = card.onclick || card.addEventListener;
                const hasCursor = getComputedStyle(card).cursor;
                
                addTestResult(`卡片 ${index + 1}`, 'info', 
                    `点击事件: ${hasClickEvent ? '有' : '无'}, 鼠标样式: ${hasCursor}`);
            });
        }

        // 辅助函数
        function addTestResult(title, type, message) {
            const container = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            
            resultDiv.innerHTML = `
                <strong>[${timestamp}] ${title}:</strong><br>
                ${message}
            `;
            
            container.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function addTestSection(title) {
            const container = document.getElementById('test-results');
            const sectionDiv = document.createElement('div');
            sectionDiv.className = 'test-section';
            sectionDiv.innerHTML = `<h3>📋 ${title}</h3>`;
            container.appendChild(sectionDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
    </script>
</body>
</html>
