<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slug Generation Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Slug Generation Debug Test</h1>
    <p>This page helps debug the slug generation issue in url-handler.js</p>

    <div class="test-section">
        <h2>Quote Data Analysis</h2>
        <button onclick="analyzeQuoteData()">Analyze Quote 499001</button>
        <div id="quote-analysis-results"></div>
    </div>

    <div class="test-section">
        <h2>Slug Generation Test</h2>
        <button onclick="testSlugGeneration()">Test Slug Generation</button>
        <div id="slug-test-results"></div>
    </div>

    <div class="test-section">
        <h2>URL Handler Test</h2>
        <button onclick="testUrlHandler()">Test URL Handler</button>
        <div id="url-handler-results"></div>
    </div>

    <div class="test-section">
        <h2>Error Reproduction</h2>
        <button onclick="reproduceError()">Reproduce Original Error</button>
        <div id="error-reproduction-results"></div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>

    <script>
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
        }

        async function analyzeQuoteData() {
            const container = 'quote-analysis-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Analyzing quote data...');
            
            try {
                // Switch to production API
                if (window.QuoteseAPIMode && typeof window.QuoteseAPIMode.useProductionAPI === 'function') {
                    window.QuoteseAPIMode.useProductionAPI();
                    addResult(container, 'success', 'Switched to production API');
                }
                
                // Get quote data
                const quote = await window.ApiClient.getQuoteById('499001');
                
                if (quote && quote.author) {
                    addResult(container, 'success', 'Quote data loaded successfully');
                    
                    const author = quote.author;
                    addResult(container, 'info', `Author ID: ${author.id}`);
                    addResult(container, 'info', `Author Name: "${author.name}"`);
                    addResult(container, 'info', `Author Name Type: ${typeof author.name}`);
                    addResult(container, 'info', `Author Name Length: ${author.name.length}`);
                    
                    // Character analysis
                    const chars = author.name.split('').map((c, i) => 
                        `${c} (${c.charCodeAt(0)})`).join(', ');
                    addResult(container, 'info', `Characters: ${chars}`);
                    
                    // Check for special characters
                    const hasSpecialChars = /[^\w\s-]/.test(author.name);
                    addResult(container, hasSpecialChars ? 'warning' : 'success', 
                        `Has special characters: ${hasSpecialChars}`);
                    
                    // Check for non-ASCII characters
                    const hasNonASCII = /[^\x00-\x7F]/.test(author.name);
                    addResult(container, hasNonASCII ? 'warning' : 'success', 
                        `Has non-ASCII characters: ${hasNonASCII}`);
                    
                    return author;
                } else {
                    addResult(container, 'error', 'Failed to load quote or author data');
                    return null;
                }
                
            } catch (error) {
                addResult(container, 'error', `Analysis failed: ${error.message}`);
                return null;
            }
        }

        function testSlugGeneration() {
            const container = 'slug-test-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing slug generation...');
            
            // Test cases
            const testCases = [
                'Adam Sandler',
                'Albert Einstein',
                'Marie Curie',
                'Leonardo da Vinci',
                'Jean-Paul Sartre',
                'José Martí',
                'François Mitterrand',
                'Björk Guðmundsdóttir',
                '',
                null,
                undefined,
                '   ',
                'A',
                'A B',
                'A-B',
                'A_B',
                'A.B',
                'A@B',
                'A#B',
                'A$B',
                'A%B'
            ];
            
            testCases.forEach(testCase => {
                try {
                    const slug = UrlHandler.slugify(testCase);
                    const isValid = slug && slug.length > 0;
                    addResult(container, isValid ? 'success' : 'warning', 
                        `"${testCase}" → "${slug}" (${isValid ? 'VALID' : 'EMPTY'})`);
                } catch (error) {
                    addResult(container, 'error', 
                        `"${testCase}" → ERROR: ${error.message}`);
                }
            });
        }

        async function testUrlHandler() {
            const container = 'url-handler-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing URL handler...');
            
            // Get real author data
            const author = await analyzeQuoteData();
            
            if (author) {
                try {
                    // Test getAuthorUrl
                    addResult(container, 'info', `Testing getAuthorUrl with: ${JSON.stringify(author)}`);
                    const authorUrl = UrlHandler.getAuthorUrl(author);
                    addResult(container, 'success', `Author URL generated: ${authorUrl}`);
                    
                } catch (error) {
                    addResult(container, 'error', `getAuthorUrl failed: ${error.message}`);
                    addResult(container, 'error', `Stack: ${error.stack}`);
                }
            }
            
            // Test with various author objects
            const testAuthors = [
                { name: 'Adam Sandler', id: 1145 },
                { name: 'Albert Einstein', id: 1 },
                { name: '', id: 2 },
                { name: null, id: 3 },
                { id: 4 },
                null,
                undefined
            ];
            
            testAuthors.forEach((testAuthor, index) => {
                try {
                    const url = UrlHandler.getAuthorUrl(testAuthor);
                    addResult(container, 'success', 
                        `Test ${index + 1}: ${JSON.stringify(testAuthor)} → ${url}`);
                } catch (error) {
                    addResult(container, 'error', 
                        `Test ${index + 1}: ${JSON.stringify(testAuthor)} → ERROR: ${error.message}`);
                }
            });
        }

        async function reproduceError() {
            const container = 'error-reproduction-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Attempting to reproduce the original error...');
            
            try {
                // Get the exact quote data that's causing the issue
                const quote = await window.ApiClient.getQuoteById('499001');
                
                if (quote && quote.author) {
                    addResult(container, 'info', 'Quote data loaded, testing author URL generation...');
                    
                    // This should be the exact call that's failing
                    const authorUrl = UrlHandler.getAuthorUrl(quote.author);
                    addResult(container, 'success', `No error! Author URL: ${authorUrl}`);
                    
                    // Test the renderAuthors function context
                    addResult(container, 'info', 'Testing in renderAuthors context...');
                    
                    // Simulate what happens in renderAuthors
                    const authors = [quote.author];
                    authors.forEach(author => {
                        try {
                            const url = UrlHandler.getAuthorUrl(author);
                            addResult(container, 'success', `Author ${author.name} URL: ${url}`);
                        } catch (error) {
                            addResult(container, 'error', `Author ${author.name} failed: ${error.message}`);
                        }
                    });
                    
                } else {
                    addResult(container, 'error', 'Failed to load quote data');
                }
                
            } catch (error) {
                addResult(container, 'error', `Error reproduction failed: ${error.message}`);
                addResult(container, 'error', `Stack: ${error.stack}`);
            }
        }

        // Auto-run analysis on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                analyzeQuoteData();
            }, 1000);
        });

        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error caught:', event.error);
            const container = document.createElement('div');
            container.className = 'test-result error';
            container.innerHTML = `<strong>GLOBAL ERROR:</strong> ${event.error.message}<br>
                <strong>File:</strong> ${event.filename}<br>
                <strong>Line:</strong> ${event.lineno}<br>
                <strong>Stack:</strong> ${event.error.stack}`;
            document.body.appendChild(container);
        });
    </script>
</body>
</html>
