<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Detail Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Quote Detail Debug Test</h1>
    <p>This page helps debug the quote detail page data loading issue.</p>

    <div class="test-section">
        <h2>API Client Method Check</h2>
        <button onclick="checkApiMethods()">Check API Methods</button>
        <div id="api-methods-results"></div>
    </div>

    <div class="test-section">
        <h2>Direct API Test</h2>
        <button onclick="testDirectAPI()">Test getQuoteById(499001)</button>
        <div id="direct-api-results"></div>
    </div>

    <div class="test-section">
        <h2>Quote Page Simulation</h2>
        <button onclick="simulateQuotePage()">Simulate Quote Page Loading</button>
        <div id="quote-page-results"></div>
    </div>

    <div class="test-section">
        <h2>Error Reproduction</h2>
        <button onclick="reproduceError()">Try to Reproduce Error</button>
        <div id="error-reproduction-results"></div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>

    <script>
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
        }

        function checkApiMethods() {
            const container = 'api-methods-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Checking API client methods...');
            
            // Check if ApiClient exists
            if (typeof window.ApiClient === 'undefined') {
                addResult(container, 'error', 'window.ApiClient is undefined');
                return;
            }
            
            addResult(container, 'success', 'window.ApiClient exists');
            
            // Check available methods
            const methods = Object.getOwnPropertyNames(Object.getPrototypeOf(window.ApiClient));
            addResult(container, 'info', `Available methods: ${methods.join(', ')}`);
            
            // Check specific methods
            const methodsToCheck = ['getQuote', 'getQuoteById', 'getQuotes', 'getRelatedQuotesByAuthor'];
            methodsToCheck.forEach(method => {
                const exists = typeof window.ApiClient[method] === 'function';
                addResult(container, exists ? 'success' : 'warning', 
                    `${method}: ${exists ? 'EXISTS' : 'NOT FOUND'}`);
            });
            
            // Show ApiClient configuration
            const config = {
                apiEndpoint: window.ApiClient.apiEndpoint,
                graphqlEndpoint: window.ApiClient.graphqlEndpoint,
                useMockData: window.ApiClient.useMockData
            };
            
            const configInfo = `<pre>ApiClient Configuration:
${JSON.stringify(config, null, 2)}</pre>`;
            document.getElementById(container).innerHTML += configInfo;
        }

        async function testDirectAPI() {
            const container = 'direct-api-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing direct API call...');
            
            try {
                // Switch to production API
                if (window.QuoteseAPIMode && typeof window.QuoteseAPIMode.useProductionAPI === 'function') {
                    window.QuoteseAPIMode.useProductionAPI();
                    addResult(container, 'success', 'Switched to production API');
                }
                
                // Test getQuoteById
                if (typeof window.ApiClient.getQuoteById === 'function') {
                    addResult(container, 'info', 'Calling getQuoteById(499001)...');
                    const quote = await window.ApiClient.getQuoteById('499001');
                    
                    if (quote) {
                        addResult(container, 'success', 
                            `Quote loaded successfully: "${quote.content.substring(0, 50)}..." by ${quote.author.name}`);
                        
                        const quoteInfo = `<pre>Quote Data:
${JSON.stringify(quote, null, 2)}</pre>`;
                        document.getElementById(container).innerHTML += quoteInfo;
                    } else {
                        addResult(container, 'error', 'getQuoteById returned null');
                    }
                } else {
                    addResult(container, 'error', 'getQuoteById method not found');
                }
                
            } catch (error) {
                addResult(container, 'error', `API call failed: ${error.message}`);
                addResult(container, 'error', `Stack trace: ${error.stack}`);
            }
        }

        async function simulateQuotePage() {
            const container = 'quote-page-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Simulating quote page loading...');
            
            try {
                // Simulate the exact code from quote.js
                const quoteId = '499001';
                addResult(container, 'info', `Getting quote by ID: ${quoteId}`);
                
                // This is the exact line that's causing the error
                const quote = await window.ApiClient.getQuoteById(quoteId);
                
                if (quote) {
                    addResult(container, 'success', 'Quote page simulation successful');
                    addResult(container, 'info', `Quote: "${quote.content.substring(0, 50)}..."`);
                } else {
                    addResult(container, 'error', 'Quote page simulation failed: no quote returned');
                }
                
            } catch (error) {
                addResult(container, 'error', `Quote page simulation failed: ${error.message}`);
                addResult(container, 'error', `Error type: ${error.constructor.name}`);
                addResult(container, 'error', `Stack: ${error.stack}`);
            }
        }

        async function reproduceError() {
            const container = 'error-reproduction-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Attempting to reproduce the exact error...');
            
            try {
                // Try calling the old method that doesn't exist
                if (typeof window.ApiClient.getQuote === 'function') {
                    addResult(container, 'warning', 'getQuote method exists (unexpected)');
                    const result = await window.ApiClient.getQuote('499001');
                    addResult(container, 'info', 'getQuote call succeeded');
                } else {
                    addResult(container, 'error', 'getQuote method does not exist (this would cause the error)');
                    
                    // Try to call it anyway to reproduce the error
                    try {
                        await window.ApiClient.getQuote('499001');
                    } catch (error) {
                        addResult(container, 'success', `Successfully reproduced error: ${error.message}`);
                    }
                }
                
            } catch (error) {
                addResult(container, 'success', `Error reproduced: ${error.message}`);
            }
        }

        // Auto-run basic checks on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                checkApiMethods();
            }, 1000);
        });

        // Global error handler to catch any errors
        window.addEventListener('error', (event) => {
            console.error('Global error caught:', event.error);
            const container = document.createElement('div');
            container.className = 'test-result error';
            container.innerHTML = `<strong>GLOBAL ERROR:</strong> ${event.error.message}<br>
                <strong>File:</strong> ${event.filename}<br>
                <strong>Line:</strong> ${event.lineno}<br>
                <strong>Stack:</strong> ${event.error.stack}`;
            document.body.appendChild(container);
        });
    </script>
</body>
</html>
