<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Life Category Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Life Category 调试工具</h1>
    <p>专门调试 "Life" 类别 (ID: 71523) 的查询问题</p>
    
    <!-- 加载必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/url-handler.js"></script>

    <div class="test-section">
        <h2>🔧 环境信息</h2>
        <div id="environment-info"></div>
    </div>

    <div class="test-section">
        <h2>🔍 直接API查询测试</h2>
        <button onclick="testDirectApiQueries()">测试直接API查询</button>
        <div id="direct-api-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 API客户端查询测试</h2>
        <button onclick="testApiClientQueries()">测试API客户端查询</button>
        <div id="api-client-results"></div>
    </div>

    <div class="test-section">
        <h2>🧪 完整页面逻辑测试</h2>
        <button onclick="testPageLogic()">测试页面逻辑</button>
        <div id="page-logic-results"></div>
    </div>

    <script>
        const API_ENDPOINT = 'https://api.quotese.com/api/';

        function addResult(containerId, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById(containerId).appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 显示环境信息
        function showEnvironmentInfo() {
            const container = 'environment-info';
            addResult(container, `📋 API端点: ${window.AppConfig?.apiEndpoint || 'Not configured'}`, 'info');
            addResult(container, `📋 使用模拟数据: ${window.ApiClient?.useMockData || 'Unknown'}`, 'info');
            addResult(container, `📋 当前URL: ${window.location.href}`, 'info');
            addResult(container, `📋 测试目标: Life 类别 (ID: 71523)`, 'info');
        }

        // 测试直接API查询
        async function testDirectApiQueries() {
            const container = 'direct-api-results';
            clearResults(container);
            
            addResult(container, '🔍 测试直接GraphQL查询...', 'info');

            // 测试1: 通过ID直接查询
            try {
                addResult(container, '1️⃣ 通过ID查询类别 (ID: 71523)...', 'info');
                const query1 = `
                    query {
                        category(id: 71523) {
                            id
                            name
                            createdAt
                            updatedAt
                            quotesCount
                        }
                    }
                `;

                const response1 = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query: query1 })
                });

                if (response1.ok) {
                    const data1 = await response1.json();
                    if (data1.errors) {
                        addResult(container, `❌ ID查询有错误: ${JSON.stringify(data1.errors)}`, 'error');
                    } else if (data1.data?.category) {
                        addResult(container, `✅ ID查询成功: ${JSON.stringify(data1.data.category)}`, 'success');
                    } else {
                        addResult(container, `❌ ID查询无结果`, 'error');
                    }
                } else {
                    addResult(container, `❌ ID查询请求失败: ${response1.status}`, 'error');
                }
            } catch (error) {
                addResult(container, `❌ ID查询异常: ${error.message}`, 'error');
            }

            // 测试2: 通过精确名称查询
            try {
                addResult(container, '2️⃣ 通过精确名称查询 "Life"...', 'info');
                const query2 = `
                    query {
                        categoryByExactName(name: "Life") {
                            id
                            name
                            createdAt
                            updatedAt
                            quotesCount
                        }
                    }
                `;

                const response2 = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query: query2 })
                });

                if (response2.ok) {
                    const data2 = await response2.json();
                    if (data2.errors) {
                        addResult(container, `❌ 精确名称查询有错误: ${JSON.stringify(data2.errors)}`, 'error');
                    } else if (data2.data?.categoryByExactName) {
                        addResult(container, `✅ 精确名称查询成功: ${JSON.stringify(data2.data.categoryByExactName)}`, 'success');
                    } else {
                        addResult(container, `❌ 精确名称查询无结果`, 'warning');
                    }
                } else {
                    addResult(container, `❌ 精确名称查询请求失败: ${response2.status}`, 'error');
                }
            } catch (error) {
                addResult(container, `❌ 精确名称查询异常: ${error.message}`, 'error');
            }

            // 测试3: 通过搜索查询
            try {
                addResult(container, '3️⃣ 通过搜索查询 "Life"...', 'info');
                const query3 = `
                    query {
                        categories(search: "Life", first: 10) {
                            id
                            name
                            createdAt
                            updatedAt
                            quotesCount
                        }
                    }
                `;

                const response3 = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query: query3 })
                });

                if (response3.ok) {
                    const data3 = await response3.json();
                    if (data3.errors) {
                        addResult(container, `❌ 搜索查询有错误: ${JSON.stringify(data3.errors)}`, 'error');
                    } else if (data3.data?.categories) {
                        addResult(container, `✅ 搜索查询成功，找到 ${data3.data.categories.length} 个结果:`, 'success');
                        addResult(container, `<pre>${JSON.stringify(data3.data.categories, null, 2)}</pre>`, 'info');
                    } else {
                        addResult(container, `❌ 搜索查询无结果`, 'warning');
                    }
                } else {
                    addResult(container, `❌ 搜索查询请求失败: ${response3.status}`, 'error');
                }
            } catch (error) {
                addResult(container, `❌ 搜索查询异常: ${error.message}`, 'error');
            }
        }

        // 测试API客户端查询
        async function testApiClientQueries() {
            const container = 'api-client-results';
            clearResults(container);
            
            addResult(container, '🔍 测试API客户端查询方法...', 'info');

            // 确保使用生产API
            window.ApiClient.useMockData = false;

            const testQueries = [
                { name: 'life', description: '小写 "life"' },
                { name: 'Life', description: '首字母大写 "Life"' },
                { name: 'LIFE', description: '全大写 "LIFE"' },
                { name: 'liFe', description: '混合大小写 "liFe"' }
            ];

            for (const test of testQueries) {
                try {
                    addResult(container, `🔍 测试查询: ${test.description}...`, 'info');
                    const result = await window.ApiClient.getCategoryByName(test.name);
                    
                    if (result) {
                        addResult(container, `✅ ${test.description} 查询成功: ID=${result.id}, Name="${result.name}", Count=${result.count}`, 'success');
                    } else {
                        addResult(container, `❌ ${test.description} 查询失败`, 'warning');
                    }
                } catch (error) {
                    addResult(container, `❌ ${test.description} 查询异常: ${error.message}`, 'error');
                }
            }
        }

        // 测试页面逻辑
        async function testPageLogic() {
            const container = 'page-logic-results';
            clearResults(container);
            
            addResult(container, '🔍 测试完整页面逻辑...', 'info');

            // 1. 测试slug解析
            const categorySlug = 'life';
            const categoryName = window.UrlHandler.deslugify(categorySlug);
            addResult(container, `1️⃣ Slug解析: "${categorySlug}" → "${categoryName}"`, 'success');

            // 2. 测试多重查询逻辑（模拟category.js的逻辑）
            try {
                addResult(container, '2️⃣ 执行多重查询逻辑...', 'info');
                
                // 首先尝试使用原始slug（小写）
                let category = await window.ApiClient.getCategoryByName(categorySlug);
                addResult(container, `   尝试slug "${categorySlug}": ${category ? '成功' : '失败'}`, category ? 'success' : 'warning');
                
                // 如果slug查询失败，尝试使用转换后的名称（首字母大写）
                if (!category) {
                    category = await window.ApiClient.getCategoryByName(categoryName);
                    addResult(container, `   尝试名称 "${categoryName}": ${category ? '成功' : '失败'}`, category ? 'success' : 'warning');
                }
                
                // 如果还是失败，尝试使用小写名称
                if (!category) {
                    const lowerCaseName = categoryName.toLowerCase();
                    category = await window.ApiClient.getCategoryByName(lowerCaseName);
                    addResult(container, `   尝试小写 "${lowerCaseName}": ${category ? '成功' : '失败'}`, category ? 'success' : 'warning');
                }
                
                if (category) {
                    addResult(container, `✅ 最终找到类别: ID=${category.id}, Name="${category.name}"`, 'success');
                    
                    // 3. 测试名言查询
                    addResult(container, '3️⃣ 测试名言查询...', 'info');
                    const quotesData = await window.ApiClient.getQuotes(1, 5, { categoryId: category.id });
                    if (quotesData && quotesData.quotes && quotesData.quotes.length > 0) {
                        addResult(container, `✅ 名言查询成功: 获取到 ${quotesData.quotes.length} 条名言，总计 ${quotesData.totalCount} 条`, 'success');
                    } else {
                        addResult(container, `❌ 名言查询失败或无数据`, 'error');
                    }
                } else {
                    addResult(container, `❌ 所有查询方式都失败，无法找到类别`, 'error');
                }
                
            } catch (error) {
                addResult(container, `❌ 页面逻辑测试异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示环境信息
        window.addEventListener('load', () => {
            showEnvironmentInfo();
        });
    </script>
</body>
</html>
