<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调用调试 - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .debug-result {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #1f2937;
            color: #f3f4f6;
            padding: 12px;
            border-radius: 6px;
            margin: 8px 0;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-success {
            background: #065f46;
            color: #d1fae5;
        }
        .debug-error {
            background: #7f1d1d;
            color: #fecaca;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-bug mr-3 text-red-500"></i>
                API调用调试分析
            </h1>
            
            <!-- 配置信息 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">1. 配置信息</h2>
                <div id="config-info" class="debug-result">检测中...</div>
            </div>
            
            <!-- Authors API测试 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">2. Authors API测试</h2>
                <button onclick="testAuthorsAPI()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 mb-4">
                    测试Authors API
                </button>
                <div id="authors-result" class="debug-result">点击按钮开始测试...</div>
            </div>
            
            <!-- Categories API测试 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">3. Categories API测试</h2>
                <button onclick="testCategoriesAPI()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 mb-4">
                    测试Categories API
                </button>
                <div id="categories-result" class="debug-result">点击按钮开始测试...</div>
            </div>
            
            <!-- Sources API测试 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">4. Sources API测试</h2>
                <button onclick="testSourcesAPI()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mb-4">
                    测试Sources API
                </button>
                <div id="sources-result" class="debug-result">点击按钮开始测试...</div>
            </div>
            
            <!-- 具体Source查询测试 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">5. 具体Source查询测试</h2>
                <div class="mb-4">
                    <input type="text" id="source-name" value="healology" placeholder="输入source名称" 
                           class="border border-gray-300 rounded px-3 py-2 mr-2">
                    <button onclick="testSourceByName()" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                        测试getSourceByName
                    </button>
                </div>
                <div id="source-by-name-result" class="debug-result">点击按钮开始测试...</div>
            </div>
            
            <!-- 网络请求监控 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">6. 网络请求监控</h2>
                <p class="text-sm text-gray-600 mb-4">请打开浏览器开发者工具的Network标签页来查看实际的API请求</p>
                <div id="network-info" class="debug-result">
网络请求监控提示：
1. 打开浏览器开发者工具 (F12)
2. 切换到 Network 标签页
3. 点击上面的测试按钮
4. 观察实际发送的HTTP请求
5. 检查请求URL、请求体、响应状态码和响应内容
                </div>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>

    <script>
        // 显示配置信息
        document.addEventListener('DOMContentLoaded', function() {
            showConfigInfo();
        });
        
        function showConfigInfo() {
            const configInfo = document.getElementById('config-info');
            try {
                let info = '当前配置信息:\n';
                info += '=' * 50 + '\n';
                
                if (window.AppConfig) {
                    info += `AppConfig.apiEndpoint: ${window.AppConfig.apiEndpoint}\n`;
                    info += `AppConfig.useMockData: ${window.AppConfig.useMockData}\n`;
                    info += `AppConfig.debug: ${window.AppConfig.debug}\n`;
                } else {
                    info += 'AppConfig: 未找到\n';
                }
                
                if (window.ApiClient) {
                    info += `\nApiClient.apiEndpoint: ${window.ApiClient.apiEndpoint}\n`;
                    info += `ApiClient.useMockData: ${window.ApiClient.useMockData}\n`;
                } else {
                    info += '\nApiClient: 未找到\n';
                }
                
                info += '\n当前页面URL: ' + window.location.href + '\n';
                info += '当前时间: ' + new Date().toISOString() + '\n';
                
                configInfo.textContent = info;
                configInfo.className = 'debug-result debug-success';
            } catch (error) {
                configInfo.textContent = `配置信息获取错误: ${error.message}`;
                configInfo.className = 'debug-result debug-error';
            }
        }
        
        async function testAuthorsAPI() {
            const resultDiv = document.getElementById('authors-result');
            resultDiv.textContent = '测试Authors API中...';
            resultDiv.className = 'debug-result';
            
            try {
                console.log('开始测试Authors API...');
                
                // 强制使用生产API
                window.ApiClient.useMockData = false;
                
                // 测试getPopularAuthors
                const popularAuthors = await window.ApiClient.getPopularAuthors(5);
                
                let result = 'Authors API测试结果:\n';
                result += '=' * 50 + '\n';
                result += `getPopularAuthors(5) 结果:\n`;
                result += `返回数量: ${popularAuthors ? popularAuthors.length : 0}\n`;
                
                if (popularAuthors && popularAuthors.length > 0) {
                    result += '\n前3个作者:\n';
                    popularAuthors.slice(0, 3).forEach((author, index) => {
                        result += `${index + 1}. ${author.name} (ID: ${author.id}, 名言数: ${author.count})\n`;
                    });
                    
                    // 测试getAuthorByName
                    const firstAuthor = popularAuthors[0];
                    result += `\n测试getAuthorByName("${firstAuthor.name}"):\n`;
                    const authorByName = await window.ApiClient.getAuthorByName(firstAuthor.name);
                    if (authorByName) {
                        result += `✅ 成功找到: ${authorByName.name} (ID: ${authorByName.id})\n`;
                    } else {
                        result += `❌ 未找到作者\n`;
                    }
                    
                    resultDiv.className = 'debug-result debug-success';
                } else {
                    result += '❌ 未获取到作者数据\n';
                    resultDiv.className = 'debug-result debug-error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.textContent = `Authors API测试错误: ${error.message}\n${error.stack}`;
                resultDiv.className = 'debug-result debug-error';
                console.error('Authors API测试错误:', error);
            }
        }
        
        async function testCategoriesAPI() {
            const resultDiv = document.getElementById('categories-result');
            resultDiv.textContent = '测试Categories API中...';
            resultDiv.className = 'debug-result';
            
            try {
                console.log('开始测试Categories API...');
                
                // 强制使用生产API
                window.ApiClient.useMockData = false;
                
                // 测试getPopularCategories
                const popularCategories = await window.ApiClient.getPopularCategories(5);
                
                let result = 'Categories API测试结果:\n';
                result += '=' * 50 + '\n';
                result += `getPopularCategories(5) 结果:\n`;
                result += `返回数量: ${popularCategories ? popularCategories.length : 0}\n`;
                
                if (popularCategories && popularCategories.length > 0) {
                    result += '\n前3个类别:\n';
                    popularCategories.slice(0, 3).forEach((category, index) => {
                        result += `${index + 1}. ${category.name} (ID: ${category.id}, 名言数: ${category.count})\n`;
                    });
                    
                    // 测试getCategoryByName
                    const firstCategory = popularCategories[0];
                    result += `\n测试getCategoryByName("${firstCategory.name}"):\n`;
                    const categoryByName = await window.ApiClient.getCategoryByName(firstCategory.name);
                    if (categoryByName) {
                        result += `✅ 成功找到: ${categoryByName.name} (ID: ${categoryByName.id})\n`;
                    } else {
                        result += `❌ 未找到类别\n`;
                    }
                    
                    resultDiv.className = 'debug-result debug-success';
                } else {
                    result += '❌ 未获取到类别数据\n';
                    resultDiv.className = 'debug-result debug-error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.textContent = `Categories API测试错误: ${error.message}\n${error.stack}`;
                resultDiv.className = 'debug-result debug-error';
                console.error('Categories API测试错误:', error);
            }
        }
        
        async function testSourcesAPI() {
            const resultDiv = document.getElementById('sources-result');
            resultDiv.textContent = '测试Sources API中...';
            resultDiv.className = 'debug-result';
            
            try {
                console.log('开始测试Sources API...');
                
                // 强制使用生产API
                window.ApiClient.useMockData = false;
                
                // 测试getPopularSources
                const popularSources = await window.ApiClient.getPopularSources(5);
                
                let result = 'Sources API测试结果:\n';
                result += '=' * 50 + '\n';
                result += `getPopularSources(5) 结果:\n`;
                result += `返回数量: ${popularSources ? popularSources.length : 0}\n`;
                
                if (popularSources && popularSources.length > 0) {
                    result += '\n前3个来源:\n';
                    popularSources.slice(0, 3).forEach((source, index) => {
                        result += `${index + 1}. ${source.name} (ID: ${source.id}, 名言数: ${source.count})\n`;
                    });
                    
                    resultDiv.className = 'debug-result debug-success';
                } else {
                    result += '❌ 未获取到来源数据\n';
                    resultDiv.className = 'debug-result debug-error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.textContent = `Sources API测试错误: ${error.message}\n${error.stack}`;
                resultDiv.className = 'debug-result debug-error';
                console.error('Sources API测试错误:', error);
            }
        }
        
        async function testSourceByName() {
            const resultDiv = document.getElementById('source-by-name-result');
            const sourceName = document.getElementById('source-name').value.trim();
            
            if (!sourceName) {
                resultDiv.textContent = '请输入source名称';
                resultDiv.className = 'debug-result debug-error';
                return;
            }
            
            resultDiv.textContent = `测试getSourceByName("${sourceName}")中...`;
            resultDiv.className = 'debug-result';
            
            try {
                console.log(`开始测试getSourceByName("${sourceName}")...`);
                
                // 强制使用生产API
                window.ApiClient.useMockData = false;
                
                const source = await window.ApiClient.getSourceByName(sourceName);
                
                let result = `getSourceByName("${sourceName}") 测试结果:\n`;
                result += '=' * 50 + '\n';
                
                if (source) {
                    result += `✅ 成功找到来源:\n`;
                    result += `ID: ${source.id}\n`;
                    result += `名称: ${source.name}\n`;
                    result += `名言数量: ${source.count || source.quotesCount || 0}\n`;
                    result += `创建时间: ${source.createdAt || 'N/A'}\n`;
                    result += `更新时间: ${source.updatedAt || 'N/A'}\n`;
                    
                    // 测试用这个sourceId获取名言
                    result += `\n测试getQuotes(1, 3, {sourceId: ${source.id}}):\n`;
                    const quotesData = await window.ApiClient.getQuotes(1, 3, { sourceId: source.id });
                    if (quotesData && quotesData.quotes && quotesData.quotes.length > 0) {
                        result += `✅ 成功获取${quotesData.quotes.length}条名言 (总计${quotesData.totalCount}条)\n`;
                        result += `第一条名言: "${quotesData.quotes[0].content.substring(0, 100)}..."\n`;
                    } else {
                        result += `❌ 未获取到该来源的名言\n`;
                    }
                    
                    resultDiv.className = 'debug-result debug-success';
                } else {
                    result += `❌ 未找到名为"${sourceName}"的来源\n`;
                    resultDiv.className = 'debug-result debug-error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.textContent = `getSourceByName测试错误: ${error.message}\n${error.stack}`;
                resultDiv.className = 'debug-result debug-error';
                console.error('getSourceByName测试错误:', error);
            }
        }
    </script>
</body>
</html>
