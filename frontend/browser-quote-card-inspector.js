// 在浏览器控制台中运行此脚本来检查名言卡片状态

console.log('🔍 开始检查页面上的名言卡片...');

// 检查名言卡片的函数
function inspectQuoteCards() {
    const results = {
        totalCards: 0,
        cardsWithValidIds: 0,
        cardsWithMissingIds: 0,
        cardsWithInvalidIds: 0,
        cardsWithClickEvents: 0,
        cardsWithoutClickEvents: 0,
        sampleIds: [],
        issues: []
    };
    
    // 查找所有名言卡片
    const quoteCards = document.querySelectorAll('.quote-card-component');
    results.totalCards = quoteCards.length;
    
    console.log(`📊 找到 ${quoteCards.length} 个名言卡片`);
    
    if (quoteCards.length === 0) {
        console.log('❌ 页面上没有找到名言卡片');
        console.log('💡 可能的原因：');
        console.log('   1. 页面还在加载中');
        console.log('   2. API调用失败');
        console.log('   3. 名言卡片使用了不同的CSS类名');
        return results;
    }
    
    // 检查每个名言卡片
    quoteCards.forEach((card, index) => {
        console.log(`\n🔍 检查名言卡片 ${index + 1}:`);
        
        // 检查entity ID
        const quoteId = card.getAttribute('data-quote-id');
        if (!quoteId) {
            results.cardsWithMissingIds++;
            console.log(`   ❌ 缺少 data-quote-id 属性`);
            results.issues.push(`Card ${index + 1}: Missing data-quote-id`);
        } else {
            const id = parseInt(quoteId);
            if (isNaN(id) || id <= 0) {
                results.cardsWithInvalidIds++;
                console.log(`   ❌ 无效的 quote ID: "${quoteId}"`);
                results.issues.push(`Card ${index + 1}: Invalid quote ID "${quoteId}"`);
            } else {
                results.cardsWithValidIds++;
                console.log(`   ✅ 有效的 quote ID: ${quoteId}`);
                if (results.sampleIds.length < 5) {
                    results.sampleIds.push(quoteId);
                }
            }
        }
        
        // 检查点击事件
        const hasClickStyling = card.classList.contains('cursor-pointer') || card.style.cursor === 'pointer';
        if (hasClickStyling) {
            results.cardsWithClickEvents++;
            console.log(`   ✅ 有点击样式`);
        } else {
            results.cardsWithoutClickEvents++;
            console.log(`   ❌ 缺少点击样式`);
            results.issues.push(`Card ${index + 1}: Missing click styling`);
        }
        
        // 检查卡片内容
        const content = card.querySelector('p');
        if (content) {
            console.log(`   📝 内容: "${content.textContent.substring(0, 50)}..."`);
        } else {
            console.log(`   ❌ 缺少内容元素`);
            results.issues.push(`Card ${index + 1}: Missing content element`);
        }
        
        // 检查作者信息
        const authorLink = card.querySelector('a[href*="/authors/"]');
        if (authorLink) {
            console.log(`   👤 作者: ${authorLink.textContent}`);
        } else {
            console.log(`   ⚠️  未找到作者链接`);
        }
    });
    
    return results;
}

// 测试名言卡片点击功能
function testQuoteCardClicks() {
    console.log('\n🧪 测试名言卡片点击功能...');
    
    const quoteCards = document.querySelectorAll('.quote-card-component');
    if (quoteCards.length === 0) {
        console.log('❌ 没有找到名言卡片进行测试');
        return;
    }
    
    // 测试第一个名言卡片
    const firstCard = quoteCards[0];
    const quoteId = firstCard.getAttribute('data-quote-id');
    
    console.log(`🎯 测试第一个名言卡片 (ID: ${quoteId})`);
    
    // 检查是否有导航优化函数
    if (typeof window.navigateToEntityWithId === 'function') {
        console.log('✅ 找到优化导航函数');
    } else {
        console.log('⚠️  未找到优化导航函数，将使用直接导航');
    }
    
    // 模拟点击事件
    console.log('🖱️  模拟点击事件...');
    
    // 保存原始位置
    const originalHref = window.location.href;
    
    // 创建点击事件
    const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
    });
    
    // 监听导航
    let navigationTriggered = false;
    const originalNavigate = window.navigateToEntityWithId;
    
    if (originalNavigate) {
        window.navigateToEntityWithId = function(type, entity, url) {
            navigationTriggered = true;
            console.log(`🚀 导航触发: ${type}, URL: ${url}`);
            // 恢复原始函数
            window.navigateToEntityWithId = originalNavigate;
        };
    }
    
    // 触发点击
    firstCard.dispatchEvent(clickEvent);
    
    // 检查结果
    setTimeout(() => {
        if (navigationTriggered) {
            console.log('✅ 点击事件成功触发导航');
        } else if (window.location.href !== originalHref) {
            console.log('✅ 页面导航成功（直接导航）');
            console.log(`   从: ${originalHref}`);
            console.log(`   到: ${window.location.href}`);
        } else {
            console.log('❌ 点击事件未触发导航');
            console.log('💡 可能的原因：');
            console.log('   1. 点击事件监听器未正确添加');
            console.log('   2. 事件被其他元素阻止');
            console.log('   3. URL生成失败');
        }
    }, 100);
}

// 检查API连接
async function checkAPIConnection() {
    console.log('\n🌐 检查API连接...');
    
    try {
        // 切换到生产API
        if (window.QuoteseAPIMode && typeof window.QuoteseAPIMode.useProductionAPI === 'function') {
            window.QuoteseAPIMode.useProductionAPI();
            console.log('✅ 已切换到生产API');
        }
        
        // 测试API调用
        const quotesData = await window.ApiClient.getQuotes(1, 1);
        if (quotesData && quotesData.quotes && quotesData.quotes.length > 0) {
            const quote = quotesData.quotes[0];
            console.log('✅ API连接正常');
            console.log(`   示例名言: ID ${quote.id}, "${quote.content.substring(0, 50)}..."`);
            
            // 测试详情页API调用
            const detailQuote = await window.ApiClient.getQuoteById(quote.id);
            if (detailQuote) {
                console.log('✅ 详情页API调用正常');
            } else {
                console.log('❌ 详情页API调用失败');
            }
        } else {
            console.log('❌ API调用失败或返回空数据');
        }
    } catch (error) {
        console.log('❌ API连接错误:', error.message);
    }
}

// 生成诊断报告
function generateDiagnosticReport() {
    console.log('\n📋 生成诊断报告...');
    
    const results = inspectQuoteCards();
    
    console.log('\n📊 诊断报告:');
    console.log('='.repeat(50));
    console.log(`页面类型: ${document.title}`);
    console.log(`URL: ${window.location.href}`);
    console.log(`时间: ${new Date().toLocaleString()}`);
    console.log('');
    console.log('名言卡片统计:');
    console.log(`  总数: ${results.totalCards}`);
    console.log(`  有效ID: ${results.cardsWithValidIds}`);
    console.log(`  缺少ID: ${results.cardsWithMissingIds}`);
    console.log(`  无效ID: ${results.cardsWithInvalidIds}`);
    console.log(`  有点击事件: ${results.cardsWithClickEvents}`);
    console.log(`  无点击事件: ${results.cardsWithoutClickEvents}`);
    
    if (results.sampleIds.length > 0) {
        console.log(`  示例ID: ${results.sampleIds.join(', ')}`);
    }
    
    if (results.issues.length > 0) {
        console.log('\n⚠️  发现的问题:');
        results.issues.forEach(issue => {
            console.log(`  - ${issue}`);
        });
    }
    
    // 计算成功率
    const successRate = results.totalCards > 0 ? 
        ((results.cardsWithValidIds / results.totalCards) * 100).toFixed(1) : 0;
    
    console.log(`\n📈 成功率: ${successRate}%`);
    
    if (successRate < 100) {
        console.log('\n💡 建议的修复措施:');
        if (results.cardsWithMissingIds > 0) {
            console.log('  - 确保QuoteCardComponent.render()正确设置data-quote-id属性');
        }
        if (results.cardsWithInvalidIds > 0) {
            console.log('  - 检查API返回的名言数据中的ID字段');
        }
        if (results.cardsWithoutClickEvents > 0) {
            console.log('  - 确保名言卡片添加了cursor-pointer类和点击事件监听器');
        }
    }
    
    console.log('='.repeat(50));
    
    return results;
}

// 主函数
async function runCompleteInspection() {
    console.log('🚀 开始完整的名言卡片检查...');
    
    // 等待页面加载完成
    if (document.readyState !== 'complete') {
        console.log('⏳ 等待页面加载完成...');
        await new Promise(resolve => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }
    
    // 等待一段时间让JavaScript加载名言卡片
    console.log('⏳ 等待名言卡片加载...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 运行检查
    const results = generateDiagnosticReport();
    await checkAPIConnection();
    testQuoteCardClicks();
    
    console.log('\n✅ 检查完成！');
    
    return results;
}

// 导出函数供手动调用
window.QuoteCardInspector = {
    inspect: inspectQuoteCards,
    testClicks: testQuoteCardClicks,
    checkAPI: checkAPIConnection,
    generateReport: generateDiagnosticReport,
    runComplete: runCompleteInspection
};

console.log('📋 名言卡片检查器已加载');
console.log('💡 使用方法:');
console.log('   - QuoteCardInspector.runComplete() - 运行完整检查');
console.log('   - QuoteCardInspector.inspect() - 检查名言卡片');
console.log('   - QuoteCardInspector.testClicks() - 测试点击功能');
console.log('   - QuoteCardInspector.checkAPI() - 检查API连接');
console.log('   - QuoteCardInspector.generateReport() - 生成诊断报告');

// 自动运行完整检查
runCompleteInspection();
