<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { margin: 5px; padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>API调试页面</h1>
    <div>
        <button onclick="testConfig()">测试配置</button>
        <button onclick="testApiClient()">测试API客户端</button>
        <button onclick="testCategories()">测试类别API</button>
        <button onclick="testAuthors()">测试作者API</button>
        <button onclick="testSources()">测试来源API</button>
        <button onclick="clearResults()">清除结果</button>
    </div>
    <div id="results"></div>
    
    <!-- Load scripts in correct order -->
    <script src="/js/config.js?v=20250626"></script>
    <script src="/js/api-client.js?v=20250626"></script>
    
    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        function testConfig() {
            addResult('测试配置...', 'info');
            
            if (window.AppConfig) {
                addResult(`✅ 配置已加载`, 'success');
                addResult(`<pre>${JSON.stringify(window.AppConfig, null, 2)}</pre>`, 'info');
            } else {
                addResult(`❌ 配置未加载`, 'error');
            }
        }
        
        function testApiClient() {
            addResult('测试API客户端...', 'info');
            
            if (window.ApiClient) {
                addResult(`✅ API客户端已加载`, 'success');
                addResult(`API客户端类型: ${typeof window.ApiClient}`, 'info');
                
                // 检查API客户端的方法
                const methods = Object.getOwnPropertyNames(Object.getPrototypeOf(window.ApiClient));
                addResult(`可用方法: ${methods.join(', ')}`, 'info');
            } else {
                addResult(`❌ API客户端未加载`, 'error');
            }
        }
        
        async function testCategories() {
            addResult('测试类别API...', 'info');
            
            if (!window.ApiClient) {
                addResult(`❌ API客户端未加载`, 'error');
                return;
            }
            
            try {
                if (typeof window.ApiClient.getPopularCategories === 'function') {
                    const categories = await window.ApiClient.getPopularCategories();
                    addResult(`✅ 类别API成功 - 获取到 ${categories.length} 个类别`, 'success');
                    addResult(`<pre>${JSON.stringify(categories.slice(0, 3), null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ getPopularCategories方法不存在`, 'error');
                }
            } catch (error) {
                addResult(`❌ 类别API失败: ${error.message}`, 'error');
                console.error('Categories API error:', error);
            }
        }
        
        async function testAuthors() {
            addResult('测试作者API...', 'info');
            
            if (!window.ApiClient) {
                addResult(`❌ API客户端未加载`, 'error');
                return;
            }
            
            try {
                if (typeof window.ApiClient.getPopularAuthors === 'function') {
                    const authors = await window.ApiClient.getPopularAuthors();
                    addResult(`✅ 作者API成功 - 获取到 ${authors.length} 个作者`, 'success');
                    addResult(`<pre>${JSON.stringify(authors.slice(0, 3), null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ getPopularAuthors方法不存在`, 'error');
                }
            } catch (error) {
                addResult(`❌ 作者API失败: ${error.message}`, 'error');
                console.error('Authors API error:', error);
            }
        }
        
        async function testSources() {
            addResult('测试来源API...', 'info');
            
            if (!window.ApiClient) {
                addResult(`❌ API客户端未加载`, 'error');
                return;
            }
            
            try {
                if (typeof window.ApiClient.getPopularSources === 'function') {
                    const sources = await window.ApiClient.getPopularSources();
                    addResult(`✅ 来源API成功 - 获取到 ${sources.length} 个来源`, 'success');
                    addResult(`<pre>${JSON.stringify(sources.slice(0, 3), null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ getPopularSources方法不存在`, 'error');
                }
            } catch (error) {
                addResult(`❌ 来源API失败: ${error.message}`, 'error');
                console.error('Sources API error:', error);
            }
        }
        
        // 页面加载后自动测试配置
        window.addEventListener('load', function() {
            setTimeout(() => {
                testConfig();
                testApiClient();
            }, 100);
        });
    </script>
</body>
</html>
