<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产API连接测试 - Quotese</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .control-panel {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .api-mode {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .mode-production {
            background: #28a745;
            color: white;
        }
        .mode-local {
            background: #17a2b8;
            color: white;
        }
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔗 生产API连接测试</h1>
        <p>测试本地前端与生产API (api.quotese.com) 的连接和兼容性</p>
        <div id="current-mode">
            <span>当前API模式:</span>
            <span id="api-mode-indicator" class="api-mode">检测中...</span>
        </div>
    </div>

    <div class="control-panel">
        <h3>🎛️ API模式控制</h3>
        <button onclick="switchToProductionAPI()">切换到生产API</button>
        <button onclick="switchToLocalAPI()">切换到本地API</button>
        <button onclick="getCurrentMode()">查看当前模式</button>
        <button onclick="testAPIConnection()">测试API连接</button>
        <button onclick="runAllTests()">运行完整测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div id="test-results"></div>

    <!-- 加载配置文件 -->
    <script src="js/config.js"></script>
    
    <script>
        let testCounter = 0;
        
        // 页面加载时显示当前模式
        window.addEventListener('load', function() {
            updateModeIndicator();
            addTestResult('页面加载', 'info', '生产API测试页面已加载');
        });

        // 更新模式指示器
        function updateModeIndicator() {
            const config = window.AppConfig;
            const indicator = document.getElementById('api-mode-indicator');
            
            if (config.apiEndpoint.includes('api.quotese.com')) {
                indicator.textContent = '生产API';
                indicator.className = 'api-mode mode-production';
            } else {
                indicator.textContent = '本地API';
                indicator.className = 'api-mode mode-local';
            }
        }

        // 切换到生产API
        function switchToProductionAPI() {
            const result = window.QuoteseAPIMode.useProductionAPI();
            addTestResult('切换API模式', 'success', result);
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // 切换到本地API
        function switchToLocalAPI() {
            const result = window.QuoteseAPIMode.useLocalAPI();
            addTestResult('切换API模式', 'success', result);
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // 获取当前模式
        function getCurrentMode() {
            const mode = window.QuoteseAPIMode.getCurrentMode();
            addTestResult('当前API模式', 'info', 
                `API端点: ${mode.apiEndpoint}<br>` +
                `GraphQL端点: ${mode.graphqlEndpoint}<br>` +
                `使用生产API: ${mode.isUsingProduction}<br>` +
                `调试模式: ${mode.debug}`
            );
        }

        // 测试API连接
        async function testAPIConnection() {
            addTestResult('API连接测试', 'info', '正在测试连接...', true);
            
            try {
                const result = await window.QuoteseAPIMode.testConnection();
                addTestResult('API连接测试', 'success', result);
            } catch (error) {
                addTestResult('API连接测试', 'error', `连接失败: ${error.message}`);
            }
        }

        // 运行完整测试
        async function runAllTests() {
            addTestResult('完整测试', 'info', '开始运行完整测试套件...', true);
            
            // 基础连接测试
            await testAPIConnection();
            
            // GraphQL测试
            await testGraphQLAPI();
            
            // REST API测试
            await testRESTAPI();
            
            // 页面数据加载测试
            await testPageDataLoading();
            
            addTestResult('完整测试', 'success', '所有测试已完成');
        }

        // 测试GraphQL API
        async function testGraphQLAPI() {
            addTestSection('GraphQL API测试');

            const config = window.AppConfig;

            try {
                // 测试基础查询 - 使用正确的字段名
                const response = await fetch(config.graphqlEndpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: `
                            query {
                                categories {
                                    id
                                    name
                                }
                            }
                        `
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.errors) {
                        addTestResult('GraphQL Categories查询', 'error',
                            `GraphQL错误: ${data.errors[0]?.message || '未知错误'}`);
                    } else {
                        addTestResult('GraphQL Categories查询', 'success',
                            `获取到 ${data.data?.categories?.length || 0} 个类别`);

                        if (data.data?.categories?.length > 0) {
                            addJSONDisplay('Categories数据示例', data.data.categories.slice(0, 3));
                        }
                    }
                } else {
                    addTestResult('GraphQL Categories查询', 'error',
                        `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addTestResult('GraphQL API测试', 'error', `请求失败: ${error.message}`);
            }
        }

        // 测试REST API
        async function testRESTAPI() {
            addTestSection('REST API测试');
            
            const config = window.AppConfig;
            const endpoints = [
                { name: 'Categories', url: `${config.apiEndpoint}categories/` },
                { name: 'Authors', url: `${config.apiEndpoint}authors/` },
                { name: 'Sources', url: `${config.apiEndpoint}sources/` }
            ];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url);
                    
                    if (response.ok) {
                        const data = await response.json();
                        addTestResult(`REST ${endpoint.name}`, 'success', 
                            `状态码: ${response.status}, 数据量: ${data.length || 'N/A'}`);
                    } else {
                        addTestResult(`REST ${endpoint.name}`, 'error', 
                            `HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    addTestResult(`REST ${endpoint.name}`, 'error', 
                        `请求失败: ${error.message}`);
                }
            }
        }

        // 测试页面数据加载
        async function testPageDataLoading() {
            addTestSection('页面数据加载测试');
            
            // 这里可以测试实际的页面数据加载逻辑
            addTestResult('页面数据加载', 'info', '此测试需要在实际页面中进行');
        }

        // 添加测试结果
        function addTestResult(title, type, message, showLoading = false) {
            const container = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            const loadingIcon = showLoading ? '<span class="loading"></span> ' : '';
            
            resultDiv.innerHTML = `
                <strong>[${timestamp}] ${title}:</strong><br>
                ${loadingIcon}${message}
            `;
            
            container.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 添加测试分组
        function addTestSection(title) {
            const container = document.getElementById('test-results');
            const sectionDiv = document.createElement('div');
            sectionDiv.className = 'test-section';
            sectionDiv.innerHTML = `<h3>📋 ${title}</h3>`;
            container.appendChild(sectionDiv);
        }

        // 添加JSON数据显示
        function addJSONDisplay(title, data) {
            const container = document.getElementById('test-results');
            const jsonDiv = document.createElement('div');
            jsonDiv.innerHTML = `
                <strong>${title}:</strong>
                <div class="json-display">${JSON.stringify(data, null, 2)}</div>
            `;
            container.appendChild(jsonDiv);
        }

        // 清空结果
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            testCounter = 0;
        }
    </script>
</body>
</html>
