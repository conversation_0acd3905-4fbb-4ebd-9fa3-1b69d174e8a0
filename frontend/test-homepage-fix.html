<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 200px; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>🔧 首页修复测试</h1>
    
    <div class="test-section">
        <h2>📋 强制重新加载配置</h2>
        <div id="config-reload-result"></div>
        <button onclick="forceReloadConfig()">强制重新加载配置</button>
    </div>
    
    <div class="test-section">
        <h2>🌐 重新初始化API客户端</h2>
        <div id="api-reinit-result"></div>
        <button onclick="reinitializeApiClient()">重新初始化API客户端</button>
    </div>
    
    <div class="test-section">
        <h2>🧪 测试修复后的API调用</h2>
        <div id="fixed-api-test"></div>
        <button onclick="testFixedAPI()">测试修复后的API</button>
    </div>

    <script>
        function addResult(container, message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            container.appendChild(div);
        }
        
        function forceReloadConfig() {
            const container = document.getElementById('config-reload-result');
            container.innerHTML = '';
            
            addResult(container, '🔄 强制重新加载配置...', 'info');
            
            // 清除可能的缓存
            if (window.AppConfig) {
                delete window.AppConfig;
            }
            if (window.Config) {
                delete window.Config;
            }
            
            // 重新加载配置脚本
            const timestamp = new Date().getTime();
            const script = document.createElement('script');
            script.src = `js/config.js?v=${timestamp}`;
            script.onload = function() {
                addResult(container, '✅ 配置脚本重新加载成功', 'success');
                
                if (window.AppConfig) {
                    addResult(container, `🔗 REST API端点: ${window.AppConfig.apiEndpoint}`, 'info');
                    addResult(container, `🔍 GraphQL端点: ${window.AppConfig.graphqlEndpoint || '❌ 未配置'}`, window.AppConfig.graphqlEndpoint ? 'success' : 'error');
                    
                    const pre = document.createElement('pre');
                    pre.textContent = JSON.stringify(window.AppConfig, null, 2);
                    container.appendChild(pre);
                } else {
                    addResult(container, '❌ 配置仍未加载', 'error');
                }
            };
            script.onerror = function() {
                addResult(container, '❌ 配置脚本加载失败', 'error');
            };
            document.head.appendChild(script);
        }
        
        function reinitializeApiClient() {
            const container = document.getElementById('api-reinit-result');
            container.innerHTML = '';
            
            addResult(container, '🔄 重新初始化API客户端...', 'info');
            
            if (!window.AppConfig) {
                addResult(container, '❌ 配置未加载，请先重新加载配置', 'error');
                return;
            }
            
            if (!window.AppConfig.graphqlEndpoint) {
                addResult(container, '❌ GraphQL端点未配置', 'error');
                return;
            }
            
            // 清除旧的API客户端
            if (window.ApiClient) {
                delete window.ApiClient;
            }
            
            // 重新加载API客户端脚本
            const timestamp = new Date().getTime();
            const script = document.createElement('script');
            script.src = `js/api-client.js?v=${timestamp}`;
            script.onload = function() {
                addResult(container, '✅ API客户端脚本重新加载成功', 'success');
                
                if (window.ApiClient) {
                    addResult(container, `🔗 REST端点: ${window.ApiClient.apiEndpoint}`, 'info');
                    addResult(container, `🔍 GraphQL端点: ${window.ApiClient.graphqlEndpoint || '❌ 未配置'}`, window.ApiClient.graphqlEndpoint ? 'success' : 'error');
                    addResult(container, `🎭 模拟数据: ${window.ApiClient.useMockData}`, 'info');
                } else {
                    addResult(container, '❌ API客户端仍未初始化', 'error');
                }
            };
            script.onerror = function() {
                addResult(container, '❌ API客户端脚本加载失败', 'error');
            };
            document.head.appendChild(script);
        }
        
        async function testFixedAPI() {
            const container = document.getElementById('fixed-api-test');
            container.innerHTML = '';
            
            addResult(container, '🧪 测试修复后的API调用...', 'info');
            
            if (!window.ApiClient) {
                addResult(container, '❌ API客户端未初始化', 'error');
                return;
            }
            
            if (!window.ApiClient.graphqlEndpoint) {
                addResult(container, '❌ GraphQL端点未配置', 'error');
                return;
            }
            
            try {
                // 测试getQuotes方法
                addResult(container, '📊 测试getQuotes方法...', 'info');
                const quotesResult = await window.ApiClient.getQuotes(1, 3);
                
                if (quotesResult && quotesResult.quotes && quotesResult.quotes.length > 0) {
                    addResult(container, `✅ getQuotes成功: 获取到 ${quotesResult.quotes.length} 条名言`, 'success');
                    addResult(container, `📊 总数: ${quotesResult.totalCount}`, 'info');
                    
                    // 显示第一条名言的详细信息
                    const firstQuote = quotesResult.quotes[0];
                    addResult(container, `📝 第一条名言: "${firstQuote.content}"`, 'info');
                    addResult(container, `👤 作者: ${firstQuote.author.name}`, 'info');
                    addResult(container, `📂 类别数: ${firstQuote.categories.length}`, 'info');
                    addResult(container, `📚 来源数: ${firstQuote.sources.length}`, 'info');
                    
                    const pre = document.createElement('pre');
                    pre.textContent = JSON.stringify(quotesResult, null, 2);
                    container.appendChild(pre);
                } else {
                    addResult(container, '❌ getQuotes返回空数据', 'error');
                }
                
                // 测试getPopularAuthors方法
                addResult(container, '👤 测试getPopularAuthors方法...', 'info');
                const authorsResult = await window.ApiClient.getPopularAuthors(5);
                
                if (authorsResult && authorsResult.length > 0) {
                    addResult(container, `✅ getPopularAuthors成功: 获取到 ${authorsResult.length} 个作者`, 'success');
                    addResult(container, `👤 第一个作者: ${authorsResult[0].name} (${authorsResult[0].count} 条名言)`, 'info');
                } else {
                    addResult(container, '❌ getPopularAuthors返回空数据', 'error');
                }
                
                // 测试getPopularCategories方法
                addResult(container, '📂 测试getPopularCategories方法...', 'info');
                const categoriesResult = await window.ApiClient.getPopularCategories(5);
                
                if (categoriesResult && categoriesResult.length > 0) {
                    addResult(container, `✅ getPopularCategories成功: 获取到 ${categoriesResult.length} 个类别`, 'success');
                    addResult(container, `📂 第一个类别: ${categoriesResult[0].name} (${categoriesResult[0].count} 条名言)`, 'info');
                } else {
                    addResult(container, '❌ getPopularCategories返回空数据', 'error');
                }
                
            } catch (error) {
                addResult(container, `❌ API测试失败: ${error.message}`, 'error');
                console.error('API测试错误:', error);
            }
        }
    </script>
</body>
</html>
