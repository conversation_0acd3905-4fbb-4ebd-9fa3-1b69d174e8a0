<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sources Page Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .loading { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <h1>Sources 页面功能测试</h1>
    <p>专门测试 sources 页面的 API 调用和数据加载</p>
    
    <button onclick="runSourcesTests()">运行 Sources 测试</button>
    <button onclick="clearResults()">清除结果</button>
    
    <div id="test-results"></div>

    <!-- 加载必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/url-handler.js"></script>

    <script>
        const resultsContainer = document.getElementById('test-results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsContainer.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults() {
            resultsContainer.innerHTML = '';
        }

        async function testSourceByName() {
            addResult('🔍 测试: getSourceByName("healology")', 'loading');
            
            try {
                // 确保使用生产API
                window.ApiClient.useMockData = false;
                
                const source = await window.ApiClient.getSourceByName('healology');
                
                if (source) {
                    addResult(`✅ 找到来源: ${source.name} (ID: ${source.id})`, 'success');
                    addResult(`<pre>${JSON.stringify(source, null, 2)}</pre>`, 'info');
                    return source;
                } else {
                    addResult(`❌ 未找到来源 "healology"`, 'error');
                    return null;
                }
            } catch (error) {
                addResult(`❌ getSourceByName 异常: ${error.message}`, 'error');
                console.error('getSourceByName error:', error);
                return null;
            }
        }

        async function testQuotesBySource(sourceId) {
            addResult(`🔍 测试: getQuotes with sourceId=${sourceId}`, 'loading');
            
            try {
                // 确保使用生产API
                window.ApiClient.useMockData = false;
                
                const quotesData = await window.ApiClient.getQuotes(1, 10, { sourceId: sourceId });
                
                if (quotesData && quotesData.quotes) {
                    addResult(`✅ 获取到 ${quotesData.quotes.length} 条名言，总计 ${quotesData.totalCount} 条`, 'success');
                    addResult(`<pre>第一条名言: ${JSON.stringify(quotesData.quotes[0], null, 2)}</pre>`, 'info');
                    return quotesData;
                } else {
                    addResult(`❌ 未获取到名言数据`, 'error');
                    return null;
                }
            } catch (error) {
                addResult(`❌ getQuotes 异常: ${error.message}`, 'error');
                console.error('getQuotes error:', error);
                return null;
            }
        }

        async function testPopularSources() {
            addResult('🔍 测试: getPopularSources(10)', 'loading');
            
            try {
                // 确保使用生产API
                window.ApiClient.useMockData = false;
                
                const sources = await window.ApiClient.getPopularSources(10);
                
                if (sources && sources.length > 0) {
                    addResult(`✅ 获取到 ${sources.length} 个热门来源`, 'success');
                    addResult(`<pre>前3个来源: ${JSON.stringify(sources.slice(0, 3), null, 2)}</pre>`, 'info');
                    return sources;
                } else {
                    addResult(`❌ 未获取到热门来源数据`, 'error');
                    return [];
                }
            } catch (error) {
                addResult(`❌ getPopularSources 异常: ${error.message}`, 'error');
                console.error('getPopularSources error:', error);
                return [];
            }
        }

        async function testPopularAuthors() {
            addResult('🔍 测试: getPopularAuthors(10)', 'loading');
            
            try {
                // 确保使用生产API
                window.ApiClient.useMockData = false;
                
                const authors = await window.ApiClient.getPopularAuthors(10);
                
                if (authors && authors.length > 0) {
                    addResult(`✅ 获取到 ${authors.length} 个热门作者`, 'success');
                    addResult(`<pre>前3个作者: ${JSON.stringify(authors.slice(0, 3), null, 2)}</pre>`, 'info');
                    return authors;
                } else {
                    addResult(`❌ 未获取到热门作者数据`, 'error');
                    return [];
                }
            } catch (error) {
                addResult(`❌ getPopularAuthors 异常: ${error.message}`, 'error');
                console.error('getPopularAuthors error:', error);
                return [];
            }
        }

        async function testPopularCategories() {
            addResult('🔍 测试: getPopularCategories(10)', 'loading');
            
            try {
                // 确保使用生产API
                window.ApiClient.useMockData = false;
                
                const categories = await window.ApiClient.getPopularCategories(10);
                
                if (categories && categories.length > 0) {
                    addResult(`✅ 获取到 ${categories.length} 个热门分类`, 'success');
                    addResult(`<pre>前3个分类: ${JSON.stringify(categories.slice(0, 3), null, 2)}</pre>`, 'info');
                    return categories;
                } else {
                    addResult(`❌ 未获取到热门分类数据`, 'error');
                    return [];
                }
            } catch (error) {
                addResult(`❌ getPopularCategories 异常: ${error.message}`, 'error');
                console.error('getPopularCategories error:', error);
                return [];
            }
        }

        async function runSourcesTests() {
            clearResults();
            addResult('🚀 开始 Sources 页面功能测试...', 'info');
            
            // 初始化 ApiClient
            if (!window.ApiClient) {
                window.ApiClient = new ApiClient();
            }
            
            addResult(`📋 API 端点: ${window.AppConfig.apiEndpoint}`, 'info');
            addResult(`📋 使用模拟数据: ${window.ApiClient.useMockData}`, 'info');
            
            // 测试 1: 根据名称获取来源
            const source = await testSourceByName();
            
            if (source) {
                // 测试 2: 根据来源ID获取名言
                await testQuotesBySource(source.id);
            }
            
            // 测试 3: 获取热门来源
            await testPopularSources();
            
            // 测试 4: 获取热门作者
            await testPopularAuthors();
            
            // 测试 5: 获取热门分类
            await testPopularCategories();
            
            addResult('✅ 所有测试完成', 'success');
        }

        // 页面加载时显示配置信息
        window.addEventListener('load', () => {
            addResult('📋 Sources 页面测试工具已加载', 'info');
            addResult(`📋 当前环境: ${window.location.hostname}`, 'info');
            if (window.AppConfig) {
                addResult(`📋 API 配置: ${JSON.stringify(window.AppConfig, null, 2)}`, 'info');
            }
        });
    </script>
</body>
</html>
