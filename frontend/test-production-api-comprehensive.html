<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production API Comprehensive Test - Quote Detail Page</title>
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
</head>
<body class="light-mode bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">Production API Comprehensive Test</h1>
        
        <!-- API Mode Control -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">🔧 API Mode Control</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3">Current Configuration</h3>
                    <div id="current-config" class="bg-gray-100 dark:bg-gray-700 p-4 rounded text-sm font-mono">
                        Loading configuration...
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-3">Quick Actions</h3>
                    <div class="space-y-3">
                        <button onclick="switchToProductionAPI()" class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                            🚀 Switch to Production API
                        </button>
                        <button onclick="switchToLocalAPI()" class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                            🏠 Switch to Local API
                        </button>
                        <button onclick="testAPIConnection()" class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                            🔍 Test API Connection
                        </button>
                        <button onclick="refreshPage()" class="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                            🔄 Refresh Page
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Status Dashboard -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">📊 Test Status Dashboard</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 dark:bg-blue-900 rounded-lg p-4 text-center">
                    <div class="text-3xl mb-2">🔗</div>
                    <h3 class="font-bold text-lg mb-2">API Connection</h3>
                    <div id="api-connection-status" class="text-lg font-bold">-</div>
                </div>
                <div class="bg-green-50 dark:bg-green-900 rounded-lg p-4 text-center">
                    <div class="text-3xl mb-2">📊</div>
                    <h3 class="font-bold text-lg mb-2">Data Loading</h3>
                    <div id="data-loading-status" class="text-lg font-bold">-</div>
                </div>
                <div class="bg-purple-50 dark:bg-purple-900 rounded-lg p-4 text-center">
                    <div class="text-3xl mb-2">🌐</div>
                    <h3 class="font-bold text-lg mb-2">CORS Status</h3>
                    <div id="cors-status" class="text-lg font-bold">-</div>
                </div>
                <div class="bg-orange-50 dark:bg-orange-900 rounded-lg p-4 text-center">
                    <div class="text-3xl mb-2">⚡</div>
                    <h3 class="font-bold text-lg mb-2">Performance</h3>
                    <div id="performance-status" class="text-lg font-bold">-</div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <button onclick="runFullProductionTest()" class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition-colors">
                    🧪 Run Full Production Test
                </button>
                <button onclick="testQuoteDetailPage()" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors">
                    📄 Test Quote Detail Page
                </button>
                <button onclick="testCORSConfiguration()" class="px-4 py-2 bg-pink-500 text-white rounded hover:bg-pink-600 transition-colors">
                    🌐 Test CORS Configuration
                </button>
            </div>
        </div>
        
        <!-- Quote Detail Page Test -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">📄 Quote Detail Page Test</h2>
            
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Test Quote ID:</label>
                <div class="flex gap-2">
                    <input type="number" id="testQuoteId" value="1" 
                           class="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button onclick="loadQuoteDetail()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                        Load Quote
                    </button>
                    <button onclick="testMultipleQuotes()" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                        Test Multiple
                    </button>
                </div>
            </div>
            
            <!-- Quote Display Area -->
            <div id="quote-display-area" class="border-2 border-dashed border-gray-300 rounded-lg p-6 min-h-[200px]">
                <div class="text-center text-gray-500">
                    <div class="text-4xl mb-4">💬</div>
                    <p>Click "Load Quote" to test quote detail functionality with production API</p>
                </div>
            </div>
        </div>
        
        <!-- Performance Metrics -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">⚡ Performance Metrics</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded">
                    <h3 class="font-semibold mb-2">API Response Time</h3>
                    <div id="api-response-time" class="text-2xl font-bold text-blue-600">-</div>
                    <div class="text-sm text-gray-500">milliseconds</div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded">
                    <h3 class="font-semibold mb-2">Component Render Time</h3>
                    <div id="component-render-time" class="text-2xl font-bold text-green-600">-</div>
                    <div class="text-sm text-gray-500">milliseconds</div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded">
                    <h3 class="font-semibold mb-2">Total Load Time</h3>
                    <div id="total-load-time" class="text-2xl font-bold text-purple-600">-</div>
                    <div class="text-sm text-gray-500">milliseconds</div>
                </div>
            </div>
            
            <button onclick="runPerformanceTest()" class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors">
                🏃‍♂️ Run Performance Test
            </button>
        </div>
        
        <!-- Data Compatibility Test -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">🔄 Data Compatibility Test</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3">Local Data Sample</h3>
                    <div id="local-data-sample" class="bg-gray-100 dark:bg-gray-700 p-4 rounded text-sm font-mono max-h-60 overflow-y-auto">
                        Switch to local API and load a quote to see sample data...
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-3">Production Data Sample</h3>
                    <div id="production-data-sample" class="bg-gray-100 dark:bg-gray-700 p-4 rounded text-sm font-mono max-h-60 overflow-y-auto">
                        Switch to production API and load a quote to see sample data...
                    </div>
                </div>
            </div>
            
            <div class="mt-4 flex gap-2">
                <button onclick="compareDataStructures()" class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors">
                    🔍 Compare Data Structures
                </button>
                <button onclick="validateDataIntegrity()" class="px-4 py-2 bg-teal-500 text-white rounded hover:bg-teal-600 transition-colors">
                    ✅ Validate Data Integrity
                </button>
            </div>
        </div>
        
        <!-- Test Log -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold mb-4">📝 Test Log</h2>
            <div id="test-log" class="bg-gray-100 dark:bg-gray-700 p-4 rounded min-h-[300px] font-mono text-sm overflow-y-auto max-h-96">
                Production API test environment initialized...
            </div>
            <div class="mt-4 flex gap-2">
                <button onclick="clearLog()" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                    🗑️ Clear Log
                </button>
                <button onclick="exportTestReport()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                    📊 Export Test Report
                </button>
                <button onclick="copyLogToClipboard()" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                    📋 Copy to Clipboard
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/debug.js?v=20250629"></script>
    <script src="/js/config.js?v=20250629"></script>
    <script src="/js/api-client.js?v=20250629"></script>
    <script src="/js/url-handler.js?v=20250629"></script>
    <script src="/js/components/quote-card.js?v=20250629"></script>
    
    <script>
        // Test state and data storage
        let testResults = {
            apiConnection: false,
            dataLoading: false,
            corsStatus: false,
            performance: false
        };
        
        let performanceMetrics = {
            apiResponseTime: 0,
            componentRenderTime: 0,
            totalLoadTime: 0
        };
        
        let localDataSample = null;
        let productionDataSample = null;
        
        // Utility functions
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const typeColors = {
                'info': 'text-blue-600',
                'success': 'text-green-600',
                'error': 'text-red-600',
                'warning': 'text-yellow-600'
            };
            const color = typeColors[type] || 'text-gray-600';
            
            logDiv.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = 'Test log cleared...';
        }
        
        function updateStatus(category, status, details = '') {
            const statusDiv = document.getElementById(`${category.replace(/([A-Z])/g, '-$1').toLowerCase()}-status`);
            if (!statusDiv) return;
            
            const statusText = status ? '✅ PASS' : '❌ FAIL';
            const statusColor = status ? 'text-green-600' : 'text-red-600';
            
            statusDiv.innerHTML = `<div class="${statusColor}">${statusText}</div>`;
            if (details) {
                statusDiv.innerHTML += `<div class="text-xs text-gray-500 mt-1">${details}</div>`;
            }
            
            testResults[category] = status;
        }
        
        function updatePerformanceMetric(metric, value) {
            performanceMetrics[metric] = value;
            const element = document.getElementById(metric.replace(/([A-Z])/g, '-$1').toLowerCase());
            if (element) {
                element.textContent = value.toFixed(2);
            }
        }
        
        function updateCurrentConfig() {
            const configDiv = document.getElementById('current-config');
            
            if (typeof window.QuoteseAPIMode !== 'undefined') {
                const mode = window.QuoteseAPIMode.getCurrentMode();
                configDiv.innerHTML = `
                    <div><strong>Mode:</strong> <span class="${mode.isUsingProduction ? 'text-red-600' : 'text-blue-600'}">${mode.isUsingProduction ? 'Production' : 'Local'}</span></div>
                    <div><strong>API Endpoint:</strong> ${mode.apiEndpoint}</div>
                    <div><strong>GraphQL Endpoint:</strong> ${mode.graphqlEndpoint}</div>
                    <div><strong>Debug:</strong> ${mode.debug}</div>
                `;
            } else {
                configDiv.innerHTML = '<div class="text-red-600">Configuration not available</div>';
            }
        }
        
        // API Mode Control Functions
        function switchToProductionAPI() {
            log('🔄 Switching to production API...', 'info');
            
            if (typeof window.QuoteseAPIMode !== 'undefined') {
                const result = window.QuoteseAPIMode.useProductionAPI();
                log(result, 'success');
                log('🔄 Please refresh the page to apply the new configuration', 'warning');
                
                // Show refresh reminder
                setTimeout(() => {
                    if (confirm('Configuration changed. Refresh page now to apply changes?')) {
                        window.location.reload();
                    }
                }, 1000);
            } else {
                log('❌ QuoteseAPIMode not available', 'error');
            }
        }
        
        function switchToLocalAPI() {
            log('🔄 Switching to local API...', 'info');
            
            if (typeof window.QuoteseAPIMode !== 'undefined') {
                const result = window.QuoteseAPIMode.useLocalAPI();
                log(result, 'success');
                log('🔄 Please refresh the page to apply the new configuration', 'warning');
                
                // Show refresh reminder
                setTimeout(() => {
                    if (confirm('Configuration changed. Refresh page now to apply changes?')) {
                        window.location.reload();
                    }
                }, 1000);
            } else {
                log('❌ QuoteseAPIMode not available', 'error');
            }
        }
        
        async function testAPIConnection() {
            log('🔍 Testing API connection...', 'info');
            
            if (typeof window.QuoteseAPIMode !== 'undefined') {
                try {
                    const startTime = performance.now();
                    const result = await window.QuoteseAPIMode.testConnection();
                    const connectionTime = performance.now() - startTime;
                    
                    log(result, 'success');
                    log(`Connection test completed in ${connectionTime.toFixed(2)}ms`, 'info');
                    updateStatus('apiConnection', true, `${connectionTime.toFixed(0)}ms`);
                    updatePerformanceMetric('apiResponseTime', connectionTime);
                } catch (error) {
                    log(`❌ API connection test failed: ${error.message}`, 'error');
                    updateStatus('apiConnection', false, error.message);
                }
            } else {
                log('❌ QuoteseAPIMode not available', 'error');
                updateStatus('apiConnection', false, 'QuoteseAPIMode not available');
            }
        }
        
        function refreshPage() {
            log('🔄 Refreshing page...', 'info');
            window.location.reload();
        }
        
        // Quote Detail Testing Functions
        async function loadQuoteDetail() {
            const quoteId = document.getElementById('testQuoteId').value;
            log(`📄 Loading quote detail for ID: ${quoteId}...`, 'info');
            
            try {
                if (typeof window.ApiClient === 'undefined') {
                    throw new Error('ApiClient not available');
                }
                
                const startTime = performance.now();
                const quote = await window.ApiClient.getQuoteById(parseInt(quoteId));
                const apiTime = performance.now() - startTime;
                
                if (quote) {
                    log(`✅ Quote loaded successfully in ${apiTime.toFixed(2)}ms`, 'success');
                    
                    // Render quote card
                    const renderStartTime = performance.now();
                    await renderQuoteInDisplay(quote);
                    const renderTime = performance.now() - renderStartTime;
                    
                    const totalTime = performance.now() - startTime;
                    
                    // Update metrics
                    updatePerformanceMetric('apiResponseTime', apiTime);
                    updatePerformanceMetric('componentRenderTime', renderTime);
                    updatePerformanceMetric('totalLoadTime', totalTime);
                    
                    updateStatus('dataLoading', true, `${apiTime.toFixed(0)}ms`);
                    updateStatus('performance', totalTime < 1000, `${totalTime.toFixed(0)}ms`);
                    
                    // Store data sample for comparison
                    const currentMode = window.QuoteseAPIMode?.getCurrentMode();
                    if (currentMode?.isUsingProduction) {
                        productionDataSample = quote;
                        document.getElementById('production-data-sample').textContent = JSON.stringify(quote, null, 2);
                        log('📊 Production data sample stored for comparison', 'info');
                    } else {
                        localDataSample = quote;
                        document.getElementById('local-data-sample').textContent = JSON.stringify(quote, null, 2);
                        log('📊 Local data sample stored for comparison', 'info');
                    }
                    
                } else {
                    log(`❌ Quote with ID ${quoteId} not found`, 'error');
                    updateStatus('dataLoading', false, 'Quote not found');
                }
                
            } catch (error) {
                log(`❌ Failed to load quote: ${error.message}`, 'error');
                updateStatus('dataLoading', false, error.message);
                
                // Check if it's a CORS error
                if (error.message.includes('CORS') || error.message.includes('fetch') || error.message.includes('network')) {
                    updateStatus('corsStatus', false, 'CORS/Network issue');
                    log('❌ CORS or network configuration issue detected', 'error');
                }
            }
        }
        
        async function renderQuoteInDisplay(quote) {
            const displayArea = document.getElementById('quote-display-area');
            
            // Try to use QuoteCardComponent if available
            if (typeof QuoteCardComponent !== 'undefined') {
                try {
                    const quoteCard = QuoteCardComponent.render(quote, 0, {
                        showActions: true,
                        showAuthorAvatar: true,
                        showCategories: true,
                        showSources: true,
                        showDate: true,
                        isDetailPage: true
                    });
                    
                    displayArea.innerHTML = '';
                    displayArea.appendChild(quoteCard);
                    log('✅ Quote rendered using QuoteCardComponent', 'success');
                } catch (error) {
                    log(`⚠ QuoteCardComponent failed, using fallback: ${error.message}`, 'warning');
                    renderQuoteFallback(quote, displayArea);
                }
            } else {
                log('⚠ QuoteCardComponent not available, using fallback', 'warning');
                renderQuoteFallback(quote, displayArea);
            }
        }
        
        function renderQuoteFallback(quote, container) {
            container.innerHTML = `
                <div class="bg-gradient-to-br from-yellow-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
                    <blockquote class="text-lg font-serif leading-relaxed mb-4">
                        "${quote.content}"
                    </blockquote>
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-600 dark:text-yellow-300 border-2 border-yellow-400 dark:border-yellow-600 mr-3">
                            <span class="text-sm font-bold">${quote.author?.name?.charAt(0) || 'A'}</span>
                        </div>
                        <div>
                            <div class="font-semibold text-yellow-600 dark:text-yellow-400">${quote.author?.name || 'Unknown Author'}</div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                Categories: ${quote.categories?.map(c => c.name).join(', ') || 'None'} | 
                                Sources: ${quote.sources?.map(s => s.name).join(', ') || 'None'}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        async function testMultipleQuotes() {
            log('🔄 Testing multiple quotes...', 'info');
            
            const testIds = [1, 10, 100, 500, 1000];
            let successCount = 0;
            let totalTime = 0;
            
            for (const id of testIds) {
                try {
                    document.getElementById('testQuoteId').value = id;
                    const startTime = performance.now();
                    
                    const quote = await window.ApiClient.getQuoteById(id);
                    const loadTime = performance.now() - startTime;
                    totalTime += loadTime;
                    
                    if (quote) {
                        successCount++;
                        log(`✅ Quote ${id}: loaded in ${loadTime.toFixed(2)}ms`, 'success');
                    } else {
                        log(`⚠ Quote ${id}: not found`, 'warning');
                    }
                    
                    // Small delay between requests
                    await new Promise(resolve => setTimeout(resolve, 200));
                } catch (error) {
                    log(`❌ Quote ${id}: failed - ${error.message}`, 'error');
                }
            }
            
            const avgTime = totalTime / testIds.length;
            log(`📊 Multiple quotes test completed: ${successCount}/${testIds.length} successful`, 'info');
            log(`📊 Average load time: ${avgTime.toFixed(2)}ms`, 'info');
            
            updateStatus('dataLoading', successCount > testIds.length / 2, `${successCount}/${testIds.length}`);
            updateStatus('performance', avgTime < 500, `${avgTime.toFixed(0)}ms avg`);
        }
        
        // Additional test functions
        async function testCORSConfiguration() {
            log('🌐 Testing CORS configuration...', 'info');

            try {
                const config = window.AppConfig;
                if (!config) {
                    throw new Error('App configuration not available');
                }

                // Test preflight request
                const response = await fetch(config.graphqlEndpoint, {
                    method: 'OPTIONS',
                    headers: {
                        'Content-Type': 'application/json',
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });

                log(`✅ CORS preflight response: ${response.status}`, 'success');

                // Check CORS headers
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };

                log('🔍 CORS Headers:', 'info');
                Object.entries(corsHeaders).forEach(([key, value]) => {
                    log(`  ${key}: ${value || 'Not set'}`, 'info');
                });

                updateStatus('corsStatus', true, 'Headers OK');

            } catch (error) {
                log(`❌ CORS test failed: ${error.message}`, 'error');
                updateStatus('corsStatus', false, error.message);
            }
        }

        async function runPerformanceTest() {
            log('🏃‍♂️ Running performance test...', 'info');

            const iterations = 5;
            let totalApiTime = 0;
            let totalRenderTime = 0;

            for (let i = 0; i < iterations; i++) {
                try {
                    const startTime = performance.now();
                    const quote = await window.ApiClient.getQuoteById(1);
                    const apiTime = performance.now() - startTime;

                    const renderStartTime = performance.now();
                    await renderQuoteInDisplay(quote);
                    const renderTime = performance.now() - renderStartTime;

                    totalApiTime += apiTime;
                    totalRenderTime += renderTime;

                    log(`Iteration ${i + 1}: API ${apiTime.toFixed(2)}ms, Render ${renderTime.toFixed(2)}ms`, 'info');

                    await new Promise(resolve => setTimeout(resolve, 100));
                } catch (error) {
                    log(`❌ Performance test iteration ${i + 1} failed: ${error.message}`, 'error');
                }
            }

            const avgApiTime = totalApiTime / iterations;
            const avgRenderTime = totalRenderTime / iterations;
            const avgTotalTime = avgApiTime + avgRenderTime;

            updatePerformanceMetric('apiResponseTime', avgApiTime);
            updatePerformanceMetric('componentRenderTime', avgRenderTime);
            updatePerformanceMetric('totalLoadTime', avgTotalTime);

            log(`📊 Performance test completed (${iterations} iterations):`, 'success');
            log(`  Average API time: ${avgApiTime.toFixed(2)}ms`, 'info');
            log(`  Average render time: ${avgRenderTime.toFixed(2)}ms`, 'info');
            log(`  Average total time: ${avgTotalTime.toFixed(2)}ms`, 'info');

            updateStatus('performance', avgTotalTime < 1000, `${avgTotalTime.toFixed(0)}ms avg`);
        }

        function compareDataStructures() {
            log('🔍 Comparing data structures between local and production...', 'info');

            if (!localDataSample || !productionDataSample) {
                log('⚠ Need both local and production data samples for comparison', 'warning');
                log('💡 Switch between APIs and load the same quote ID to collect samples', 'info');
                return;
            }

            // Compare data structures
            const localKeys = Object.keys(localDataSample);
            const productionKeys = Object.keys(productionDataSample);

            const missingInProduction = localKeys.filter(key => !productionKeys.includes(key));
            const missingInLocal = productionKeys.filter(key => !localKeys.includes(key));

            if (missingInProduction.length === 0 && missingInLocal.length === 0) {
                log('✅ Data structures are compatible', 'success');
            } else {
                log('⚠ Data structure differences found:', 'warning');
                if (missingInProduction.length > 0) {
                    log(`  Missing in production: ${missingInProduction.join(', ')}`, 'warning');
                }
                if (missingInLocal.length > 0) {
                    log(`  Missing in local: ${missingInLocal.join(', ')}`, 'warning');
                }
            }

            // Compare specific fields
            const fieldsToCompare = ['id', 'content', 'author', 'categories', 'sources'];
            fieldsToCompare.forEach(field => {
                const localValue = localDataSample[field];
                const productionValue = productionDataSample[field];

                if (JSON.stringify(localValue) === JSON.stringify(productionValue)) {
                    log(`✅ Field '${field}' matches between environments`, 'success');
                } else {
                    log(`⚠ Field '${field}' differs between environments`, 'warning');
                }
            });
        }

        function validateDataIntegrity() {
            log('✅ Validating data integrity...', 'info');

            const currentMode = window.QuoteseAPIMode?.getCurrentMode();
            const sample = currentMode?.isUsingProduction ? productionDataSample : localDataSample;

            if (!sample) {
                log('⚠ No data sample available for validation', 'warning');
                return;
            }

            // Validate required fields
            const requiredFields = ['id', 'content', 'author'];
            const missingFields = requiredFields.filter(field => !sample[field]);

            if (missingFields.length === 0) {
                log('✅ All required fields present', 'success');
            } else {
                log(`❌ Missing required fields: ${missingFields.join(', ')}`, 'error');
            }

            // Validate data types
            if (typeof sample.id === 'number') {
                log('✅ ID field is numeric', 'success');
            } else {
                log('❌ ID field should be numeric', 'error');
            }

            if (typeof sample.content === 'string' && sample.content.length > 0) {
                log('✅ Content field is valid string', 'success');
            } else {
                log('❌ Content field should be non-empty string', 'error');
            }

            if (sample.author && typeof sample.author.name === 'string') {
                log('✅ Author name is valid', 'success');
            } else {
                log('❌ Author name should be a string', 'error');
            }
        }

        async function testQuoteDetailPage() {
            log('📄 Testing complete quote detail page functionality...', 'info');

            try {
                // Test main quote loading
                await loadQuoteDetail();

                // Test related quotes
                if (window.ApiClient && productionDataSample?.author?.id) {
                    log('🔗 Testing related quotes...', 'info');
                    const relatedQuotes = await window.ApiClient.getRelatedQuotesByAuthor(
                        productionDataSample.author.id,
                        productionDataSample.id,
                        5
                    );
                    log(`✅ Related quotes loaded: ${relatedQuotes?.length || 0} quotes`, 'success');
                }

                // Test sidebar data
                log('📊 Testing sidebar data...', 'info');
                const [categories, authors, sources] = await Promise.all([
                    window.ApiClient.getCategories(10),
                    window.ApiClient.getAuthors(10),
                    window.ApiClient.getSources(10)
                ]);

                log(`✅ Categories loaded: ${categories?.length || 0}`, 'success');
                log(`✅ Authors loaded: ${authors?.length || 0}`, 'success');
                log(`✅ Sources loaded: ${sources?.length || 0}`, 'success');

                log('🎉 Quote detail page functionality test completed successfully', 'success');

            } catch (error) {
                log(`❌ Quote detail page test failed: ${error.message}`, 'error');
            }
        }

        async function runFullProductionTest() {
            log('=== 🧪 Starting Full Production API Test ===', 'info');

            try {
                // Test 1: API Connection
                log('Step 1: Testing API connection...', 'info');
                await testAPIConnection();

                // Test 2: CORS Configuration
                log('Step 2: Testing CORS configuration...', 'info');
                await testCORSConfiguration();

                // Test 3: Quote Detail Page
                log('Step 3: Testing quote detail page...', 'info');
                await testQuoteDetailPage();

                // Test 4: Performance
                log('Step 4: Running performance test...', 'info');
                await runPerformanceTest();

                // Test 5: Data validation
                log('Step 5: Validating data integrity...', 'info');
                validateDataIntegrity();

                // Summary
                const passedTests = Object.values(testResults).filter(Boolean).length;
                const totalTests = Object.keys(testResults).length;

                log('=== 📊 Production API Test Summary ===', 'info');
                log(`Tests passed: ${passedTests}/${totalTests}`, passedTests === totalTests ? 'success' : 'warning');

                if (passedTests === totalTests) {
                    log('🎉 All production API tests passed! Ready for deployment.', 'success');
                } else {
                    log('⚠ Some tests failed. Please review the issues before deployment.', 'warning');
                }

            } catch (error) {
                log(`❌ Full production test failed: ${error.message}`, 'error');
            }
        }

        function exportTestReport() {
            const logContent = document.getElementById('test-log').textContent;
            const currentConfig = window.QuoteseAPIMode?.getCurrentMode();

            const report = `
Production API Comprehensive Test Report
Generated: ${new Date().toISOString()}

Configuration:
${JSON.stringify(currentConfig, null, 2)}

Test Results:
${JSON.stringify(testResults, null, 2)}

Performance Metrics:
${JSON.stringify(performanceMetrics, null, 2)}

Data Samples:
Local: ${localDataSample ? 'Available' : 'Not Available'}
Production: ${productionDataSample ? 'Available' : 'Not Available'}

Detailed Log:
${logContent}
            `;

            const blob = new Blob([report], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `production-api-test-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);

            log('📊 Test report exported successfully', 'success');
        }

        async function copyLogToClipboard() {
            try {
                const logContent = document.getElementById('test-log').textContent;
                await navigator.clipboard.writeText(logContent);
                log('📋 Log copied to clipboard', 'success');
            } catch (error) {
                log('❌ Failed to copy to clipboard', 'error');
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            log('🚀 Production API comprehensive test environment initialized', 'success');
            updateCurrentConfig();

            // Check current API mode
            const currentMode = window.QuoteseAPIMode?.getCurrentMode();
            if (currentMode?.isUsingProduction) {
                log('✅ Currently using production API', 'success');
                log('🧪 Ready to run production tests', 'info');
            } else {
                log('ℹ Currently using local API', 'info');
                log('💡 Click "Switch to Production API" to test with production data', 'info');
            }

            // Auto-test API connection
            setTimeout(() => {
                testAPIConnection();
            }, 1000);
        });
    </script>
</body>
</html>
