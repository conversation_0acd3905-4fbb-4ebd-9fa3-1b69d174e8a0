<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Manager Test - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .seo-tag {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px;
            margin: 4px 0;
        }
        .seo-section {
            margin-bottom: 24px;
            padding: 16px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-search mr-3 text-yellow-500"></i>
                SEO Manager Test Page
            </h1>
            
            <!-- 测试控制面板 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">Test Controls</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    <button onclick="testHomePage()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Test Home Page
                    </button>
                    <button onclick="testAuthorPage()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        Test Author Page
                    </button>
                    <button onclick="testCategoryPage()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        Test Category Page
                    </button>
                    <button onclick="testSourcePage()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                        Test Source Page
                    </button>
                    <button onclick="testQuotePage()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        Test Quote Page
                    </button>
                    <button onclick="validateSEO()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        Validate SEO
                    </button>
                </div>
                
                <div class="flex gap-4">
                    <button onclick="getCurrentSEO()" class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                        Get Current SEO
                    </button>
                    <button onclick="clearResults()" class="bg-gray-400 text-white px-4 py-2 rounded hover:bg-gray-500">
                        Clear Results
                    </button>
                </div>
            </div>
            
            <!-- 结果显示区域 -->
            <div id="results" class="space-y-6">
                <!-- 结果将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- Core Scripts -->
    <script src="js/url-handler.js"></script>
    <script src="js/seo-manager.js"></script>
    
    <script>
        // 测试函数
        function testHomePage() {
            const pageData = {
                pageType: 'home',
                params: {},
                canonicalUrl: 'https://quotese.com/'
            };
            
            SEOManager.updatePageSEO(pageData);
            displayResults('Home Page SEO Test', pageData);
        }
        
        function testAuthorPage() {
            const pageData = {
                pageType: 'author-detail',
                params: {
                    authorSlug: 'albert-einstein',
                    authorName: 'Albert Einstein'
                },
                canonicalUrl: 'https://quotese.com/authors/albert-einstein/',
                author: 'Albert Einstein',
                section: 'Authors'
            };
            
            SEOManager.updatePageSEO(pageData);
            displayResults('Author Page SEO Test', pageData);
        }
        
        function testCategoryPage() {
            const pageData = {
                pageType: 'category-detail',
                params: {
                    categorySlug: 'inspirational',
                    categoryName: 'Inspirational'
                },
                canonicalUrl: 'https://quotese.com/categories/inspirational/',
                section: 'Categories'
            };
            
            SEOManager.updatePageSEO(pageData);
            displayResults('Category Page SEO Test', pageData);
        }
        
        function testSourcePage() {
            const pageData = {
                pageType: 'source-detail',
                params: {
                    sourceSlug: 'the-art-of-war',
                    sourceName: 'The Art of War'
                },
                canonicalUrl: 'https://quotese.com/sources/the-art-of-war/',
                section: 'Sources'
            };
            
            SEOManager.updatePageSEO(pageData);
            displayResults('Source Page SEO Test', pageData);
        }
        
        function testQuotePage() {
            const pageData = {
                pageType: 'quote-detail',
                params: {
                    quoteId: 123
                },
                canonicalUrl: 'https://quotese.com/quotes/123/',
                quoteText: 'Imagination is more important than knowledge.',
                authorName: 'Albert Einstein',
                section: 'Quotes'
            };
            
            SEOManager.updatePageSEO(pageData);
            displayResults('Quote Page SEO Test', pageData);
        }
        
        function validateSEO() {
            const validation = SEOManager.validateSEO();
            displayValidationResults(validation);
        }
        
        function getCurrentSEO() {
            const currentSEO = SEOManager.getCurrentSEOData();
            displayCurrentSEO(currentSEO);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function displayResults(testName, pageData) {
            const resultsDiv = document.getElementById('results');
            
            const resultHTML = `
                <div class="seo-section bg-white rounded-lg shadow-md">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">${testName}</h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium mb-2">Input Data:</h4>
                            <div class="seo-tag">${JSON.stringify(pageData, null, 2)}</div>
                        </div>
                        
                        <div>
                            <h4 class="font-medium mb-2">Generated SEO Tags:</h4>
                            <div class="space-y-2">
                                <div class="seo-tag">
                                    <strong>Title:</strong> ${document.title}
                                </div>
                                <div class="seo-tag">
                                    <strong>Description:</strong> ${getMetaContent('name', 'description') || 'Not set'}
                                </div>
                                <div class="seo-tag">
                                    <strong>Keywords:</strong> ${getMetaContent('name', 'keywords') || 'Not set'}
                                </div>
                                <div class="seo-tag">
                                    <strong>OG Title:</strong> ${getMetaContent('property', 'og:title') || 'Not set'}
                                </div>
                                <div class="seo-tag">
                                    <strong>OG Description:</strong> ${getMetaContent('property', 'og:description') || 'Not set'}
                                </div>
                                <div class="seo-tag">
                                    <strong>Canonical URL:</strong> ${getLinkHref('rel', 'canonical') || 'Not set'}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            resultsDiv.insertAdjacentHTML('beforeend', resultHTML);
        }
        
        function displayValidationResults(validation) {
            const resultsDiv = document.getElementById('results');
            
            const statusClass = validation.valid ? 'text-green-600' : 'text-red-600';
            const statusIcon = validation.valid ? 'fa-check-circle' : 'fa-exclamation-triangle';
            
            const resultHTML = `
                <div class="seo-section bg-white rounded-lg shadow-md">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">
                        <i class="fas ${statusIcon} mr-2 ${statusClass}"></i>
                        SEO Validation Results
                    </h3>
                    
                    <div class="mb-4">
                        <span class="font-medium">Status: </span>
                        <span class="${statusClass}">${validation.valid ? 'Valid' : 'Invalid'}</span>
                    </div>
                    
                    ${validation.errors.length > 0 ? `
                        <div class="mb-4">
                            <h4 class="font-medium text-red-600 mb-2">Errors:</h4>
                            <ul class="list-disc list-inside space-y-1">
                                ${validation.errors.map(error => `<li class="text-red-600">${error}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                    
                    ${validation.warnings.length > 0 ? `
                        <div class="mb-4">
                            <h4 class="font-medium text-yellow-600 mb-2">Warnings:</h4>
                            <ul class="list-disc list-inside space-y-1">
                                ${validation.warnings.map(warning => `<li class="text-yellow-600">${warning}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            `;
            
            resultsDiv.insertAdjacentHTML('beforeend', resultHTML);
        }
        
        function displayCurrentSEO(seoData) {
            const resultsDiv = document.getElementById('results');
            
            const resultHTML = `
                <div class="seo-section bg-white rounded-lg shadow-md">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">Current SEO Data</h3>
                    <div class="seo-tag">${JSON.stringify(seoData, null, 2)}</div>
                </div>
            `;
            
            resultsDiv.insertAdjacentHTML('beforeend', resultHTML);
        }
        
        // 辅助函数
        function getMetaContent(attribute, value) {
            const metaTag = document.querySelector(`meta[${attribute}="${value}"]`);
            return metaTag ? metaTag.getAttribute('content') : null;
        }
        
        function getLinkHref(attribute, value) {
            const linkTag = document.querySelector(`link[${attribute}="${value}"]`);
            return linkTag ? linkTag.getAttribute('href') : null;
        }
        
        // 监听SEO更新事件
        window.addEventListener('seoUpdated', function(event) {
            console.log('SEO Updated Event:', event.detail);
        });
        
        // 页面加载完成后显示初始状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SEO Test Page loaded');
            console.log('SEOManager available:', typeof SEOManager !== 'undefined');
        });
    </script>
</body>
</html>
