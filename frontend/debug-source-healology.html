<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Source Healology - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .debug-section {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            background: #f9fafb;
        }
        .debug-result {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #1f2937;
            color: #f3f4f6;
            padding: 8px;
            border-radius: 4px;
            margin: 8px 0;
            white-space: pre-wrap;
        }
        .debug-success {
            background: #065f46;
            color: #d1fae5;
        }
        .debug-error {
            background: #7f1d1d;
            color: #fecaca;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-bug mr-3 text-red-500"></i>
                Debug Source Healology URL Issue
            </h1>
            
            <!-- URL信息 -->
            <div class="debug-section">
                <h2 class="text-xl font-semibold mb-4">URL信息</h2>
                <div id="url-info" class="debug-result">检测中...</div>
            </div>
            
            <!-- UrlHandler测试 -->
            <div class="debug-section">
                <h2 class="text-xl font-semibold mb-4">UrlHandler测试</h2>
                <div id="urlhandler-test" class="debug-result">检测中...</div>
            </div>
            
            <!-- PageRouter测试 -->
            <div class="debug-section">
                <h2 class="text-xl font-semibold mb-4">PageRouter测试</h2>
                <div id="pagerouter-test" class="debug-result">检测中...</div>
            </div>
            
            <!-- API测试 -->
            <div class="debug-section">
                <h2 class="text-xl font-semibold mb-4">API测试</h2>
                <div id="api-test" class="debug-result">检测中...</div>
            </div>
            
            <!-- 手动初始化测试 -->
            <div class="debug-section">
                <h2 class="text-xl font-semibold mb-4">手动初始化测试</h2>
                <button onclick="testManualInit()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mb-4">
                    <i class="fas fa-play mr-2"></i>手动初始化Source页面
                </button>
                <div id="manual-init-test" class="debug-result">点击按钮开始测试...</div>
            </div>
            
            <!-- 错误日志 -->
            <div class="debug-section">
                <h2 class="text-xl font-semibold mb-4">错误日志</h2>
                <div id="error-log" class="debug-result">监听中...</div>
            </div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/debug.js"></script>
    <script src="js/component-loader.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/theme.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/seo-manager.js"></script>
    <script src="js/page-router.js"></script>
    <script src="js/mobile-menu.js"></script>
    <script src="js/components/pagination.js"></script>
    <script src="js/components/quote-card.js"></script>
    <script src="js/components/breadcrumb.js"></script>
    <script src="js/social-meta.js"></script>
    <script src="js/global-fix.js"></script>
    <script src="js/pages/source.js"></script>

    <script>
        // 错误监听
        const errorLog = document.getElementById('error-log');
        let errorCount = 0;
        
        window.addEventListener('error', function(e) {
            errorCount++;
            const errorInfo = `[${errorCount}] ${e.error ? e.error.stack : e.message}\n文件: ${e.filename}:${e.lineno}:${e.colno}\n`;
            errorLog.textContent += errorInfo;
            errorLog.className = 'debug-result debug-error';
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            errorCount++;
            const errorInfo = `[${errorCount}] Promise Rejection: ${e.reason}\n`;
            errorLog.textContent += errorInfo;
            errorLog.className = 'debug-result debug-error';
        });
        
        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runDebugTests, 1000);
        });
        
        function runDebugTests() {
            testUrlInfo();
            testUrlHandler();
            testPageRouter();
            testAPI();
        }
        
        function testUrlInfo() {
            const urlInfo = document.getElementById('url-info');
            try {
                const info = {
                    'Current URL': window.location.href,
                    'Pathname': window.location.pathname,
                    'Search': window.location.search,
                    'Hash': window.location.hash
                };
                
                urlInfo.textContent = JSON.stringify(info, null, 2);
                urlInfo.className = 'debug-result debug-success';
            } catch (error) {
                urlInfo.textContent = `错误: ${error.message}`;
                urlInfo.className = 'debug-result debug-error';
            }
        }
        
        function testUrlHandler() {
            const urlHandlerTest = document.getElementById('urlhandler-test');
            try {
                const results = {};
                
                // 检查UrlHandler是否存在
                results['UrlHandler存在'] = !!window.UrlHandler;
                
                if (window.UrlHandler) {
                    // 测试parseSourceFromPath
                    results['parseSourceFromPath()'] = window.UrlHandler.parseSourceFromPath();
                    
                    // 测试getCurrentPageType
                    results['getCurrentPageType()'] = window.UrlHandler.getCurrentPageType();
                    
                    // 测试isValidSlug
                    results['isValidSlug("healology")'] = window.UrlHandler.isValidSlug('healology');
                    
                    // 测试deslugify
                    if (window.UrlHandler.parseSourceFromPath()) {
                        results['deslugify(slug)'] = window.UrlHandler.deslugify(window.UrlHandler.parseSourceFromPath());
                    }
                }
                
                urlHandlerTest.textContent = JSON.stringify(results, null, 2);
                urlHandlerTest.className = 'debug-result debug-success';
            } catch (error) {
                urlHandlerTest.textContent = `错误: ${error.message}\n${error.stack}`;
                urlHandlerTest.className = 'debug-result debug-error';
            }
        }
        
        function testPageRouter() {
            const pageRouterTest = document.getElementById('pagerouter-test');
            try {
                const results = {};
                
                // 检查PageRouter是否存在
                results['PageRouter存在'] = !!window.PageRouter;
                
                if (window.PageRouter) {
                    // 测试getCurrentPageType
                    results['getCurrentPageType()'] = window.PageRouter.getCurrentPageType();
                    
                    // 测试getPageParams
                    results['getPageParams()'] = window.PageRouter.getPageParams();
                    
                    // 测试validateParams
                    const params = window.PageRouter.getPageParams();
                    results['validateParams()'] = window.PageRouter.validateParams(params);
                }
                
                pageRouterTest.textContent = JSON.stringify(results, null, 2);
                pageRouterTest.className = 'debug-result debug-success';
            } catch (error) {
                pageRouterTest.textContent = `错误: ${error.message}\n${error.stack}`;
                pageRouterTest.className = 'debug-result debug-error';
            }
        }
        
        function testAPI() {
            const apiTest = document.getElementById('api-test');
            try {
                const results = {};
                
                // 检查ApiClient是否存在
                results['ApiClient存在'] = !!window.ApiClient;
                
                if (window.ApiClient) {
                    // 测试getSourceByName方法是否存在
                    results['getSourceByName方法存在'] = typeof window.ApiClient.getSourceByName === 'function';
                    
                    // 测试其他方法
                    results['getQuotes方法存在'] = typeof window.ApiClient.getQuotes === 'function';
                    results['getPopularCategories方法存在'] = typeof window.ApiClient.getPopularCategories === 'function';
                }
                
                apiTest.textContent = JSON.stringify(results, null, 2);
                apiTest.className = 'debug-result debug-success';
            } catch (error) {
                apiTest.textContent = `错误: ${error.message}\n${error.stack}`;
                apiTest.className = 'debug-result debug-error';
            }
        }
        
        async function testManualInit() {
            const manualInitTest = document.getElementById('manual-init-test');
            try {
                manualInitTest.textContent = '开始手动初始化...\n';
                manualInitTest.className = 'debug-result';
                
                // 检查initSourcePage函数是否存在
                if (typeof initSourcePage !== 'function') {
                    throw new Error('initSourcePage函数不存在');
                }
                
                manualInitTest.textContent += '找到initSourcePage函数\n';
                
                // 手动调用初始化函数
                const params = {
                    sourceSlug: 'healology',
                    sourceName: 'Healology'
                };
                
                manualInitTest.textContent += `调用initSourcePage，参数: ${JSON.stringify(params)}\n`;
                
                await initSourcePage(params);
                
                manualInitTest.textContent += '初始化完成！\n';
                manualInitTest.className = 'debug-result debug-success';
                
            } catch (error) {
                manualInitTest.textContent += `错误: ${error.message}\n${error.stack}`;
                manualInitTest.className = 'debug-result debug-error';
            }
        }
        
        // 模拟healology URL环境
        if (window.location.pathname !== '/sources/healology/') {
            console.log('当前不在healology页面，模拟URL环境');
            // 可以通过history API模拟URL
            // history.replaceState({}, '', '/sources/healology/');
        }
    </script>
</body>
</html>
