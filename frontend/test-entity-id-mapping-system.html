<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实体ID映射系统测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .stats-display { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .performance-metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .metric-card { background: white; padding: 10px; border-radius: 5px; border: 1px solid #ddd; text-align: center; }
    </style>
</head>
<body>
    <h1>🧪 实体ID映射系统测试</h1>
    <p>测试Categories、Authors、Sources页面的已知ID映射表实施效果</p>
    
    <!-- 加载必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/entity-id-mapper.js"></script>

    <div class="test-section">
        <h2>📊 系统状态</h2>
        <button onclick="showSystemStatus()">显示系统状态</button>
        <div id="system-status"></div>
    </div>

    <div class="test-section">
        <h2>🏷️ Categories映射测试</h2>
        <button onclick="testCategoryMappings()">测试类别映射</button>
        <div id="category-test-results"></div>
    </div>

    <div class="test-section">
        <h2>👤 Authors映射测试</h2>
        <button onclick="testAuthorMappings()">测试作者映射</button>
        <div id="author-test-results"></div>
    </div>

    <div class="test-section">
        <h2>📚 Sources映射测试</h2>
        <button onclick="testSourceMappings()">测试来源映射</button>
        <div id="source-test-results"></div>
    </div>

    <div class="test-section">
        <h2>⚡ 性能对比测试</h2>
        <button onclick="runPerformanceTest()">运行性能测试</button>
        <div id="performance-results"></div>
    </div>

    <div class="test-section">
        <h2>🔄 通用查找函数测试</h2>
        <button onclick="testUniversalFinder()">测试通用查找</button>
        <div id="universal-finder-results"></div>
    </div>

    <script>
        function addResult(containerId, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById(containerId).appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 显示系统状态
        function showSystemStatus() {
            const container = 'system-status';
            clearResults(container);
            
            addResult(container, '📋 EntityIdMapper系统状态:', 'info');
            
            // 显示各实体类型的映射统计
            const entityTypes = ['categories', 'authors', 'sources'];
            
            entityTypes.forEach(entityType => {
                const stats = window.EntityIdMapper.getMappingStats(entityType);
                addResult(container, 
                    `📊 ${entityType.toUpperCase()}: ${stats.found}/${stats.total} 个实体有ID (${stats.coverage})`, 
                    stats.found > 0 ? 'success' : 'warning'
                );
            });
            
            // 显示运行时统计
            const runtimeStats = window.EntityIdMapper.getStats();
            addResult(container, 
                `🎯 运行时统计: 命中 ${runtimeStats.hits} 次, 未命中 ${runtimeStats.misses} 次, API查询 ${runtimeStats.apiQueries} 次 (命中率: ${runtimeStats.hitRate})`, 
                'info'
            );
        }

        // 测试类别映射
        async function testCategoryMappings() {
            const container = 'category-test-results';
            clearResults(container);
            
            addResult(container, '🔍 测试类别映射...', 'info');
            
            // 确保使用生产API
            window.ApiClient.useMockData = false;
            
            const testCategories = ['life', 'writing', 'friendship', 'wisdom', 'love'];
            
            for (const categorySlug of testCategories) {
                try {
                    const startTime = performance.now();
                    
                    // 使用通用查找函数
                    const categoryName = window.UrlHandler.deslugify(categorySlug);
                    const result = await window.findEntityWithPriority(
                        'categories', 
                        categorySlug, 
                        categoryName, 
                        window.ApiClient.getCategoryByName.bind(window.ApiClient)
                    );
                    
                    const endTime = performance.now();
                    const duration = Math.round(endTime - startTime);
                    
                    if (result) {
                        const source = result.fromCache ? '缓存' : 'API';
                        addResult(container, 
                            `✅ "${categorySlug}" → ID: ${result.id} (${source}, ${duration}ms)`, 
                            'success'
                        );
                    } else {
                        addResult(container, `❌ "${categorySlug}" 查找失败 (${duration}ms)`, 'error');
                    }
                    
                } catch (error) {
                    addResult(container, `❌ "${categorySlug}" 异常: ${error.message}`, 'error');
                }
            }
        }

        // 测试作者映射
        async function testAuthorMappings() {
            const container = 'author-test-results';
            clearResults(container);
            
            addResult(container, '🔍 测试作者映射...', 'info');
            
            // 确保使用生产API
            window.ApiClient.useMockData = false;
            
            const testAuthors = ['albert-einstein', 'steve-jobs', 'pearl-zhu', 'mark-twain', 'oscar-wilde'];
            
            for (const authorSlug of testAuthors) {
                try {
                    const startTime = performance.now();
                    
                    // 使用通用查找函数
                    const authorName = window.UrlHandler.deslugify(authorSlug);
                    const result = await window.findEntityWithPriority(
                        'authors', 
                        authorSlug, 
                        authorName, 
                        window.ApiClient.getAuthorByName.bind(window.ApiClient)
                    );
                    
                    const endTime = performance.now();
                    const duration = Math.round(endTime - startTime);
                    
                    if (result) {
                        const source = result.fromCache ? '缓存' : 'API';
                        addResult(container, 
                            `✅ "${authorSlug}" → ID: ${result.id} (${source}, ${duration}ms)`, 
                            'success'
                        );
                    } else {
                        addResult(container, `❌ "${authorSlug}" 查找失败 (${duration}ms)`, 'error');
                    }
                    
                } catch (error) {
                    addResult(container, `❌ "${authorSlug}" 异常: ${error.message}`, 'error');
                }
            }
        }

        // 测试来源映射
        async function testSourceMappings() {
            const container = 'source-test-results';
            clearResults(container);
            
            addResult(container, '🔍 测试来源映射...', 'info');
            
            // 确保使用生产API
            window.ApiClient.useMockData = false;
            
            const testSources = ['meditations', 'healology', 'interview', 'speech', 'letter'];
            
            for (const sourceSlug of testSources) {
                try {
                    const startTime = performance.now();
                    
                    // 使用通用查找函数
                    const sourceName = window.UrlHandler.deslugify(sourceSlug);
                    const result = await window.findEntityWithPriority(
                        'sources', 
                        sourceSlug, 
                        sourceName, 
                        window.ApiClient.getSourceByName.bind(window.ApiClient)
                    );
                    
                    const endTime = performance.now();
                    const duration = Math.round(endTime - startTime);
                    
                    if (result) {
                        const source = result.fromCache ? '缓存' : 'API';
                        addResult(container, 
                            `✅ "${sourceSlug}" → ID: ${result.id} (${source}, ${duration}ms)`, 
                            'success'
                        );
                    } else {
                        addResult(container, `❌ "${sourceSlug}" 查找失败 (${duration}ms)`, 'error');
                    }
                    
                } catch (error) {
                    addResult(container, `❌ "${sourceSlug}" 异常: ${error.message}`, 'error');
                }
            }
        }

        // 性能对比测试
        async function runPerformanceTest() {
            const container = 'performance-results';
            clearResults(container);
            
            addResult(container, '⚡ 运行性能对比测试...', 'info');
            
            // 重置统计
            window.EntityIdMapper.resetStats();
            
            // 测试已知实体（应该使用缓存）
            const knownEntities = [
                { type: 'categories', slug: 'life', name: 'Life' },
                { type: 'categories', slug: 'writing', name: 'Writing' },
                { type: 'authors', slug: 'albert-einstein', name: 'Albert Einstein' }
            ];
            
            let cacheHits = 0;
            let cacheTotalTime = 0;
            
            for (const entity of knownEntities) {
                const startTime = performance.now();
                const id = window.EntityIdMapper.getKnownId(entity.type, entity.slug);
                const endTime = performance.now();
                
                if (id) {
                    cacheHits++;
                    cacheTotalTime += (endTime - startTime);
                }
            }
            
            // 测试未知实体（需要API查询）
            window.ApiClient.useMockData = false;
            
            const unknownEntities = [
                { type: 'categories', slug: 'unknown-category', name: 'Unknown Category' }
            ];
            
            let apiQueries = 0;
            let apiTotalTime = 0;
            
            for (const entity of unknownEntities) {
                const startTime = performance.now();
                try {
                    await window.ApiClient.getCategoryByName(entity.name);
                } catch (error) {
                    // 预期会失败
                }
                const endTime = performance.now();
                
                apiQueries++;
                apiTotalTime += (endTime - startTime);
            }
            
            // 显示性能指标
            const avgCacheTime = cacheHits > 0 ? (cacheTotalTime / cacheHits).toFixed(2) : 0;
            const avgApiTime = apiQueries > 0 ? (apiTotalTime / apiQueries).toFixed(2) : 0;
            const speedup = avgApiTime > 0 ? Math.round(avgApiTime / avgCacheTime) : 0;
            
            addResult(container, `📊 性能对比结果:`, 'info');
            addResult(container, `🚀 缓存查询: ${cacheHits} 次, 平均 ${avgCacheTime}ms`, 'success');
            addResult(container, `🌐 API查询: ${apiQueries} 次, 平均 ${avgApiTime}ms`, 'warning');
            addResult(container, `⚡ 性能提升: ${speedup}x 倍`, 'success');
            
            // 显示最终统计
            const finalStats = window.EntityIdMapper.getStats();
            addResult(container, `📈 总体统计: ${finalStats.hitRate} 命中率`, 'info');
        }

        // 测试通用查找函数
        async function testUniversalFinder() {
            const container = 'universal-finder-results';
            clearResults(container);
            
            addResult(container, '🔄 测试通用查找函数...', 'info');
            
            // 确保使用生产API
            window.ApiClient.useMockData = false;
            
            const testCases = [
                {
                    type: 'categories',
                    slug: 'life',
                    name: 'Life',
                    apiMethod: window.ApiClient.getCategoryByName.bind(window.ApiClient)
                },
                {
                    type: 'authors',
                    slug: 'albert-einstein',
                    name: 'Albert Einstein',
                    apiMethod: window.ApiClient.getAuthorByName.bind(window.ApiClient)
                }
            ];
            
            for (const testCase of testCases) {
                try {
                    const startTime = performance.now();
                    
                    const result = await window.findEntityWithPriority(
                        testCase.type,
                        testCase.slug,
                        testCase.name,
                        testCase.apiMethod
                    );
                    
                    const endTime = performance.now();
                    const duration = Math.round(endTime - startTime);
                    
                    if (result) {
                        const source = result.fromCache ? '缓存命中' : 'API查询';
                        addResult(container, 
                            `✅ ${testCase.type}/${testCase.slug} → ID: ${result.id} (${source}, ${duration}ms)`, 
                            'success'
                        );
                    } else {
                        addResult(container, 
                            `❌ ${testCase.type}/${testCase.slug} 查找失败 (${duration}ms)`, 
                            'error'
                        );
                    }
                    
                } catch (error) {
                    addResult(container, 
                        `❌ ${testCase.type}/${testCase.slug} 异常: ${error.message}`, 
                        'error'
                    );
                }
            }
        }

        // 页面加载时显示系统状态
        window.addEventListener('load', () => {
            console.log('实体ID映射系统测试工具已加载');
            showSystemStatus();
        });
    </script>
</body>
</html>
