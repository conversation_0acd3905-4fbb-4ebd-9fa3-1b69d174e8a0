<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browse All Categories - Quotese.com</title>
    <meta name="description" content="Explore our complete collection of quote categories. Find inspirational quotes by topic including life, love, success, wisdom and more.">
    <meta name="keywords" content="quote categories, inspirational topics, quote themes, motivational categories">
    <link rel="canonical" href="https://quotese.com/categories/">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Browse All Quote Categories - Quotese.com">
    <meta property="og:description" content="Explore our complete collection of quote categories. Find inspirational quotes by topic.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://quotese.com/categories/">
    <meta property="og:site_name" content="Quotese.com">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Browse All Quote Categories - Quotese.com">
    <meta name="twitter:description" content="Explore our complete collection of quote categories. Find inspirational quotes by topic.">

    <!-- Stylesheets -->
    <link href="/css/dist/combined.css" rel="stylesheet">
    <link href="/css/pages/categories.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
    <!-- Navigation -->
    <div id="navigation-container"></div>

    <main class="container mx-auto px-4 py-8">
        <!-- Breadcrumb Navigation -->
        <nav id="breadcrumb-container" class="mb-6" aria-label="Breadcrumb">
            <!-- Breadcrumb will be loaded dynamically -->
        </nav>

        <!-- Page Header -->
        <header class="page-header mb-8">
            <h1 id="page-title" class="text-3xl md:text-4xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                Browse All Categories
            </h1>
            <p id="page-description" class="text-lg text-gray-600 dark:text-gray-400 max-w-3xl">
                Explore our complete collection of quote categories. Find inspiration by topic and discover new themes that resonate with your interests.
            </p>
        </header>

        <!-- Search and Control Toolbar -->
        <section class="toolbar mb-6">
            <div class="flex flex-col md:flex-row gap-4 items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                <!-- Search Container -->
                <div class="search-container flex-1 max-w-md w-full">
                    <div class="relative">
                        <input type="text"
                               id="categories-search"
                               placeholder="Search categories..."
                               class="w-full px-4 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                               autocomplete="off">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <!-- Control Options -->
                <div class="controls flex gap-4 items-center">
                    <!-- Sort Select -->
                    <div class="flex items-center gap-2">
                        <label for="sort-select" class="text-sm font-medium text-gray-700 dark:text-gray-300">Sort:</label>
                        <select id="sort-select" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500">
                            <option value="popularity">Most Popular</option>
                            <option value="alphabetical">A-Z</option>
                            <option value="count">Quote Count</option>
                        </select>
                    </div>

                    <!-- View Select -->
                    <div class="flex items-center gap-2">
                        <label for="view-select" class="text-sm font-medium text-gray-700 dark:text-gray-300">View:</label>
                        <select id="view-select" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500">
                            <option value="grid">Grid View</option>
                            <option value="list">List View</option>
                        </select>
                    </div>
                </div>
            </div>
        </section>

        <!-- Statistics Information -->
        <section class="stats mb-6">
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <p class="text-sm text-blue-800 dark:text-blue-200">
                        <i class="fas fa-info-circle mr-2"></i>
                        Showing <span id="showing-count" class="font-semibold">0</span> of
                        <span id="total-count" class="font-semibold">0</span> categories
                    </p>
                    <div class="flex gap-2">
                        <button id="test-manual-btn" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 text-sm font-medium">
                            <i class="fas fa-play mr-1"></i>Manual Test
                        </button>
                        <button id="refresh-btn" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm font-medium">
                            <i class="fas fa-refresh mr-1"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Content Area -->
        <section class="main-content">
            <!-- Loading State -->
            <div id="loading-container" class="text-center py-12" style="display: none;">
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-gray-600 dark:text-gray-400">Loading categories...</p>
            </div>

            <!-- Error State -->
            <div id="error-container" class="text-center py-12" style="display: none;">
                <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Failed to load categories</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">Please try refreshing the page</p>
                <button onclick="location.reload()" class="btn-primary">
                    <i class="fas fa-refresh mr-2"></i>Refresh Page
                </button>
            </div>

            <!-- Categories Display Area -->
            <div id="categories-container" class="categories-display">
                <!-- Categories will be loaded dynamically -->
            </div>

            <!-- Load More Button -->
            <div id="load-more-container" class="text-center mt-8" style="display: none;">
                <button id="load-more-btn" class="btn-primary px-6 py-3">
                    <i class="fas fa-plus mr-2"></i>Load More Categories
                </button>
            </div>

            <!-- Pagination Container -->
            <div id="pagination-container" class="mt-8">
                <!-- Pagination will be generated dynamically -->
            </div>
        </section>


    </main>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Scripts -->
    <script src="/js/config.js?v=20250626"></script> <!-- Load config first -->
    <script src="/js/api-client.js?v=20250626"></script> <!-- Load API client with GraphQL support -->
    <script src="/js/url-handler.js?v=20250626"></script>
    <script src="/js/page-router.js?v=20250626"></script>
    <script src="/js/seo-manager.js?v=20250626"></script>
    <script src="/js/components/breadcrumb.js?v=20250626"></script>
    <script src="/js/components/navigation.js?v=20250626"></script>
    <script src="/js/components/footer.js?v=20250626"></script>
    <script src="/js/pages/categories.js?v=20250626"></script>

    <!-- Initialize Page -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Categories page DOM loaded, initializing...');

            // Debug: Check all dependencies
            console.log('🔍 Checking dependencies...');
            console.log('- PageRouter:', !!window.PageRouter);
            console.log('- ApiClient:', !!window.ApiClient);
            console.log('- ComponentLoader:', !!window.ComponentLoader);
            console.log('- UrlHandler:', !!window.UrlHandler);
            console.log('- initCategoriesListPage:', !!window.initCategoriesListPage);

            // Check current page type
            if (window.UrlHandler) {
                const pageType = window.UrlHandler.getCurrentPageType();
                console.log('📄 Current page type:', pageType);
            }

            // Wait a bit for all scripts to load, then initialize
            setTimeout(() => {
                console.log('⏰ Delayed initialization starting...');

                // Try direct initialization first
                if (window.initCategoriesListPage) {
                    console.log('🎯 Calling initCategoriesListPage directly...');
                    window.initCategoriesListPage({}).then(() => {
                        console.log('✅ Direct initialization successful');
                    }).catch(error => {
                        console.error('❌ Direct initialization failed:', error);

                        // Fallback to PageRouter
                        if (window.PageRouter) {
                            console.log('🔄 Trying PageRouter fallback...');
                            window.PageRouter.initializePage().catch(routerError => {
                                console.error('❌ PageRouter initialization failed:', routerError);
                                showErrorFallback();
                            });
                        } else {
                            console.error('❌ PageRouter not available');
                            showErrorFallback();
                        }
                    });
                } else if (window.PageRouter) {
                    console.log('🔄 Using PageRouter initialization...');
                    window.PageRouter.initializePage().catch(error => {
                        console.error('❌ PageRouter initialization failed:', error);
                        showErrorFallback();
                    });
                } else {
                    console.error('❌ No initialization method available');
                    showErrorFallback();
                }
            }, 1000); // Wait 1 second for all scripts to load

            function showErrorFallback() {
                console.log('🚨 Showing error fallback...');
                const errorContainer = document.getElementById('error-container');
                const loadingContainer = document.getElementById('loading-container');

                if (errorContainer) {
                    errorContainer.style.display = 'block';
                    errorContainer.innerHTML = `
                        <div class="text-center py-12">
                            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                            <h3 class="text-lg font-semibold mb-2">页面加载失败</h3>
                            <p class="text-gray-600 mb-4">请检查浏览器控制台获取详细错误信息</p>
                            <button onclick="location.reload()" class="btn-primary">
                                <i class="fas fa-refresh mr-2"></i>刷新页面
                            </button>
                        </div>
                    `;
                }

                if (loadingContainer) {
                    loadingContainer.style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
