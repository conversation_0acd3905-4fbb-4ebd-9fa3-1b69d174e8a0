<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Detail Fix Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .page-link {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .page-link:hover { background-color: #218838; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Quote Detail Fix Verification</h1>
    <p>This page verifies that the JavaScript errors in the quote detail page have been resolved.</p>

    <div class="test-section">
        <h2>Fix Summary</h2>
        <div class="info test-result">
            <strong>Issues Fixed:</strong>
            <ul>
                <li>✅ Fixed API call in loadAuthors() - changed from getAuthors(5) to getAuthors(1, 5)</li>
                <li>✅ Enhanced slugify() function with better error handling</li>
                <li>✅ Improved getAuthorUrl() with fallback mechanisms</li>
                <li>✅ Added robust error handling for edge cases</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>URL Handler Tests</h2>
        <button onclick="testUrlHandler()">Test URL Handler Functions</button>
        <div id="url-handler-results"></div>
    </div>

    <div class="test-section">
        <h2>Quote Detail Page Test</h2>
        <button onclick="testQuoteDetailPage()">Test Quote Detail Loading</button>
        <a href="/quotes/499001/?use-production-api=true" target="_blank" class="page-link">Open Quote Detail Page</a>
        <div id="quote-detail-results"></div>
    </div>

    <div class="test-section">
        <h2>Authors Loading Test</h2>
        <button onclick="testAuthorsLoading()">Test Authors Loading</button>
        <div id="authors-loading-results"></div>
    </div>

    <div class="test-section">
        <h2>Complete Integration Test</h2>
        <button onclick="runCompleteTest()">Run All Tests</button>
        <div id="complete-test-results"></div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>

    <script>
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
        }

        function testUrlHandler() {
            const container = 'url-handler-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing URL handler functions...');
            
            // Test slugify function
            const testCases = [
                { input: 'Adam Sandler', expected: 'adam-sandler' },
                { input: 'Albert Einstein', expected: 'albert-einstein' },
                { input: '', expected: '' },
                { input: null, expected: '' },
                { input: undefined, expected: '' },
                { input: '   ', expected: '' },
                { input: 'Jean-Paul Sartre', expected: 'jean-paul-sartre' },
                { input: 'José Martí', expected: 'jos-mart' }
            ];
            
            testCases.forEach(testCase => {
                try {
                    const result = UrlHandler.slugify(testCase.input);
                    const passed = result === testCase.expected;
                    addResult(container, passed ? 'success' : 'warning', 
                        `slugify("${testCase.input}") → "${result}" ${passed ? '✅' : '⚠️ Expected: "' + testCase.expected + '"'}`);
                } catch (error) {
                    addResult(container, 'error', `slugify("${testCase.input}") → ERROR: ${error.message}`);
                }
            });
            
            // Test getAuthorUrl function
            const authorTestCases = [
                { name: 'Adam Sandler', id: 1145 },
                { name: 'Albert Einstein', id: 1 },
                { name: '', id: 2 },
                { name: null, id: 3 },
                { id: 4 },
                null,
                undefined
            ];
            
            authorTestCases.forEach((author, index) => {
                try {
                    const url = UrlHandler.getAuthorUrl(author);
                    addResult(container, 'success', 
                        `getAuthorUrl(${JSON.stringify(author)}) → ${url}`);
                } catch (error) {
                    addResult(container, 'error', 
                        `getAuthorUrl(${JSON.stringify(author)}) → ERROR: ${error.message}`);
                }
            });
        }

        async function testQuoteDetailPage() {
            const container = 'quote-detail-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing quote detail page loading...');
            
            try {
                // Switch to production API
                if (window.QuoteseAPIMode && typeof window.QuoteseAPIMode.useProductionAPI === 'function') {
                    window.QuoteseAPIMode.useProductionAPI();
                    addResult(container, 'success', 'Switched to production API');
                }
                
                // Test the exact quote that was causing issues
                const quote = await window.ApiClient.getQuoteById('499001');
                
                if (quote) {
                    addResult(container, 'success', 
                        `Quote loaded: "${quote.content.substring(0, 50)}..." by ${quote.author.name}`);
                    
                    // Test URL generation for this specific author
                    try {
                        const authorUrl = UrlHandler.getAuthorUrl(quote.author);
                        addResult(container, 'success', `Author URL generated: ${authorUrl}`);
                    } catch (error) {
                        addResult(container, 'error', `Author URL generation failed: ${error.message}`);
                    }
                    
                    return quote;
                } else {
                    addResult(container, 'error', 'Failed to load quote data');
                    return null;
                }
                
            } catch (error) {
                addResult(container, 'error', `Quote detail test failed: ${error.message}`);
                return null;
            }
        }

        async function testAuthorsLoading() {
            const container = 'authors-loading-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing authors loading...');
            
            try {
                // Test the corrected API call
                addResult(container, 'info', 'Calling getAuthors(1, 5)...');
                const authorsData = await window.ApiClient.getAuthors(1, 5);
                
                if (authorsData && authorsData.authors) {
                    addResult(container, 'success', 
                        `Authors loaded: ${authorsData.authors.length} authors`);
                    
                    // Test URL generation for each author
                    authorsData.authors.forEach((author, index) => {
                        try {
                            const url = UrlHandler.getAuthorUrl(author);
                            addResult(container, 'success', 
                                `Author ${index + 1}: ${author.name} → ${url}`);
                        } catch (error) {
                            addResult(container, 'error', 
                                `Author ${index + 1}: ${author.name} → ERROR: ${error.message}`);
                        }
                    });
                    
                    return authorsData;
                } else {
                    addResult(container, 'error', 'Failed to load authors data');
                    return null;
                }
                
            } catch (error) {
                addResult(container, 'error', `Authors loading test failed: ${error.message}`);
                return null;
            }
        }

        async function runCompleteTest() {
            const container = 'complete-test-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Running complete integration test...');
            
            let testsPassed = 0;
            let totalTests = 0;
            
            // Test 1: URL Handler
            totalTests++;
            try {
                testUrlHandler();
                testsPassed++;
                addResult(container, 'success', 'Test 1: URL Handler functions passed');
            } catch (error) {
                addResult(container, 'error', `Test 1: URL Handler functions failed: ${error.message}`);
            }
            
            // Test 2: Quote Detail Loading
            totalTests++;
            try {
                const quote = await testQuoteDetailPage();
                if (quote) {
                    testsPassed++;
                    addResult(container, 'success', 'Test 2: Quote detail loading passed');
                } else {
                    addResult(container, 'error', 'Test 2: Quote detail loading failed');
                }
            } catch (error) {
                addResult(container, 'error', `Test 2: Quote detail loading failed: ${error.message}`);
            }
            
            // Test 3: Authors Loading
            totalTests++;
            try {
                const authors = await testAuthorsLoading();
                if (authors) {
                    testsPassed++;
                    addResult(container, 'success', 'Test 3: Authors loading passed');
                } else {
                    addResult(container, 'error', 'Test 3: Authors loading failed');
                }
            } catch (error) {
                addResult(container, 'error', `Test 3: Authors loading failed: ${error.message}`);
            }
            
            // Final result
            const allPassed = testsPassed === totalTests;
            addResult(container, allPassed ? 'success' : 'warning', 
                `Final Result: ${testsPassed}/${totalTests} tests passed`);
            
            if (allPassed) {
                addResult(container, 'success', 
                    '🎉 All tests passed! Quote detail page JavaScript errors have been resolved.');
            } else {
                addResult(container, 'warning', 
                    '⚠️ Some tests failed. Please check the individual test results above.');
            }
        }

        // Auto-run URL handler test on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                testUrlHandler();
            }, 1000);
        });
    </script>
</body>
</html>
