<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Detail Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .quote-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .quote-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Quote Detail Functionality Test</h1>
    <p>This page tests the implemented quote detail functionality.</p>

    <div class="test-section">
        <h2>Automated Test Suite</h2>
        <button onclick="window.QuoteDetailTests.runAllTests()" style="background-color: #28a745; font-size: 16px; padding: 15px 30px;">🚀 Run All Tests</button>
        <p><em>Check browser console for detailed test results</em></p>
    </div>

    <div class="test-section">
        <h2>1. API Client Tests</h2>
        <button onclick="testGetQuoteById()">Test getQuoteById()</button>
        <button onclick="testGetRelatedQuotes()">Test getRelatedQuotesByAuthor()</button>
        <div id="api-test-results"></div>
    </div>

    <div class="test-section">
        <h2>2. Quote Card Click Test</h2>
        <p>Click on the quote card below to test navigation to detail page:</p>
        <div id="quote-card-container"></div>
        <button onclick="createTestQuoteCard()">Create Test Quote Card</button>
    </div>

    <div class="test-section">
        <h2>3. URL Navigation Tests</h2>
        <button onclick="testDirectNavigation()">Test Direct Navigation to Quote Detail</button>
        <button onclick="testInvalidQuoteId()">Test Invalid Quote ID</button>
        <div id="navigation-test-results"></div>
    </div>

    <div class="test-section">
        <h2>4. Page Functionality Tests</h2>
        <button onclick="testPageInitialization()">Test Page Initialization</button>
        <button onclick="testErrorHandling()">Test Error Handling</button>
        <div id="page-test-results"></div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/components/quote-card.js"></script>
    <script src="js/pages/quote.js"></script>
    <script src="test-api-functionality.js"></script>

    <script>
        // Test functions
        function addTestResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
        }

        async function testGetQuoteById() {
            const container = 'api-test-results';
            try {
                addTestResult(container, 'info', 'Testing getQuoteById(21)...');
                const quote = await window.ApiClient.getQuoteById(21);
                
                if (quote && quote.id && quote.content && quote.author) {
                    addTestResult(container, 'success', `Quote loaded: "${quote.content.substring(0, 50)}..." by ${quote.author.name}`);
                    
                    // Display full quote data
                    const pre = document.createElement('pre');
                    pre.textContent = JSON.stringify(quote, null, 2);
                    document.getElementById(container).appendChild(pre);
                } else {
                    addTestResult(container, 'error', 'Quote data incomplete or missing');
                }
            } catch (error) {
                addTestResult(container, 'error', `API Error: ${error.message}`);
            }
        }

        async function testGetRelatedQuotes() {
            const container = 'api-test-results';
            try {
                addTestResult(container, 'info', 'Testing getRelatedQuotesByAuthor(10, 21, 3)...');
                const relatedQuotes = await window.ApiClient.getRelatedQuotesByAuthor(10, 21, 3);
                
                if (Array.isArray(relatedQuotes)) {
                    addTestResult(container, 'success', `Found ${relatedQuotes.length} related quotes`);
                    
                    relatedQuotes.forEach((quote, index) => {
                        addTestResult(container, 'info', `Related Quote ${index + 1}: "${quote.content.substring(0, 40)}..." by ${quote.author.name}`);
                    });
                } else {
                    addTestResult(container, 'error', 'Related quotes result is not an array');
                }
            } catch (error) {
                addTestResult(container, 'error', `Related Quotes API Error: ${error.message}`);
            }
        }

        async function createTestQuoteCard() {
            const container = document.getElementById('quote-card-container');
            container.innerHTML = '';
            
            try {
                // Get a quote to create a card
                const quote = await window.ApiClient.getQuoteById(21);
                
                if (quote) {
                    const quoteCard = QuoteCardComponent.render(quote, 0, {
                        showActions: true,
                        showAuthorAvatar: true
                    });
                    
                    container.appendChild(quoteCard);
                    addTestResult('api-test-results', 'success', 'Test quote card created with click functionality');
                } else {
                    addTestResult('api-test-results', 'error', 'Failed to load quote for card creation');
                }
            } catch (error) {
                addTestResult('api-test-results', 'error', `Card creation error: ${error.message}`);
            }
        }

        function testDirectNavigation() {
            const container = 'navigation-test-results';
            addTestResult(container, 'info', 'Testing direct navigation to /quotes/21/');
            
            // Open in new tab to avoid losing test page
            window.open('/quotes/21/', '_blank');
            addTestResult(container, 'success', 'Navigation initiated - check new tab');
        }

        function testInvalidQuoteId() {
            const container = 'navigation-test-results';
            addTestResult(container, 'info', 'Testing invalid quote ID navigation to /quotes/99999/');
            
            // Open in new tab to avoid losing test page
            window.open('/quotes/99999/', '_blank');
            addTestResult(container, 'success', 'Invalid ID navigation initiated - check new tab for error handling');
        }

        async function testPageInitialization() {
            const container = 'page-test-results';
            try {
                addTestResult(container, 'info', 'Testing page initialization with quote ID 21...');
                
                // Simulate page initialization
                await initQuotePage({ quoteId: 21 });
                addTestResult(container, 'success', 'Page initialization completed successfully');
            } catch (error) {
                addTestResult(container, 'error', `Page initialization error: ${error.message}`);
            }
        }

        async function testErrorHandling() {
            const container = 'page-test-results';
            try {
                addTestResult(container, 'info', 'Testing error handling with invalid quote ID...');
                
                // Test with invalid quote ID
                await window.ApiClient.getQuoteById(99999);
                addTestResult(container, 'warning', 'Expected error did not occur');
            } catch (error) {
                addTestResult(container, 'success', `Error handling working: ${error.message}`);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('api-test-results', 'info', 'Test page loaded. Click buttons to run tests.');
        });
    </script>
</body>
</html>
