<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Handling Test - Quote Detail Page</title>
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
</head>
<body class="light-mode bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">Error Handling Test</h1>
        
        <!-- Test Controls -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Error Scenarios</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="testInvalidQuoteId()" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    Test Invalid Quote ID (999999)
                </button>
                <button onclick="testNetworkError()" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    Simulate Network Error
                </button>
                <button onclick="testEmptyRelatedQuotes()" class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    Test Empty Related Quotes
                </button>
                <button onclick="testMissingComponents()" class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600">
                    Test Missing Components
                </button>
            </div>
            
            <div class="mt-4">
                <button onclick="resetStates()" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    Reset All States
                </button>
            </div>
        </div>
        
        <!-- Error State Simulation -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Error State Simulation</h2>
            
            <!-- Quote Card Error States -->
            <section class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Quote Card Error States</h3>
                
                <!-- Loading State -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">Loading State:</h4>
                    <div class="relative p-6 sm:p-8 md:p-10 bg-gradient-to-br from-yellow-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700" id="quote-loading-demo">
                        <div class="flex justify-center items-center py-8">
                            <div class="loading-spinner"></div>
                            <span class="ml-3 text-gray-600 dark:text-gray-300">Loading quote...</span>
                        </div>
                    </div>
                </div>
                
                <!-- Error State -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">Error State:</h4>
                    <div class="relative p-6 sm:p-8 md:p-10 bg-gradient-to-br from-red-50 to-white dark:from-red-900 dark:to-gray-900 rounded-xl shadow-lg border border-red-200 dark:border-red-700" id="quote-error-demo">
                        <div class="text-center py-8">
                            <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-4"></i>
                            <h2 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2">Quote Not Found</h2>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">The quote you're looking for doesn't exist or has been removed.</p>
                            <div class="space-x-4">
                                <a href="/" class="inline-block px-6 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors">
                                    Go Home
                                </a>
                                <button class="px-6 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors" onclick="window.history.back()">
                                    Go Back
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Related Quotes Error States -->
            <section class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Related Quotes Error States</h3>
                
                <!-- Loading State -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">Loading State:</h4>
                    <div class="flex justify-center py-12" id="related-quotes-loading-demo">
                        <div class="loading-spinner" role="status">
                            <span class="sr-only">Loading quotes...</span>
                        </div>
                    </div>
                </div>
                
                <!-- Error State -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">Error State:</h4>
                    <div class="text-center py-12" id="related-quotes-error-demo">
                        <div class="text-gray-500 dark:text-gray-400">
                            <i class="fas fa-exclamation-triangle text-yellow-500 text-2xl mb-2"></i>
                            <p>Unable to load related quotes.</p>
                            <button class="mt-2 px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors" onclick="window.location.reload()">
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Empty State -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">Empty State:</h4>
                    <div class="text-center py-12" id="related-quotes-empty-demo">
                        <div class="text-gray-500 dark:text-gray-400">
                            <i class="fas fa-quote-right text-yellow-500 text-2xl mb-2"></i>
                            <p>No other quotes found by this author.</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        
        <!-- Test Results -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold mb-4">Test Results</h2>
            <div id="test-results" class="bg-gray-100 dark:bg-gray-700 p-4 rounded min-h-[200px] font-mono text-sm">
                Ready for error handling tests...
            </div>
            <button onclick="clearResults()" class="mt-4 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                Clear Results
            </button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/debug.js?v=20250629"></script>
    <script src="/js/api-client.js?v=20250629"></script>
    
    <script>
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const typeColor = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-blue-600';
            resultsDiv.innerHTML += `<div class="${typeColor}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = 'Ready for error handling tests...';
        }
        
        async function testInvalidQuoteId() {
            logResult('Testing invalid quote ID (999999)...', 'info');
            
            try {
                if (typeof window.ApiClient === 'undefined') {
                    logResult('ERROR: ApiClient not available', 'error');
                    return;
                }
                
                const quote = await window.ApiClient.getQuoteById(999999);
                
                if (quote === null || quote === undefined) {
                    logResult('✓ Invalid quote ID correctly returns null/undefined', 'success');
                    logResult('✓ Error handling working as expected', 'success');
                } else {
                    logResult('✗ Invalid quote ID should return null/undefined', 'error');
                    logResult(`Unexpected result: ${JSON.stringify(quote)}`, 'error');
                }
                
            } catch (error) {
                logResult(`✓ Error properly caught: ${error.message}`, 'success');
                logResult('✓ Exception handling working correctly', 'success');
            }
        }
        
        async function testNetworkError() {
            logResult('Simulating network error...', 'info');
            
            // Temporarily override ApiClient to simulate network error
            const originalApiClient = window.ApiClient;
            
            window.ApiClient = {
                getQuoteById: async () => {
                    throw new Error('Network error: Failed to fetch');
                }
            };
            
            try {
                await window.ApiClient.getQuoteById(1);
                logResult('✗ Network error simulation failed', 'error');
            } catch (error) {
                logResult(`✓ Network error properly caught: ${error.message}`, 'success');
                logResult('✓ Network error handling working correctly', 'success');
            }
            
            // Restore original ApiClient
            window.ApiClient = originalApiClient;
            logResult('✓ ApiClient restored', 'info');
        }
        
        async function testEmptyRelatedQuotes() {
            logResult('Testing empty related quotes scenario...', 'info');
            
            try {
                if (typeof window.ApiClient === 'undefined') {
                    logResult('ERROR: ApiClient not available', 'error');
                    return;
                }
                
                // Test with an author that might have only one quote
                const relatedQuotes = await window.ApiClient.getRelatedQuotesByAuthor(999999, 1, 5);
                
                if (!relatedQuotes || relatedQuotes.length === 0) {
                    logResult('✓ Empty related quotes correctly returns empty array', 'success');
                    logResult('✓ Empty state handling working as expected', 'success');
                } else {
                    logResult(`Found ${relatedQuotes.length} related quotes (not empty)`, 'info');
                }
                
            } catch (error) {
                logResult(`✓ Error in related quotes properly caught: ${error.message}`, 'success');
            }
        }
        
        function testMissingComponents() {
            logResult('Testing missing components scenario...', 'info');
            
            // Test QuoteCardComponent availability
            if (typeof QuoteCardComponent === 'undefined') {
                logResult('✓ QuoteCardComponent missing - fallback should be used', 'success');
            } else {
                logResult('QuoteCardComponent is available', 'info');
                
                // Test with invalid data
                try {
                    const result = QuoteCardComponent.render(null);
                    if (!result) {
                        logResult('✓ QuoteCardComponent handles null data correctly', 'success');
                    }
                } catch (error) {
                    logResult(`✓ QuoteCardComponent error handling: ${error.message}`, 'success');
                }
            }
            
            // Test UrlHandler availability
            if (typeof window.UrlHandler === 'undefined') {
                logResult('✓ UrlHandler missing - fallback should be used', 'success');
            } else {
                logResult('UrlHandler is available', 'info');
            }
        }
        
        function resetStates() {
            logResult('Resetting all error states...', 'info');
            
            // Reset demo states to default
            const loadingDemo = document.getElementById('quote-loading-demo');
            const errorDemo = document.getElementById('quote-error-demo');
            
            if (loadingDemo) {
                loadingDemo.style.display = 'block';
            }
            if (errorDemo) {
                errorDemo.style.display = 'block';
            }
            
            logResult('✓ All states reset to default', 'success');
        }
        
        // Initialize
        window.addEventListener('load', () => {
            logResult('Error handling test environment initialized', 'success');
            logResult('Available for testing error scenarios...', 'info');
            
            // Check component availability
            logResult(`ApiClient available: ${typeof window.ApiClient !== 'undefined'}`, 'info');
            logResult(`QuoteCardComponent available: ${typeof QuoteCardComponent !== 'undefined'}`, 'info');
            logResult(`UrlHandler available: ${typeof window.UrlHandler !== 'undefined'}`, 'info');
        });
    </script>
</body>
</html>
