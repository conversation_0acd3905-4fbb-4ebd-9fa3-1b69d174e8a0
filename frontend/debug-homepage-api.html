<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页API调用调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 300px; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .network-log { background: #f9f9f9; padding: 10px; border-radius: 3px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 首页API调用调试</h1>
    
    <div class="debug-section">
        <h2>📋 配置状态</h2>
        <div id="config-status"></div>
        <button onclick="checkConfig()">检查配置</button>
    </div>
    
    <div class="debug-section">
        <h2>🌐 API客户端状态</h2>
        <div id="api-client-status"></div>
        <button onclick="checkApiClient()">检查API客户端</button>
    </div>
    
    <div class="debug-section">
        <h2>🧪 直接API测试</h2>
        <div id="direct-api-test"></div>
        <button onclick="testDirectAPI()">测试直接API调用</button>
    </div>
    
    <div class="debug-section">
        <h2>📊 首页方法测试</h2>
        <div id="homepage-method-test"></div>
        <button onclick="testHomepageMethods()">测试首页方法</button>
    </div>
    
    <div class="debug-section">
        <h2>🔍 网络请求监控</h2>
        <div id="network-monitor"></div>
        <button onclick="startNetworkMonitoring()">开始监控网络请求</button>
    </div>

    <!-- 加载必要的JavaScript文件 -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    
    <script>
        let networkRequests = [];
        
        function addResult(container, message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            container.appendChild(div);
        }
        
        function checkConfig() {
            const container = document.getElementById('config-status');
            container.innerHTML = '';
            
            addResult(container, '🔍 检查配置状态...', 'info');
            
            if (window.AppConfig) {
                addResult(container, '✅ AppConfig已加载', 'success');
                addResult(container, `🔗 REST API端点: ${window.AppConfig.apiEndpoint}`, 'info');
                addResult(container, `🔍 GraphQL端点: ${window.AppConfig.graphqlEndpoint || '❌ 未配置'}`, window.AppConfig.graphqlEndpoint ? 'success' : 'error');
                addResult(container, `🎭 模拟数据: ${window.AppConfig.useMockData}`, 'info');
                addResult(container, `🐛 调试模式: ${window.AppConfig.debug}`, 'info');
                
                const pre = document.createElement('pre');
                pre.textContent = JSON.stringify(window.AppConfig, null, 2);
                container.appendChild(pre);
            } else {
                addResult(container, '❌ AppConfig未加载', 'error');
            }
        }
        
        function checkApiClient() {
            const container = document.getElementById('api-client-status');
            container.innerHTML = '';
            
            addResult(container, '🔍 检查API客户端状态...', 'info');
            
            if (window.ApiClient) {
                addResult(container, '✅ ApiClient已初始化', 'success');
                addResult(container, `🔗 REST端点: ${window.ApiClient.apiEndpoint}`, 'info');
                addResult(container, `🔍 GraphQL端点: ${window.ApiClient.graphqlEndpoint || '❌ 未配置'}`, window.ApiClient.graphqlEndpoint ? 'success' : 'error');
                addResult(container, `🎭 模拟数据: ${window.ApiClient.useMockData}`, 'info');
                
                // 检查方法
                const methods = ['query', 'getQuotes', 'getTopQuotes', 'getPopularAuthors', 'getPopularCategories', 'getPopularSources'];
                methods.forEach(method => {
                    if (typeof window.ApiClient[method] === 'function') {
                        addResult(container, `✅ ${method}方法存在`, 'success');
                    } else {
                        addResult(container, `❌ ${method}方法不存在`, 'error');
                    }
                });
            } else {
                addResult(container, '❌ ApiClient未初始化', 'error');
            }
        }
        
        async function testDirectAPI() {
            const container = document.getElementById('direct-api-test');
            container.innerHTML = '';
            
            addResult(container, '🧪 开始直接API测试...', 'info');
            
            // 测试GraphQL端点
            try {
                addResult(container, '🔍 测试GraphQL端点...', 'info');
                const response = await fetch('http://127.0.0.1:8000/graphql/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        query: 'query { quotes(first: 2) { id content author { name } } quotesCount }'
                    })
                });
                
                addResult(container, `📡 响应状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                addResult(container, `📝 Content-Type: ${response.headers.get('Content-Type')}`, 'info');
                
                const responseText = await response.text();
                addResult(container, `📄 响应长度: ${responseText.length} 字符`, 'info');
                
                if (responseText) {
                    try {
                        const data = JSON.parse(responseText);
                        addResult(container, '✅ JSON解析成功', 'success');
                        
                        const pre = document.createElement('pre');
                        pre.textContent = JSON.stringify(data, null, 2);
                        container.appendChild(pre);
                    } catch (jsonError) {
                        addResult(container, `❌ JSON解析失败: ${jsonError.message}`, 'error');
                        addResult(container, `📄 原始响应内容:`, 'warning');
                        const pre = document.createElement('pre');
                        pre.textContent = responseText;
                        container.appendChild(pre);
                    }
                } else {
                    addResult(container, '❌ 响应内容为空', 'error');
                }
            } catch (error) {
                addResult(container, `❌ 网络请求失败: ${error.message}`, 'error');
            }
        }
        
        async function testHomepageMethods() {
            const container = document.getElementById('homepage-method-test');
            container.innerHTML = '';
            
            addResult(container, '📊 开始测试首页方法...', 'info');
            
            if (!window.ApiClient) {
                addResult(container, '❌ ApiClient未初始化', 'error');
                return;
            }
            
            // 测试getQuotes方法
            try {
                addResult(container, '🔍 测试getQuotes方法...', 'info');
                const quotesResult = await window.ApiClient.getQuotes(1, 3);
                
                if (quotesResult && quotesResult.quotes) {
                    addResult(container, `✅ getQuotes成功: 获取到 ${quotesResult.quotes.length} 条名言`, 'success');
                    addResult(container, `📊 总数: ${quotesResult.totalCount}`, 'info');
                    
                    const pre = document.createElement('pre');
                    pre.textContent = JSON.stringify(quotesResult, null, 2);
                    container.appendChild(pre);
                } else {
                    addResult(container, '❌ getQuotes返回数据格式错误', 'error');
                }
            } catch (error) {
                addResult(container, `❌ getQuotes失败: ${error.message}`, 'error');
                console.error('getQuotes error:', error);
            }
            
            // 测试getTopQuotes方法
            try {
                addResult(container, '🔍 测试getTopQuotes方法...', 'info');
                const topQuotesResult = await window.ApiClient.getTopQuotes(1, 3);
                
                if (topQuotesResult && topQuotesResult.quotes) {
                    addResult(container, `✅ getTopQuotes成功: 获取到 ${topQuotesResult.quotes.length} 条名言`, 'success');
                    addResult(container, `📊 总数: ${topQuotesResult.totalCount}`, 'info');
                } else {
                    addResult(container, '❌ getTopQuotes返回数据格式错误', 'error');
                }
            } catch (error) {
                addResult(container, `❌ getTopQuotes失败: ${error.message}`, 'error');
                console.error('getTopQuotes error:', error);
            }
        }
        
        function startNetworkMonitoring() {
            const container = document.getElementById('network-monitor');
            container.innerHTML = '';
            
            addResult(container, '🔍 开始监控网络请求...', 'info');
            
            // 重写fetch函数来监控请求
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                const url = args[0];
                const options = args[1] || {};
                
                const logDiv = document.createElement('div');
                logDiv.className = 'network-log';
                logDiv.innerHTML = `
                    <strong>📡 网络请求:</strong><br>
                    <strong>URL:</strong> ${url}<br>
                    <strong>方法:</strong> ${options.method || 'GET'}<br>
                    <strong>时间:</strong> ${new Date().toLocaleTimeString()}
                `;
                
                if (options.body) {
                    logDiv.innerHTML += `<br><strong>请求体:</strong><br><pre>${options.body}</pre>`;
                }
                
                container.appendChild(logDiv);
                
                return originalFetch.apply(this, args)
                    .then(response => {
                        logDiv.innerHTML += `<br><strong>响应状态:</strong> ${response.status} ${response.statusText}`;
                        logDiv.innerHTML += `<br><strong>Content-Type:</strong> ${response.headers.get('Content-Type')}`;
                        return response;
                    })
                    .catch(error => {
                        logDiv.innerHTML += `<br><strong style="color: red;">错误:</strong> ${error.message}`;
                        throw error;
                    });
            };
            
            addResult(container, '✅ 网络监控已启动，现在可以测试其他功能', 'success');
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkConfig();
                checkApiClient();
            }, 500);
        });
    </script>
</body>
</html>
