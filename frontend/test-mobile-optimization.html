<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端优化测试 - Quotese</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .stats-table th {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📱 移动端优化测试工具</h1>
        <p>此工具用于验证移动端性能优化功能是否正常工作。</p>
        
        <div class="test-section">
            <h2>🔍 设备检测测试</h2>
            <button onclick="testDeviceDetection()">运行设备检测测试</button>
            <div id="device-detection-results"></div>
        </div>
        
        <div class="test-section">
            <h2>💾 缓存大小限制测试</h2>
            <button onclick="testCacheSizeLimit()">测试缓存大小限制</button>
            <div id="cache-size-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🧹 内存优化测试</h2>
            <button onclick="testMemoryOptimization()">测试内存优化</button>
            <div id="memory-optimization-results"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 性能统计</h2>
            <button onclick="showPerformanceStats()">显示性能统计</button>
            <div id="performance-stats-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🚀 综合测试</h2>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="all-tests-results"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="/js/mobile-performance-optimizer.js?v=20250626"></script>
    <script src="/js/entity-id-mapper.js?v=20250626"></script>
    <script src="/js/optimized-navigation.js?v=20250626"></script>

    <script>
        // 测试设备检测
        function testDeviceDetection() {
            const resultsDiv = document.getElementById('device-detection-results');
            resultsDiv.innerHTML = '<div class="info">正在运行设备检测测试...</div>';
            
            setTimeout(() => {
                let results = '';
                
                if (window.mobileOptimizer) {
                    const isMobile = window.mobileOptimizer.isMobile;
                    const config = window.mobileOptimizer.config;
                    
                    results += `<div class="test-result ${isMobile ? 'success' : 'info'}">
                        <strong>设备类型:</strong> ${isMobile ? '移动端' : '桌面端'}
                    </div>`;
                    
                    results += `<div class="test-result info">
                        <strong>User Agent:</strong> ${navigator.userAgent}
                    </div>`;
                    
                    results += `<div class="test-result info">
                        <strong>屏幕尺寸:</strong> ${window.innerWidth} x ${window.innerHeight}
                    </div>`;
                    
                    results += `<div class="test-result info">
                        <strong>触摸支持:</strong> ${'ontouchstart' in window ? '是' : '否'}
                    </div>`;
                    
                    results += `<div class="test-result ${isMobile ? 'success' : 'info'}">
                        <strong>缓存大小限制:</strong> ${config.maxCacheSize} 个实体
                    </div>`;
                    
                    results += `<div class="test-result ${config.aggressiveCleanup ? 'success' : 'info'}">
                        <strong>积极清理:</strong> ${config.aggressiveCleanup ? '启用' : '禁用'}
                    </div>`;
                } else {
                    results = '<div class="error">移动端优化器未找到！</div>';
                }
                
                resultsDiv.innerHTML = results;
            }, 500);
        }
        
        // 测试缓存大小限制
        function testCacheSizeLimit() {
            const resultsDiv = document.getElementById('cache-size-results');
            resultsDiv.innerHTML = '<div class="info">正在测试缓存大小限制...</div>';
            
            setTimeout(() => {
                let results = '';
                
                if (window.entityCache) {
                    const cacheStats = {};
                    let totalCacheSize = 0;
                    
                    ['categories', 'authors', 'sources'].forEach(type => {
                        const cache = window.entityCache[type];
                        if (cache) {
                            cacheStats[type] = cache.size;
                            totalCacheSize += cache.size;
                        } else {
                            cacheStats[type] = 0;
                        }
                    });
                    
                    results += '<table class="stats-table">';
                    results += '<tr><th>缓存类型</th><th>当前大小</th><th>状态</th></tr>';
                    
                    Object.entries(cacheStats).forEach(([type, size]) => {
                        const maxSize = window.mobileOptimizer?.config.maxCacheSize || 60;
                        const status = size <= maxSize ? '正常' : '超限';
                        const statusClass = size <= maxSize ? 'success' : 'warning';
                        
                        results += `<tr>
                            <td>${type}</td>
                            <td>${size}</td>
                            <td><span class="${statusClass}">${status}</span></td>
                        </tr>`;
                    });
                    
                    results += '</table>';
                    
                    const maxTotal = (window.mobileOptimizer?.config.maxCacheSize || 60) * 3;
                    results += `<div class="test-result ${totalCacheSize <= maxTotal ? 'success' : 'warning'}">
                        <strong>总缓存大小:</strong> ${totalCacheSize} / ${maxTotal} (${((totalCacheSize / maxTotal) * 100).toFixed(1)}%)
                    </div>`;
                } else {
                    results = '<div class="error">实体缓存未找到！</div>';
                }
                
                resultsDiv.innerHTML = results;
            }, 500);
        }
        
        // 测试内存优化
        function testMemoryOptimization() {
            const resultsDiv = document.getElementById('memory-optimization-results');
            resultsDiv.innerHTML = '<div class="info">正在测试内存优化...</div>';
            
            setTimeout(() => {
                let results = '';
                
                if (window.mobileOptimizer) {
                    const stats = window.mobileOptimizer.getStats();
                    
                    results += `<div class="test-result info">
                        <strong>优化次数:</strong> ${stats.optimizationCount}
                    </div>`;
                    
                    results += `<div class="test-result info">
                        <strong>清理次数:</strong> ${stats.cleanupCount}
                    </div>`;
                    
                    // 估算内存使用
                    let memoryUsage = 0;
                    if (performance.memory) {
                        memoryUsage = (performance.memory.usedJSHeapSize / (1024 * 1024)).toFixed(2);
                        results += `<div class="test-result ${memoryUsage > 100 ? 'warning' : 'success'}">
                            <strong>JS堆内存使用:</strong> ${memoryUsage} MB
                        </div>`;
                    } else {
                        results += `<div class="test-result info">
                            <strong>内存信息:</strong> 浏览器不支持performance.memory
                        </div>`;
                    }
                    
                    // DOM元素数量
                    const elementCount = document.querySelectorAll('*').length;
                    results += `<div class="test-result info">
                        <strong>DOM元素数量:</strong> ${elementCount}
                    </div>`;
                    
                } else {
                    results = '<div class="error">移动端优化器未找到！</div>';
                }
                
                resultsDiv.innerHTML = results;
            }, 500);
        }
        
        // 显示性能统计
        function showPerformanceStats() {
            const resultsDiv = document.getElementById('performance-stats-results');
            resultsDiv.innerHTML = '<div class="info">正在收集性能统计...</div>';
            
            setTimeout(() => {
                let results = '';
                
                if (window.mobileOptimizer) {
                    const stats = window.mobileOptimizer.getStats();
                    
                    results += '<table class="stats-table">';
                    results += '<tr><th>指标</th><th>值</th><th>状态</th></tr>';
                    
                    const metrics = [
                        ['设备类型', stats.isMobile ? '移动端' : '桌面端', 'info'],
                        ['最大缓存大小', stats.config.maxCacheSize, 'info'],
                        ['积极清理', stats.config.aggressiveCleanup ? '启用' : '禁用', stats.config.aggressiveCleanup ? 'success' : 'info'],
                        ['内存阈值', stats.config.memoryThreshold + ' MB', 'info'],
                        ['清理间隔', (stats.config.cleanupInterval / 1000) + ' 秒', 'info'],
                        ['优化次数', stats.optimizationCount, stats.optimizationCount > 0 ? 'success' : 'warning'],
                        ['清理次数', stats.cleanupCount, 'info']
                    ];
                    
                    metrics.forEach(([metric, value, status]) => {
                        results += `<tr>
                            <td>${metric}</td>
                            <td>${value}</td>
                            <td><span class="${status}">●</span></td>
                        </tr>`;
                    });
                    
                    results += '</table>';
                } else {
                    results = '<div class="error">移动端优化器未找到！</div>';
                }
                
                resultsDiv.innerHTML = results;
            }, 500);
        }
        
        // 运行所有测试
        function runAllTests() {
            const resultsDiv = document.getElementById('all-tests-results');
            resultsDiv.innerHTML = '<div class="info">正在运行综合测试...</div>';
            
            // 依次运行所有测试
            testDeviceDetection();
            setTimeout(() => testCacheSizeLimit(), 1000);
            setTimeout(() => testMemoryOptimization(), 2000);
            setTimeout(() => showPerformanceStats(), 3000);
            
            setTimeout(() => {
                let summary = '<h3>📋 测试总结</h3>';
                
                const isMobileOptimized = window.mobileOptimizer && window.mobileOptimizer.isInitialized;
                const hasEntityCache = !!window.entityCache;
                const isDeviceMobile = window.mobileOptimizer?.isMobile || false;
                
                summary += `<div class="test-result ${isMobileOptimized ? 'success' : 'error'}">
                    <strong>移动端优化器:</strong> ${isMobileOptimized ? '✅ 已初始化' : '❌ 未初始化'}
                </div>`;
                
                summary += `<div class="test-result ${hasEntityCache ? 'success' : 'warning'}">
                    <strong>实体缓存:</strong> ${hasEntityCache ? '✅ 可用' : '⚠️ 不可用'}
                </div>`;
                
                summary += `<div class="test-result info">
                    <strong>设备类型:</strong> ${isDeviceMobile ? '📱 移动端' : '🖥️ 桌面端'}
                </div>`;
                
                if (isMobileOptimized && isDeviceMobile) {
                    summary += '<div class="test-result success"><strong>✅ 移动端优化已启用并正常工作</strong></div>';
                } else if (isMobileOptimized && !isDeviceMobile) {
                    summary += '<div class="test-result info"><strong>ℹ️ 桌面端环境，使用标准配置</strong></div>';
                } else {
                    summary += '<div class="test-result error"><strong>❌ 移动端优化未正常工作</strong></div>';
                }
                
                resultsDiv.innerHTML = summary;
            }, 4000);
        }
        
        // 页面加载时自动运行基础检测
        window.addEventListener('load', () => {
            console.log('📱 Mobile optimization test page loaded');
            
            // 等待脚本加载完成
            setTimeout(() => {
                if (window.mobileOptimizer) {
                    console.log('✅ Mobile optimizer detected');
                } else {
                    console.log('⚠️ Mobile optimizer not found');
                }
            }, 1000);
        });
    </script>
</body>
</html>
