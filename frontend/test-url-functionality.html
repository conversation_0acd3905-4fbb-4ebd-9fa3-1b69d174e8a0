<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL功能完整性测试 - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .test-result {
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .test-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .test-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-check-circle mr-3 text-green-500"></i>
                URL功能完整性测试
            </h1>
            
            <!-- 测试控制面板 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">测试控制面板</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    <button onclick="runAllTests()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        <i class="fas fa-play mr-2"></i>运行所有测试
                    </button>
                    <button onclick="testBasicUrls()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        <i class="fas fa-link mr-2"></i>基础URL测试
                    </button>
                    <button onclick="testPageRouter()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        <i class="fas fa-route mr-2"></i>路由功能测试
                    </button>
                    <button onclick="testSEOManager()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                        <i class="fas fa-search mr-2"></i>SEO功能测试
                    </button>
                    <button onclick="testUrlHandler()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        <i class="fas fa-cogs mr-2"></i>URL处理测试
                    </button>
                    <button onclick="clearResults()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        <i class="fas fa-trash mr-2"></i>清除结果
                    </button>
                </div>
                
                <div class="text-sm text-gray-600">
                    <p><strong>测试说明：</strong>此测试页面将验证所有新的语义化URL格式、页面路由功能、SEO标签生成等核心功能。</p>
                </div>
            </div>
            
            <!-- 测试结果显示区域 -->
            <div id="test-results" class="space-y-6">
                <!-- 测试结果将在这里显示 -->
            </div>
            
            <!-- 测试统计 -->
            <div id="test-summary" class="mt-8 bg-white rounded-lg shadow-md p-6 hidden">
                <h3 class="text-lg font-semibold mb-4">测试统计</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600" id="total-tests">0</div>
                        <div class="text-sm text-gray-600">总测试数</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600" id="passed-tests">0</div>
                        <div class="text-sm text-gray-600">通过</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600" id="failed-tests">0</div>
                        <div class="text-sm text-gray-600">失败</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-600" id="warning-tests">0</div>
                        <div class="text-sm text-gray-600">警告</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Core Scripts -->
    <script src="js/url-handler.js"></script>
    <script src="js/seo-manager.js"></script>
    <script src="js/page-router.js"></script>
    
    <script>
        // 测试结果存储
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0
        };

        // 测试用例定义
        const testCases = {
            basicUrls: [
                { name: '首页访问', url: '/', expected: 'index.html' },
                { name: '作者列表页', url: '/authors/', expected: 'authors.html' },
                { name: '作者详情页', url: '/authors/albert-einstein/', expected: 'author.html' },
                { name: '作者名言页', url: '/authors/albert-einstein/quotes/', expected: 'author.html' },
                { name: '类别列表页', url: '/categories/', expected: 'categories.html' },
                { name: '类别详情页', url: '/categories/inspirational/', expected: 'category.html' },
                { name: '类别名言页', url: '/categories/inspirational/quotes/', expected: 'category.html' },
                { name: '来源列表页', url: '/sources/', expected: 'sources.html' },
                { name: '来源详情页', url: '/sources/the-art-of-war/', expected: 'source.html' },
                { name: '名言列表页', url: '/quotes/', expected: 'quotes.html' },
                { name: '名言详情页', url: '/quotes/123/', expected: 'quote.html' },
                { name: '搜索页面', url: '/search/', expected: 'search.html' }
            ],
            
            urlHandlerTests: [
                { name: 'slugify函数测试', input: 'Albert Einstein', expected: 'albert-einstein' },
                { name: 'deslugify函数测试', input: 'albert-einstein', expected: 'Albert Einstein' },
                { name: '页面类型检测 - 首页', url: '/', expected: 'home' },
                { name: '页面类型检测 - 作者页', url: '/authors/test/', expected: 'author-detail' },
                { name: '页面类型检测 - 类别页', url: '/categories/test/', expected: 'category-detail' },
                { name: '页面类型检测 - 名言页', url: '/quotes/123/', expected: 'quote-detail' }
            ],
            
            seoTests: [
                { name: 'SEO管理器初始化', test: () => typeof SEOManager !== 'undefined' },
                { name: 'SEO模板配置', test: () => SEOManager.TEMPLATES && Object.keys(SEOManager.TEMPLATES).length > 0 },
                { name: 'SEO验证功能', test: () => typeof SEOManager.validateSEO === 'function' },
                { name: 'Meta标签更新功能', test: () => typeof SEOManager.updatePageSEO === 'function' }
            ]
        };

        // 运行所有测试
        async function runAllTests() {
            clearResults();
            addTestSection('开始运行完整的URL功能测试套件', 'info');
            
            await testBasicUrls();
            await testUrlHandler();
            await testPageRouter();
            await testSEOManager();
            
            updateTestSummary();
            addTestSection('所有测试完成', 'info');
        }

        // 基础URL测试
        async function testBasicUrls() {
            addTestSection('基础URL访问测试', 'info');
            
            for (const testCase of testCases.basicUrls) {
                try {
                    // 模拟URL访问测试
                    const result = await simulateUrlAccess(testCase.url);
                    
                    if (result.success) {
                        addTestResult(testCase.name, 'success', `URL ${testCase.url} 可以正常访问`);
                        testResults.passed++;
                    } else {
                        addTestResult(testCase.name, 'error', `URL ${testCase.url} 访问失败: ${result.error}`);
                        testResults.failed++;
                    }
                } catch (error) {
                    addTestResult(testCase.name, 'error', `测试异常: ${error.message}`);
                    testResults.failed++;
                }
                testResults.total++;
            }
        }

        // URL处理器测试
        async function testUrlHandler() {
            addTestSection('UrlHandler功能测试', 'info');
            
            // 测试slugify函数
            if (typeof UrlHandler !== 'undefined' && UrlHandler.slugify) {
                for (const testCase of testCases.urlHandlerTests.filter(t => t.input && t.expected)) {
                    try {
                        const result = UrlHandler.slugify(testCase.input);
                        if (result === testCase.expected) {
                            addTestResult(testCase.name, 'success', `输入: "${testCase.input}" → 输出: "${result}"`);
                            testResults.passed++;
                        } else {
                            addTestResult(testCase.name, 'error', `期望: "${testCase.expected}", 实际: "${result}"`);
                            testResults.failed++;
                        }
                    } catch (error) {
                        addTestResult(testCase.name, 'error', `测试异常: ${error.message}`);
                        testResults.failed++;
                    }
                    testResults.total++;
                }
            } else {
                addTestResult('UrlHandler可用性', 'error', 'UrlHandler未定义或slugify函数不存在');
                testResults.failed++;
                testResults.total++;
            }
        }

        // 页面路由器测试
        async function testPageRouter() {
            addTestSection('PageRouter功能测试', 'info');
            
            const routerTests = [
                { name: 'PageRouter可用性', test: () => typeof PageRouter !== 'undefined' },
                { name: '页面初始化器配置', test: () => PageRouter.pageInitializers && Object.keys(PageRouter.pageInitializers).length > 0 },
                { name: '参数提取器配置', test: () => PageRouter.parameterExtractors && Object.keys(PageRouter.parameterExtractors).length > 0 },
                { name: '页面初始化函数', test: () => typeof PageRouter.initializePage === 'function' }
            ];
            
            for (const test of routerTests) {
                try {
                    const result = test.test();
                    if (result) {
                        addTestResult(test.name, 'success', '功能正常');
                        testResults.passed++;
                    } else {
                        addTestResult(test.name, 'error', '功能异常或不存在');
                        testResults.failed++;
                    }
                } catch (error) {
                    addTestResult(test.name, 'error', `测试异常: ${error.message}`);
                    testResults.failed++;
                }
                testResults.total++;
            }
        }

        // SEO管理器测试
        async function testSEOManager() {
            addTestSection('SEOManager功能测试', 'info');
            
            for (const test of testCases.seoTests) {
                try {
                    const result = test.test();
                    if (result) {
                        addTestResult(test.name, 'success', '功能正常');
                        testResults.passed++;
                    } else {
                        addTestResult(test.name, 'error', '功能异常或不存在');
                        testResults.failed++;
                    }
                } catch (error) {
                    addTestResult(test.name, 'error', `测试异常: ${error.message}`);
                    testResults.failed++;
                }
                testResults.total++;
            }
        }

        // 模拟URL访问
        async function simulateUrlAccess(url) {
            return new Promise((resolve) => {
                // 由于我们在静态环境中，这里模拟URL访问测试
                // 在实际环境中，这里应该发送HTTP请求
                
                // 检查URL格式是否正确
                const urlPattern = /^\/([a-z-]+\/)*([a-z0-9-]+\/)?(\d+\/)?$/;
                const isValidFormat = url === '/' || urlPattern.test(url);
                
                if (isValidFormat) {
                    resolve({ success: true });
                } else {
                    resolve({ success: false, error: 'URL格式不正确' });
                }
            });
        }

        // 添加测试部分标题
        function addTestSection(title, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const sectionHTML = `
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-${type === 'info' ? 'info-circle text-blue-500' : 'check-circle text-green-500'} mr-2"></i>
                        ${title}
                    </h3>
                    <div id="section-${Date.now()}" class="space-y-2">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
            `;
            resultsDiv.insertAdjacentHTML('beforeend', sectionHTML);
        }

        // 添加测试结果
        function addTestResult(testName, status, message) {
            const lastSection = document.querySelector('#test-results > div:last-child > div:last-child');
            if (!lastSection) return;
            
            const statusClass = status === 'success' ? 'test-success' : status === 'error' ? 'test-error' : 'test-warning';
            const iconClass = status === 'success' ? 'fa-check text-green-600' : status === 'error' ? 'fa-times text-red-600' : 'fa-exclamation-triangle text-yellow-600';
            
            const resultHTML = `
                <div class="test-result border rounded p-3 ${statusClass}">
                    <div class="flex items-center">
                        <i class="fas ${iconClass} mr-2"></i>
                        <span class="font-medium">${testName}</span>
                    </div>
                    <div class="mt-1 text-sm">${message}</div>
                </div>
            `;
            
            lastSection.insertAdjacentHTML('beforeend', resultHTML);
        }

        // 更新测试统计
        function updateTestSummary() {
            const summaryDiv = document.getElementById('test-summary');
            summaryDiv.classList.remove('hidden');
            
            document.getElementById('total-tests').textContent = testResults.total;
            document.getElementById('passed-tests').textContent = testResults.passed;
            document.getElementById('failed-tests').textContent = testResults.failed;
            document.getElementById('warning-tests').textContent = testResults.warnings;
        }

        // 清除测试结果
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('test-summary').classList.add('hidden');
            testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('URL功能测试页面已加载');
            
            // 显示当前环境信息
            addTestSection('测试环境信息', 'info');
            addTestResult('当前URL', 'success', window.location.href);
            addTestResult('用户代理', 'success', navigator.userAgent);
            addTestResult('UrlHandler可用性', typeof UrlHandler !== 'undefined' ? 'success' : 'error', 
                         typeof UrlHandler !== 'undefined' ? '已加载' : '未加载');
            addTestResult('PageRouter可用性', typeof PageRouter !== 'undefined' ? 'success' : 'error', 
                         typeof PageRouter !== 'undefined' ? '已加载' : '未加载');
            addTestResult('SEOManager可用性', typeof SEOManager !== 'undefined' ? 'success' : 'error', 
                         typeof SEOManager !== 'undefined' ? '已加载' : '未加载');
        });
    </script>
</body>
</html>
