<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>长Slug数据加载分析工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .slug-test { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .api-result { margin: 5px 0; padding: 8px; background: #fff; border: 1px solid #ddd; border-radius: 3px; }
        .timing { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <h1>🔍 长Slug数据加载分析工具</h1>
    <p>分析长slug Sources页面的数据加载问题，测试slug转换和API查询</p>

    <div class="test-section">
        <h2>📋 测试用例</h2>
        <div id="test-cases"></div>
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="test-section">
        <h2>🔧 Slug转换测试</h2>
        <div id="slug-conversion-results"></div>
    </div>

    <div class="test-section">
        <h2>🌐 API查询测试</h2>
        <div id="api-query-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 映射表测试</h2>
        <div id="mapping-results"></div>
    </div>

    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/entity-id-mapper.js"></script>
    <script>
        // 测试用例
        const testCases = [
            {
                name: "短slug测试",
                slug: "meditations",
                expectedName: "Meditations",
                url: "/sources/meditations/"
            },
            {
                name: "中等slug测试", 
                slug: "his-final-gift",
                expectedName: "His Final Gift",
                url: "/sources/his-final-gift/"
            },
            {
                name: "长slug测试1",
                slug: "the-9-cardinal-building-blocks-for-continued-success-in-leadership",
                expectedName: "The 9 Cardinal Building Blocks For Continued Success In Leadership",
                url: "/sources/the-9-cardinal-building-blocks-for-continued-success-in-leadership/"
            },
            {
                name: "长slug测试2",
                slug: "rise-up-and-salute-the-sun-the-writings-of-suzy-kassem",
                expectedName: "Rise Up And Salute The Sun The Writings Of Suzy Kassem",
                url: "/sources/rise-up-and-salute-the-sun-the-writings-of-suzy-kassem/"
            }
        ];

        function addResult(containerId, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById(containerId).appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults() {
            ['slug-conversion-results', 'api-query-results', 'mapping-results'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
        }

        // 显示测试用例
        function displayTestCases() {
            const container = document.getElementById('test-cases');
            container.innerHTML = '';
            
            testCases.forEach((testCase, index) => {
                const div = document.createElement('div');
                div.className = 'slug-test';
                div.innerHTML = `
                    <strong>${testCase.name}</strong><br>
                    <strong>Slug:</strong> ${testCase.slug}<br>
                    <strong>预期名称:</strong> ${testCase.expectedName}<br>
                    <strong>URL:</strong> ${testCase.url}<br>
                    <strong>Slug长度:</strong> ${testCase.slug.length} 字符
                `;
                container.appendChild(div);
            });
        }

        // 测试slug转换
        async function testSlugConversion() {
            addResult('slug-conversion-results', '🔧 开始测试Slug转换...', 'info');
            
            if (!window.UrlHandler) {
                addResult('slug-conversion-results', '❌ UrlHandler未加载', 'error');
                return;
            }

            for (const testCase of testCases) {
                const startTime = performance.now();
                const convertedName = window.UrlHandler.deslugify(testCase.slug);
                const endTime = performance.now();
                const duration = (endTime - startTime).toFixed(2);
                
                const isCorrect = convertedName === testCase.expectedName;
                const resultType = isCorrect ? 'success' : 'warning';
                
                addResult('slug-conversion-results', `
                    <strong>${testCase.name}</strong><br>
                    <strong>输入Slug:</strong> ${testCase.slug}<br>
                    <strong>转换结果:</strong> ${convertedName}<br>
                    <strong>预期结果:</strong> ${testCase.expectedName}<br>
                    <strong>转换正确:</strong> ${isCorrect ? '✅ 是' : '❌ 否'}<br>
                    <span class="timing">转换时间: ${duration}ms</span>
                `, resultType);
            }
        }

        // 测试API查询
        async function testApiQueries() {
            addResult('api-query-results', '🌐 开始测试API查询...', 'info');
            
            if (!window.ApiClient) {
                addResult('api-query-results', '❌ ApiClient未加载', 'error');
                return;
            }

            // 确保使用生产API
            window.ApiClient.useMockData = false;
            
            for (const testCase of testCases) {
                const convertedName = window.UrlHandler.deslugify(testCase.slug);
                
                addResult('api-query-results', `<strong>测试 ${testCase.name}</strong>`, 'info');
                
                // 测试1: 使用原始slug查询
                await testSingleApiQuery('原始slug', testCase.slug, 'api-query-results');
                
                // 测试2: 使用转换后的名称查询
                await testSingleApiQuery('转换名称', convertedName, 'api-query-results');
                
                // 测试3: 使用小写名称查询
                await testSingleApiQuery('小写名称', convertedName.toLowerCase(), 'api-query-results');
            }
        }

        async function testSingleApiQuery(queryType, queryValue, containerId) {
            try {
                const startTime = performance.now();
                const result = await window.ApiClient.getSourceByName(queryValue);
                const endTime = performance.now();
                const duration = (endTime - startTime).toFixed(2);
                
                if (result) {
                    addResult(containerId, `
                        ✅ <strong>${queryType}查询成功</strong><br>
                        <strong>查询值:</strong> "${queryValue}"<br>
                        <strong>找到来源:</strong> ${result.name} (ID: ${result.id})<br>
                        <span class="timing">查询时间: ${duration}ms</span>
                    `, 'success');
                } else {
                    addResult(containerId, `
                        ❌ <strong>${queryType}查询失败</strong><br>
                        <strong>查询值:</strong> "${queryValue}"<br>
                        <strong>结果:</strong> 未找到<br>
                        <span class="timing">查询时间: ${duration}ms</span>
                    `, 'error');
                }
            } catch (error) {
                addResult(containerId, `
                    ❌ <strong>${queryType}查询异常</strong><br>
                    <strong>查询值:</strong> "${queryValue}"<br>
                    <strong>错误:</strong> ${error.message}<br>
                `, 'error');
            }
        }

        // 测试映射表
        async function testMappingTable() {
            addResult('mapping-results', '📊 开始测试映射表...', 'info');
            
            if (!window.EntityIdMapper) {
                addResult('mapping-results', '❌ EntityIdMapper未加载', 'error');
                return;
            }

            // 获取映射表统计
            const stats = window.EntityIdMapper.getStats();
            addResult('mapping-results', `
                <strong>映射表统计:</strong><br>
                ${JSON.stringify(stats, null, 2)}
            `, 'info');

            // 测试每个slug是否在映射表中
            for (const testCase of testCases) {
                const convertedName = window.UrlHandler.deslugify(testCase.slug);
                
                // 检查是否在映射表中
                const mappedId = window.EntityIdMapper.getSourceId(convertedName);
                
                if (mappedId) {
                    addResult('mapping-results', `
                        ✅ <strong>${testCase.name}在映射表中</strong><br>
                        <strong>名称:</strong> ${convertedName}<br>
                        <strong>映射ID:</strong> ${mappedId}
                    `, 'success');
                } else {
                    addResult('mapping-results', `
                        ❌ <strong>${testCase.name}不在映射表中</strong><br>
                        <strong>名称:</strong> ${convertedName}<br>
                        <strong>需要API查询</strong>
                    `, 'warning');
                }
            }
        }

        // 运行所有测试
        async function runAllTests() {
            clearResults();
            
            addResult('slug-conversion-results', '🚀 开始完整测试流程...', 'info');
            
            // 等待所有脚本加载
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 按顺序运行测试
            await testSlugConversion();
            await testApiQueries();
            await testMappingTable();
            
            addResult('mapping-results', '✅ 所有测试完成！', 'success');
        }

        // 页面加载时显示测试用例
        window.addEventListener('load', () => {
            displayTestCases();
            addResult('slug-conversion-results', '📋 长Slug分析工具已加载，点击"运行所有测试"开始分析', 'info');
        });
    </script>
</body>
</html>
