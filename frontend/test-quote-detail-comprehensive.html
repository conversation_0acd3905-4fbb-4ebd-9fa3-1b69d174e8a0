<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Quote Detail Test - quotese.com</title>
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
</head>
<body class="light-mode bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">Comprehensive Quote Detail Test</h1>
        
        <!-- Test Results Dashboard -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="font-bold text-lg mb-2">API Tests</h3>
                <div id="api-status" class="text-gray-500">Not tested</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="font-bold text-lg mb-2">Component Tests</h3>
                <div id="component-status" class="text-gray-500">Not tested</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="font-bold text-lg mb-2">Error Handling</h3>
                <div id="error-status" class="text-gray-500">Not tested</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="font-bold text-lg mb-2">Integration</h3>
                <div id="integration-status" class="text-gray-500">Not tested</div>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Test Controls</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Function Tests -->
                <div>
                    <h3 class="text-lg font-semibold mb-3">Basic Function Tests</h3>
                    <div class="space-y-2">
                        <button onclick="testAPIAvailability()" class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                            Test API Availability
                        </button>
                        <button onclick="testComponentAvailability()" class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                            Test Component Availability
                        </button>
                        <button onclick="testQuoteLoading(1)" class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                            Test Valid Quote Loading (ID: 1)
                        </button>
                        <button onclick="testQuoteLoading(999999)" class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                            Test Invalid Quote Loading (ID: 999999)
                        </button>
                    </div>
                </div>
                
                <!-- Advanced Tests -->
                <div>
                    <h3 class="text-lg font-semibold mb-3">Advanced Tests</h3>
                    <div class="space-y-2">
                        <button onclick="testRelatedQuotes()" class="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                            Test Related Quotes Loading
                        </button>
                        <button onclick="testSidebarData()" class="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                            Test Sidebar Data Loading
                        </button>
                        <button onclick="testErrorStates()" class="w-full px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600">
                            Test Error States
                        </button>
                        <button onclick="runFullIntegrationTest()" class="w-full px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600">
                            Run Full Integration Test
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Custom Quote ID Test -->
            <div class="mt-6">
                <h3 class="text-lg font-semibold mb-3">Custom Quote ID Test</h3>
                <div class="flex gap-2">
                    <input type="number" id="customQuoteId" placeholder="Enter quote ID" 
                           class="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button onclick="testCustomQuote()" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        Test Custom ID
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Test Results Log -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold mb-4">Test Results Log</h2>
            <div id="test-log" class="bg-gray-100 dark:bg-gray-700 p-4 rounded min-h-[300px] font-mono text-sm overflow-y-auto max-h-96">
                Test log will appear here...
            </div>
            <div class="mt-4 flex gap-2">
                <button onclick="clearLog()" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    Clear Log
                </button>
                <button onclick="exportLog()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    Export Log
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/debug.js?v=20250629"></script>
    <script src="/js/component-loader.js?v=20250629"></script>
    <script src="/js/mock-data.js?v=20250629"></script>
    <script src="/js/api-client.js?v=20250629"></script>
    <script src="/js/theme.js?v=20250629"></script>
    <script src="/js/url-handler.js?v=20250629"></script>
    <script src="/js/components/quote-card.js?v=20250629"></script>
    <script src="/js/components/breadcrumb.js?v=20250629"></script>
    
    <script>
        // Test state
        let testResults = {
            api: false,
            components: false,
            errorHandling: false,
            integration: false
        };
        
        // Logging functions
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const typeColors = {
                'info': 'text-blue-600',
                'success': 'text-green-600',
                'error': 'text-red-600',
                'warning': 'text-yellow-600'
            };
            const color = typeColors[type] || 'text-gray-600';
            
            logDiv.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = 'Test log cleared...';
        }
        
        function exportLog() {
            const logContent = document.getElementById('test-log').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `quote-detail-test-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function updateStatus(category, status, message = '') {
            const statusDiv = document.getElementById(`${category}-status`);
            const colors = {
                'pass': 'text-green-600',
                'fail': 'text-red-600',
                'warning': 'text-yellow-600',
                'testing': 'text-blue-600'
            };
            
            statusDiv.className = colors[status] || 'text-gray-500';
            statusDiv.textContent = message || status.toUpperCase();
            
            if (status === 'pass') {
                testResults[category] = true;
            }
        }
        
        // Test functions
        async function testAPIAvailability() {
            log('Testing API availability...', 'info');
            updateStatus('api', 'testing', 'Testing...');
            
            try {
                if (typeof window.ApiClient === 'undefined') {
                    throw new Error('ApiClient not available');
                }
                
                log('✓ ApiClient is available', 'success');
                
                // Test basic API methods
                const methods = ['getQuoteById', 'getRelatedQuotesByAuthor', 'getCategories', 'getAuthors', 'getSources'];
                let methodsAvailable = 0;
                
                for (const method of methods) {
                    if (typeof window.ApiClient[method] === 'function') {
                        log(`✓ ApiClient.${method} is available`, 'success');
                        methodsAvailable++;
                    } else {
                        log(`✗ ApiClient.${method} is NOT available`, 'error');
                    }
                }
                
                if (methodsAvailable === methods.length) {
                    updateStatus('api', 'pass', 'All methods available');
                    log('✓ All API methods are available', 'success');
                } else {
                    updateStatus('api', 'warning', `${methodsAvailable}/${methods.length} methods`);
                    log(`⚠ Only ${methodsAvailable}/${methods.length} API methods available`, 'warning');
                }
                
            } catch (error) {
                updateStatus('api', 'fail', 'API not available');
                log(`✗ API test failed: ${error.message}`, 'error');
            }
        }
        
        async function testComponentAvailability() {
            log('Testing component availability...', 'info');
            updateStatus('component', 'testing', 'Testing...');
            
            try {
                const components = [
                    { name: 'QuoteCardComponent', obj: window.QuoteCardComponent },
                    { name: 'UrlHandler', obj: window.UrlHandler },
                    { name: 'BreadcrumbComponent', obj: window.BreadcrumbComponent }
                ];
                
                let componentsAvailable = 0;
                
                for (const comp of components) {
                    if (typeof comp.obj !== 'undefined') {
                        log(`✓ ${comp.name} is available`, 'success');
                        componentsAvailable++;
                        
                        // Test QuoteCardComponent render method
                        if (comp.name === 'QuoteCardComponent' && typeof comp.obj.render === 'function') {
                            log(`✓ ${comp.name}.render method is available`, 'success');
                        }
                    } else {
                        log(`✗ ${comp.name} is NOT available`, 'error');
                    }
                }
                
                if (componentsAvailable === components.length) {
                    updateStatus('component', 'pass', 'All components available');
                    log('✓ All components are available', 'success');
                } else {
                    updateStatus('component', 'warning', `${componentsAvailable}/${components.length} components`);
                    log(`⚠ Only ${componentsAvailable}/${components.length} components available`, 'warning');
                }
                
            } catch (error) {
                updateStatus('component', 'fail', 'Components not available');
                log(`✗ Component test failed: ${error.message}`, 'error');
            }
        }
        
        async function testQuoteLoading(quoteId) {
            log(`Testing quote loading for ID: ${quoteId}...`, 'info');
            
            try {
                if (typeof window.ApiClient === 'undefined') {
                    throw new Error('ApiClient not available');
                }
                
                const quote = await window.ApiClient.getQuoteById(quoteId);
                
                if (quote) {
                    log(`✓ Quote ${quoteId} loaded successfully`, 'success');
                    log(`  Content: "${quote.content.substring(0, 50)}..."`, 'info');
                    log(`  Author: ${quote.author?.name || 'Unknown'}`, 'info');
                    log(`  Categories: ${quote.categories?.length || 0}`, 'info');
                    log(`  Sources: ${quote.sources?.length || 0}`, 'info');
                    
                    // Test QuoteCardComponent rendering
                    if (typeof QuoteCardComponent !== 'undefined') {
                        try {
                            const card = QuoteCardComponent.render(quote, 0, {
                                showActions: true,
                                showAuthorAvatar: true,
                                showCategories: true,
                                showSources: true,
                                showDate: true,
                                isDetailPage: true
                            });
                            
                            if (card) {
                                log(`✓ QuoteCardComponent rendered successfully for quote ${quoteId}`, 'success');
                            } else {
                                log(`✗ QuoteCardComponent returned null for quote ${quoteId}`, 'error');
                            }
                        } catch (renderError) {
                            log(`✗ QuoteCardComponent render failed: ${renderError.message}`, 'error');
                        }
                    }
                    
                } else {
                    log(`✗ Quote ${quoteId} not found (returned null)`, 'warning');
                }
                
            } catch (error) {
                log(`✗ Quote loading failed for ID ${quoteId}: ${error.message}`, 'error');
            }
        }
        
        async function testRelatedQuotes() {
            log('Testing related quotes loading...', 'info');
            
            try {
                // First get a quote to test related quotes
                const quote = await window.ApiClient.getQuoteById(1);
                if (!quote || !quote.author?.id) {
                    throw new Error('No valid quote found to test related quotes');
                }
                
                const relatedQuotes = await window.ApiClient.getRelatedQuotesByAuthor(quote.author.id, quote.id, 5);
                
                log(`✓ Related quotes loaded: ${relatedQuotes?.length || 0} quotes`, 'success');
                
                if (relatedQuotes && relatedQuotes.length > 0) {
                    relatedQuotes.forEach((rq, index) => {
                        log(`  ${index + 1}. "${rq.content.substring(0, 30)}..."`, 'info');
                    });
                }
                
            } catch (error) {
                log(`✗ Related quotes test failed: ${error.message}`, 'error');
            }
        }
        
        async function testSidebarData() {
            log('Testing sidebar data loading...', 'info');
            
            try {
                const [categories, authors, sources] = await Promise.all([
                    window.ApiClient.getCategories(10),
                    window.ApiClient.getAuthors(10),
                    window.ApiClient.getSources(10)
                ]);
                
                log(`✓ Categories loaded: ${categories?.length || 0}`, 'success');
                log(`✓ Authors loaded: ${authors?.length || 0}`, 'success');
                log(`✓ Sources loaded: ${sources?.length || 0}`, 'success');
                
            } catch (error) {
                log(`✗ Sidebar data test failed: ${error.message}`, 'error');
            }
        }
        
        async function testErrorStates() {
            log('Testing error states...', 'info');
            updateStatus('error', 'testing', 'Testing...');
            
            try {
                // Test invalid quote ID
                const invalidQuote = await window.ApiClient.getQuoteById(999999);
                if (!invalidQuote) {
                    log('✓ Invalid quote ID correctly returns null', 'success');
                } else {
                    log('✗ Invalid quote ID should return null', 'error');
                }
                
                updateStatus('error', 'pass', 'Error handling works');
                
            } catch (error) {
                log(`✓ Error handling working: ${error.message}`, 'success');
                updateStatus('error', 'pass', 'Error handling works');
            }
        }
        
        async function runFullIntegrationTest() {
            log('=== Starting Full Integration Test ===', 'info');
            updateStatus('integration', 'testing', 'Testing...');
            
            try {
                await testAPIAvailability();
                await testComponentAvailability();
                await testQuoteLoading(1);
                await testQuoteLoading(999999);
                await testRelatedQuotes();
                await testSidebarData();
                await testErrorStates();
                
                const passedTests = Object.values(testResults).filter(Boolean).length;
                const totalTests = Object.keys(testResults).length;
                
                if (passedTests === totalTests) {
                    updateStatus('integration', 'pass', 'All tests passed');
                    log('=== ✓ Full Integration Test PASSED ===', 'success');
                } else {
                    updateStatus('integration', 'warning', `${passedTests}/${totalTests} passed`);
                    log(`=== ⚠ Integration Test: ${passedTests}/${totalTests} passed ===`, 'warning');
                }
                
            } catch (error) {
                updateStatus('integration', 'fail', 'Integration test failed');
                log(`=== ✗ Integration Test FAILED: ${error.message} ===`, 'error');
            }
        }
        
        function testCustomQuote() {
            const customId = document.getElementById('customQuoteId').value;
            if (customId) {
                testQuoteLoading(parseInt(customId));
            } else {
                log('✗ Please enter a quote ID', 'error');
            }
        }
        
        // Initialize
        window.addEventListener('load', () => {
            log('Test environment initialized', 'success');
            log('Ready to run tests...', 'info');
        });
    </script>
</body>
</html>
