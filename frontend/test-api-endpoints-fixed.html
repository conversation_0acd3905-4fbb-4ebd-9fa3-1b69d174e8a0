<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API端点修正测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .loading { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>🔧 API端点修正测试</h1>
    
    <div class="test-section">
        <h2>📋 配置信息</h2>
        <div id="config-info"></div>
        <button onclick="testConfig()">测试配置</button>
    </div>
    
    <div class="test-section">
        <h2>🌐 REST API测试</h2>
        <div id="rest-api-result"></div>
        <button onclick="testRestAPI()">测试REST API</button>
    </div>
    
    <div class="test-section">
        <h2>🔍 GraphQL API测试</h2>
        <div id="graphql-api-result"></div>
        <button onclick="testGraphQLAPI()">测试GraphQL API</button>
    </div>
    
    <div class="test-section">
        <h2>📊 API客户端测试</h2>
        <div id="api-client-result"></div>
        <button onclick="testApiClient()">测试API客户端</button>
    </div>

    <!-- JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    
    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            return div;
        }
        
        function testConfig() {
            const container = document.getElementById('config-info');
            container.innerHTML = '';
            
            container.appendChild(addResult('🔍 测试配置加载...', 'loading'));
            
            if (window.AppConfig) {
                container.appendChild(addResult('✅ 配置加载成功', 'success'));
                container.appendChild(addResult(`🔗 REST API端点: ${window.AppConfig.apiEndpoint}`, 'info'));
                container.appendChild(addResult(`🔍 GraphQL端点: ${window.AppConfig.graphqlEndpoint || '未配置'}`, window.AppConfig.graphqlEndpoint ? 'success' : 'error'));
                container.appendChild(addResult(`🎭 模拟数据: ${window.AppConfig.useMockData}`, 'info'));
                container.appendChild(addResult(`🐛 调试模式: ${window.AppConfig.debug}`, 'info'));
                
                const pre = document.createElement('pre');
                pre.textContent = JSON.stringify(window.AppConfig, null, 2);
                container.appendChild(pre);
            } else {
                container.appendChild(addResult('❌ 配置加载失败', 'error'));
            }
            
            if (window.ApiClient) {
                container.appendChild(addResult('✅ API客户端初始化成功', 'success'));
                container.appendChild(addResult(`🔗 REST端点: ${window.ApiClient.apiEndpoint}`, 'info'));
                container.appendChild(addResult(`🔍 GraphQL端点: ${window.ApiClient.graphqlEndpoint || '未配置'}`, window.ApiClient.graphqlEndpoint ? 'success' : 'error'));
            } else {
                container.appendChild(addResult('❌ API客户端初始化失败', 'error'));
            }
        }
        
        async function testRestAPI() {
            const container = document.getElementById('rest-api-result');
            container.innerHTML = '';
            
            container.appendChild(addResult('🔍 测试REST API...', 'loading'));
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    container.appendChild(addResult('✅ REST API连接成功', 'success'));
                    container.appendChild(addResult(`📝 消息: ${data.message}`, 'info'));
                    
                    const pre = document.createElement('pre');
                    pre.textContent = JSON.stringify(data, null, 2);
                    container.appendChild(pre);
                } else {
                    container.appendChild(addResult(`❌ REST API请求失败: ${response.status} ${response.statusText}`, 'error'));
                }
            } catch (error) {
                container.appendChild(addResult(`❌ REST API连接错误: ${error.message}`, 'error'));
            }
        }
        
        async function testGraphQLAPI() {
            const container = document.getElementById('graphql-api-result');
            container.innerHTML = '';
            
            container.appendChild(addResult('🔍 测试GraphQL API...', 'loading'));
            
            const query = `
                query {
                    quotes(first: 3) {
                        id
                        content
                        author {
                            name
                        }
                    }
                    quotesCount
                }
            `;
            
            try {
                const response = await fetch('http://127.0.0.1:8000/graphql/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({ query })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.errors) {
                        container.appendChild(addResult(`❌ GraphQL查询错误: ${data.errors[0].message}`, 'error'));
                    } else {
                        container.appendChild(addResult('✅ GraphQL API连接成功', 'success'));
                        container.appendChild(addResult(`📊 获取到 ${data.data.quotes.length} 条名言`, 'info'));
                        container.appendChild(addResult(`📈 总数量: ${data.data.quotesCount}`, 'info'));
                        
                        const pre = document.createElement('pre');
                        pre.textContent = JSON.stringify(data.data, null, 2);
                        container.appendChild(pre);
                    }
                } else {
                    container.appendChild(addResult(`❌ GraphQL API请求失败: ${response.status} ${response.statusText}`, 'error'));
                }
            } catch (error) {
                container.appendChild(addResult(`❌ GraphQL API连接错误: ${error.message}`, 'error'));
            }
        }
        
        async function testApiClient() {
            const container = document.getElementById('api-client-result');
            container.innerHTML = '';
            
            container.appendChild(addResult('🔍 测试API客户端...', 'loading'));
            
            if (!window.ApiClient) {
                container.appendChild(addResult('❌ API客户端未初始化', 'error'));
                return;
            }
            
            try {
                // 测试GraphQL查询
                container.appendChild(addResult('📊 测试getTopQuotes方法...', 'loading'));
                const quotes = await window.ApiClient.getTopQuotes(1, 3);
                
                if (quotes && quotes.quotes && quotes.quotes.length > 0) {
                    container.appendChild(addResult('✅ API客户端GraphQL查询成功', 'success'));
                    container.appendChild(addResult(`📊 获取到 ${quotes.quotes.length} 条名言`, 'info'));
                    container.appendChild(addResult(`📈 总数量: ${quotes.totalCount}`, 'info'));
                    
                    const pre = document.createElement('pre');
                    pre.textContent = JSON.stringify(quotes, null, 2);
                    container.appendChild(pre);
                } else {
                    container.appendChild(addResult('❌ API客户端返回数据为空', 'error'));
                }
            } catch (error) {
                container.appendChild(addResult(`❌ API客户端测试失败: ${error.message}`, 'error'));
                console.error('API客户端测试错误:', error);
            }
        }
        
        // 页面加载时自动测试配置
        window.addEventListener('load', function() {
            setTimeout(testConfig, 500);
        });
    </script>
</body>
</html>
