<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化Categories测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .category { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .loading { color: #666; font-style: italic; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>简化Categories测试</h1>
    
    <div id="status">正在加载...</div>
    <div id="categories-list"></div>
    
    <!-- Load scripts in correct order -->
    <script src="/js/config.js?v=20250626"></script>
    <script src="/js/api-client.js?v=20250626"></script>
    
    <script>
        async function loadAndDisplayCategories() {
            const statusDiv = document.getElementById('status');
            const listDiv = document.getElementById('categories-list');
            
            try {
                statusDiv.innerHTML = '<span class="loading">正在检查配置...</span>';
                
                // 检查配置
                if (!window.AppConfig) {
                    throw new Error('配置未加载');
                }
                
                statusDiv.innerHTML = '<span class="loading">正在检查API客户端...</span>';
                
                // 检查API客户端
                if (!window.ApiClient) {
                    throw new Error('API客户端未初始化');
                }
                
                statusDiv.innerHTML = '<span class="loading">正在加载类别数据...</span>';
                
                // 加载类别数据
                const categories = await window.ApiClient.getPopularCategories(20);
                
                statusDiv.innerHTML = `<span class="success">成功加载 ${categories.length} 个类别</span>`;
                
                // 显示类别
                listDiv.innerHTML = categories.map(category => `
                    <div class="category">
                        <strong>${category.name}</strong> - ${category.quotesCount || category.count} quotes
                    </div>
                `).join('');
                
            } catch (error) {
                statusDiv.innerHTML = `<span class="error">错误: ${error.message}</span>`;
                console.error('加载失败:', error);
            }
        }
        
        // 页面加载后开始测试
        window.addEventListener('load', function() {
            setTimeout(loadAndDisplayCategories, 500);
        });
    </script>
</body>
</html>
