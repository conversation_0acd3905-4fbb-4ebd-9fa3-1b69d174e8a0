<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API端点配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .loading {
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 API端点配置测试</h1>
        <p>此页面用于验证首页API端点配置是否正确修复</p>
        
        <div class="test-result info">
            <strong>测试目标：</strong>确保所有页面都使用统一的生产API端点 <code>http://************:8000/api/</code>
        </div>

        <button onclick="testConfiguration()">🧪 测试配置</button>
        <button onclick="testApiConnection()">🌐 测试API连接</button>
        <button onclick="testHomePage()">🏠 测试首页数据加载</button>
        <button onclick="clearResults()">🧹 清除结果</button>

        <div id="results"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testConfiguration() {
            addResult('🔍 开始测试配置...', 'info');
            
            // 测试当前环境检测
            const hostname = window.location.hostname;
            addResult(`📍 当前主机名: ${hostname}`, 'info');
            
            // 测试配置获取
            if (window.AppConfig) {
                addResult(`✅ 配置加载成功`, 'success');
                addResult(`🔗 API端点: ${window.AppConfig.apiEndpoint}`, 'info');
                addResult(`🎭 模拟数据: ${window.AppConfig.useMockData}`, 'info');
                addResult(`🐛 调试模式: ${window.AppConfig.debug}`, 'info');
                
                // 检查是否使用正确的生产API
                if (window.AppConfig.apiEndpoint === 'http://************:8000/api/') {
                    addResult(`✅ API端点配置正确！使用生产API`, 'success');
                } else {
                    addResult(`❌ API端点配置错误！应该使用 http://************:8000/api/`, 'error');
                }
                
                // 检查是否禁用模拟数据
                if (!window.AppConfig.useMockData) {
                    addResult(`✅ 模拟数据已正确禁用`, 'success');
                } else {
                    addResult(`❌ 模拟数据未禁用，应该使用真实API数据`, 'error');
                }
            } else {
                addResult(`❌ 配置加载失败`, 'error');
            }
            
            // 测试API客户端
            if (window.ApiClient) {
                addResult(`✅ API客户端初始化成功`, 'success');
                addResult(`🔗 API客户端端点: ${window.ApiClient.apiEndpoint}`, 'info');
                addResult(`🎭 API客户端模拟数据: ${window.ApiClient.useMockData}`, 'info');
            } else {
                addResult(`❌ API客户端初始化失败`, 'error');
            }
        }

        async function testApiConnection() {
            addResult('🌐 开始测试API连接...', 'info');
            
            if (!window.ApiClient) {
                addResult('❌ API客户端未初始化', 'error');
                return;
            }
            
            try {
                // 测试简单的API调用
                addResult('📡 正在调用API...', 'loading');
                const result = await window.ApiClient.getQuotes(1, 5);
                
                if (result && result.quotes && result.quotes.length > 0) {
                    addResult(`✅ API连接成功！获取到 ${result.quotes.length} 条名言`, 'success');
                    addResult(`📊 总数量: ${result.totalCount}`, 'info');
                    addResult(`📄 总页数: ${result.totalPages}`, 'info');
                    
                    // 显示第一条名言作为示例
                    const firstQuote = result.quotes[0];
                    addResult(`💬 示例名言: "${firstQuote.content.substring(0, 100)}..."`, 'info');
                    if (firstQuote.author) {
                        addResult(`👤 作者: ${firstQuote.author.name}`, 'info');
                    }
                } else {
                    addResult('❌ API返回数据为空', 'error');
                }
            } catch (error) {
                addResult(`❌ API连接失败: ${error.message}`, 'error');
                console.error('API测试错误:', error);
            }
        }

        async function testHomePage() {
            addResult('🏠 开始测试首页数据加载...', 'info');

            if (!window.ApiClient) {
                addResult('❌ API客户端未初始化', 'error');
                return;
            }

            try {
                // 测试首页相关的API调用
                addResult('📡 测试名言数据（空过滤器）...', 'loading');
                const quotesResult = await window.ApiClient.getQuotes(1, 5, {});

                addResult('📡 测试名言数据（带搜索过滤器）...', 'loading');
                const quotesWithSearchResult = await window.ApiClient.getQuotes(1, 5, { search: 'life' });

                addResult('📡 测试热门类别...', 'loading');
                const categoriesResult = await window.ApiClient.getPopularCategories(20);

                addResult('📡 测试热门作者...', 'loading');
                const authorsResult = await window.ApiClient.getPopularAuthors(20);

                addResult('📡 测试热门来源...', 'loading');
                const sourcesResult = await window.ApiClient.getPopularSources(20);

                // 汇总结果
                let successCount = 0;
                let totalTests = 5;

                if (quotesResult && quotesResult.quotes && quotesResult.quotes.length > 0) {
                    addResult(`✅ 名言数据加载成功 (${quotesResult.quotes.length}条)`, 'success');
                    addResult(`📊 总数量: ${quotesResult.totalCount}, 总页数: ${quotesResult.totalPages}`, 'info');
                    successCount++;
                } else {
                    addResult('❌ 名言数据加载失败', 'error');
                }

                if (quotesWithSearchResult && quotesWithSearchResult.quotes && quotesWithSearchResult.quotes.length > 0) {
                    addResult(`✅ 搜索名言数据加载成功 (${quotesWithSearchResult.quotes.length}条)`, 'success');
                    successCount++;
                } else {
                    addResult('❌ 搜索名言数据加载失败', 'error');
                }

                if (categoriesResult && categoriesResult.length > 0) {
                    addResult(`✅ 热门类别加载成功 (${categoriesResult.length}个)`, 'success');
                    successCount++;
                } else {
                    addResult('❌ 热门类别加载失败', 'error');
                }

                if (authorsResult && authorsResult.length > 0) {
                    addResult(`✅ 热门作者加载成功 (${authorsResult.length}个)`, 'success');
                    successCount++;
                } else {
                    addResult('❌ 热门作者加载失败', 'error');
                }

                if (sourcesResult && sourcesResult.length > 0) {
                    addResult(`✅ 热门来源加载成功 (${sourcesResult.length}个)`, 'success');
                    successCount++;
                } else {
                    addResult('❌ 热门来源加载失败', 'error');
                }

                // 总结
                if (successCount === totalTests) {
                    addResult(`🎉 首页数据加载测试完全成功！(${successCount}/${totalTests})`, 'success');
                    addResult(`🔧 GraphQL语法错误已修复`, 'success');
                } else {
                    addResult(`⚠️ 首页数据加载部分成功 (${successCount}/${totalTests})`, 'error');
                }

            } catch (error) {
                addResult(`❌ 首页数据加载测试失败: ${error.message}`, 'error');
                addResult(`🔍 错误详情: ${error.stack}`, 'error');
                console.error('首页测试错误:', error);
            }
        }

        // 页面加载时自动运行配置测试
        window.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 页面加载完成，开始自动测试...', 'info');
            setTimeout(testConfiguration, 500);
        });
    </script>
</body>
</html>
