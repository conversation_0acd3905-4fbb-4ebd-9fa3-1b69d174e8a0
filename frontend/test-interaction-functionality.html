<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interaction Functionality Test - Quote Detail Page</title>
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
</head>
<body class="light-mode bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">Interaction Functionality Test</h1>
        
        <!-- Test Controls -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Interaction Tests</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <button onclick="testBreadcrumbNavigation()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    Test Breadcrumb Navigation
                </button>
                <button onclick="testQuoteCardClicks()" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    Test Quote Card Clicks
                </button>
                <button onclick="testSidebarLinks()" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    Test Sidebar Links
                </button>
                <button onclick="testResponsiveDesign()" class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600">
                    Test Responsive Design
                </button>
                <button onclick="testURLHandling()" class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600">
                    Test URL Handling
                </button>
                <button onclick="testSEOMetadata()" class="px-4 py-2 bg-pink-500 text-white rounded hover:bg-pink-600">
                    Test SEO Metadata
                </button>
            </div>
        </div>
        
        <!-- Responsive Design Test -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Responsive Design Simulation</h2>
            
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Viewport Width:</label>
                <div class="flex gap-2">
                    <button onclick="setViewportWidth(320)" class="px-3 py-1 bg-gray-500 text-white rounded text-sm">320px (Mobile)</button>
                    <button onclick="setViewportWidth(768)" class="px-3 py-1 bg-gray-500 text-white rounded text-sm">768px (Tablet)</button>
                    <button onclick="setViewportWidth(1024)" class="px-3 py-1 bg-gray-500 text-white rounded text-sm">1024px (Desktop)</button>
                    <button onclick="setViewportWidth(1920)" class="px-3 py-1 bg-gray-500 text-white rounded text-sm">1920px (Large)</button>
                </div>
            </div>
            
            <!-- Layout Test Container -->
            <div id="layout-test-container" class="border-2 border-dashed border-gray-300 p-4 rounded">
                <div class="flex flex-col lg:flex-row gap-8">
                    <!-- Left Column (Related Quotes) -->
                    <section class="lg:w-2/3 bg-blue-100 p-4 rounded">
                        <h3 class="font-bold mb-2">Left Column (2/3 width)</h3>
                        <p class="text-sm">Related quotes section</p>
                        <div class="mt-2 space-y-2">
                            <div class="bg-white p-2 rounded shadow-sm">Sample Quote Card 1</div>
                            <div class="bg-white p-2 rounded shadow-sm">Sample Quote Card 2</div>
                        </div>
                    </section>
                    
                    <!-- Right Column (Sidebar) -->
                    <aside class="lg:w-1/3 bg-green-100 p-4 rounded">
                        <h3 class="font-bold mb-2">Right Column (1/3 width)</h3>
                        <p class="text-sm">Sidebar recommendations</p>
                        <div class="mt-2 space-y-2">
                            <div class="bg-white p-2 rounded shadow-sm">Popular Categories</div>
                            <div class="bg-white p-2 rounded shadow-sm">Popular Authors</div>
                            <div class="bg-white p-2 rounded shadow-sm">Popular Sources</div>
                        </div>
                    </aside>
                </div>
            </div>
        </div>
        
        <!-- Breadcrumb Test -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Breadcrumb Navigation Test</h2>
            <div id="breadcrumb-test-container" class="bg-gray-100 p-4 rounded">
                <!-- Breadcrumb will be generated here -->
                <nav aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-2 text-sm">
                        <li><a href="/" class="text-blue-600 hover:text-blue-800">Home</a></li>
                        <li><span class="text-gray-500">/</span></li>
                        <li><a href="/quotes/" class="text-blue-600 hover:text-blue-800">Quotes</a></li>
                        <li><span class="text-gray-500">/</span></li>
                        <li class="text-gray-700">Quote Detail</li>
                    </ol>
                </nav>
            </div>
        </div>
        
        <!-- URL Handling Test -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">URL Handling Test</h2>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Test URLs:</label>
                    <div class="space-y-2">
                        <div class="flex items-center gap-2">
                            <code class="bg-gray-100 px-2 py-1 rounded text-sm flex-1">/quotes/1/</code>
                            <button onclick="testURL('/quotes/1/')" class="px-3 py-1 bg-blue-500 text-white rounded text-sm">Test</button>
                        </div>
                        <div class="flex items-center gap-2">
                            <code class="bg-gray-100 px-2 py-1 rounded text-sm flex-1">/quotes/123/</code>
                            <button onclick="testURL('/quotes/123/')" class="px-3 py-1 bg-blue-500 text-white rounded text-sm">Test</button>
                        </div>
                        <div class="flex items-center gap-2">
                            <code class="bg-gray-100 px-2 py-1 rounded text-sm flex-1">/quotes/invalid/</code>
                            <button onclick="testURL('/quotes/invalid/')" class="px-3 py-1 bg-red-500 text-white rounded text-sm">Test</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold mb-4">Test Results</h2>
            <div id="test-results" class="bg-gray-100 dark:bg-gray-700 p-4 rounded min-h-[200px] font-mono text-sm overflow-y-auto max-h-96">
                Ready for interaction tests...
            </div>
            <button onclick="clearResults()" class="mt-4 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                Clear Results
            </button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/debug.js?v=20250629"></script>
    <script src="/js/url-handler.js?v=20250629"></script>
    <script src="/js/components/breadcrumb.js?v=20250629"></script>
    
    <script>
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const typeColor = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : type === 'warning' ? 'text-yellow-600' : 'text-blue-600';
            resultsDiv.innerHTML += `<div class="${typeColor}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = 'Ready for interaction tests...';
        }
        
        function testBreadcrumbNavigation() {
            logResult('Testing breadcrumb navigation...', 'info');
            
            try {
                // Test UrlHandler availability
                if (typeof window.UrlHandler === 'undefined') {
                    logResult('✗ UrlHandler not available', 'error');
                    return;
                }
                
                logResult('✓ UrlHandler is available', 'success');
                
                // Test breadcrumb generation methods
                const methods = ['getCurrentPageType', 'parseQuoteIdFromPath', 'getQuoteUrl'];
                let methodsAvailable = 0;
                
                for (const method of methods) {
                    if (typeof window.UrlHandler[method] === 'function') {
                        logResult(`✓ UrlHandler.${method} is available`, 'success');
                        methodsAvailable++;
                    } else {
                        logResult(`✗ UrlHandler.${method} is NOT available`, 'error');
                    }
                }
                
                if (methodsAvailable === methods.length) {
                    logResult('✓ All breadcrumb-related methods available', 'success');
                } else {
                    logResult(`⚠ Only ${methodsAvailable}/${methods.length} methods available`, 'warning');
                }
                
                // Test BreadcrumbComponent
                if (typeof window.BreadcrumbComponent !== 'undefined') {
                    logResult('✓ BreadcrumbComponent is available', 'success');
                } else {
                    logResult('⚠ BreadcrumbComponent not available', 'warning');
                }
                
            } catch (error) {
                logResult(`✗ Breadcrumb test failed: ${error.message}`, 'error');
            }
        }
        
        function testQuoteCardClicks() {
            logResult('Testing quote card click functionality...', 'info');
            
            try {
                // Create a test quote card
                const testCard = document.createElement('div');
                testCard.className = 'quote-card-component cursor-pointer p-4 bg-white rounded shadow';
                testCard.innerHTML = '<p>Test Quote Card</p>';
                
                // Add click event listener
                let clickHandled = false;
                testCard.addEventListener('click', (e) => {
                    clickHandled = true;
                    logResult('✓ Quote card click event triggered', 'success');
                });
                
                // Simulate click
                testCard.click();
                
                if (clickHandled) {
                    logResult('✓ Quote card click handling works', 'success');
                } else {
                    logResult('✗ Quote card click not handled', 'error');
                }
                
                // Test URL generation
                if (typeof window.UrlHandler !== 'undefined' && typeof window.UrlHandler.getQuoteUrl === 'function') {
                    try {
                        const testQuote = { id: 123 };
                        const url = window.UrlHandler.getQuoteUrl(testQuote);
                        logResult(`✓ Quote URL generated: ${url}`, 'success');
                    } catch (error) {
                        logResult(`✗ Quote URL generation failed: ${error.message}`, 'error');
                    }
                } else {
                    logResult('⚠ UrlHandler.getQuoteUrl not available', 'warning');
                }
                
            } catch (error) {
                logResult(`✗ Quote card click test failed: ${error.message}`, 'error');
            }
        }
        
        function testSidebarLinks() {
            logResult('Testing sidebar link functionality...', 'info');
            
            try {
                // Test URL generation methods for sidebar links
                if (typeof window.UrlHandler === 'undefined') {
                    logResult('✗ UrlHandler not available for sidebar links', 'error');
                    return;
                }
                
                const linkMethods = [
                    { name: 'getAuthorUrl', testData: { name: 'Test Author', id: 1 } },
                    { name: 'getCategoryUrl', testData: { name: 'Test Category', id: 1 } },
                    { name: 'getSourceUrl', testData: { name: 'Test Source', id: 1 } }
                ];
                
                let methodsWorking = 0;
                
                for (const method of linkMethods) {
                    if (typeof window.UrlHandler[method.name] === 'function') {
                        try {
                            const url = window.UrlHandler[method.name](method.testData);
                            logResult(`✓ ${method.name} generated: ${url}`, 'success');
                            methodsWorking++;
                        } catch (error) {
                            logResult(`✗ ${method.name} failed: ${error.message}`, 'error');
                        }
                    } else {
                        logResult(`✗ ${method.name} not available`, 'error');
                    }
                }
                
                if (methodsWorking === linkMethods.length) {
                    logResult('✓ All sidebar link methods working', 'success');
                } else {
                    logResult(`⚠ Only ${methodsWorking}/${linkMethods.length} link methods working`, 'warning');
                }
                
            } catch (error) {
                logResult(`✗ Sidebar links test failed: ${error.message}`, 'error');
            }
        }
        
        function testResponsiveDesign() {
            logResult('Testing responsive design...', 'info');
            
            try {
                const container = document.getElementById('layout-test-container');
                if (!container) {
                    logResult('✗ Layout test container not found', 'error');
                    return;
                }
                
                // Test different viewport widths
                const viewports = [
                    { width: 320, name: 'Mobile' },
                    { width: 768, name: 'Tablet' },
                    { width: 1024, name: 'Desktop' },
                    { width: 1920, name: 'Large Desktop' }
                ];
                
                for (const viewport of viewports) {
                    // Simulate viewport width by checking CSS classes
                    const flexContainer = container.querySelector('.flex');
                    if (flexContainer) {
                        const hasResponsiveClasses = flexContainer.classList.contains('flex-col') && 
                                                   flexContainer.classList.contains('lg:flex-row');
                        
                        if (hasResponsiveClasses) {
                            logResult(`✓ ${viewport.name} (${viewport.width}px): Responsive classes present`, 'success');
                        } else {
                            logResult(`✗ ${viewport.name} (${viewport.width}px): Missing responsive classes`, 'error');
                        }
                    }
                }
                
                // Test column widths
                const leftColumn = container.querySelector('.lg\\:w-2\\/3');
                const rightColumn = container.querySelector('.lg\\:w-1\\/3');
                
                if (leftColumn && rightColumn) {
                    logResult('✓ Column width classes are correct (2/3 + 1/3)', 'success');
                } else {
                    logResult('✗ Column width classes are missing or incorrect', 'error');
                }
                
            } catch (error) {
                logResult(`✗ Responsive design test failed: ${error.message}`, 'error');
            }
        }
        
        function testURLHandling() {
            logResult('Testing URL handling functionality...', 'info');
            
            try {
                if (typeof window.UrlHandler === 'undefined') {
                    logResult('✗ UrlHandler not available', 'error');
                    return;
                }
                
                // Test quote ID parsing
                const testPaths = [
                    { path: '/quotes/123/', expected: '123' },
                    { path: '/quotes/1/', expected: '1' },
                    { path: '/quotes/invalid/', expected: null }
                ];
                
                for (const test of testPaths) {
                    try {
                        // Mock the current path
                        const originalPath = window.location.pathname;
                        
                        // Test parseQuoteIdFromPath with different paths
                        if (typeof window.UrlHandler.parseQuoteIdFromPath === 'function') {
                            // This would need to be tested with actual path changes
                            logResult(`Testing path: ${test.path}`, 'info');
                        }
                        
                    } catch (error) {
                        logResult(`✗ Path test failed for ${test.path}: ${error.message}`, 'error');
                    }
                }
                
                // Test URL generation
                const testQuote = { id: 123 };
                if (typeof window.UrlHandler.getQuoteUrl === 'function') {
                    const url = window.UrlHandler.getQuoteUrl(testQuote);
                    if (url === '/quotes/123/') {
                        logResult('✓ Quote URL generation working correctly', 'success');
                    } else {
                        logResult(`✗ Quote URL generation incorrect: ${url}`, 'error');
                    }
                }
                
            } catch (error) {
                logResult(`✗ URL handling test failed: ${error.message}`, 'error');
            }
        }
        
        function testSEOMetadata() {
            logResult('Testing SEO metadata functionality...', 'info');
            
            try {
                // Check for meta tags
                const metaTags = [
                    'title',
                    'meta[name="description"]',
                    'meta[property="og:title"]',
                    'meta[property="og:description"]',
                    'meta[name="twitter:title"]'
                ];
                
                let metaTagsFound = 0;
                
                for (const selector of metaTags) {
                    const element = document.querySelector(selector);
                    if (element) {
                        logResult(`✓ Found ${selector}`, 'success');
                        metaTagsFound++;
                    } else {
                        logResult(`✗ Missing ${selector}`, 'error');
                    }
                }
                
                if (metaTagsFound === metaTags.length) {
                    logResult('✓ All SEO meta tags present', 'success');
                } else {
                    logResult(`⚠ Only ${metaTagsFound}/${metaTags.length} meta tags found`, 'warning');
                }
                
                // Test dynamic metadata update capability
                if (typeof window.SocialMetaUtil !== 'undefined') {
                    logResult('✓ SocialMetaUtil available for dynamic updates', 'success');
                } else {
                    logResult('⚠ SocialMetaUtil not available', 'warning');
                }
                
            } catch (error) {
                logResult(`✗ SEO metadata test failed: ${error.message}`, 'error');
            }
        }
        
        function setViewportWidth(width) {
            logResult(`Setting viewport width to ${width}px...`, 'info');
            
            // This is a simulation - in real testing, you'd use browser dev tools
            const container = document.getElementById('layout-test-container');
            if (container) {
                container.style.maxWidth = width + 'px';
                container.style.margin = '0 auto';
                logResult(`✓ Container width set to ${width}px for testing`, 'success');
            }
        }
        
        function testURL(url) {
            logResult(`Testing URL: ${url}`, 'info');
            
            try {
                if (typeof window.UrlHandler !== 'undefined' && typeof window.UrlHandler.parseQuoteIdFromPath === 'function') {
                    // This would need actual URL navigation to test properly
                    logResult(`URL format appears valid: ${url}`, 'info');
                    
                    // Extract ID from URL manually for testing
                    const match = url.match(/\/quotes\/(\d+)\//);
                    if (match) {
                        logResult(`✓ Quote ID extracted: ${match[1]}`, 'success');
                    } else {
                        logResult(`✗ Invalid quote URL format: ${url}`, 'error');
                    }
                } else {
                    logResult('⚠ URL parsing function not available', 'warning');
                }
            } catch (error) {
                logResult(`✗ URL test failed: ${error.message}`, 'error');
            }
        }
        
        // Initialize
        window.addEventListener('load', () => {
            logResult('Interaction functionality test environment initialized', 'success');
            logResult('Available components:', 'info');
            logResult(`- UrlHandler: ${typeof window.UrlHandler !== 'undefined' ? 'Available' : 'Not Available'}`, 'info');
            logResult(`- BreadcrumbComponent: ${typeof window.BreadcrumbComponent !== 'undefined' ? 'Available' : 'Not Available'}`, 'info');
        });
    </script>
</body>
</html>
