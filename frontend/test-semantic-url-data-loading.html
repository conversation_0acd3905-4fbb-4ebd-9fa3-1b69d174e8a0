<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语义化URL数据加载诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>语义化URL数据加载诊断工具</h1>
    <p>专门诊断category和source页面的数据加载问题</p>
    
    <!-- 加载必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/url-handler.js"></script>

    <div class="test-section">
        <h2>🔧 环境检查</h2>
        <div id="environment-status"></div>
    </div>

    <div class="test-section">
        <h2>🧪 Slug解析测试</h2>
        <button onclick="testSlugParsing()">测试Slug解析</button>
        <div id="slug-test-results"></div>
    </div>

    <div class="test-section">
        <h2>🔍 API查询测试</h2>
        <button onclick="testApiQueries()">测试API查询</button>
        <div id="api-test-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 完整数据流测试</h2>
        <button onclick="testCompleteDataFlow()">测试完整数据流</button>
        <div id="dataflow-test-results"></div>
    </div>

    <script>
        function addResult(containerId, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById(containerId).appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 环境检查
        function checkEnvironment() {
            const container = 'environment-status';
            clearResults(container);
            
            addResult(container, `📋 API端点: ${window.AppConfig?.apiEndpoint || 'Not configured'}`, 'info');
            addResult(container, `📋 使用模拟数据: ${window.ApiClient?.useMockData || 'Unknown'}`, 'info');
            addResult(container, `📋 当前URL: ${window.location.href}`, 'info');
        }

        // 测试Slug解析
        function testSlugParsing() {
            const container = 'slug-test-results';
            clearResults(container);
            
            addResult(container, '🔍 测试Slug解析功能...', 'info');
            
            // 测试URL解析函数
            const testUrls = [
                { url: '/categories/life/', type: 'category', expected: 'life' },
                { url: '/categories/friendship/', type: 'category', expected: 'friendship' },
                { url: '/sources/healology/', type: 'source', expected: 'healology' },
                { url: '/sources/meditations/', type: 'source', expected: 'meditations' },
                { url: '/sources/interview/', type: 'source', expected: 'interview' }
            ];

            testUrls.forEach(test => {
                // 临时修改URL进行测试
                const originalPath = window.location.pathname;
                
                try {
                    // 模拟URL路径
                    Object.defineProperty(window.location, 'pathname', {
                        writable: true,
                        value: test.url
                    });

                    let slug, name;
                    if (test.type === 'category') {
                        slug = window.UrlHandler.parseCategoryFromPath();
                        name = slug ? window.UrlHandler.deslugify(slug) : null;
                    } else if (test.type === 'source') {
                        slug = window.UrlHandler.parseSourceFromPath();
                        name = slug ? window.UrlHandler.deslugify(slug) : null;
                    }

                    if (slug === test.expected) {
                        addResult(container, `✅ ${test.url} → slug: "${slug}", name: "${name}"`, 'success');
                    } else {
                        addResult(container, `❌ ${test.url} → 期望: "${test.expected}", 实际: "${slug}"`, 'error');
                    }
                } catch (error) {
                    addResult(container, `❌ ${test.url} → 解析错误: ${error.message}`, 'error');
                } finally {
                    // 恢复原始URL
                    Object.defineProperty(window.location, 'pathname', {
                        writable: true,
                        value: originalPath
                    });
                }
            });
        }

        // 测试API查询
        async function testApiQueries() {
            const container = 'api-test-results';
            clearResults(container);
            
            addResult(container, '🔍 测试API查询功能...', 'info');
            
            // 确保使用生产API
            window.ApiClient.useMockData = false;
            
            // 测试类别查询
            const categoryTests = [
                { name: 'Life', slug: 'life' },
                { name: 'Friendship', slug: 'friendship' },
                { name: 'Writing', slug: 'writing' }
            ];

            for (const test of categoryTests) {
                try {
                    addResult(container, `🔍 查询类别: "${test.name}"...`, 'info');
                    const category = await window.ApiClient.getCategoryByName(test.name);
                    
                    if (category) {
                        addResult(container, `✅ 类别 "${test.name}" 找到: ID=${category.id}, Count=${category.count}`, 'success');
                        addResult(container, `<pre>${JSON.stringify(category, null, 2)}</pre>`, 'info');
                    } else {
                        addResult(container, `❌ 类别 "${test.name}" 未找到`, 'error');
                    }
                } catch (error) {
                    addResult(container, `❌ 查询类别 "${test.name}" 失败: ${error.message}`, 'error');
                }
            }

            // 测试来源查询
            const sourceTests = [
                { name: 'healology', slug: 'healology' },
                { name: 'meditations', slug: 'meditations' },
                { name: 'interview', slug: 'interview' }
            ];

            for (const test of sourceTests) {
                try {
                    addResult(container, `🔍 查询来源: "${test.name}"...`, 'info');
                    const source = await window.ApiClient.getSourceByName(test.name);
                    
                    if (source) {
                        addResult(container, `✅ 来源 "${test.name}" 找到: ID=${source.id}, Count=${source.count}`, 'success');
                        addResult(container, `<pre>${JSON.stringify(source, null, 2)}</pre>`, 'info');
                    } else {
                        addResult(container, `❌ 来源 "${test.name}" 未找到`, 'error');
                    }
                } catch (error) {
                    addResult(container, `❌ 查询来源 "${test.name}" 失败: ${error.message}`, 'error');
                }
            }
        }

        // 测试完整数据流
        async function testCompleteDataFlow() {
            const container = 'dataflow-test-results';
            clearResults(container);

            addResult(container, '🔍 测试完整数据流...', 'info');

            // 测试类别数据流：life
            try {
                addResult(container, '📋 测试类别 "life" 完整数据流...', 'info');

                // 1. 解析slug
                const categorySlug = 'life';
                const categoryName = window.UrlHandler.deslugify(categorySlug);
                addResult(container, `1️⃣ Slug解析: "${categorySlug}" → "${categoryName}"`, 'success');

                // 2. 测试多种查询方式
                addResult(container, `2️⃣ 测试类别查询（多种方式）...`, 'info');

                // 2a. 尝试原始slug
                let category = await window.ApiClient.getCategoryByName(categorySlug);
                if (category) {
                    addResult(container, `✅ 使用slug "${categorySlug}" 查询成功: ID=${category.id}, Name="${category.name}"`, 'success');
                } else {
                    addResult(container, `❌ 使用slug "${categorySlug}" 查询失败`, 'warning');

                    // 2b. 尝试转换后的名称
                    category = await window.ApiClient.getCategoryByName(categoryName);
                    if (category) {
                        addResult(container, `✅ 使用名称 "${categoryName}" 查询成功: ID=${category.id}, Name="${category.name}"`, 'success');
                    } else {
                        addResult(container, `❌ 使用名称 "${categoryName}" 查询失败`, 'warning');

                        // 2c. 尝试小写名称
                        const lowerName = categoryName.toLowerCase();
                        category = await window.ApiClient.getCategoryByName(lowerName);
                        if (category) {
                            addResult(container, `✅ 使用小写名称 "${lowerName}" 查询成功: ID=${category.id}, Name="${category.name}"`, 'success');
                        } else {
                            addResult(container, `❌ 所有查询方式都失败`, 'error');
                        }
                    }
                }

                if (!category) {
                    addResult(container, `2️⃣ ❌ 类别查询完全失败`, 'error');
                    return;
                }

                // 3. 查询名言
                addResult(container, `3️⃣ 查询类别名言...`, 'info');
                const quotesData = await window.ApiClient.getQuotes(1, 5, { categoryId: category.id });
                if (quotesData && quotesData.quotes && quotesData.quotes.length > 0) {
                    addResult(container, `✅ 名言查询成功: 获取到 ${quotesData.quotes.length} 条名言，总计 ${quotesData.totalCount} 条`, 'success');
                    addResult(container, `<pre>第一条名言: "${quotesData.quotes[0].text.substring(0, 100)}..."</pre>`, 'info');
                } else {
                    addResult(container, `❌ 名言查询失败或无数据`, 'error');
                }

            } catch (error) {
                addResult(container, `❌ 类别数据流测试失败: ${error.message}`, 'error');
            }

            // 测试来源数据流：healology
            try {
                addResult(container, '📋 测试来源 "healology" 完整数据流...', 'info');

                // 1. 解析slug
                const sourceSlug = 'healology';
                const sourceName = window.UrlHandler.deslugify(sourceSlug);
                addResult(container, `1️⃣ Slug解析: "${sourceSlug}" → "${sourceName}"`, 'success');

                // 2. 测试多种查询方式
                addResult(container, `2️⃣ 测试来源查询（多种方式）...`, 'info');

                // 2a. 尝试原始slug
                let source = await window.ApiClient.getSourceByName(sourceSlug);
                if (source) {
                    addResult(container, `✅ 使用slug "${sourceSlug}" 查询成功: ID=${source.id}, Name="${source.name}"`, 'success');
                } else {
                    addResult(container, `❌ 使用slug "${sourceSlug}" 查询失败`, 'warning');

                    // 2b. 尝试转换后的名称
                    source = await window.ApiClient.getSourceByName(sourceName);
                    if (source) {
                        addResult(container, `✅ 使用名称 "${sourceName}" 查询成功: ID=${source.id}, Name="${source.name}"`, 'success');
                    } else {
                        addResult(container, `❌ 使用名称 "${sourceName}" 查询失败`, 'warning');

                        // 2c. 尝试小写名称
                        const lowerName = sourceName.toLowerCase();
                        source = await window.ApiClient.getSourceByName(lowerName);
                        if (source) {
                            addResult(container, `✅ 使用小写名称 "${lowerName}" 查询成功: ID=${source.id}, Name="${source.name}"`, 'success');
                        } else {
                            addResult(container, `❌ 所有查询方式都失败`, 'error');
                        }
                    }
                }

                if (!source) {
                    addResult(container, `2️⃣ ❌ 来源查询完全失败`, 'error');
                    return;
                }

                // 3. 查询名言
                addResult(container, `3️⃣ 查询来源名言...`, 'info');
                const quotesData = await window.ApiClient.getQuotes(1, 5, { sourceId: source.id });
                if (quotesData && quotesData.quotes && quotesData.quotes.length > 0) {
                    addResult(container, `✅ 名言查询成功: 获取到 ${quotesData.quotes.length} 条名言，总计 ${quotesData.totalCount} 条`, 'success');
                    addResult(container, `<pre>第一条名言: "${quotesData.quotes[0].text.substring(0, 100)}..."</pre>`, 'info');
                } else {
                    addResult(container, `❌ 名言查询失败或无数据`, 'error');
                }

            } catch (error) {
                addResult(container, `❌ 来源数据流测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时检查环境
        window.addEventListener('load', () => {
            checkEnvironment();
        });
    </script>
</body>
</html>
