<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Flow Analysis</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .flow-step {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .flow-step h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Data Flow Analysis</h1>
    <p>This tool analyzes the complete data flow from quote card generation to detail page loading.</p>

    <div class="test-section">
        <h2>Complete Data Flow Test</h2>
        <button onclick="runCompleteDataFlowTest()">🔄 Run Complete Data Flow Test</button>
        <div id="data-flow-results"></div>
    </div>

    <div class="test-section">
        <h2>Page-Specific Tests</h2>
        <button onclick="testHomepageFlow()">🏠 Test Homepage Flow</button>
        <button onclick="testCategoryPageFlow()">📂 Test Category Page Flow</button>
        <button onclick="testAuthorPageFlow()">👤 Test Author Page Flow</button>
        <button onclick="testSourcePageFlow()">📚 Test Source Page Flow</button>
        <div id="page-specific-results"></div>
    </div>

    <div class="test-section">
        <h2>Navigation Testing</h2>
        <button onclick="testNavigationMethods()">🧭 Test Navigation Methods</button>
        <div id="navigation-results"></div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/components/quote-card.js"></script>

    <script>
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
        }

        function addFlowStep(containerId, stepNumber, title, content, status = 'info') {
            const container = document.getElementById(containerId);
            const step = document.createElement('div');
            step.className = 'flow-step';
            step.innerHTML = `
                <h4>Step ${stepNumber}: ${title}</h4>
                <div class="test-result ${status}">${content}</div>
            `;
            container.appendChild(step);
        }

        async function runCompleteDataFlowTest() {
            const container = 'data-flow-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Starting complete data flow analysis...');
            
            try {
                // Switch to production API
                if (window.QuoteseAPIMode && typeof window.QuoteseAPIMode.useProductionAPI === 'function') {
                    window.QuoteseAPIMode.useProductionAPI();
                    addFlowStep(container, 1, 'API Configuration', 'Switched to production API', 'success');
                } else {
                    addFlowStep(container, 1, 'API Configuration', 'Production API switch not available', 'warning');
                }
                
                // Step 2: Get sample quote data from API
                const quotesData = await window.ApiClient.getQuotes(1, 1);
                if (quotesData && quotesData.quotes && quotesData.quotes.length > 0) {
                    const sampleQuote = quotesData.quotes[0];
                    addFlowStep(container, 2, 'API Data Retrieval', 
                        `Retrieved sample quote: ID ${sampleQuote.id}, "${sampleQuote.content.substring(0, 50)}..."`, 'success');
                    
                    // Step 3: Generate quote card
                    const quoteCard = QuoteCardComponent.render(sampleQuote, 0, { showActions: true });
                    addFlowStep(container, 3, 'Quote Card Generation', 'Quote card component rendered successfully', 'success');
                    
                    // Step 4: Verify entity ID
                    const entityId = quoteCard.getAttribute('data-quote-id');
                    if (entityId === sampleQuote.id.toString()) {
                        addFlowStep(container, 4, 'Entity ID Verification', 
                            `Entity ID correctly set: ${entityId}`, 'success');
                    } else {
                        addFlowStep(container, 4, 'Entity ID Verification', 
                            `Entity ID mismatch: card=${entityId}, data=${sampleQuote.id}`, 'error');
                        return;
                    }
                    
                    // Step 5: Test URL generation
                    const quoteUrl = UrlHandler.getQuoteUrl(sampleQuote);
                    addFlowStep(container, 5, 'URL Generation', `Generated URL: ${quoteUrl}`, 'success');
                    
                    // Step 6: Test URL parsing
                    const originalPath = window.location.pathname;
                    try {
                        Object.defineProperty(window.location, 'pathname', {
                            writable: true,
                            value: quoteUrl
                        });
                        
                        const parsedId = UrlHandler.parseQuoteIdFromPath();
                        if (parsedId === parseInt(sampleQuote.id)) {
                            addFlowStep(container, 6, 'URL Parsing', 
                                `URL parsing successful: ${parsedId}`, 'success');
                        } else {
                            addFlowStep(container, 6, 'URL Parsing', 
                                `URL parsing failed: expected ${sampleQuote.id}, got ${parsedId}`, 'error');
                        }
                    } finally {
                        Object.defineProperty(window.location, 'pathname', {
                            writable: true,
                            value: originalPath
                        });
                    }
                    
                    // Step 7: Test detail page API call
                    const detailQuote = await window.ApiClient.getQuoteById(sampleQuote.id);
                    if (detailQuote) {
                        addFlowStep(container, 7, 'Detail Page API Call', 
                            `Detail page data loaded: "${detailQuote.content.substring(0, 50)}..."`, 'success');
                    } else {
                        addFlowStep(container, 7, 'Detail Page API Call', 
                            'Detail page API call returned null', 'error');
                        return;
                    }
                    
                    // Step 8: Test click event simulation
                    let clickHandled = false;
                    const originalNavigate = window.navigateToEntityWithId;
                    const originalLocation = window.location.href;
                    
                    // Mock navigation function
                    window.navigateToEntityWithId = (type, entity, url) => {
                        clickHandled = true;
                        addFlowStep(container, 8, 'Click Event Handling', 
                            `Navigation triggered: ${type}, URL: ${url}`, 'success');
                    };
                    
                    // Simulate click
                    const clickEvent = new MouseEvent('click', { bubbles: true });
                    quoteCard.dispatchEvent(clickEvent);
                    
                    // Restore original function
                    if (originalNavigate) {
                        window.navigateToEntityWithId = originalNavigate;
                    } else {
                        delete window.navigateToEntityWithId;
                    }
                    
                    if (!clickHandled) {
                        addFlowStep(container, 8, 'Click Event Handling', 
                            'Click event not handled by navigation function (may use direct navigation)', 'warning');
                    }
                    
                    addResult(container, 'success', '🎉 Complete data flow test completed successfully!');
                    
                } else {
                    addFlowStep(container, 2, 'API Data Retrieval', 'Failed to retrieve sample quote data', 'error');
                }
                
            } catch (error) {
                addResult(container, 'error', `Data flow test failed: ${error.message}`);
                console.error('Data flow test error:', error);
            }
        }

        async function testHomepageFlow() {
            const container = 'page-specific-results';
            addResult(container, 'info', 'Testing homepage data flow...');
            
            try {
                // Simulate homepage quote loading
                const quotesData = await window.ApiClient.getTopQuotes(1, 5);
                if (quotesData && quotesData.quotes) {
                    addResult(container, 'success', 
                        `Homepage: Retrieved ${quotesData.quotes.length} top quotes`);
                    
                    // Test quote card generation for homepage
                    quotesData.quotes.forEach((quote, index) => {
                        const quoteCard = QuoteCardComponent.render(quote, index, {
                            showAuthorAvatar: false,
                            showActions: false
                        });
                        
                        const entityId = quoteCard.getAttribute('data-quote-id');
                        if (entityId === quote.id.toString()) {
                            addResult(container, 'success', 
                                `Homepage quote ${index + 1}: Entity ID correct (${entityId})`);
                        } else {
                            addResult(container, 'error', 
                                `Homepage quote ${index + 1}: Entity ID mismatch`);
                        }
                    });
                } else {
                    addResult(container, 'error', 'Homepage: Failed to retrieve top quotes');
                }
            } catch (error) {
                addResult(container, 'error', `Homepage test failed: ${error.message}`);
            }
        }

        async function testCategoryPageFlow() {
            const container = 'page-specific-results';
            addResult(container, 'info', 'Testing category page data flow...');
            
            try {
                // Test with love-quotes category
                const quotesData = await window.ApiClient.getQuotes(1, 5, { categorySlug: 'love-quotes' });
                if (quotesData && quotesData.quotes) {
                    addResult(container, 'success', 
                        `Category page: Retrieved ${quotesData.quotes.length} love quotes`);
                    
                    // Test quote card generation for category page
                    quotesData.quotes.forEach((quote, index) => {
                        const quoteCard = QuoteCardComponent.render(quote, index, {
                            showAuthorAvatar: false,
                            showActions: false,
                            highlightCurrentCategory: true,
                            currentCategoryName: 'Love Quotes'
                        });
                        
                        const entityId = quoteCard.getAttribute('data-quote-id');
                        if (entityId === quote.id.toString()) {
                            addResult(container, 'success', 
                                `Category quote ${index + 1}: Entity ID correct (${entityId})`);
                        } else {
                            addResult(container, 'error', 
                                `Category quote ${index + 1}: Entity ID mismatch`);
                        }
                    });
                } else {
                    addResult(container, 'error', 'Category page: Failed to retrieve category quotes');
                }
            } catch (error) {
                addResult(container, 'error', `Category page test failed: ${error.message}`);
            }
        }

        async function testAuthorPageFlow() {
            const container = 'page-specific-results';
            addResult(container, 'info', 'Testing author page data flow...');
            
            try {
                // Test with a known author
                const quotesData = await window.ApiClient.getQuotes(1, 5, { authorSlug: 'lailah-gifty-akita' });
                if (quotesData && quotesData.quotes) {
                    addResult(container, 'success', 
                        `Author page: Retrieved ${quotesData.quotes.length} author quotes`);
                    
                    // Test quote card generation for author page
                    quotesData.quotes.forEach((quote, index) => {
                        const quoteCard = QuoteCardComponent.render(quote, index, {
                            showAuthorAvatar: false,
                            showActions: false,
                            hideEmptyAvatar: true
                        });
                        
                        const entityId = quoteCard.getAttribute('data-quote-id');
                        if (entityId === quote.id.toString()) {
                            addResult(container, 'success', 
                                `Author quote ${index + 1}: Entity ID correct (${entityId})`);
                        } else {
                            addResult(container, 'error', 
                                `Author quote ${index + 1}: Entity ID mismatch`);
                        }
                    });
                } else {
                    addResult(container, 'error', 'Author page: Failed to retrieve author quotes');
                }
            } catch (error) {
                addResult(container, 'error', `Author page test failed: ${error.message}`);
            }
        }

        async function testSourcePageFlow() {
            const container = 'page-specific-results';
            addResult(container, 'info', 'Testing source page data flow...');
            
            try {
                // Test with a known source
                const quotesData = await window.ApiClient.getQuotes(1, 5, { sourceSlug: 'and-being-free' });
                if (quotesData && quotesData.quotes) {
                    addResult(container, 'success', 
                        `Source page: Retrieved ${quotesData.quotes.length} source quotes`);
                    
                    // Test quote card generation for source page
                    quotesData.quotes.forEach((quote, index) => {
                        const quoteCard = QuoteCardComponent.render(quote, index, {
                            showAuthorAvatar: false,
                            showActions: false,
                            hideEmptyAvatar: true
                        });
                        
                        const entityId = quoteCard.getAttribute('data-quote-id');
                        if (entityId === quote.id.toString()) {
                            addResult(container, 'success', 
                                `Source quote ${index + 1}: Entity ID correct (${entityId})`);
                        } else {
                            addResult(container, 'error', 
                                `Source quote ${index + 1}: Entity ID mismatch`);
                        }
                    });
                } else {
                    addResult(container, 'error', 'Source page: Failed to retrieve source quotes');
                }
            } catch (error) {
                addResult(container, 'error', `Source page test failed: ${error.message}`);
            }
        }

        function testNavigationMethods() {
            const container = 'navigation-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing navigation methods...');
            
            // Test if navigation optimization is available
            if (typeof window.navigateToEntityWithId === 'function') {
                addResult(container, 'success', 'Optimized navigation function available');
            } else {
                addResult(container, 'warning', 'Optimized navigation function not available, using direct navigation');
            }
            
            // Test URL generation for different quote IDs
            const testIds = ['499001', '499002', '123', '1'];
            
            testIds.forEach(id => {
                try {
                    const url = UrlHandler.getQuoteUrl({ id });
                    addResult(container, 'success', `URL generation for ID ${id}: ${url}`);
                } catch (error) {
                    addResult(container, 'error', `URL generation for ID ${id}: ${error.message}`);
                }
            });
            
            // Test URL parsing
            const testUrls = ['/quotes/499001/', '/quotes/123/', '/quotes/invalid/'];
            
            testUrls.forEach(testUrl => {
                const originalPath = window.location.pathname;
                try {
                    Object.defineProperty(window.location, 'pathname', {
                        writable: true,
                        value: testUrl
                    });
                    
                    const parsedId = UrlHandler.parseQuoteIdFromPath();
                    addResult(container, parsedId ? 'success' : 'warning', 
                        `URL parsing for ${testUrl}: ${parsedId || 'null'}`);
                } finally {
                    Object.defineProperty(window.location, 'pathname', {
                        writable: true,
                        value: originalPath
                    });
                }
            });
        }

        // Auto-run basic test on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                addResult('data-flow-results', 'info', 
                    'Data flow analysis tool ready. Click "Run Complete Data Flow Test" to begin.');
            }, 1000);
        });
    </script>
</body>
</html>
