<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Categories Test - Quotese.com</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/dist/combined.css">
    <link rel="stylesheet" href="css/pages/categories.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Simple Categories Test</h1>
        
        <div>
            <button class="test-button" onclick="testBasicDOM()">测试基础DOM</button>
            <button class="test-button" onclick="testAPICall()">测试API调用</button>
            <button class="test-button" onclick="testRenderMockData()">渲染模拟数据</button>
            <button class="test-button" onclick="testFullFlow()">测试完整流程</button>
        </div>
        
        <div id="debug-info" class="debug-info">等待测试...</div>
        
        <!-- Categories display area -->
        <div style="margin-top: 20px;">
            <h2>Categories Display Area</h2>
            <div id="categories-container" class="categories-display grid-view">
                <!-- Categories will be rendered here -->
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="js/dist/core.js"></script>
    <script src="js/url-handler.js"></script>
    
    <script>
        function log(message) {
            const debugInfo = document.getElementById('debug-info');
            debugInfo.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('debug-info').textContent = '';
        }
        
        function testBasicDOM() {
            clearLog();
            log('🔍 测试基础DOM...');
            
            const container = document.getElementById('categories-container');
            log(`✅ Categories container found: ${!!container}`);
            log(`📊 Container className: ${container ? container.className : 'N/A'}`);
            log(`📊 Container children: ${container ? container.children.length : 'N/A'}`);
            
            // Test basic DOM manipulation
            if (container) {
                container.innerHTML = '<div style="padding: 20px; background: #e3f2fd; border-radius: 8px;">测试DOM操作成功！</div>';
                log('✅ 基础DOM操作测试成功');
            } else {
                log('❌ Categories container 未找到');
            }
        }
        
        async function testAPICall() {
            clearLog();
            log('🔍 测试API调用...');
            
            try {
                if (!window.ApiClient) {
                    throw new Error('ApiClient 不可用');
                }
                
                log('✅ ApiClient 可用');
                log('🔄 调用 getPopularCategories(20)...');
                
                const categories = await window.ApiClient.getPopularCategories(20);
                
                log(`✅ API调用成功`);
                log(`📊 返回数据类型: ${typeof categories}`);
                log(`📊 是否为数组: ${Array.isArray(categories)}`);
                log(`📊 数据长度: ${categories ? categories.length : 'N/A'}`);
                
                if (categories && categories.length > 0) {
                    log(`📋 前3个分类:`);
                    categories.slice(0, 3).forEach((cat, index) => {
                        log(`  ${index + 1}. ${cat.name} (ID: ${cat.id}, Count: ${cat.count || cat.quotesCount || 0})`);
                    });
                    
                    // Store for rendering test
                    window.testCategories = categories;
                } else {
                    log('❌ 没有返回数据');
                }
                
            } catch (error) {
                log(`❌ API调用失败: ${error.message}`);
                console.error('API调用错误:', error);
            }
        }
        
        function testRenderMockData() {
            clearLog();
            log('🎨 测试渲染模拟数据...');
            
            const mockCategories = [
                { id: 1, name: 'Life', count: 150 },
                { id: 2, name: 'Love', count: 120 },
                { id: 3, name: 'Success', count: 100 },
                { id: 4, name: 'Wisdom', count: 95 },
                { id: 5, name: 'Motivation', count: 90 },
                { id: 6, name: 'Happiness', count: 85 }
            ];
            
            const container = document.getElementById('categories-container');
            if (!container) {
                log('❌ Categories container 未找到');
                return;
            }
            
            log(`📦 准备渲染 ${mockCategories.length} 个模拟分类`);
            
            // Clear container
            container.innerHTML = '';
            container.className = 'categories-display grid-view';
            
            // Render categories
            mockCategories.forEach((category, index) => {
                log(`🎨 渲染分类 ${index + 1}: ${category.name}`);
                
                const categoryCard = document.createElement('a');
                categoryCard.href = '#';
                categoryCard.className = 'category-card';
                categoryCard.style.animationDelay = `${index * 0.1}s`;
                
                categoryCard.innerHTML = `
                    <div class="category-icon">
                        <i class="fas fa-tag"></i>
                    </div>
                    <h3 class="category-title">${category.name}</h3>
                    <p class="category-count">${category.count || 0} quotes</p>
                `;
                
                container.appendChild(categoryCard);
            });
            
            log(`✅ 渲染完成! Container 现在有 ${container.children.length} 个子元素`);
        }
        
        async function testFullFlow() {
            clearLog();
            log('🚀 测试完整流程...');
            
            try {
                // Step 1: Test API
                await testAPICall();
                
                if (!window.testCategories || window.testCategories.length === 0) {
                    log('⚠️ 没有API数据，使用模拟数据');
                    testRenderMockData();
                    return;
                }
                
                // Step 2: Render real data
                log('🎨 渲染真实API数据...');
                
                const categories = window.testCategories;
                const container = document.getElementById('categories-container');
                
                if (!container) {
                    log('❌ Categories container 未找到');
                    return;
                }
                
                // Clear container
                container.innerHTML = '';
                container.className = 'categories-display grid-view';
                
                // Render categories
                categories.forEach((category, index) => {
                    const categoryCard = document.createElement('a');
                    categoryCard.href = '#';
                    categoryCard.className = 'category-card';
                    categoryCard.style.animationDelay = `${index * 0.1}s`;
                    
                    categoryCard.innerHTML = `
                        <div class="category-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <h3 class="category-title">${category.name}</h3>
                        <p class="category-count">${category.count || 0} quotes</p>
                    `;
                    
                    container.appendChild(categoryCard);
                });
                
                log(`✅ 完整流程测试成功! 渲染了 ${categories.length} 个分类`);
                
            } catch (error) {
                log(`❌ 完整流程测试失败: ${error.message}`);
                console.error('完整流程错误:', error);
            }
        }
        
        // Auto-run basic test on load
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成');
            setTimeout(() => {
                testBasicDOM();
            }, 500);
        });
    </script>
</body>
</html>
