<nav class="sticky top-0 z-50 bg-white dark:bg-gray-900 shadow-md">
    <div class="container mx-auto px-4 py-3">
        <div class="flex justify-between items-center">
            <!-- Logo with link to homepage -->
            <div class="flex items-center space-x-2">
                <a href="https://quotese.com/index.html" class="flex items-center space-x-2 hover:opacity-90 transition-opacity">
                    <i class="fas fa-quote-left text-yellow-500 text-2xl"></i>
                    <h1 class="text-xl font-bold"><span class="text-yellow-500">quotes</span>e.com</h1>
                </a>
            </div>

            <!-- Main Navigation -->
            <div class="hidden md:flex space-x-8">
                <a href="/" class="nav-link text-gray-600 dark:text-gray-300 hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors duration-300 font-medium">
                    <i class="fas fa-home mr-1"></i>Home
                </a>
                <a href="/categories/" class="nav-link text-gray-600 dark:text-gray-300 hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors duration-300 font-medium">
                    <i class="fas fa-tags mr-1"></i>Categories
                </a>
                <a href="/authors/" class="nav-link text-gray-600 dark:text-gray-300 hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors duration-300 font-medium">
                    <i class="fas fa-users mr-1"></i>Authors
                </a>
                <a href="/sources/" class="nav-link text-gray-600 dark:text-gray-300 hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors duration-300 font-medium">
                    <i class="fas fa-book mr-1"></i>Sources
                </a>
            </div>

            <!-- Search and Theme Toggle -->
            <div class="flex items-center space-x-4">
                <div class="relative hidden md:block">
                    <input type="text" placeholder="Search quotes..." class="pl-10 pr-4 py-2 rounded-full border border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:bg-gray-800 dark:text-gray-100">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
                <!-- Theme toggle button has been hidden as requested -->
                <button class="md:hidden p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800" id="mobile-menu-button">
                    <i class="fas fa-bars text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Menu (Hidden by default) -->
        <div id="mobile-menu" class="hidden md:hidden mt-4 pb-2">
            <div class="relative mb-4">
                <input type="text" placeholder="Search quotes..." class="w-full pl-10 pr-4 py-2 rounded-full border border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:bg-gray-800 dark:text-gray-100">
                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            </div>
            <div class="flex flex-col space-y-3">
                <a href="/" class="mobile-nav-link flex items-center py-2 px-3 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors duration-300">
                    <i class="fas fa-home mr-3 w-5"></i>Home
                </a>
                <a href="/categories/" class="mobile-nav-link flex items-center py-2 px-3 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors duration-300">
                    <i class="fas fa-tags mr-3 w-5"></i>Categories
                </a>
                <a href="/authors/" class="mobile-nav-link flex items-center py-2 px-3 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors duration-300">
                    <i class="fas fa-users mr-3 w-5"></i>Authors
                </a>
                <a href="/sources/" class="mobile-nav-link flex items-center py-2 px-3 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors duration-300">
                    <i class="fas fa-book mr-3 w-5"></i>Sources
                </a>
            </div>
        </div>
    </div>
</nav>
