<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整环境验证测试 - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .test-result {
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .test-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .test-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .api-result {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px;
            margin: 4px 0;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-check-double mr-3 text-green-500"></i>
                完整环境验证测试
            </h1>
            
            <!-- 环境状态概览 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">环境状态概览</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-4 border rounded text-center">
                        <div class="text-2xl mb-2">🌐</div>
                        <h3 class="font-medium">前端服务</h3>
                        <p class="text-sm text-gray-600">localhost:8081</p>
                        <div id="frontend-status" class="mt-2">
                            <span class="inline-block w-3 h-3 bg-green-500 rounded-full mr-1"></span>
                            <span class="text-green-600 text-sm">运行中</span>
                        </div>
                    </div>
                    <div class="p-4 border rounded text-center">
                        <div class="text-2xl mb-2">⚙️</div>
                        <h3 class="font-medium">后端服务</h3>
                        <p class="text-sm text-gray-600">localhost:8001</p>
                        <div id="backend-status" class="mt-2">
                            <span class="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-1"></span>
                            <span class="text-yellow-600 text-sm">检测中...</span>
                        </div>
                    </div>
                    <div class="p-4 border rounded text-center">
                        <div class="text-2xl mb-2">🗄️</div>
                        <h3 class="font-medium">数据库</h3>
                        <p class="text-sm text-gray-600">SQLite</p>
                        <div id="database-status" class="mt-2">
                            <span class="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-1"></span>
                            <span class="text-yellow-600 text-sm">检测中...</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 测试控制面板 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">完整环境测试</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <button onclick="testEnvironmentSetup()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        <i class="fas fa-cog mr-2"></i>环境设置
                    </button>
                    <button onclick="testFrontendModules()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        <i class="fas fa-puzzle-piece mr-2"></i>前端模块
                    </button>
                    <button onclick="testBackendAPIs()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        <i class="fas fa-server mr-2"></i>后端API
                    </button>
                    <button onclick="testDataIntegrity()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                        <i class="fas fa-database mr-2"></i>数据完整性
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <button onclick="testURLFunctionality()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        <i class="fas fa-link mr-2"></i>URL功能
                    </button>
                    <button onclick="testSEOFeatures()" class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                        <i class="fas fa-search mr-2"></i>SEO功能
                    </button>
                    <button onclick="testPageNavigation()" class="bg-pink-500 text-white px-4 py-2 rounded hover:bg-pink-600">
                        <i class="fas fa-route mr-2"></i>页面导航
                    </button>
                    <button onclick="testUserExperience()" class="bg-teal-500 text-white px-4 py-2 rounded hover:bg-teal-600">
                        <i class="fas fa-user mr-2"></i>用户体验
                    </button>
                </div>
                
                <div class="flex gap-4">
                    <button onclick="runCompleteTest()" class="bg-gray-800 text-white px-6 py-2 rounded hover:bg-gray-900">
                        <i class="fas fa-play mr-2"></i>运行完整测试
                    </button>
                    <button onclick="clearResults()" class="bg-gray-400 text-white px-4 py-2 rounded hover:bg-gray-500">
                        <i class="fas fa-trash mr-2"></i>清除结果
                    </button>
                    <button onclick="generateReport()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        <i class="fas fa-file-alt mr-2"></i>生成报告
                    </button>
                </div>
            </div>
            
            <!-- 测试结果显示区域 -->
            <div id="test-results" class="space-y-6">
                <!-- 测试结果将在这里显示 -->
            </div>
            
            <!-- 测试统计 -->
            <div id="test-summary" class="mt-8 bg-white rounded-lg shadow-md p-6 hidden">
                <h3 class="text-lg font-semibold mb-4">测试统计</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600" id="total-tests">0</div>
                        <div class="text-sm text-gray-600">总测试数</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600" id="passed-tests">0</div>
                        <div class="text-sm text-gray-600">通过</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600" id="failed-tests">0</div>
                        <div class="text-sm text-gray-600">失败</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-600" id="warning-tests">0</div>
                        <div class="text-sm text-gray-600">警告</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Core Scripts -->
    <script src="js/url-handler.js"></script>
    <script src="js/seo-manager.js"></script>
    <script src="js/page-router.js"></script>
    <script src="test-pages-functionality.js"></script>
    
    <script>
        const API_BASE_URL = 'http://localhost:8001';
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0
        };

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkEnvironmentStatus();
        });

        // 检查环境状态
        async function checkEnvironmentStatus() {
            // 检查后端状态
            try {
                const response = await fetch(`${API_BASE_URL}/admin/`, { mode: 'no-cors' });
                updateBackendStatus(true);
            } catch (error) {
                updateBackendStatus(false);
            }

            // 检查数据库状态（通过API）
            try {
                const response = await fetch(`${API_BASE_URL}/api/authors/`, { mode: 'cors' });
                if (response.ok) {
                    updateDatabaseStatus(true);
                } else {
                    updateDatabaseStatus(false);
                }
            } catch (error) {
                updateDatabaseStatus(false);
            }
        }

        // 更新后端状态
        function updateBackendStatus(isOnline) {
            const statusDiv = document.getElementById('backend-status');
            if (isOnline) {
                statusDiv.innerHTML = `
                    <span class="inline-block w-3 h-3 bg-green-500 rounded-full mr-1"></span>
                    <span class="text-green-600 text-sm">运行中</span>
                `;
            } else {
                statusDiv.innerHTML = `
                    <span class="inline-block w-3 h-3 bg-red-500 rounded-full mr-1"></span>
                    <span class="text-red-600 text-sm">离线</span>
                `;
            }
        }

        // 更新数据库状态
        function updateDatabaseStatus(isConnected) {
            const statusDiv = document.getElementById('database-status');
            if (isConnected) {
                statusDiv.innerHTML = `
                    <span class="inline-block w-3 h-3 bg-green-500 rounded-full mr-1"></span>
                    <span class="text-green-600 text-sm">连接正常</span>
                `;
            } else {
                statusDiv.innerHTML = `
                    <span class="inline-block w-3 h-3 bg-red-500 rounded-full mr-1"></span>
                    <span class="text-red-600 text-sm">连接失败</span>
                `;
            }
        }

        // 测试环境设置
        async function testEnvironmentSetup() {
            addTestSection('环境设置测试');
            
            // 测试前端服务
            addTestResult('前端服务', 'success', '前端HTTP服务器运行正常 (localhost:8081)');
            testResults.passed++;
            testResults.total++;
            
            // 测试后端服务
            try {
                const response = await fetch(`${API_BASE_URL}/admin/`, { mode: 'no-cors' });
                addTestResult('后端服务', 'success', 'Django开发服务器运行正常 (localhost:8001)');
                testResults.passed++;
            } catch (error) {
                addTestResult('后端服务', 'error', `Django服务器连接失败: ${error.message}`);
                testResults.failed++;
            }
            testResults.total++;
            
            // 测试数据库连接
            try {
                const response = await fetch(`${API_BASE_URL}/api/authors/`, { mode: 'cors' });
                if (response.ok) {
                    addTestResult('数据库连接', 'success', 'SQLite数据库连接正常');
                    testResults.passed++;
                } else {
                    addTestResult('数据库连接', 'error', `数据库API响应异常: ${response.status}`);
                    testResults.failed++;
                }
            } catch (error) {
                addTestResult('数据库连接', 'error', `数据库连接失败: ${error.message}`);
                testResults.failed++;
            }
            testResults.total++;
        }

        // 测试前端模块
        async function testFrontendModules() {
            addTestSection('前端模块测试');
            
            const modules = [
                { name: 'UrlHandler', obj: window.UrlHandler },
                { name: 'PageRouter', obj: window.PageRouter },
                { name: 'SEOManager', obj: window.SEOManager }
            ];
            
            modules.forEach(module => {
                if (module.obj && typeof module.obj === 'object') {
                    addTestResult(module.name, 'success', `${module.name}模块加载正常`);
                    testResults.passed++;
                } else {
                    addTestResult(module.name, 'error', `${module.name}模块未加载或不可用`);
                    testResults.failed++;
                }
                testResults.total++;
            });
        }

        // 测试后端API
        async function testBackendAPIs() {
            addTestSection('后端API测试');
            
            const apis = [
                { name: '作者API', url: '/api/authors/' },
                { name: '类别API', url: '/api/categories/' },
                { name: '来源API', url: '/api/sources/' },
                { name: '名言API', url: '/api/quotes/' }
            ];
            
            for (const api of apis) {
                try {
                    const response = await fetch(`${API_BASE_URL}${api.url}`, { mode: 'cors' });
                    if (response.ok) {
                        const data = await response.json();
                        addTestResult(api.name, 'success', `API正常，返回${data.length || 0}条记录`);
                        testResults.passed++;
                    } else {
                        addTestResult(api.name, 'error', `API响应异常: ${response.status}`);
                        testResults.failed++;
                    }
                } catch (error) {
                    addTestResult(api.name, 'error', `API请求失败: ${error.message}`);
                    testResults.failed++;
                }
                testResults.total++;
            }
        }

        // 测试数据完整性
        async function testDataIntegrity() {
            addTestSection('数据完整性测试');
            
            try {
                // 检查作者数据
                const authorsResponse = await fetch(`${API_BASE_URL}/api/authors/`, { mode: 'cors' });
                if (authorsResponse.ok) {
                    const authors = await authorsResponse.json();
                    if (authors.length >= 10) {
                        addTestResult('作者数据', 'success', `作者数据完整，共${authors.length}个作者`);
                        testResults.passed++;
                    } else {
                        addTestResult('作者数据', 'warning', `作者数据较少，仅${authors.length}个作者`);
                        testResults.warnings++;
                    }
                } else {
                    addTestResult('作者数据', 'error', '无法获取作者数据');
                    testResults.failed++;
                }
                testResults.total++;
                
                // 检查名言数据
                const quotesResponse = await fetch(`${API_BASE_URL}/api/quotes/`, { mode: 'cors' });
                if (quotesResponse.ok) {
                    const quotes = await quotesResponse.json();
                    if (quotes.length >= 10) {
                        addTestResult('名言数据', 'success', `名言数据完整，共${quotes.length}条名言`);
                        testResults.passed++;
                    } else {
                        addTestResult('名言数据', 'warning', `名言数据较少，仅${quotes.length}条名言`);
                        testResults.warnings++;
                    }
                } else {
                    addTestResult('名言数据', 'error', '无法获取名言数据');
                    testResults.failed++;
                }
                testResults.total++;
                
            } catch (error) {
                addTestResult('数据完整性', 'error', `数据检查失败: ${error.message}`);
                testResults.failed++;
                testResults.total++;
            }
        }

        // 测试URL功能
        async function testURLFunctionality() {
            addTestSection('URL功能测试');
            
            if (window.PageFunctionalityTester) {
                const tester = new PageFunctionalityTester();
                const summary = await tester.runAllTests();
                
                addTestResult('URL功能测试', 'success', `完成${summary.total}个测试，成功率${summary.successRate}%`);
                testResults.passed += summary.passed;
                testResults.failed += summary.failed;
                testResults.total += summary.total;
            } else {
                addTestResult('URL功能测试', 'error', 'PageFunctionalityTester未加载');
                testResults.failed++;
                testResults.total++;
            }
        }

        // 测试SEO功能
        async function testSEOFeatures() {
            addTestSection('SEO功能测试');
            
            if (window.SEOManager) {
                // 测试SEO验证
                const validation = SEOManager.validateSEO();
                if (validation.valid) {
                    addTestResult('SEO验证', 'success', 'SEO标签验证通过');
                    testResults.passed++;
                } else {
                    addTestResult('SEO验证', 'warning', `SEO验证有${validation.errors.length}个错误`);
                    testResults.warnings++;
                }
                testResults.total++;
                
                // 测试SEO数据生成
                const testPageData = {
                    pageType: 'author-detail',
                    params: { authorName: 'Albert Einstein', authorSlug: 'albert-einstein' },
                    canonicalUrl: 'http://localhost:8081/authors/albert-einstein/'
                };
                
                try {
                    const seoData = SEOManager.generateSEOData(testPageData);
                    if (seoData && seoData.title && seoData.description) {
                        addTestResult('SEO数据生成', 'success', 'SEO数据生成正常');
                        testResults.passed++;
                    } else {
                        addTestResult('SEO数据生成', 'error', 'SEO数据生成异常');
                        testResults.failed++;
                    }
                } catch (error) {
                    addTestResult('SEO数据生成', 'error', `SEO生成失败: ${error.message}`);
                    testResults.failed++;
                }
                testResults.total++;
            } else {
                addTestResult('SEO功能', 'error', 'SEOManager未加载');
                testResults.failed++;
                testResults.total++;
            }
        }

        // 测试页面导航
        async function testPageNavigation() {
            addTestSection('页面导航测试');
            
            const testUrls = [
                { name: '首页', url: '/' },
                { name: '作者页', url: '/authors/albert-einstein/' },
                { name: '类别页', url: '/categories/inspirational/' },
                { name: '名言页', url: '/quotes/123/' }
            ];
            
            testUrls.forEach(test => {
                try {
                    // 模拟URL变化
                    const originalUrl = window.location.href;
                    history.pushState({}, '', test.url);
                    
                    if (window.UrlHandler) {
                        const pageType = UrlHandler.getCurrentPageType();
                        if (pageType) {
                            addTestResult(`${test.name}导航`, 'success', `页面类型识别: ${pageType}`);
                            testResults.passed++;
                        } else {
                            addTestResult(`${test.name}导航`, 'error', '页面类型识别失败');
                            testResults.failed++;
                        }
                    } else {
                        addTestResult(`${test.name}导航`, 'error', 'UrlHandler不可用');
                        testResults.failed++;
                    }
                    
                    // 恢复原URL
                    history.pushState({}, '', originalUrl);
                } catch (error) {
                    addTestResult(`${test.name}导航`, 'error', `导航测试失败: ${error.message}`);
                    testResults.failed++;
                }
                testResults.total++;
            });
        }

        // 测试用户体验
        async function testUserExperience() {
            addTestSection('用户体验测试');
            
            // 测试页面加载速度
            const startTime = performance.now();
            await new Promise(resolve => setTimeout(resolve, 100)); // 模拟加载
            const loadTime = performance.now() - startTime;
            
            if (loadTime < 1000) {
                addTestResult('页面加载速度', 'success', `加载时间: ${loadTime.toFixed(2)}ms`);
                testResults.passed++;
            } else {
                addTestResult('页面加载速度', 'warning', `加载时间较慢: ${loadTime.toFixed(2)}ms`);
                testResults.warnings++;
            }
            testResults.total++;
            
            // 测试响应式设计
            const isMobile = window.innerWidth < 768;
            addTestResult('响应式设计', 'success', `当前视口: ${window.innerWidth}x${window.innerHeight} (${isMobile ? '移动端' : '桌面端'})`);
            testResults.passed++;
            testResults.total++;
            
            // 测试JavaScript错误
            const hasErrors = window.onerror !== null;
            addTestResult('JavaScript错误', hasErrors ? 'warning' : 'success', hasErrors ? '检测到JavaScript错误' : '无JavaScript错误');
            if (hasErrors) {
                testResults.warnings++;
            } else {
                testResults.passed++;
            }
            testResults.total++;
        }

        // 运行完整测试
        async function runCompleteTest() {
            clearResults();
            testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };
            
            addTestSection('开始运行完整环境验证测试');
            
            await testEnvironmentSetup();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testFrontendModules();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testBackendAPIs();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDataIntegrity();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testURLFunctionality();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testSEOFeatures();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPageNavigation();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testUserExperience();
            
            updateTestSummary();
            addTestSection('完整环境验证测试完成');
        }

        // 生成测试报告
        function generateReport() {
            const report = {
                timestamp: new Date().toISOString(),
                environment: {
                    frontend: 'localhost:8081',
                    backend: 'localhost:8001',
                    database: 'SQLite'
                },
                results: testResults,
                successRate: testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0
            };
            
            const reportText = JSON.stringify(report, null, 2);
            const blob = new Blob([reportText], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `quotese-environment-test-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            addTestSection('测试报告已生成并下载');
        }

        // 工具函数
        function addTestSection(title) {
            const resultsDiv = document.getElementById('test-results');
            const sectionHTML = `
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        ${title}
                    </h3>
                    <div id="section-${Date.now()}" class="space-y-2">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
            `;
            resultsDiv.insertAdjacentHTML('beforeend', sectionHTML);
        }

        function addTestResult(testName, status, message) {
            const lastSection = document.querySelector('#test-results > div:last-child > div:last-child');
            if (!lastSection) return;
            
            const statusClass = status === 'success' ? 'test-success' : status === 'error' ? 'test-error' : 'test-warning';
            const iconClass = status === 'success' ? 'fa-check text-green-600' : status === 'error' ? 'fa-times text-red-600' : 'fa-exclamation-triangle text-yellow-600';
            
            const resultHTML = `
                <div class="test-result border rounded p-3 ${statusClass}">
                    <div class="flex items-center">
                        <i class="fas ${iconClass} mr-2"></i>
                        <span class="font-medium">${testName}</span>
                    </div>
                    <div class="mt-1 text-sm">${message}</div>
                </div>
            `;
            
            lastSection.insertAdjacentHTML('beforeend', resultHTML);
        }

        function updateTestSummary() {
            const summaryDiv = document.getElementById('test-summary');
            summaryDiv.classList.remove('hidden');
            
            document.getElementById('total-tests').textContent = testResults.total;
            document.getElementById('passed-tests').textContent = testResults.passed;
            document.getElementById('failed-tests').textContent = testResults.failed;
            document.getElementById('warning-tests').textContent = testResults.warnings;
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('test-summary').classList.add('hidden');
        }
    </script>
</body>
</html>
