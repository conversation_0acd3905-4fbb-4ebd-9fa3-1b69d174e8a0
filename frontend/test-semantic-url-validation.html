<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语义化URL页面验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
        .test-card { border: 1px solid #ddd; border-radius: 5px; padding: 15px; }
        .test-card h4 { margin-top: 0; color: #333; }
        .performance-metric { display: inline-block; margin: 5px; padding: 5px 10px; background: #f8f9fa; border-radius: 3px; font-size: 12px; }
        .url-link { color: #007bff; text-decoration: none; font-family: monospace; }
        .url-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>🧪 语义化URL页面验证测试</h1>
    <p>系统性验证Categories、Authors、Sources页面的数据加载功能和映射表效果</p>
    
    <!-- 加载必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/entity-id-mapper.js"></script>

    <div class="test-section">
        <h2>📊 测试概览</h2>
        <button onclick="runAllTests()">🚀 运行所有测试</button>
        <button onclick="clearAllResults()">🧹 清空结果</button>
        <div id="test-overview"></div>
    </div>

    <div class="test-section">
        <h2>🏷️ Categories页面测试</h2>
        <button onclick="testCategoryPages()">测试Categories页面</button>
        <div id="category-test-results"></div>
    </div>

    <div class="test-section">
        <h2>👤 Authors页面测试</h2>
        <button onclick="testAuthorPages()">测试Authors页面</button>
        <div id="author-test-results"></div>
    </div>

    <div class="test-section">
        <h2>📚 Sources页面测试</h2>
        <button onclick="testSourcePages()">测试Sources页面</button>
        <div id="source-test-results"></div>
    </div>

    <div class="test-section">
        <h2>⚡ 性能对比分析</h2>
        <button onclick="runPerformanceAnalysis()">运行性能分析</button>
        <div id="performance-analysis"></div>
    </div>

    <script>
        // 测试配置
        const TEST_CONFIG = {
            categories: {
                known: [
                    { slug: 'life', name: 'Life', expectedId: 71523 },
                    { slug: 'writing', name: 'Writing', expectedId: 142145 }
                ],
                unknown: [
                    { slug: 'friendship', name: 'Friendship' },
                    { slug: 'wisdom', name: 'Wisdom' },
                    { slug: 'art', name: 'Art' }
                ]
            },
            authors: {
                known: [
                    { slug: 'albert-einstein', name: 'Albert Einstein', expectedId: 2013 }
                ],
                unknown: [
                    { slug: 'pearl-zhu', name: 'Pearl Zhu' },
                    { slug: 'steve-jobs', name: 'Steve Jobs' },
                    { slug: 'mark-twain', name: 'Mark Twain' }
                ]
            },
            sources: {
                known: [],
                unknown: [
                    { slug: 'meditations', name: 'Meditations' },
                    { slug: 'healology', name: 'Healology' },
                    { slug: 'interview', name: 'Interview' }
                ]
            }
        };

        function addResult(containerId, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById(containerId).appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function clearAllResults() {
            ['test-overview', 'category-test-results', 'author-test-results', 'source-test-results', 'performance-analysis'].forEach(clearResults);
        }

        // 测试单个页面的数据加载
        async function testPageDataLoading(entityType, slug, name, expectedId = null) {
            const startTime = performance.now();
            
            try {
                // 确保使用生产API
                window.ApiClient.useMockData = false;
                
                let result = null;
                let source = 'unknown';
                
                // 检查是否使用了映射表
                if (window.EntityIdMapper) {
                    const knownId = window.EntityIdMapper.getKnownId(entityType, slug);
                    if (knownId) {
                        result = { id: knownId, name: name, fromCache: true };
                        source = 'mapping';
                    }
                }
                
                // 如果没有映射，使用API查询
                if (!result) {
                    const apiMethods = {
                        categories: window.ApiClient.getCategoryByName.bind(window.ApiClient),
                        authors: window.ApiClient.getAuthorByName.bind(window.ApiClient),
                        sources: window.ApiClient.getSourceByName.bind(window.ApiClient)
                    };
                    
                    result = await apiMethods[entityType](name);
                    source = 'api';
                }
                
                const endTime = performance.now();
                const duration = Math.round(endTime - startTime);
                
                if (result) {
                    const idMatch = expectedId ? (result.id === expectedId ? '✅' : '❌') : '❓';
                    return {
                        success: true,
                        result: result,
                        source: source,
                        duration: duration,
                        idMatch: idMatch,
                        message: `${idMatch} ${slug} → ID: ${result.id} (${source}, ${duration}ms)`
                    };
                } else {
                    return {
                        success: false,
                        source: source,
                        duration: duration,
                        message: `❌ ${slug} 未找到 (${duration}ms)`
                    };
                }
                
            } catch (error) {
                const endTime = performance.now();
                const duration = Math.round(endTime - startTime);
                return {
                    success: false,
                    source: 'error',
                    duration: duration,
                    message: `❌ ${slug} 异常: ${error.message} (${duration}ms)`
                };
            }
        }

        // 测试Categories页面
        async function testCategoryPages() {
            const container = 'category-test-results';
            clearResults(container);
            
            addResult(container, '🔍 测试Categories页面数据加载...', 'info');
            
            const allCategories = [...TEST_CONFIG.categories.known, ...TEST_CONFIG.categories.unknown];
            const results = [];
            
            for (const category of allCategories) {
                const testResult = await testPageDataLoading('categories', category.slug, category.name, category.expectedId);
                results.push(testResult);
                
                const type = testResult.success ? 'success' : 'error';
                addResult(container, testResult.message, type);
                
                // 添加页面链接
                const url = `/categories/${category.slug}/`;
                addResult(container, `   📄 <a href="${url}" class="url-link" target="_blank">${url}</a>`, 'info');
            }
            
            // 统计结果
            const successful = results.filter(r => r.success).length;
            const fromMapping = results.filter(r => r.source === 'mapping').length;
            const avgDuration = Math.round(results.reduce((sum, r) => sum + r.duration, 0) / results.length);
            
            addResult(container, `📊 Categories测试完成: ${successful}/${allCategories.length} 成功, ${fromMapping} 个使用映射表, 平均耗时 ${avgDuration}ms`, 'info');
        }

        // 测试Authors页面
        async function testAuthorPages() {
            const container = 'author-test-results';
            clearResults(container);
            
            addResult(container, '🔍 测试Authors页面数据加载...', 'info');
            
            const allAuthors = [...TEST_CONFIG.authors.known, ...TEST_CONFIG.authors.unknown];
            const results = [];
            
            for (const author of allAuthors) {
                const testResult = await testPageDataLoading('authors', author.slug, author.name, author.expectedId);
                results.push(testResult);
                
                const type = testResult.success ? 'success' : 'error';
                addResult(container, testResult.message, type);
                
                // 添加页面链接
                const url = `/authors/${author.slug}/`;
                addResult(container, `   📄 <a href="${url}" class="url-link" target="_blank">${url}</a>`, 'info');
            }
            
            // 统计结果
            const successful = results.filter(r => r.success).length;
            const fromMapping = results.filter(r => r.source === 'mapping').length;
            const avgDuration = Math.round(results.reduce((sum, r) => sum + r.duration, 0) / results.length);
            
            addResult(container, `📊 Authors测试完成: ${successful}/${allAuthors.length} 成功, ${fromMapping} 个使用映射表, 平均耗时 ${avgDuration}ms`, 'info');
        }

        // 测试Sources页面
        async function testSourcePages() {
            const container = 'source-test-results';
            clearResults(container);
            
            addResult(container, '🔍 测试Sources页面数据加载...', 'info');
            
            const allSources = [...TEST_CONFIG.sources.known, ...TEST_CONFIG.sources.unknown];
            const results = [];
            
            for (const source of allSources) {
                const testResult = await testPageDataLoading('sources', source.slug, source.name, source.expectedId);
                results.push(testResult);
                
                const type = testResult.success ? 'success' : 'error';
                addResult(container, testResult.message, type);
                
                // 添加页面链接
                const url = `/sources/${source.slug}/`;
                addResult(container, `   📄 <a href="${url}" class="url-link" target="_blank">${url}</a>`, 'info');
            }
            
            // 统计结果
            const successful = results.filter(r => r.success).length;
            const fromMapping = results.filter(r => r.source === 'mapping').length;
            const avgDuration = Math.round(results.reduce((sum, r) => sum + r.duration, 0) / results.length);
            
            addResult(container, `📊 Sources测试完成: ${successful}/${allSources.length} 成功, ${fromMapping} 个使用映射表, 平均耗时 ${avgDuration}ms`, 'info');
        }

        // 性能对比分析
        async function runPerformanceAnalysis() {
            const container = 'performance-analysis';
            clearResults(container);
            
            addResult(container, '⚡ 运行性能对比分析...', 'info');
            
            // 重置EntityIdMapper统计
            if (window.EntityIdMapper) {
                window.EntityIdMapper.resetStats();
            }
            
            // 测试已知实体（应该使用映射表）
            const knownEntities = [
                { type: 'categories', slug: 'life', name: 'Life' },
                { type: 'categories', slug: 'writing', name: 'Writing' },
                { type: 'authors', slug: 'albert-einstein', name: 'Albert Einstein' }
            ];
            
            let mappingTotalTime = 0;
            let mappingCount = 0;
            
            for (const entity of knownEntities) {
                const startTime = performance.now();
                const id = window.EntityIdMapper?.getKnownId(entity.type, entity.slug);
                const endTime = performance.now();
                
                if (id) {
                    mappingTotalTime += (endTime - startTime);
                    mappingCount++;
                }
            }
            
            // 测试API查询性能
            window.ApiClient.useMockData = false;
            const apiStartTime = performance.now();
            try {
                await window.ApiClient.getCategoryByName('nonexistent-category');
            } catch (error) {
                // 预期会失败
            }
            const apiEndTime = performance.now();
            const apiDuration = apiEndTime - apiStartTime;
            
            // 计算性能指标
            const avgMappingTime = mappingCount > 0 ? (mappingTotalTime / mappingCount).toFixed(2) : 0;
            const speedup = avgMappingTime > 0 ? Math.round(apiDuration / avgMappingTime) : 0;
            
            addResult(container, `📊 性能对比结果:`, 'info');
            addResult(container, `🚀 映射表查询: ${mappingCount} 次, 平均 ${avgMappingTime}ms`, 'success');
            addResult(container, `🌐 API查询: 平均 ${Math.round(apiDuration)}ms`, 'warning');
            addResult(container, `⚡ 性能提升: ${speedup}x 倍`, 'success');
            
            // 显示EntityIdMapper统计
            if (window.EntityIdMapper) {
                const stats = window.EntityIdMapper.getStats();
                addResult(container, `📈 映射表统计: 命中 ${stats.hits} 次, 未命中 ${stats.misses} 次, 命中率 ${stats.hitRate}`, 'info');
            }
        }

        // 运行所有测试
        async function runAllTests() {
            clearAllResults();
            
            addResult('test-overview', '🚀 开始运行所有测试...', 'info');
            
            await testCategoryPages();
            await testAuthorPages();
            await testSourcePages();
            await runPerformanceAnalysis();
            
            addResult('test-overview', '✅ 所有测试完成！', 'success');
        }

        // 页面加载时显示系统状态
        window.addEventListener('load', () => {
            console.log('语义化URL页面验证测试工具已加载');
            
            // 显示EntityIdMapper状态
            if (window.EntityIdMapper) {
                addResult('test-overview', '✅ EntityIdMapper已加载', 'success');
                
                const entityTypes = ['categories', 'authors', 'sources'];
                entityTypes.forEach(entityType => {
                    const stats = window.EntityIdMapper.getMappingStats(entityType);
                    addResult('test-overview', 
                        `📊 ${entityType.toUpperCase()}: ${stats.found}/${stats.total} 个实体有ID (${stats.coverage})`, 
                        stats.found > 0 ? 'success' : 'warning'
                    );
                });
            } else {
                addResult('test-overview', '❌ EntityIdMapper未加载', 'error');
            }
        });
    </script>
</body>
</html>
