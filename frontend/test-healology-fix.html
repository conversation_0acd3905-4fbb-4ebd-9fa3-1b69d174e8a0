<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Healology Fix - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .test-result {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            background: #1f2937;
            color: #f3f4f6;
            padding: 12px;
            border-radius: 6px;
            margin: 8px 0;
            white-space: pre-wrap;
        }
        .test-success {
            background: #065f46;
            color: #d1fae5;
        }
        .test-error {
            background: #7f1d1d;
            color: #fecaca;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-check-circle mr-3 text-green-500"></i>
                Test Healology Fix
            </h1>
            
            <!-- 测试结果 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">测试结果</h2>
                <div id="test-results" class="test-result">运行测试中...</div>
            </div>
            
            <!-- 手动测试按钮 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">手动测试</h2>
                <button onclick="runManualTest()" class="bg-blue-500 text-white px-6 py-3 rounded hover:bg-blue-600 mr-4">
                    <i class="fas fa-play mr-2"></i>测试Healology数据加载
                </button>
                <button onclick="testOtherSources()" class="bg-green-500 text-white px-6 py-3 rounded hover:bg-green-600">
                    <i class="fas fa-list mr-2"></i>测试其他来源
                </button>
                <div id="manual-test-results" class="test-result mt-4">点击按钮开始手动测试...</div>
            </div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/mock-data.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/url-handler.js"></script>

    <script>
        // 自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runAutoTests, 500);
        });
        
        function runAutoTests() {
            const testResults = document.getElementById('test-results');
            let results = '';
            let allPassed = true;
            
            try {
                // 测试1: 检查MockData是否包含Healology
                results += '测试1: 检查MockData中的Healology来源\n';
                const healologySource = MockData.sources.find(s => s.name === 'Healology');
                if (healologySource) {
                    results += `✅ 找到Healology来源: ID=${healologySource.id}, 名称=${healologySource.name}, 数量=${healologySource.count}\n`;
                } else {
                    results += '❌ 未找到Healology来源\n';
                    allPassed = false;
                }
                
                // 测试2: 检查Healology相关的名言
                results += '\n测试2: 检查Healology相关的名言\n';
                const healologyQuotes = MockData.quotes.filter(q => 
                    q.sources && q.sources.some(s => s.name === 'Healology')
                );
                if (healologyQuotes.length > 0) {
                    results += `✅ 找到${healologyQuotes.length}条Healology名言:\n`;
                    healologyQuotes.forEach((quote, index) => {
                        results += `   ${index + 1}. "${quote.content.substring(0, 50)}..." - ${quote.author.name}\n`;
                    });
                } else {
                    results += '❌ 未找到Healology相关的名言\n';
                    allPassed = false;
                }
                
                // 测试3: 测试ApiClient.getSourceByName
                results += '\n测试3: 测试ApiClient.getSourceByName("healology")\n';
                if (typeof ApiClient !== 'undefined' && typeof ApiClient.getSourceByName === 'function') {
                    const source = ApiClient.getSourceByName('healology');
                    if (source && source.name === 'Healology') {
                        results += `✅ ApiClient.getSourceByName("healology")返回正确: ${source.name}\n`;
                    } else {
                        results += `❌ ApiClient.getSourceByName("healology")返回错误: ${source ? source.name : 'null'}\n`;
                        allPassed = false;
                    }
                } else {
                    results += '❌ ApiClient.getSourceByName方法不存在\n';
                    allPassed = false;
                }
                
                // 测试4: 测试UrlHandler.parseSourceFromPath
                results += '\n测试4: 测试UrlHandler.parseSourceFromPath()\n';
                if (typeof UrlHandler !== 'undefined' && typeof UrlHandler.parseSourceFromPath === 'function') {
                    // 模拟healology URL
                    const originalPathname = window.location.pathname;
                    Object.defineProperty(window.location, 'pathname', {
                        writable: true,
                        value: '/sources/healology/'
                    });
                    
                    const sourceSlug = UrlHandler.parseSourceFromPath();
                    if (sourceSlug === 'healology') {
                        results += `✅ UrlHandler.parseSourceFromPath()返回正确: "${sourceSlug}"\n`;
                    } else {
                        results += `❌ UrlHandler.parseSourceFromPath()返回错误: "${sourceSlug}"\n`;
                        allPassed = false;
                    }
                    
                    // 恢复原始pathname
                    Object.defineProperty(window.location, 'pathname', {
                        writable: true,
                        value: originalPathname
                    });
                } else {
                    results += '❌ UrlHandler.parseSourceFromPath方法不存在\n';
                    allPassed = false;
                }
                
                // 总结
                results += '\n' + '='.repeat(50) + '\n';
                if (allPassed) {
                    results += '🎉 所有测试通过！Healology修复成功！\n';
                    testResults.className = 'test-result test-success';
                } else {
                    results += '⚠️  部分测试失败，需要进一步检查\n';
                    testResults.className = 'test-result test-error';
                }
                
            } catch (error) {
                results += `\n❌ 测试过程中发生错误: ${error.message}\n${error.stack}`;
                testResults.className = 'test-result test-error';
            }
            
            testResults.textContent = results;
        }
        
        async function runManualTest() {
            const manualResults = document.getElementById('manual-test-results');
            manualResults.textContent = '开始手动测试...\n';
            manualResults.className = 'test-result';
            
            try {
                // 测试完整的数据加载流程
                manualResults.textContent += '1. 获取Healology来源信息...\n';
                const source = ApiClient.getSourceByName('healology');
                
                if (!source) {
                    throw new Error('无法获取Healology来源信息');
                }
                
                manualResults.textContent += `   ✅ 来源信息: ${source.name} (ID: ${source.id})\n`;
                
                manualResults.textContent += '2. 获取Healology相关名言...\n';
                const quotes = ApiClient.getQuotes({ sourceId: source.id, limit: 10 });
                
                if (!quotes || quotes.length === 0) {
                    throw new Error('无法获取Healology相关名言');
                }
                
                manualResults.textContent += `   ✅ 找到${quotes.length}条名言\n`;
                
                manualResults.textContent += '3. 显示前3条名言:\n';
                quotes.slice(0, 3).forEach((quote, index) => {
                    manualResults.textContent += `   ${index + 1}. "${quote.content.substring(0, 60)}..." - ${quote.author.name}\n`;
                });
                
                manualResults.textContent += '\n🎉 手动测试成功！Healology页面应该能正常工作了！\n';
                manualResults.className = 'test-result test-success';
                
            } catch (error) {
                manualResults.textContent += `\n❌ 手动测试失败: ${error.message}\n`;
                manualResults.className = 'test-result test-error';
            }
        }
        
        function testOtherSources() {
            const manualResults = document.getElementById('manual-test-results');
            manualResults.textContent = '测试其他来源...\n';
            manualResults.className = 'test-result';
            
            try {
                const allSources = MockData.sources;
                manualResults.textContent += `找到${allSources.length}个来源:\n`;
                
                allSources.forEach((source, index) => {
                    const slug = UrlHandler.slugify(source.name);
                    manualResults.textContent += `${index + 1}. ${source.name} -> /sources/${slug}/\n`;
                });
                
                manualResults.textContent += '\n✅ 所有来源列表显示完成\n';
                manualResults.className = 'test-result test-success';
                
            } catch (error) {
                manualResults.textContent += `\n❌ 测试其他来源失败: ${error.message}\n`;
                manualResults.className = 'test-result test-error';
            }
        }
    </script>
</body>
</html>
