<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quotes from Books, Movies & Speeches | Best Sources | Quotese.com</title>
    <meta name="description" content="Discover powerful quotes from bestselling books, iconic movies, and historic speeches. Find inspiration from the world's greatest sources!">
    <meta name="keywords" content="quote sources, book quotes, speech quotes, literature quotes, famous works">
    <link rel="canonical" href="https://quotese.com/sources/">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link href="/css/variables.css" rel="stylesheet">
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/responsive.css" rel="stylesheet">
    <link href="/css/performance-optimizations.css" rel="stylesheet">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "Quote Sources Collection",
        "description": "Explore quotes from books, speeches, movies and other sources of inspiration.",
        "url": "https://quotese.com/sources/",
        "mainEntity": {
            "@type": "ItemList",
            "name": "Sources List",
            "description": "Collection of books, speeches, and other sources of inspirational quotes"
        },
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://quotese.com/"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Sources",
                    "item": "https://quotese.com/sources/"
                }
            ]
        },
        "author": {
            "@type": "Organization",
            "name": "Quotese Editorial Team",
            "url": "https://quotese.com/about/"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Quotese.com",
            "url": "https://quotese.com/"
        }
    }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <div id="navigation-container"></div>
    
    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Breadcrumb Navigation -->
            <nav id="breadcrumb-container" class="mb-6" aria-label="Breadcrumb" data-breadcrumb-container>
                <!-- Breadcrumb will be loaded dynamically -->
            </nav>

            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-book mr-3 text-orange-500"></i>
                    Quote Sources
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Discover quotes from famous books, speeches, and literary works that have shaped our world.
                </p>
            </div>
            
            <!-- Loading State -->
            <div id="loading-container" class="text-center py-12" style="display: none;">
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-gray-600 dark:text-gray-400">Loading sources...</p>
            </div>

            <!-- Error State -->
            <div id="error-container" class="text-center py-12" style="display: none;">
                <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Failed to load sources</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">Please try refreshing the page</p>
                <button onclick="location.reload()" class="btn-primary">
                    <i class="fas fa-refresh mr-2"></i>Retry
                </button>
            </div>

            <!-- Search Bar -->
            <div class="mb-8">
                <div class="max-w-md mx-auto">
                    <div class="relative">
                        <input type="text" id="search-input" placeholder="Search sources..."
                               class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Sources Display Area -->
            <div id="sources-container" class="sources-display">
                <!-- Sources will be loaded dynamically -->
            </div>

            <!-- Pagination -->
            <div id="pagination-container" class="mt-12"></div>
        </div>
    </main>
    
    <!-- Footer -->
    <div id="footer-container"></div>
    
    <!-- Performance Monitor (Critical) -->
    <script src="/js/performance-monitor.js?v=20250627"></script>

    <!-- Core Scripts (Optimized Loading) -->
    <script src="/js/config.js?v=20250626"></script> <!-- Load config first -->
    <script src="/js/api-client.js?v=20250626"></script> <!-- Load API client with GraphQL support -->
    <script src="/js/theme.js?v=20250626" defer></script>
    <script src="/js/url-handler.js?v=20250626"></script>
    <script src="/js/seo-manager.js?v=20250626" defer></script>
    <script src="/js/page-router.js?v=20250626"></script>
    <script src="/js/mobile-menu.js?v=20250626" defer></script>
    <script src="/js/components/pagination.js?v=20250626" defer></script>
    <script src="/js/components/breadcrumb.js?v=20250626" defer></script>
    <script src="/js/navigation-state.js?v=20250627" defer></script>
    <script src="/js/social-meta.js?v=20250626" defer></script>

    <!-- Page Scripts -->
    <script src="/js/pages/sources.js?v=20250626"></script>

    <!-- Component Loader -->
    <script src="/js/component-loader.js?v=20250626"></script>

    <!-- Initialize Page -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Sources page DOM loaded, initializing...');

            // Debug: Check all dependencies
            console.log('🔍 Checking dependencies...');
            console.log('- PageRouter:', !!window.PageRouter);
            console.log('- ApiClient:', !!window.ApiClient);
            console.log('- ComponentLoader:', !!window.ComponentLoader);
            console.log('- UrlHandler:', !!window.UrlHandler);
            console.log('- initSourcesListPage:', !!window.initSourcesListPage);

            // Wait a bit for all scripts to load, then initialize
            setTimeout(() => {
                console.log('⏰ Delayed initialization starting...');

                // Try direct initialization first
                if (window.initSourcesListPage) {
                    console.log('🎯 Calling initSourcesListPage directly...');
                    window.initSourcesListPage({}).then(() => {
                        console.log('✅ Direct initialization successful');
                    }).catch(error => {
                        console.error('❌ Direct initialization failed:', error);
                        showErrorFallback();
                    });
                } else {
                    console.error('❌ No initialization method available');
                    showErrorFallback();
                }
            }, 1000); // Wait 1 second for all scripts to load

            function showErrorFallback() {
                console.log('🚨 Showing error fallback...');
                const errorContainer = document.getElementById('error-container');
                const loadingContainer = document.getElementById('loading-container');

                if (errorContainer) {
                    errorContainer.style.display = 'block';
                    errorContainer.innerHTML = `
                        <div class="text-center py-12">
                            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                            <h3 class="text-lg font-semibold mb-2">页面加载失败</h3>
                            <p class="text-gray-600 mb-4">请检查浏览器控制台获取详细错误信息</p>
                            <button onclick="location.reload()" class="btn-primary">
                                <i class="fas fa-refresh mr-2"></i>刷新页面
                            </button>
                        </div>
                    `;
                }

                if (loadingContainer) {
                    loadingContainer.style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
