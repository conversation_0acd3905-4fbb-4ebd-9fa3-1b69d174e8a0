<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuoteCardComponent Fix Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .page-link {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .page-link:hover { background-color: #218838; }
        .error-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>QuoteCardComponent Fix Verification</h1>
    <p>This tool verifies that the QuoteCardComponent undefined error has been resolved on quote detail pages.</p>

    <div class="test-section">
        <h2>Fix Summary</h2>
        <div class="info test-result">
            <strong>Issues Fixed:</strong>
            <ul>
                <li>✅ Added missing quote-card.js script to quote.html</li>
                <li>✅ Ensured proper script loading order (quote-card.js before quote.js)</li>
                <li>✅ Added debugging code to check QuoteCardComponent availability</li>
                <li>✅ Implemented fallback mechanism when component is unavailable</li>
                <li>✅ Enhanced error handling in renderQuoteDetails function</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>Script Loading Test</h2>
        <button onclick="testScriptLoading()">🔍 Test Script Loading</button>
        <div id="script-loading-results"></div>
    </div>

    <div class="test-section">
        <h2>Component Availability Test</h2>
        <button onclick="testComponentAvailability()">🧪 Test Component Availability</button>
        <div id="component-availability-results"></div>
    </div>

    <div class="test-section">
        <h2>Quote Detail Pages Test</h2>
        <div>
            <h3>Test Quote Detail Pages</h3>
            <a href="/quotes/499276/?use-production-api=true" target="_blank" class="page-link">Quote 499276</a>
            <a href="/quotes/499001/?use-production-api=true" target="_blank" class="page-link">Quote 499001</a>
            <a href="/quotes/499002/?use-production-api=true" target="_blank" class="page-link">Quote 499002</a>
        </div>
        <button onclick="testQuoteDetailPages()">🔄 Test Quote Detail Pages</button>
        <div id="quote-detail-pages-results"></div>
    </div>

    <div class="test-section">
        <h2>Error Monitoring</h2>
        <button onclick="monitorJavaScriptErrors()">🚨 Monitor JavaScript Errors</button>
        <div id="error-monitoring-results"></div>
        <div id="error-log" class="error-display" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>Component Rendering Test</h2>
        <button onclick="testComponentRendering()">🎨 Test Component Rendering</button>
        <div id="component-rendering-results"></div>
    </div>

    <script>
        // Global error tracking
        const errorLog = [];
        let errorMonitoring = false;

        // Capture JavaScript errors
        window.addEventListener('error', (event) => {
            const error = {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error ? event.error.stack : 'No stack trace',
                timestamp: new Date().toISOString()
            };
            errorLog.push(error);
            
            if (errorMonitoring) {
                displayError(error);
            }
        });

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
        }

        function displayError(error) {
            const errorLogDiv = document.getElementById('error-log');
            errorLogDiv.style.display = 'block';
            
            const errorEntry = document.createElement('div');
            errorEntry.innerHTML = `
                <strong>Error at ${error.timestamp}:</strong><br>
                <strong>Message:</strong> ${error.message}<br>
                <strong>File:</strong> ${error.filename}:${error.lineno}:${error.colno}<br>
                <strong>Stack:</strong><br><pre>${error.stack}</pre>
                <hr>
            `;
            errorLogDiv.appendChild(errorEntry);
        }

        function testScriptLoading() {
            const container = 'script-loading-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing script loading...');
            
            // Check if scripts are loaded
            const scripts = document.querySelectorAll('script[src]');
            const scriptSources = Array.from(scripts).map(script => script.src);
            
            // Check for required scripts
            const requiredScripts = [
                'quote-card.js',
                'quote.js',
                'api-client.js',
                'url-handler.js'
            ];
            
            requiredScripts.forEach(scriptName => {
                const found = scriptSources.some(src => src.includes(scriptName));
                if (found) {
                    addResult(container, 'success', `${scriptName}: Found in page`);
                } else {
                    addResult(container, 'error', `${scriptName}: Not found in page`);
                }
            });
            
            // Check script order
            const quoteCardIndex = scriptSources.findIndex(src => src.includes('quote-card.js'));
            const quoteIndex = scriptSources.findIndex(src => src.includes('quote.js'));
            
            if (quoteCardIndex !== -1 && quoteIndex !== -1) {
                if (quoteCardIndex < quoteIndex) {
                    addResult(container, 'success', 'Script loading order: quote-card.js loads before quote.js');
                } else {
                    addResult(container, 'error', 'Script loading order: quote.js loads before quote-card.js (WRONG)');
                }
            } else {
                addResult(container, 'warning', 'Cannot determine script loading order');
            }
        }

        function testComponentAvailability() {
            const container = 'component-availability-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing component availability...');
            
            // Test QuoteCardComponent
            if (typeof QuoteCardComponent !== 'undefined') {
                addResult(container, 'success', 'QuoteCardComponent: Defined');
                
                if (typeof QuoteCardComponent.render === 'function') {
                    addResult(container, 'success', 'QuoteCardComponent.render: Available');
                } else {
                    addResult(container, 'error', 'QuoteCardComponent.render: Not a function');
                }
                
                if (typeof QuoteCardComponent.renderList === 'function') {
                    addResult(container, 'success', 'QuoteCardComponent.renderList: Available');
                } else {
                    addResult(container, 'warning', 'QuoteCardComponent.renderList: Not available');
                }
            } else {
                addResult(container, 'error', 'QuoteCardComponent: Undefined');
            }
            
            // Test other required components
            const requiredComponents = [
                'UrlHandler',
                'ApiClient',
                'QuoteseAPIMode'
            ];
            
            requiredComponents.forEach(componentName => {
                if (typeof window[componentName] !== 'undefined') {
                    addResult(container, 'success', `${componentName}: Available`);
                } else {
                    addResult(container, 'warning', `${componentName}: Not available`);
                }
            });
        }

        async function testQuoteDetailPages() {
            const container = 'quote-detail-pages-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing quote detail pages...');
            
            const testUrls = [
                '/quotes/499276/?use-production-api=true',
                '/quotes/499001/?use-production-api=true',
                '/quotes/499002/?use-production-api=true'
            ];
            
            for (const url of testUrls) {
                try {
                    const response = await fetch(url);
                    if (response.ok) {
                        const html = await response.text();
                        
                        // Check if quote-card.js is included
                        const hasQuoteCardScript = html.includes('quote-card.js');
                        const hasQuoteScript = html.includes('quote.js');
                        const hasQuoteCardContent = html.includes('id="quote-card-content"');
                        
                        const quoteId = url.match(/\/quotes\/(\d+)\//)[1];
                        
                        if (hasQuoteCardScript && hasQuoteScript && hasQuoteCardContent) {
                            addResult(container, 'success', `Quote ${quoteId}: Page structure correct`);
                        } else {
                            addResult(container, 'error', 
                                `Quote ${quoteId}: Missing elements - Script: ${hasQuoteCardScript}, Content: ${hasQuoteCardContent}`);
                        }
                    } else {
                        addResult(container, 'error', `${url}: HTTP ${response.status}`);
                    }
                } catch (error) {
                    addResult(container, 'error', `${url}: ${error.message}`);
                }
            }
        }

        function monitorJavaScriptErrors() {
            const container = 'error-monitoring-results';
            document.getElementById(container).innerHTML = '';
            
            errorMonitoring = true;
            addResult(container, 'info', 'JavaScript error monitoring started...');
            
            // Display existing errors
            if (errorLog.length > 0) {
                addResult(container, 'warning', `Found ${errorLog.length} existing errors`);
                errorLog.forEach(error => displayError(error));
            } else {
                addResult(container, 'success', 'No JavaScript errors detected so far');
            }
            
            // Set up monitoring message
            setTimeout(() => {
                if (errorLog.length === 0) {
                    addResult(container, 'success', 'No new JavaScript errors detected during monitoring period');
                }
            }, 5000);
        }

        async function testComponentRendering() {
            const container = 'component-rendering-results';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'info', 'Testing component rendering...');
            
            // Check if QuoteCardComponent is available
            if (typeof QuoteCardComponent === 'undefined') {
                addResult(container, 'error', 'QuoteCardComponent is not available for testing');
                return;
            }
            
            // Test sample quote data
            const sampleQuote = {
                id: '499276',
                content: 'This is a test quote for component rendering verification.',
                author: { id: '1', name: 'Test Author' },
                categories: [{ id: '1', name: 'Test Category' }],
                sources: [{ id: '1', name: 'Test Source' }],
                createdAt: new Date().toISOString()
            };
            
            try {
                // Test standard rendering
                const standardCard = QuoteCardComponent.render(sampleQuote, 0, {
                    showActions: true,
                    showAuthorAvatar: true
                });
                
                if (standardCard) {
                    addResult(container, 'success', 'Standard quote card rendering: Success');
                } else {
                    addResult(container, 'error', 'Standard quote card rendering: Failed');
                }
                
                // Test detail page rendering
                const detailCard = QuoteCardComponent.render(sampleQuote, 0, {
                    showActions: true,
                    showAuthorAvatar: true,
                    isDetailPage: true
                });
                
                if (detailCard) {
                    addResult(container, 'success', 'Detail page quote card rendering: Success');
                    
                    // Add rendered card to page for visual verification
                    const testContainer = document.createElement('div');
                    testContainer.innerHTML = '<h4>Rendered Quote Card (for visual verification):</h4>';
                    testContainer.appendChild(detailCard);
                    document.getElementById(container).appendChild(testContainer);
                } else {
                    addResult(container, 'error', 'Detail page quote card rendering: Failed');
                }
                
            } catch (error) {
                addResult(container, 'error', `Component rendering test failed: ${error.message}`);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                addResult('script-loading-results', 'info', 
                    'QuoteCardComponent fix verification tool ready. Click the test buttons to begin.');
            }, 1000);
        });
    </script>
</body>
</html>
