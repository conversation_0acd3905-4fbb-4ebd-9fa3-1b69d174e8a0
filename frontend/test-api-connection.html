<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>API连接测试</h1>
    <div id="results"></div>
    
    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            resultsDiv.appendChild(div);
        }
        
        async function testAPI() {
            addResult('开始API连接测试...', 'info');
            
            // 测试REST API端点
            const endpoints = [
                'http://localhost:8000/api/categories/',
                'http://localhost:8000/api/authors/',
                'http://localhost:8000/api/sources/',
                'http://localhost:8000/graphql/'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    addResult(`测试端点: ${endpoint}`, 'info');
                    
                    if (endpoint.includes('graphql')) {
                        // 测试GraphQL端点
                        const response = await fetch(endpoint, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                query: `
                                    query {
                                        categories {
                                            id
                                            name
                                            quotesCount
                                        }
                                    }
                                `
                            })
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            if (data.data && data.data.categories) {
                                addResult(`✅ GraphQL API正常 - 获取到 ${data.data.categories.length} 个类别`, 'success');
                            } else {
                                addResult(`❌ GraphQL API返回数据格式错误`, 'error');
                            }
                        } else {
                            addResult(`❌ GraphQL API请求失败: ${response.status}`, 'error');
                        }
                    } else {
                        // 测试REST API端点
                        const response = await fetch(endpoint);
                        
                        if (response.ok) {
                            const data = await response.json();
                            addResult(`✅ REST API正常 - ${endpoint} 返回 ${data.length} 条记录`, 'success');
                        } else {
                            addResult(`❌ REST API请求失败: ${endpoint} - ${response.status}`, 'error');
                        }
                    }
                } catch (error) {
                    addResult(`❌ 连接失败: ${endpoint} - ${error.message}`, 'error');
                }
            }
            
            addResult('API连接测试完成', 'info');
        }
        
        // 页面加载后自动开始测试
        window.addEventListener('load', testAPI);
    </script>
</body>
</html>
