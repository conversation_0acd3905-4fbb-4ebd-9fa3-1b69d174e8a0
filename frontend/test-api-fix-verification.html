<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API修复验证测试 - Quotese.com</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .test-result {
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .test-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .api-data {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px;
            margin: 4px 0;
            max-height: 150px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-tools mr-3 text-green-500"></i>
                API修复验证测试
            </h1>
            
            <!-- 修复状态概览 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">修复状态概览</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-gray-800 mb-3">修复的问题</h3>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                创建了REST API视图函数
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                配置了API URL路由
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                修复了URL配置冲突
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                添加了CORS支持
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800 mb-3">API端点状态</h3>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center">
                                <span id="authors-status" class="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                                <span>/api/authors/ - 检测中...</span>
                            </li>
                            <li class="flex items-center">
                                <span id="categories-status" class="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                                <span>/api/categories/ - 检测中...</span>
                            </li>
                            <li class="flex items-center">
                                <span id="sources-status" class="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                                <span>/api/sources/ - 检测中...</span>
                            </li>
                            <li class="flex items-center">
                                <span id="quotes-status" class="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                                <span>/api/quotes/ - 检测中...</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 测试控制面板 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">修复验证测试</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    <button onclick="verifyAPIEndpoints()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        <i class="fas fa-check-circle mr-2"></i>验证API端点
                    </button>
                    <button onclick="testDataIntegrity()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        <i class="fas fa-database mr-2"></i>测试数据完整性
                    </button>
                    <button onclick="testCORSConfiguration()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        <i class="fas fa-globe mr-2"></i>测试CORS配置
                    </button>
                    <button onclick="testErrorHandling()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                        <i class="fas fa-exclamation-triangle mr-2"></i>测试错误处理
                    </button>
                    <button onclick="testPagination()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        <i class="fas fa-list mr-2"></i>测试分页功能
                    </button>
                    <button onclick="testSearchFunction()" class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                        <i class="fas fa-search mr-2"></i>测试搜索功能
                    </button>
                </div>
                
                <div class="flex gap-4">
                    <button onclick="runAllVerificationTests()" class="bg-gray-800 text-white px-6 py-2 rounded hover:bg-gray-900">
                        <i class="fas fa-play mr-2"></i>运行所有验证测试
                    </button>
                    <button onclick="clearResults()" class="bg-gray-400 text-white px-4 py-2 rounded hover:bg-gray-500">
                        <i class="fas fa-trash mr-2"></i>清除结果
                    </button>
                </div>
            </div>
            
            <!-- 测试结果显示区域 -->
            <div id="test-results" class="space-y-6">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8001';
        
        // 页面加载时检查API状态
        document.addEventListener('DOMContentLoaded', function() {
            checkAPIStatus();
        });
        
        // 检查API状态
        async function checkAPIStatus() {
            const endpoints = [
                { name: 'authors', url: '/api/authors/', statusId: 'authors-status' },
                { name: 'categories', url: '/api/categories/', statusId: 'categories-status' },
                { name: 'sources', url: '/api/sources/', statusId: 'sources-status' },
                { name: 'quotes', url: '/api/quotes/', statusId: 'quotes-status' }
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_BASE_URL}${endpoint.url}`, { mode: 'cors' });
                    const statusElement = document.getElementById(endpoint.statusId);
                    
                    if (response.ok) {
                        statusElement.className = 'inline-block w-3 h-3 bg-green-500 rounded-full mr-2';
                        statusElement.nextSibling.textContent = `${endpoint.url} - 正常`;
                    } else {
                        statusElement.className = 'inline-block w-3 h-3 bg-red-500 rounded-full mr-2';
                        statusElement.nextSibling.textContent = `${endpoint.url} - 错误 ${response.status}`;
                    }
                } catch (error) {
                    const statusElement = document.getElementById(endpoint.statusId);
                    statusElement.className = 'inline-block w-3 h-3 bg-red-500 rounded-full mr-2';
                    statusElement.nextSibling.textContent = `${endpoint.url} - 连接失败`;
                }
            }
        }
        
        // 验证API端点
        async function verifyAPIEndpoints() {
            addTestSection('API端点验证测试');
            
            const endpoints = [
                { name: '作者API', url: '/api/authors/' },
                { name: '类别API', url: '/api/categories/' },
                { name: '来源API', url: '/api/sources/' },
                { name: '名言API', url: '/api/quotes/' }
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_BASE_URL}${endpoint.url}`, { mode: 'cors' });
                    
                    if (response.ok) {
                        const data = await response.json();
                        addTestResult(endpoint.name, 'success', `状态码: ${response.status}, 数据量: ${data.length}条`);
                        addAPIData(`${endpoint.name}示例数据`, data.slice(0, 2)); // 显示前2条数据
                    } else {
                        addTestResult(endpoint.name, 'error', `HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    addTestResult(endpoint.name, 'error', `请求失败: ${error.message}`);
                }
            }
        }
        
        // 测试数据完整性
        async function testDataIntegrity() {
            addTestSection('数据完整性测试');
            
            try {
                // 检查作者数据
                const authorsResponse = await fetch(`${API_BASE_URL}/api/authors/`, { mode: 'cors' });
                if (authorsResponse.ok) {
                    const authors = await authorsResponse.json();
                    addTestResult('作者数据完整性', 'success', `共${authors.length}个作者，数据结构正确`);
                    
                    // 验证数据结构
                    if (authors.length > 0) {
                        const firstAuthor = authors[0];
                        const hasRequiredFields = firstAuthor.id && firstAuthor.name;
                        addTestResult('作者数据结构', hasRequiredFields ? 'success' : 'error', 
                                    hasRequiredFields ? '包含必需字段: id, name' : '缺少必需字段');
                    }
                }
                
                // 检查名言数据
                const quotesResponse = await fetch(`${API_BASE_URL}/api/quotes/`, { mode: 'cors' });
                if (quotesResponse.ok) {
                    const quotes = await quotesResponse.json();
                    addTestResult('名言数据完整性', 'success', `共${quotes.length}条名言，数据结构正确`);
                    
                    // 验证关联数据
                    const quotesWithAuthors = quotes.filter(q => q.author_name);
                    addTestResult('作者关联数据', 'success', `${quotesWithAuthors.length}条名言包含作者信息`);
                }
                
            } catch (error) {
                addTestResult('数据完整性测试', 'error', `测试失败: ${error.message}`);
            }
        }
        
        // 测试CORS配置
        async function testCORSConfiguration() {
            addTestSection('CORS配置测试');
            
            try {
                // 测试OPTIONS请求
                const optionsResponse = await fetch(`${API_BASE_URL}/api/authors/`, { 
                    method: 'OPTIONS',
                    mode: 'cors'
                });
                
                addTestResult('OPTIONS请求', 'success', `状态码: ${optionsResponse.status}`);
                
                // 测试CORS头
                const corsHeaders = [
                    'Access-Control-Allow-Origin',
                    'Access-Control-Allow-Methods',
                    'Access-Control-Allow-Headers'
                ];
                
                corsHeaders.forEach(header => {
                    const headerValue = optionsResponse.headers.get(header);
                    addTestResult(`CORS头: ${header}`, headerValue ? 'success' : 'error', 
                                headerValue || '未设置');
                });
                
            } catch (error) {
                addTestResult('CORS配置测试', 'error', `测试失败: ${error.message}`);
            }
        }
        
        // 测试错误处理
        async function testErrorHandling() {
            addTestSection('错误处理测试');
            
            try {
                // 测试不存在的端点
                const notFoundResponse = await fetch(`${API_BASE_URL}/api/nonexistent/`, { mode: 'cors' });
                addTestResult('404错误处理', notFoundResponse.status === 404 ? 'success' : 'error', 
                            `状态码: ${notFoundResponse.status}`);
                
                // 测试无效参数
                const invalidParamResponse = await fetch(`${API_BASE_URL}/api/authors/?page=invalid`, { mode: 'cors' });
                addTestResult('无效参数处理', 'success', `状态码: ${invalidParamResponse.status}`);
                
            } catch (error) {
                addTestResult('错误处理测试', 'error', `测试失败: ${error.message}`);
            }
        }
        
        // 测试分页功能
        async function testPagination() {
            addTestSection('分页功能测试');
            
            try {
                // 测试第一页
                const page1Response = await fetch(`${API_BASE_URL}/api/quotes/?page=1&page_size=5`, { mode: 'cors' });
                if (page1Response.ok) {
                    const page1Data = await page1Response.json();
                    addTestResult('分页参数支持', 'success', `第1页返回${page1Data.length}条记录`);
                }
                
                // 测试不同页面大小
                const smallPageResponse = await fetch(`${API_BASE_URL}/api/quotes/?page_size=3`, { mode: 'cors' });
                if (smallPageResponse.ok) {
                    const smallPageData = await smallPageResponse.json();
                    addTestResult('页面大小控制', 'success', `page_size=3返回${smallPageData.length}条记录`);
                }
                
            } catch (error) {
                addTestResult('分页功能测试', 'error', `测试失败: ${error.message}`);
            }
        }
        
        // 测试搜索功能
        async function testSearchFunction() {
            addTestSection('搜索功能测试');
            
            try {
                // 测试作者搜索
                const authorSearchResponse = await fetch(`${API_BASE_URL}/api/authors/?search=Einstein`, { mode: 'cors' });
                if (authorSearchResponse.ok) {
                    const searchResults = await authorSearchResponse.json();
                    addTestResult('作者搜索功能', 'success', `搜索"Einstein"返回${searchResults.length}个结果`);
                }
                
                // 测试名言搜索
                const quoteSearchResponse = await fetch(`${API_BASE_URL}/api/quotes/?search=love`, { mode: 'cors' });
                if (quoteSearchResponse.ok) {
                    const quoteResults = await quoteSearchResponse.json();
                    addTestResult('名言搜索功能', 'success', `搜索"love"返回${quoteResults.length}个结果`);
                }
                
            } catch (error) {
                addTestResult('搜索功能测试', 'error', `测试失败: ${error.message}`);
            }
        }
        
        // 运行所有验证测试
        async function runAllVerificationTests() {
            clearResults();
            addTestSection('开始运行API修复验证测试');
            
            await verifyAPIEndpoints();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDataIntegrity();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testCORSConfiguration();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testErrorHandling();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPagination();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testSearchFunction();
            
            addTestSection('所有API修复验证测试完成 ✅');
        }
        
        // 工具函数
        function addTestSection(title) {
            const resultsDiv = document.getElementById('test-results');
            const sectionHTML = `
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        ${title}
                    </h3>
                    <div id="section-${Date.now()}" class="space-y-2">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
            `;
            resultsDiv.insertAdjacentHTML('beforeend', sectionHTML);
        }
        
        function addTestResult(testName, status, message) {
            const lastSection = document.querySelector('#test-results > div:last-child > div:last-child');
            if (!lastSection) return;
            
            const statusClass = status === 'success' ? 'test-success' : 'test-error';
            const iconClass = status === 'success' ? 'fa-check text-green-600' : 'fa-times text-red-600';
            
            const resultHTML = `
                <div class="test-result border rounded p-3 ${statusClass}">
                    <div class="flex items-center">
                        <i class="fas ${iconClass} mr-2"></i>
                        <span class="font-medium">${testName}</span>
                    </div>
                    <div class="mt-1 text-sm">${message}</div>
                </div>
            `;
            
            lastSection.insertAdjacentHTML('beforeend', resultHTML);
        }
        
        function addAPIData(title, data) {
            const lastSection = document.querySelector('#test-results > div:last-child > div:last-child');
            if (!lastSection) return;
            
            const dataHTML = `
                <div class="mt-4">
                    <h4 class="font-medium text-gray-700 mb-2">${title}:</h4>
                    <div class="api-data">${JSON.stringify(data, null, 2)}</div>
                </div>
            `;
            
            lastSection.insertAdjacentHTML('beforeend', dataHTML);
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
    </script>
</body>
</html>
