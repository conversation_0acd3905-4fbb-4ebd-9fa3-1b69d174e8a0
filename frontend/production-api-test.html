<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production API Test - quotese.com</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-item { margin: 10px 0; padding: 15px; border-radius: 8px; border-left: 4px solid #ddd; }
        .success { background: #d4edda; border-left-color: #28a745; }
        .warning { background: #fff3cd; border-left-color: #ffc107; }
        .error { background: #f8d7da; border-left-color: #dc3545; }
        .info { background: #d1ecf1; border-left-color: #17a2b8; }
        .loading { background: #e2e3e5; border-left-color: #6c757d; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .metric { display: inline-block; margin: 5px 10px; padding: 5px 10px; background: #f8f9fa; border-radius: 4px; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">🚀 Production API Test Suite</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">📊 Test Configuration</h2>
            <div id="config-info" class="test-item info">
                <p><strong>GraphQL Endpoint:</strong> <span id="graphql-endpoint">Loading...</span></p>
                <p><strong>Test Start Time:</strong> <span id="test-start-time">Loading...</span></p>
                <p><strong>Environment:</strong> <span id="environment">Production API Test</span></p>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🧪 API Connection Tests</h2>
            <div id="api-tests">
                <div id="graphql-test" class="test-item loading">
                    <h3><i class="fas fa-database mr-2"></i>GraphQL Endpoint Test</h3>
                    <p>Testing connection to production GraphQL API...</p>
                </div>
                <div id="categories-test" class="test-item loading">
                    <h3><i class="fas fa-tags mr-2"></i>Categories API Test</h3>
                    <p>Testing categories data retrieval...</p>
                </div>
                <div id="authors-test" class="test-item loading">
                    <h3><i class="fas fa-users mr-2"></i>Authors API Test</h3>
                    <p>Testing authors data retrieval...</p>
                </div>
                <div id="sources-test" class="test-item loading">
                    <h3><i class="fas fa-book mr-2"></i>Sources API Test</h3>
                    <p>Testing sources data retrieval...</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🧭 Navigation Tests</h2>
            <div id="navigation-tests">
                <div id="breadcrumb-test" class="test-item loading">
                    <h3><i class="fas fa-route mr-2"></i>Breadcrumb Navigation Test</h3>
                    <p>Testing breadcrumb functionality with production data...</p>
                </div>
                <div id="main-nav-test" class="test-item loading">
                    <h3><i class="fas fa-compass mr-2"></i>Main Navigation Test</h3>
                    <p>Testing main navigation links...</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🎭 Prerender Service Tests</h2>
            <div id="prerender-tests">
                <div id="prerender-health-test" class="test-item loading">
                    <h3><i class="fas fa-heartbeat mr-2"></i>Prerender Health Check</h3>
                    <p>Testing prerender service availability...</p>
                </div>
                <div id="prerender-content-test" class="test-item loading">
                    <h3><i class="fas fa-code mr-2"></i>Prerender Content Test</h3>
                    <p>Testing prerendered HTML content quality...</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">⚡ Performance Metrics</h2>
            <div id="performance-metrics" class="test-item info">
                <div id="metrics-display">
                    <span class="metric">API Response Time: <span id="api-response-time">-</span>ms</span>
                    <span class="metric">Page Load Time: <span id="page-load-time">-</span>ms</span>
                    <span class="metric">Total Tests: <span id="total-tests">0</span></span>
                    <span class="metric">Passed: <span id="passed-tests">0</span></span>
                    <span class="metric">Failed: <span id="failed-tests">0</span></span>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">📋 Test Results Summary</h2>
            <div id="test-summary" class="test-item info">
                <p>Running tests...</p>
            </div>
            
            <div class="mt-6">
                <button onclick="runAllTests()" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 mr-4">
                    <i class="fas fa-play mr-2"></i>Run All Tests
                </button>
                <button onclick="resetTests()" class="bg-gray-500 text-white px-6 py-2 rounded hover:bg-gray-600 mr-4">
                    <i class="fas fa-redo mr-2"></i>Reset Tests
                </button>
                <button onclick="exportResults()" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                    <i class="fas fa-download mr-2"></i>Export Results
                </button>
            </div>
        </div>
    </div>

    <!-- Core Scripts -->
    <script src="/js/config.js"></script>
    <script src="/js/config-production-test.js"></script>
    <script src="/js/api-client.js"></script>
    
    <script>
        // Test state
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            startTime: Date.now(),
            tests: {}
        };

        // Initialize test page
        function initTestPage() {
            document.getElementById('graphql-endpoint').textContent = window.AppConfig.graphqlEndpoint;
            document.getElementById('test-start-time').textContent = new Date().toLocaleString();
            
            // Auto-run tests after initialization
            setTimeout(runAllTests, 1000);
        }

        // Update test result UI
        function updateTestResult(testId, status, message, details = null) {
            const element = document.getElementById(testId);
            if (!element) return;

            element.className = `test-item ${status}`;
            
            let icon = '';
            switch(status) {
                case 'success': icon = '✅'; break;
                case 'error': icon = '❌'; break;
                case 'warning': icon = '⚠️'; break;
                case 'loading': icon = '🔄'; break;
                default: icon = 'ℹ️';
            }

            let html = `<h3>${icon} ${element.querySelector('h3').textContent.replace(/^[✅❌⚠️🔄ℹ️]\s*/, '')}</h3>`;
            html += `<p>${message}</p>`;
            
            if (details) {
                html += `<pre>${JSON.stringify(details, null, 2)}</pre>`;
            }
            
            element.innerHTML = html;

            // Update counters
            testResults.total++;
            if (status === 'success') testResults.passed++;
            if (status === 'error') testResults.failed++;
            
            updateMetrics();
        }

        // Update performance metrics
        function updateMetrics() {
            document.getElementById('total-tests').textContent = testResults.total;
            document.getElementById('passed-tests').textContent = testResults.passed;
            document.getElementById('failed-tests').textContent = testResults.failed;
        }

        // Test GraphQL connection
        async function testGraphQLConnection() {
            try {
                const startTime = Date.now();
                
                const query = `
                    query TestConnection {
                        categories(first: 1) {
                            edges {
                                node {
                                    id
                                    name
                                }
                            }
                        }
                    }
                `;

                const response = await window.ApiClient.graphqlQuery(query);
                const responseTime = Date.now() - startTime;
                
                document.getElementById('api-response-time').textContent = responseTime;

                if (response && response.categories) {
                    updateTestResult('graphql-test', 'success', 
                        `GraphQL connection successful! Response time: ${responseTime}ms`, 
                        { responseTime, dataReceived: true });
                    return true;
                } else {
                    updateTestResult('graphql-test', 'error', 
                        'GraphQL connection failed: No data received', response);
                    return false;
                }
            } catch (error) {
                updateTestResult('graphql-test', 'error', 
                    `GraphQL connection failed: ${error.message}`, error);
                return false;
            }
        }

        // Test Categories API
        async function testCategoriesAPI() {
            try {
                const query = `
                    query TestCategories {
                        categories(first: 5) {
                            edges {
                                node {
                                    id
                                    name
                                    slug
                                    description
                                }
                            }
                        }
                    }
                `;

                const response = await window.ApiClient.graphqlQuery(query);
                
                if (response && response.categories && response.categories.edges.length > 0) {
                    const count = response.categories.edges.length;
                    updateTestResult('categories-test', 'success', 
                        `Categories API working! Retrieved ${count} categories`, 
                        { count, sample: response.categories.edges[0].node });
                    return true;
                } else {
                    updateTestResult('categories-test', 'error', 
                        'Categories API failed: No categories data', response);
                    return false;
                }
            } catch (error) {
                updateTestResult('categories-test', 'error', 
                    `Categories API failed: ${error.message}`, error);
                return false;
            }
        }

        // Test Authors API
        async function testAuthorsAPI() {
            try {
                const query = `
                    query TestAuthors {
                        authors(first: 5) {
                            edges {
                                node {
                                    id
                                    name
                                    slug
                                    bio
                                }
                            }
                        }
                    }
                `;

                const response = await window.ApiClient.graphqlQuery(query);
                
                if (response && response.authors && response.authors.edges.length > 0) {
                    const count = response.authors.edges.length;
                    updateTestResult('authors-test', 'success', 
                        `Authors API working! Retrieved ${count} authors`, 
                        { count, sample: response.authors.edges[0].node });
                    return true;
                } else {
                    updateTestResult('authors-test', 'error', 
                        'Authors API failed: No authors data', response);
                    return false;
                }
            } catch (error) {
                updateTestResult('authors-test', 'error', 
                    `Authors API failed: ${error.message}`, error);
                return false;
            }
        }

        // Test Sources API
        async function testSourcesAPI() {
            try {
                const query = `
                    query TestSources {
                        sources(first: 5) {
                            edges {
                                node {
                                    id
                                    title
                                    slug
                                    description
                                }
                            }
                        }
                    }
                `;

                const response = await window.ApiClient.graphqlQuery(query);
                
                if (response && response.sources && response.sources.edges.length > 0) {
                    const count = response.sources.edges.length;
                    updateTestResult('sources-test', 'success', 
                        `Sources API working! Retrieved ${count} sources`, 
                        { count, sample: response.sources.edges[0].node });
                    return true;
                } else {
                    updateTestResult('sources-test', 'error', 
                        'Sources API failed: No sources data', response);
                    return false;
                }
            } catch (error) {
                updateTestResult('sources-test', 'error', 
                    `Sources API failed: ${error.message}`, error);
                return false;
            }
        }

        // Test Navigation
        async function testNavigation() {
            try {
                // Test breadcrumb component
                if (window.BreadcrumbComponent) {
                    const breadcrumbs = window.BreadcrumbComponent.generateBreadcrumbItems();
                    updateTestResult('breadcrumb-test', 'success', 
                        `Breadcrumb navigation working! Generated ${breadcrumbs.length} items`, 
                        breadcrumbs);
                } else {
                    updateTestResult('breadcrumb-test', 'warning', 
                        'Breadcrumb component not loaded on this page');
                }

                // Test main navigation
                const navLinks = document.querySelectorAll('.nav-link, a[href^="/"]');
                updateTestResult('main-nav-test', 'success', 
                    `Main navigation working! Found ${navLinks.length} navigation links`);
                
                return true;
            } catch (error) {
                updateTestResult('breadcrumb-test', 'error', 
                    `Navigation test failed: ${error.message}`, error);
                return false;
            }
        }

        // Test Prerender Service
        async function testPrerenderService() {
            try {
                // Test health endpoint
                const healthResponse = await fetch('http://127.0.0.1:8082/health');
                if (healthResponse.ok) {
                    const healthData = await healthResponse.text();
                    updateTestResult('prerender-health-test', 'success', 
                        `Prerender service is healthy: ${healthData}`);
                } else {
                    updateTestResult('prerender-health-test', 'warning', 
                        'Prerender service health check failed - service may not be running');
                }

                // Test prerender content
                const prerenderResponse = await fetch('http://127.0.0.1:8082/prerender/categories/', {
                    headers: { 'User-Agent': 'Googlebot/2.1' }
                });
                
                if (prerenderResponse.ok) {
                    const content = await prerenderResponse.text();
                    const hasContent = content.includes('<title>') && content.includes('Categories');
                    
                    if (hasContent) {
                        updateTestResult('prerender-content-test', 'success', 
                            `Prerender content test passed! HTML size: ${content.length} chars`);
                    } else {
                        updateTestResult('prerender-content-test', 'warning', 
                            'Prerender content may be incomplete');
                    }
                } else {
                    updateTestResult('prerender-content-test', 'warning', 
                        'Prerender content test failed - service may not be running');
                }
                
                return true;
            } catch (error) {
                updateTestResult('prerender-health-test', 'warning', 
                    `Prerender service test failed: ${error.message} (This is normal if service is not running locally)`);
                return false;
            }
        }

        // Run all tests
        async function runAllTests() {
            console.log('🚀 Starting Production API Test Suite...');
            
            // Reset counters
            testResults = { total: 0, passed: 0, failed: 0, startTime: Date.now(), tests: {} };
            
            // Run tests sequentially
            await testGraphQLConnection();
            await testCategoriesAPI();
            await testAuthorsAPI();
            await testSourcesAPI();
            await testNavigation();
            await testPrerenderService();
            
            // Update summary
            const totalTime = Date.now() - testResults.startTime;
            document.getElementById('page-load-time').textContent = totalTime;
            
            const passRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
            const summaryElement = document.getElementById('test-summary');
            
            if (testResults.failed === 0) {
                summaryElement.className = 'test-item success';
                summaryElement.innerHTML = `
                    <h3>✅ All Tests Passed!</h3>
                    <p>Production API connection is working perfectly.</p>
                    <p><strong>Pass Rate:</strong> ${passRate}% (${testResults.passed}/${testResults.total})</p>
                    <p><strong>Total Time:</strong> ${totalTime}ms</p>
                    <p><strong>Ready for Production Deployment!</strong></p>
                `;
            } else {
                summaryElement.className = 'test-item warning';
                summaryElement.innerHTML = `
                    <h3>⚠️ Some Tests Failed</h3>
                    <p>Please review failed tests before production deployment.</p>
                    <p><strong>Pass Rate:</strong> ${passRate}% (${testResults.passed}/${testResults.total})</p>
                    <p><strong>Failed Tests:</strong> ${testResults.failed}</p>
                    <p><strong>Total Time:</strong> ${totalTime}ms</p>
                `;
            }
        }

        // Reset tests
        function resetTests() {
            location.reload();
        }

        // Export results
        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                config: window.AppConfig,
                results: testResults,
                summary: {
                    passRate: ((testResults.passed / testResults.total) * 100).toFixed(1),
                    totalTime: Date.now() - testResults.startTime,
                    readyForProduction: testResults.failed === 0
                }
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `production-api-test-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // Initialize when page loads
        window.addEventListener('load', initTestPage);
    </script>
</body>
</html>
