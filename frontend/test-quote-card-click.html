<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Card Click Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .quote-card-test {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Quote Card Click Test</h1>
    <p>This page tests quote card click functionality across different configurations.</p>

    <div class="test-section">
        <h2>API Configuration</h2>
        <button onclick="switchToProductionAPI()">Switch to Production API</button>
        <button onclick="testAPIConnection()">Test API Connection</button>
        <div id="api-status"></div>
    </div>

    <div class="test-section">
        <h2>Quote Card Tests</h2>
        <button onclick="createTestCards()">Create Test Quote Cards</button>
        <div id="test-cards-container"></div>
    </div>

    <div class="test-section">
        <h2>Page Navigation Tests</h2>
        <button onclick="testPageNavigation()">Test Page Navigation</button>
        <div id="navigation-results"></div>
    </div>

    <div class="test-section">
        <h2>Debug Information</h2>
        <button onclick="showDebugInfo()">Show Debug Info</button>
        <div id="debug-info"></div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/components/quote-card.js"></script>

    <script>
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(result);
        }

        function switchToProductionAPI() {
            window.QuoteseAPIMode.useProductionAPI();
            addResult('api-status', 'info', 'Switched to production API. Please refresh the page.');
        }

        async function testAPIConnection() {
            try {
                addResult('api-status', 'info', 'Testing API connection...');
                const result = await window.QuoteseAPIMode.testConnection();
                addResult('api-status', 'success', result);
                
                // Show current configuration
                const config = window.QuoteseAPIMode.getCurrentMode();
                const configInfo = `
                    <pre>Current API Configuration:
API Endpoint: ${config.apiEndpoint}
GraphQL Endpoint: ${config.graphqlEndpoint}
Using Production: ${config.isUsingProduction}
Debug Mode: ${config.debug}</pre>
                `;
                document.getElementById('api-status').innerHTML += configInfo;
            } catch (error) {
                addResult('api-status', 'error', `API test failed: ${error.message}`);
            }
        }

        async function createTestCards() {
            const container = document.getElementById('test-cards-container');
            container.innerHTML = '';
            
            try {
                addResult('test-cards-container', 'info', 'Loading test quotes from production API...');
                
                // Get some quotes from production API
                const quotes = await window.ApiClient.getQuotes(1, 3);
                
                if (!quotes || !quotes.quotes || quotes.quotes.length === 0) {
                    addResult('test-cards-container', 'error', 'No quotes received from API');
                    return;
                }
                
                addResult('test-cards-container', 'success', `Loaded ${quotes.quotes.length} quotes`);
                
                // Test different configurations
                const configs = [
                    { name: 'Default (showActions: false)', showActions: false },
                    { name: 'With Actions (showActions: true)', showActions: true },
                    { name: 'With Author Avatar', showActions: false, showAuthorAvatar: true }
                ];
                
                configs.forEach((config, configIndex) => {
                    const testSection = document.createElement('div');
                    testSection.className = 'quote-card-test';
                    testSection.innerHTML = `<h4>${config.name}</h4>`;
                    
                    quotes.quotes.forEach((quote, index) => {
                        const quoteCard = QuoteCardComponent.render(quote, index, config);
                        
                        // Add click tracking
                        quoteCard.addEventListener('click', (e) => {
                            console.log('Quote card clicked:', quote.id);
                            addResult('test-cards-container', 'info', 
                                `Quote card clicked: ID ${quote.id} - "${quote.content.substring(0, 30)}..."`);
                        });
                        
                        testSection.appendChild(quoteCard);
                    });
                    
                    container.appendChild(testSection);
                });
                
            } catch (error) {
                addResult('test-cards-container', 'error', `Failed to create test cards: ${error.message}`);
            }
        }

        function testPageNavigation() {
            const container = 'navigation-results';
            
            // Test URLs for different page types
            const testUrls = [
                { name: 'Homepage', url: '/' },
                { name: 'Category Page', url: '/categories/intelligence/' },
                { name: 'Author Page', url: '/authors/pearl-zhu/' },
                { name: 'Source Page', url: '/sources/1984/' },
                { name: 'Quote Detail Page', url: '/quotes/499001/' }
            ];
            
            testUrls.forEach(test => {
                addResult(container, 'info', 
                    `<a href="${test.url}" target="_blank">${test.name}: ${test.url}</a>`);
            });
            
            addResult(container, 'warning', 'Click the links above to test each page type');
        }

        async function showDebugInfo() {
            const container = document.getElementById('debug-info');
            
            try {
                // Test quote URL generation
                const testQuote = { id: '499001' };
                const quoteUrl = UrlHandler.getQuoteUrl(testQuote);
                
                const debugInfo = `
                    <pre>Debug Information:
Current URL: ${window.location.href}
Quote URL Generation Test: ${quoteUrl}
UrlHandler Available: ${typeof UrlHandler !== 'undefined'}
QuoteCardComponent Available: ${typeof QuoteCardComponent !== 'undefined'}
ApiClient Available: ${typeof window.ApiClient !== 'undefined'}

Window Objects:
- window.navigateToEntityWithId: ${typeof window.navigateToEntityWithId}
- window.AppConfig: ${JSON.stringify(window.AppConfig, null, 2)}
                    </pre>
                `;
                
                container.innerHTML = debugInfo;
                
                // Test API call
                addResult('debug-info', 'info', 'Testing API call...');
                const quote = await window.ApiClient.getQuoteById('499001');
                
                if (quote) {
                    addResult('debug-info', 'success', 
                        `API test successful: "${quote.content.substring(0, 50)}..." by ${quote.author.name}`);
                } else {
                    addResult('debug-info', 'error', 'API test failed: No quote returned');
                }
                
            } catch (error) {
                addResult('debug-info', 'error', `Debug info error: ${error.message}`);
            }
        }

        // Auto-initialize
        document.addEventListener('DOMContentLoaded', function() {
            addResult('api-status', 'info', 'Test page loaded. Use buttons to run tests.');
            
            // Auto-switch to production API for testing
            if (!localStorage.getItem('quotese-use-production-api')) {
                setTimeout(() => {
                    switchToProductionAPI();
                }, 1000);
            }
        });
    </script>
</body>
</html>
