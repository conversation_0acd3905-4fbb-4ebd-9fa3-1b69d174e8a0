<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试 - Quotese.com</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background-color: #f5f5f5; }
        .test-section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .loading { color: #007bff; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 300px; overflow-y: auto; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🧪 Quotese.com API连接测试</h1>
    
    <div class="test-section">
        <h2>配置信息</h2>
        <div id="config-info"></div>
    </div>

    <div class="test-section">
        <h2>基础连接测试</h2>
        <button onclick="testBasicConnection()">测试基础连接</button>
        <div id="basic-test-result"></div>
    </div>

    <div class="test-section">
        <h2>GraphQL查询测试</h2>
        <button onclick="testQuotesQuery()">测试名言查询</button>
        <button onclick="testStatsQuery()">测试统计查询</button>
        <button onclick="testPopularData()">测试热门数据</button>
        <div id="graphql-test-result"></div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/api-client.js"></script>
    <script>
        function showConfig() {
            const configDiv = document.getElementById('config-info');
            configDiv.innerHTML = `
                <pre>${JSON.stringify(window.AppConfig, null, 2)}</pre>
                <p><strong>API端点:</strong> ${window.AppConfig.apiEndpoint}</p>
                <p><strong>使用模拟数据:</strong> ${window.AppConfig.useMockData ? '是' : '否'}</p>
            `;
        }

        async function testBasicConnection() {
            const resultDiv = document.getElementById('basic-test-result');
            resultDiv.innerHTML = '<p class="loading">🔄 测试连接中...</p>';
            try {
                const response = await fetch(window.AppConfig.apiEndpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query: '{ quotesCount }' })
                });
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<p class="success">✅ 连接成功！</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<p class="error">❌ 连接失败: ${response.status} ${response.statusText}</p><pre>${await response.text()}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 连接错误: ${error.message}</p>`;
            }
        }

        async function testQuotesQuery() {
            const resultDiv = document.getElementById('graphql-test-result');
            resultDiv.innerHTML = '<p class="loading">🔄 测试名言查询...</p>';
            try {
                const quotes = await window.ApiClient.getTopQuotes(1, 3);
                resultDiv.innerHTML = `<p class="success">✅ 名言查询成功！</p><pre>${JSON.stringify(quotes, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 名言查询失败: ${error.message}</p>`;
            }
        }

        async function testStatsQuery() {
            const resultDiv = document.getElementById('graphql-test-result');
            resultDiv.innerHTML = '<p class="loading">🔄 测试统计查询...</p>';
            try {
                const stats = await window.ApiClient.getStats();
                resultDiv.innerHTML = `<p class="success">✅ 统计查询成功！</p><pre>${JSON.stringify(stats, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 统计查询失败: ${error.message}</p>`;
            }
        }

        async function testPopularData() {
            const resultDiv = document.getElementById('graphql-test-result');
            resultDiv.innerHTML = '<p class="loading">🔄 测试热门数据查询...</p>';
            try {
                const [categories, authors, sources] = await Promise.all([
                    window.ApiClient.getPopularCategories(5),
                    window.ApiClient.getPopularAuthors(5),
                    window.ApiClient.getPopularSources(5)
                ]);
                resultDiv.innerHTML = `
                    <p class="success">✅ 热门数据查询成功！</p>
                    <h4>热门类别:</h4><pre>${JSON.stringify(categories, null, 2)}</pre>
                    <h4>热门作者:</h4><pre>${JSON.stringify(authors, null, 2)}</pre>
                    <h4>热门来源:</h4><pre>${JSON.stringify(sources, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 热门数据查询失败: ${error.message}</p>`;
            }
        }

        window.addEventListener('load', function() {
            showConfig();
            console.log('🧪 API测试页面已加载');
        });
    </script>
</body>
</html>
