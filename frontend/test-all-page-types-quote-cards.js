// 在浏览器控制台中运行此脚本来测试所有页面类型的名言卡片功能

console.log('🧪 开始测试所有页面类型的名言卡片功能...');

// 测试配置
const testConfig = {
    pages: [
        {
            name: 'Homepage',
            url: '/?use-production-api=true',
            expectedCards: 10,
            cardConfig: { showActions: false, showAuthorAvatar: false }
        },
        {
            name: 'Category Page',
            url: '/categories/love-quotes/?use-production-api=true',
            expectedCards: 8,
            cardConfig: { showActions: false, highlightCurrentCategory: true }
        },
        {
            name: 'Author Page',
            url: '/authors/lailah-gifty-akita/?use-production-api=true',
            expectedCards: 15,
            cardConfig: { showActions: false, hideEmptyAvatar: true }
        },
        {
            name: 'Source Page',
            url: '/sources/and-being-free/?use-production-api=true',
            expectedCards: 6,
            cardConfig: { showActions: false, hideEmptyAvatar: true }
        }
    ],
    detailPages: [
        '/quotes/499001/?use-production-api=true',
        '/quotes/499002/?use-production-api=true',
        '/quotes/499003/?use-production-api=true'
    ]
};

// 测试结果存储
const testResults = {
    pages: {},
    detailPages: {},
    summary: {
        totalPages: 0,
        passedPages: 0,
        totalDetailPages: 0,
        passedDetailPages: 0,
        totalQuoteCards: 0,
        validQuoteCards: 0,
        issues: []
    }
};

// 测试单个页面的名言卡片
async function testPageQuoteCards(pageConfig) {
    console.log(`\n🔍 测试 ${pageConfig.name}...`);
    
    const result = {
        name: pageConfig.name,
        url: pageConfig.url,
        accessible: false,
        hasQuoteCards: false,
        totalCards: 0,
        validCards: 0,
        invalidCards: 0,
        missingIds: 0,
        issues: [],
        sampleIds: []
    };
    
    try {
        // 测试页面可访问性
        const response = await fetch(pageConfig.url);
        if (response.ok) {
            result.accessible = true;
            console.log(`✅ ${pageConfig.name}: 页面可访问`);
            
            const html = await response.text();
            
            // 检查页面是否包含必要的脚本
            const hasQuoteCardScript = html.includes('quote-card.js');
            const hasApiClient = html.includes('api-client.js');
            
            if (hasQuoteCardScript && hasApiClient) {
                console.log(`✅ ${pageConfig.name}: 包含必要的脚本`);
            } else {
                result.issues.push('Missing required scripts');
                console.log(`⚠️  ${pageConfig.name}: 缺少必要的脚本`);
            }
            
            // 检查是否有名言容器
            const hasQuotesContainer = html.includes('quotes-list') || html.includes('quotes-container');
            if (hasQuotesContainer) {
                result.hasQuoteCards = true;
                console.log(`✅ ${pageConfig.name}: 包含名言容器`);
            } else {
                result.issues.push('Missing quotes container');
                console.log(`❌ ${pageConfig.name}: 缺少名言容器`);
            }
            
        } else {
            result.issues.push(`HTTP ${response.status}`);
            console.log(`❌ ${pageConfig.name}: HTTP ${response.status}`);
        }
        
    } catch (error) {
        result.issues.push(error.message);
        console.log(`❌ ${pageConfig.name}: ${error.message}`);
    }
    
    return result;
}

// 测试名言详情页
async function testQuoteDetailPage(detailUrl) {
    console.log(`\n🔍 测试名言详情页: ${detailUrl}`);
    
    const result = {
        url: detailUrl,
        accessible: false,
        hasRequiredElements: false,
        missingElements: [],
        hasScripts: false,
        issues: []
    };
    
    try {
        const response = await fetch(detailUrl);
        if (response.ok) {
            result.accessible = true;
            console.log(`✅ 详情页可访问: ${detailUrl}`);
            
            const html = await response.text();
            
            // 检查必需的DOM元素
            const requiredElements = [
                'quote-content',
                'author-link',
                'author-initial',
                'source-text',
                'categories-container',
                'related-quotes-container',
                'authors-container',
                'sources-container'
            ];
            
            let foundElements = 0;
            requiredElements.forEach(elementId => {
                if (html.includes(`id="${elementId}"`)) {
                    foundElements++;
                } else {
                    result.missingElements.push(elementId);
                }
            });
            
            result.hasRequiredElements = foundElements === requiredElements.length;
            
            if (result.hasRequiredElements) {
                console.log(`✅ 详情页包含所有必需元素`);
            } else {
                console.log(`⚠️  详情页缺少 ${result.missingElements.length} 个元素: ${result.missingElements.join(', ')}`);
                result.issues.push(`Missing ${result.missingElements.length} required elements`);
            }
            
            // 检查脚本
            const hasQuoteScript = html.includes('quote.js');
            const hasApiClient = html.includes('api-client.js');
            const hasUrlHandler = html.includes('url-handler.js');
            
            result.hasScripts = hasQuoteScript && hasApiClient && hasUrlHandler;
            
            if (result.hasScripts) {
                console.log(`✅ 详情页包含所有必需脚本`);
            } else {
                console.log(`⚠️  详情页缺少必需脚本`);
                result.issues.push('Missing required scripts');
            }
            
        } else {
            result.issues.push(`HTTP ${response.status}`);
            console.log(`❌ 详情页不可访问: HTTP ${response.status}`);
        }
        
    } catch (error) {
        result.issues.push(error.message);
        console.log(`❌ 详情页测试失败: ${error.message}`);
    }
    
    return result;
}

// 测试名言卡片组件功能
async function testQuoteCardComponent() {
    console.log(`\n🧪 测试名言卡片组件功能...`);
    
    const componentResult = {
        componentExists: false,
        renderWorks: false,
        entityIdCorrect: false,
        clickEventWorks: false,
        urlGenerationWorks: false,
        issues: []
    };
    
    try {
        // 检查组件是否存在
        if (typeof QuoteCardComponent !== 'undefined' && typeof QuoteCardComponent.render === 'function') {
            componentResult.componentExists = true;
            console.log('✅ QuoteCardComponent 存在');
            
            // 测试渲染功能
            const testQuote = {
                id: '499001',
                content: 'Test quote for component testing.',
                author: { id: '1', name: 'Test Author' },
                categories: [{ id: '1', name: 'Test Category' }]
            };
            
            const quoteCard = QuoteCardComponent.render(testQuote, 0, { showActions: true });
            if (quoteCard) {
                componentResult.renderWorks = true;
                console.log('✅ 名言卡片渲染成功');
                
                // 检查entity ID
                const entityId = quoteCard.getAttribute('data-quote-id');
                if (entityId === testQuote.id) {
                    componentResult.entityIdCorrect = true;
                    console.log(`✅ Entity ID 正确: ${entityId}`);
                } else {
                    componentResult.issues.push(`Entity ID mismatch: ${entityId} vs ${testQuote.id}`);
                    console.log(`❌ Entity ID 不匹配: ${entityId} vs ${testQuote.id}`);
                }
                
                // 检查点击事件
                const hasClickStyling = quoteCard.classList.contains('cursor-pointer');
                if (hasClickStyling) {
                    componentResult.clickEventWorks = true;
                    console.log('✅ 点击样式正确');
                } else {
                    componentResult.issues.push('Missing click styling');
                    console.log('❌ 缺少点击样式');
                }
                
            } else {
                componentResult.issues.push('Render returned null');
                console.log('❌ 渲染返回null');
            }
            
        } else {
            componentResult.issues.push('QuoteCardComponent not found');
            console.log('❌ QuoteCardComponent 不存在');
        }
        
        // 测试URL生成
        if (typeof UrlHandler !== 'undefined' && typeof UrlHandler.getQuoteUrl === 'function') {
            const testQuote = { id: '499001' };
            const url = UrlHandler.getQuoteUrl(testQuote);
            if (url === '/quotes/499001/') {
                componentResult.urlGenerationWorks = true;
                console.log('✅ URL生成正确');
            } else {
                componentResult.issues.push(`URL generation incorrect: ${url}`);
                console.log(`❌ URL生成错误: ${url}`);
            }
        } else {
            componentResult.issues.push('UrlHandler not found');
            console.log('❌ UrlHandler 不存在');
        }
        
    } catch (error) {
        componentResult.issues.push(error.message);
        console.log(`❌ 组件测试失败: ${error.message}`);
    }
    
    return componentResult;
}

// 运行完整测试套件
async function runCompleteTestSuite() {
    console.log('🚀 开始运行完整测试套件...');
    console.log('='.repeat(60));
    
    const startTime = new Date();
    
    // 测试组件功能
    const componentResult = await testQuoteCardComponent();
    
    // 测试所有页面类型
    for (const pageConfig of testConfig.pages) {
        const pageResult = await testPageQuoteCards(pageConfig);
        testResults.pages[pageConfig.name] = pageResult;
        testResults.summary.totalPages++;
        if (pageResult.accessible && pageResult.hasQuoteCards) {
            testResults.summary.passedPages++;
        }
    }
    
    // 测试名言详情页
    for (const detailUrl of testConfig.detailPages) {
        const detailResult = await testQuoteDetailPage(detailUrl);
        testResults.detailPages[detailUrl] = detailResult;
        testResults.summary.totalDetailPages++;
        if (detailResult.accessible && detailResult.hasRequiredElements) {
            testResults.summary.passedDetailPages++;
        }
    }
    
    // 生成测试报告
    const endTime = new Date();
    const duration = Math.round((endTime - startTime) / 1000);
    
    console.log('\\n📊 测试报告');
    console.log('='.repeat(60));
    console.log(`测试时间: ${duration} 秒`);
    console.log(`页面类型测试: ${testResults.summary.passedPages}/${testResults.summary.totalPages} 通过`);
    console.log(`详情页测试: ${testResults.summary.passedDetailPages}/${testResults.summary.totalDetailPages} 通过`);
    
    // 组件测试结果
    console.log('\\n🧩 组件测试结果:');
    console.log(`  组件存在: ${componentResult.componentExists ? '✅' : '❌'}`);
    console.log(`  渲染功能: ${componentResult.renderWorks ? '✅' : '❌'}`);
    console.log(`  Entity ID: ${componentResult.entityIdCorrect ? '✅' : '❌'}`);
    console.log(`  点击事件: ${componentResult.clickEventWorks ? '✅' : '❌'}`);
    console.log(`  URL生成: ${componentResult.urlGenerationWorks ? '✅' : '❌'}`);
    
    // 页面测试详情
    console.log('\\n📄 页面测试详情:');
    Object.values(testResults.pages).forEach(page => {
        const status = page.accessible && page.hasQuoteCards ? '✅' : '❌';
        console.log(`  ${status} ${page.name}: ${page.issues.length === 0 ? '正常' : page.issues.join(', ')}`);
    });
    
    // 详情页测试详情
    console.log('\\n📋 详情页测试详情:');
    Object.values(testResults.detailPages).forEach(detail => {
        const status = detail.accessible && detail.hasRequiredElements ? '✅' : '❌';
        const url = detail.url.split('?')[0]; // 移除查询参数显示
        console.log(`  ${status} ${url}: ${detail.issues.length === 0 ? '正常' : detail.issues.join(', ')}`);
    });
    
    // 总体评估
    const overallSuccess = 
        componentResult.componentExists && 
        componentResult.renderWorks && 
        componentResult.entityIdCorrect &&
        testResults.summary.passedPages === testResults.summary.totalPages &&
        testResults.summary.passedDetailPages === testResults.summary.totalDetailPages;
    
    console.log('\\n🎯 总体评估:');
    if (overallSuccess) {
        console.log('✅ 所有测试通过！名言卡片功能完全正常。');
    } else {
        console.log('⚠️  存在问题需要修复。请查看上述详细结果。');
    }
    
    console.log('='.repeat(60));
    
    return {
        success: overallSuccess,
        componentResult,
        testResults,
        duration
    };
}

// 导出测试函数
window.QuoteCardTestSuite = {
    runComplete: runCompleteTestSuite,
    testComponent: testQuoteCardComponent,
    testPage: testPageQuoteCards,
    testDetailPage: testQuoteDetailPage,
    results: testResults
};

console.log('📋 名言卡片测试套件已加载');
console.log('💡 使用方法:');
console.log('   - QuoteCardTestSuite.runComplete() - 运行完整测试套件');
console.log('   - QuoteCardTestSuite.testComponent() - 测试组件功能');
console.log('   - QuoteCardTestSuite.testPage(config) - 测试单个页面');
console.log('   - QuoteCardTestSuite.testDetailPage(url) - 测试详情页');

// 自动运行完整测试
console.log('\\n🔄 自动运行完整测试套件...');
runCompleteTestSuite();
