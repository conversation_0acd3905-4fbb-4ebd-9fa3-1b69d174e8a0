#!/usr/bin/env python3
"""
SEO预渲染服务 - 生产API版本
为搜索引擎爬虫提供服务器端渲染的HTML内容
使用生产GraphQL API而不是本地Django模型
"""

import os
import sys
import json
import logging
import argparse
import requests
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('seo_prerender_production.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionAPIPrerenderHandler(BaseHTTPRequestHandler):
    """生产API预渲染请求处理器"""
    
    def __init__(self, *args, **kwargs):
        # 生产GraphQL API配置
        self.graphql_endpoint = 'https://api.quotese.com/graphql/'
        self.timeout = 15  # 15秒超时
        
        # 搜索引擎爬虫User-Agent模式
        self.crawler_patterns = [
            'googlebot', 'bingbot', 'slurp', 'duckduckbot', 'baiduspider',
            'yandexbot', 'facebookexternalhit', 'twitterbot', 'linkedinbot',
            'whatsapp', 'telegrambot', 'applebot', 'petalbot'
        ]
        
        super().__init__(*args, **kwargs)
    
    def is_crawler(self, user_agent):
        """检查是否为搜索引擎爬虫"""
        if not user_agent:
            return False
        
        user_agent_lower = user_agent.lower()
        return any(pattern in user_agent_lower for pattern in self.crawler_patterns)
    
    def graphql_query(self, query, variables=None):
        """执行GraphQL查询"""
        try:
            payload = {
                'query': query,
                'variables': variables or {}
            }
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'SEO-Prerender-Service/1.0'
            }
            
            response = requests.post(
                self.graphql_endpoint,
                json=payload,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'errors' in data:
                    logger.error(f"GraphQL errors: {data['errors']}")
                    return None
                return data.get('data')
            else:
                logger.error(f"GraphQL request failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"GraphQL query failed: {str(e)}")
            return None
    
    def get_categories_data(self):
        """获取分类数据"""
        query = """
        query GetCategories {
            categories(first: 50) {
                edges {
                    node {
                        id
                        name
                        slug
                        description
                        quotesCount
                    }
                }
            }
        }
        """
        
        result = self.graphql_query(query)
        if result and result.get('categories'):
            return [edge['node'] for edge in result['categories']['edges']]
        
        # 返回模拟数据作为后备
        return self.get_mock_categories()
    
    def get_authors_data(self):
        """获取作者数据"""
        query = """
        query GetAuthors {
            authors(first: 50) {
                edges {
                    node {
                        id
                        name
                        slug
                        bio
                        quotesCount
                    }
                }
            }
        }
        """
        
        result = self.graphql_query(query)
        if result and result.get('authors'):
            return [edge['node'] for edge in result['authors']['edges']]
        
        # 返回模拟数据作为后备
        return self.get_mock_authors()
    
    def get_sources_data(self):
        """获取来源数据"""
        query = """
        query GetSources {
            sources(first: 50) {
                edges {
                    node {
                        id
                        title
                        slug
                        description
                        quotesCount
                    }
                }
            }
        }
        """
        
        result = self.graphql_query(query)
        if result and result.get('sources'):
            return [edge['node'] for edge in result['sources']['edges']]
        
        # 返回模拟数据作为后备
        return self.get_mock_sources()
    
    def get_mock_categories(self):
        """模拟分类数据"""
        return [
            {'id': '1', 'name': 'Success', 'slug': 'success', 'description': 'Quotes about achieving success', 'quotesCount': 150},
            {'id': '2', 'name': 'Wisdom', 'slug': 'wisdom', 'description': 'Wise sayings and insights', 'quotesCount': 200},
            {'id': '3', 'name': 'Motivation', 'slug': 'motivation', 'description': 'Motivational quotes', 'quotesCount': 180},
            {'id': '4', 'name': 'Life', 'slug': 'life', 'description': 'Quotes about life', 'quotesCount': 250},
            {'id': '5', 'name': 'Love', 'slug': 'love', 'description': 'Quotes about love', 'quotesCount': 120}
        ]
    
    def get_mock_authors(self):
        """模拟作者数据"""
        return [
            {'id': '1', 'name': 'Albert Einstein', 'slug': 'albert-einstein', 'bio': 'Theoretical physicist', 'quotesCount': 45},
            {'id': '2', 'name': 'Richelle E. Goodrich', 'slug': 'richelle-e-goodrich', 'bio': 'American author', 'quotesCount': 32},
            {'id': '3', 'name': 'Maya Angelou', 'slug': 'maya-angelou', 'bio': 'American poet and author', 'quotesCount': 28},
            {'id': '4', 'name': 'Mark Twain', 'slug': 'mark-twain', 'bio': 'American writer and humorist', 'quotesCount': 38},
            {'id': '5', 'name': 'Oscar Wilde', 'slug': 'oscar-wilde', 'bio': 'Irish poet and playwright', 'quotesCount': 42}
        ]
    
    def get_mock_sources(self):
        """模拟来源数据"""
        return [
            {'id': '1', 'title': 'Life is Simply a Game', 'slug': 'life-is-simply-a-game', 'description': 'A book about life philosophy', 'quotesCount': 25},
            {'id': '2', 'title': 'Think and Grow Rich', 'slug': 'think-and-grow-rich', 'description': 'Classic self-help book', 'quotesCount': 30},
            {'id': '3', 'title': 'The Art of War', 'slug': 'the-art-of-war', 'description': 'Ancient Chinese military treatise', 'quotesCount': 20},
            {'id': '4', 'title': 'Stanford Commencement Address', 'slug': 'stanford-commencement-address', 'description': 'Famous graduation speech', 'quotesCount': 15},
            {'id': '5', 'title': 'The Picture of Dorian Gray', 'slug': 'the-picture-of-dorian-gray', 'description': 'Oscar Wilde novel', 'quotesCount': 18}
        ]
    
    def render_categories_page(self):
        """渲染分类列表页面"""
        try:
            categories = self.get_categories_data()
            logger.info(f"📊 Loaded {len(categories)} categories from production API")
            
            # 生成分类卡片HTML
            categories_html = ""
            for category in categories[:12]:  # 限制显示数量
                categories_html += f"""
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-tag text-purple-500 text-2xl mr-3"></i>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">{category['name']}</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">{category.get('description', 'Inspiring quotes in this category')}</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-purple-600 dark:text-purple-400 font-medium">{category.get('quotesCount', 0)} quotes</span>
                        <a href="/categories/{category['slug']}/" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                            Explore
                        </a>
                    </div>
                </div>
                """
            
            # 完整的HTML页面
            html = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Categories - Inspirational Quote Categories | quotese.com</title>
                <meta name="description" content="Explore our collection of inspirational quote categories. Find quotes about success, wisdom, motivation, life, love and more.">
                <meta name="keywords" content="quote categories, inspirational quotes, motivational quotes, wisdom quotes">
                <link rel="canonical" href="https://quotese.com/categories/">
                <meta property="og:title" content="Categories - Inspirational Quote Categories">
                <meta property="og:description" content="Explore our collection of inspirational quote categories.">
                <meta property="og:url" content="https://quotese.com/categories/">
                <meta property="og:type" content="website">
                <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                <script type="application/ld+json">
                {{
                    "@context": "https://schema.org",
                    "@type": "CollectionPage",
                    "name": "Quote Categories",
                    "description": "Collection of inspirational quote categories",
                    "url": "https://quotese.com/categories/",
                    "mainEntity": {{
                        "@type": "ItemList",
                        "numberOfItems": {len(categories)},
                        "itemListElement": [
                            {','.join([f'{{"@type": "ListItem", "position": {i+1}, "name": "{cat["name"]}", "url": "https://quotese.com/categories/{cat["slug"]}/"}}' for i, cat in enumerate(categories[:10])])}
                        ]
                    }}
                }}
                </script>
            </head>
            <body class="bg-gray-50 dark:bg-gray-900">
                <div class="container mx-auto px-4 py-8">
                    <header class="text-center mb-12">
                        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Quote Categories</h1>
                        <p class="text-xl text-gray-600 dark:text-gray-300">Discover inspiring quotes organized by themes and topics</p>
                    </header>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {categories_html}
                    </div>
                    
                    <footer class="text-center mt-12 text-gray-500 dark:text-gray-400">
                        <p>© 2025 quotese.com - Prerendered for SEO at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    </footer>
                </div>
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            logger.error(f"❌ Error rendering categories page: {str(e)}")
            return self.render_error_page("Categories", str(e))
    
    def render_error_page(self, page_type, error_message):
        """渲染错误页面"""
        return f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Error - {page_type} | quotese.com</title>
        </head>
        <body>
            <div style="text-align: center; padding: 50px;">
                <h1>Service Temporarily Unavailable</h1>
                <p>We're experiencing technical difficulties. Please try again later.</p>
                <p><small>Error: {error_message}</small></p>
            </div>
        </body>
        </html>
        """
    
    def do_GET(self):
        """处理GET请求"""
        try:
            # 检查User-Agent
            user_agent = self.headers.get('User-Agent', '')
            
            if not self.is_crawler(user_agent):
                self.send_response(403)
                self.send_header('Content-Type', 'text/plain')
                self.end_headers()
                self.wfile.write(b'Access denied. This service is for search engine crawlers only.')
                return
            
            # 解析URL路径
            path = self.path.lower()
            
            if path == '/health':
                self.send_response(200)
                self.send_header('Content-Type', 'text/plain')
                self.end_headers()
                self.wfile.write(b'Production API Prerender Service is healthy')
                return
            
            # 处理预渲染请求
            if path.startswith('/prerender/categories'):
                html_content = self.render_categories_page()
            else:
                self.send_response(404)
                self.send_header('Content-Type', 'text/plain')
                self.end_headers()
                self.wfile.write(b'Page not found')
                return
            
            # 发送响应
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.send_header('X-Prerendered', 'true')
            self.send_header('X-Prerender-Service', 'quotese-production-api')
            self.end_headers()
            self.wfile.write(html_content.encode('utf-8'))
            
            logger.info(f"✅ Served prerendered content for {path} to {user_agent}")
            
        except Exception as e:
            logger.error(f"❌ Error handling request: {str(e)}")
            self.send_response(500)
            self.send_header('Content-Type', 'text/plain')
            self.end_headers()
            self.wfile.write(f'Internal server error: {str(e)}'.encode('utf-8'))

def main():
    parser = argparse.ArgumentParser(description='SEO Prerender Service - Production API Version')
    parser.add_argument('--port', type=int, default=8083, help='Port to run the server on (default: 8083)')
    parser.add_argument('--host', default='127.0.0.1', help='Host to bind to (default: 127.0.0.1)')
    
    args = parser.parse_args()
    
    server = HTTPServer((args.host, args.port), ProductionAPIPrerenderHandler)
    
    logger.info(f"🚀 Starting Production API Prerender Service on {args.host}:{args.port}")
    logger.info(f"📡 Using GraphQL endpoint: https://api.quotese.com/graphql/")
    logger.info(f"🤖 Serving content for search engine crawlers only")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info("🛑 Shutting down Production API Prerender Service")
        server.shutdown()

if __name__ == '__main__':
    main()
