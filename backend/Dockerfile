FROM python:3.9-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
ENV PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    default-libmysqlclient-dev \
    gcc \
    python3-dev \
    libevent-dev \
    make \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir wheel && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install gunicorn

# 复制项目文件
COPY . .

# 创建静态文件目录
RUN mkdir -p /app/static

# 设置启动命令
CMD ["sh", "-c", "python manage.py collectstatic --noinput --settings=quotes_admin.settings_prod && python manage.py migrate --settings=quotes_admin.settings_prod && gunicorn quotes_admin.wsgi:application --bind 0.0.0.0:8000"]

EXPOSE 8000

