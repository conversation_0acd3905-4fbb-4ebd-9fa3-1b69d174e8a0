#!/bin/bash

# Quotese Sitemap自动更新脚本 v2.0
# 支持新的语义化URL架构和SEO优化
# 此脚本应该定期运行（例如，每周一次）以更新网站地图
# 版本：v2.0 - SEO重启实施
# 更新日期：2025年6月16日

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SITEMAP_PATH="$PROJECT_ROOT/frontend/sitemap.xml"
LOG_FILE="$SCRIPT_DIR/sitemap_update.log"
BACKUP_DIR="$SCRIPT_DIR/sitemap_backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 日志记录函数
log_to_file() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1" >> "$LOG_FILE"
}

# 备份现有sitemap
backup_sitemap() {
    if [ -f "$SITEMAP_PATH" ]; then
        log_info "备份现有sitemap..."
        cp "$SITEMAP_PATH" "$BACKUP_DIR/sitemap_backup_$TIMESTAMP.xml"
        log_success "备份完成: sitemap_backup_$TIMESTAMP.xml"
        log_to_file "备份现有sitemap: sitemap_backup_$TIMESTAMP.xml"
    else
        log_warning "未找到现有sitemap文件"
        log_to_file "未找到现有sitemap文件"
    fi
}

# 生成新的sitemap
generate_sitemap() {
    log_info "开始生成新的sitemap..."
    log_to_file "开始生成sitemap"

    # 切换到脚本目录
    cd "$SCRIPT_DIR"

    # 激活虚拟环境（如果存在）
    if [ -f "venv/bin/activate" ]; then
        log_info "激活虚拟环境..."
        source venv/bin/activate
    elif [ -f "../venv/bin/activate" ]; then
        log_info "激活项目虚拟环境..."
        source ../venv/bin/activate
    fi

    # 运行生成脚本
    if python generate_sitemap.py; then
        log_success "Sitemap生成成功"
        log_to_file "Sitemap生成成功"
        return 0
    else
        log_error "Sitemap生成失败"
        log_to_file "Sitemap生成失败"
        return 1
    fi
}

# 验证sitemap文件
validate_sitemap() {
    log_info "验证sitemap文件..."

    if [ ! -f "$SITEMAP_PATH" ]; then
        log_error "Sitemap文件不存在: $SITEMAP_PATH"
        return 1
    fi

    # 检查文件大小
    file_size=$(stat -f%z "$SITEMAP_PATH" 2>/dev/null || stat -c%s "$SITEMAP_PATH" 2>/dev/null || echo "0")
    if [ "$file_size" -eq 0 ]; then
        log_error "Sitemap文件为空"
        return 1
    fi

    # 检查XML格式
    if command -v xmllint >/dev/null 2>&1; then
        if xmllint --noout "$SITEMAP_PATH" 2>/dev/null; then
            log_success "Sitemap XML格式验证通过"
        else
            log_error "Sitemap XML格式验证失败"
            return 1
        fi
    else
        log_warning "xmllint未安装，跳过XML格式验证"
    fi

    # 统计URL数量
    url_count=$(grep -c "<loc>" "$SITEMAP_PATH" 2>/dev/null || echo "0")
    log_info "Sitemap包含 $url_count 个URL"
    log_to_file "Sitemap包含 $url_count 个URL，文件大小: $file_size 字节"

    return 0
}

# 提交sitemap到搜索引擎
submit_to_search_engines() {
    log_info "提交sitemap到搜索引擎..."

    local sitemap_url="https://quotese.com/sitemap.xml"
    local success_count=0

    # 提交到Google
    if curl -s -o /dev/null -w "%{http_code}" "http://www.google.com/ping?sitemap=$sitemap_url" | grep -q "200"; then
        log_success "已提交到Google Search Console"
        log_to_file "已提交到Google Search Console"
        ((success_count++))
    else
        log_warning "提交到Google失败"
        log_to_file "提交到Google失败"
    fi

    # 提交到Bing
    if curl -s -o /dev/null -w "%{http_code}" "http://www.bing.com/ping?sitemap=$sitemap_url" | grep -q "200"; then
        log_success "已提交到Bing Webmaster Tools"
        log_to_file "已提交到Bing Webmaster Tools"
        ((success_count++))
    else
        log_warning "提交到Bing失败"
        log_to_file "提交到Bing失败"
    fi

    log_info "成功提交到 $success_count 个搜索引擎"
    return 0
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份文件..."

    # 保留最近30天的备份
    find "$BACKUP_DIR" -name "sitemap_backup_*.xml" -mtime +30 -delete 2>/dev/null || true

    local backup_count=$(find "$BACKUP_DIR" -name "sitemap_backup_*.xml" | wc -l)
    log_info "当前保留 $backup_count 个备份文件"
    log_to_file "清理旧备份，当前保留 $backup_count 个备份文件"
}

# 发送通知（可选）
send_notification() {
    local status="$1"
    local message="$2"

    # 这里可以添加邮件通知、Slack通知等
    # 示例：发送邮件通知
    # echo "$message" | mail -s "Quotese Sitemap Update - $status" <EMAIL>

    log_to_file "通知: $status - $message"
}

# 显示帮助信息
show_help() {
    echo "Quotese Sitemap自动更新脚本 v2.0"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help         显示此帮助信息"
    echo "  -g, --generate     仅生成sitemap，不提交到搜索引擎"
    echo "  -s, --submit       仅提交现有sitemap到搜索引擎"
    echo "  -v, --validate     仅验证现有sitemap文件"
    echo "  --no-backup        不创建备份"
    echo "  --no-submit        不提交到搜索引擎"
    echo ""
    echo "示例:"
    echo "  $0                 完整更新流程（默认）"
    echo "  $0 -g              仅生成sitemap"
    echo "  $0 -s              仅提交到搜索引擎"
    echo "  $0 --no-submit     生成但不提交"
}

# 主函数
main() {
    local generate_only=false
    local submit_only=false
    local validate_only=false
    local no_backup=false
    local no_submit=false

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -g|--generate)
                generate_only=true
                shift
                ;;
            -s|--submit)
                submit_only=true
                shift
                ;;
            -v|--validate)
                validate_only=true
                shift
                ;;
            --no-backup)
                no_backup=true
                shift
                ;;
            --no-submit)
                no_submit=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    log_info "开始Quotese Sitemap更新流程..."
    log_to_file "开始sitemap更新流程"

    # 验证模式
    if [ "$validate_only" = true ]; then
        if validate_sitemap; then
            log_success "Sitemap验证通过"
            exit 0
        else
            log_error "Sitemap验证失败"
            exit 1
        fi
    fi

    # 仅提交模式
    if [ "$submit_only" = true ]; then
        if validate_sitemap; then
            submit_to_search_engines
            log_success "Sitemap提交完成"
            exit 0
        else
            log_error "Sitemap验证失败，无法提交"
            exit 1
        fi
    fi

    # 备份现有文件
    if [ "$no_backup" != true ]; then
        backup_sitemap
    fi

    # 生成新的sitemap
    if generate_sitemap; then
        log_success "Sitemap生成成功"

        # 验证生成的文件
        if validate_sitemap; then
            log_success "Sitemap验证通过"

            # 提交到搜索引擎（除非禁用或仅生成模式）
            if [ "$no_submit" != true ] && [ "$generate_only" != true ]; then
                submit_to_search_engines
            fi

            # 清理旧备份
            cleanup_old_backups

            # 发送成功通知
            send_notification "SUCCESS" "Sitemap更新成功"

            log_success "Sitemap更新流程完成!"
            log_to_file "Sitemap更新流程完成"

        else
            log_error "Sitemap验证失败"
            send_notification "ERROR" "Sitemap验证失败"
            exit 1
        fi

    else
        log_error "Sitemap生成失败"
        send_notification "ERROR" "Sitemap生成失败"
        exit 1
    fi
}

# 运行主函数
main "$@"
