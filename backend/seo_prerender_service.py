#!/usr/bin/env python3
"""
SEO预渲染服务 - 基于最佳实践实施
为搜索引擎爬虫提供预渲染的HTML内容
支持Authors、Categories、Sources三种页面类型的动态渲染
"""

import os
import sys
import django
import json
import re
import logging
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quotes_admin.settings')
django.setup()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('seo_prerender.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

try:
    from quotesapp.models import Authors, Categories, Sources
    logger.info("✅ Django models imported successfully")
except ImportError as e:
    logger.error(f"❌ Failed to import Django models: {e}")
    # 使用模拟数据作为后备方案
    Authors = Categories = Sources = None

class SEOPrerenderHandler(BaseHTTPRequestHandler):
    """SEO预渲染请求处理器"""
    
    def __init__(self, *args, **kwargs):
        # 搜索引擎爬虫User-Agent模式
        self.crawler_patterns = [
            r'googlebot',
            r'bingbot', 
            r'slurp',
            r'duckduckbot',
            r'baiduspider',
            r'yandexbot',
            r'facebookexternalhit',
            r'twitterbot',
            r'linkedinbot',
            r'whatsapp',
            r'telegrambot',
            r'applebot',
            r'petalbot'
        ]
        super().__init__(*args, **kwargs)
    
    def log_message(self, format, *args):
        """自定义日志消息"""
        logger.info(f"{self.address_string()} - {format % args}")
    
    def is_crawler(self, user_agent: str) -> bool:
        """检测是否为搜索引擎爬虫"""
        if not user_agent:
            return False
        
        user_agent_lower = user_agent.lower()
        is_bot = any(re.search(pattern, user_agent_lower) for pattern in self.crawler_patterns)
        
        if is_bot:
            logger.info(f"🤖 Detected crawler: {user_agent}")
        
        return is_bot
    
    def do_GET(self):
        """处理GET请求"""
        try:
            # 获取User-Agent
            user_agent = self.headers.get('User-Agent', '')
            
            # 记录请求信息
            logger.info(f"📥 Request: {self.path} from {user_agent[:100]}")
            
            # 检查是否为搜索引擎爬虫
            if not self.is_crawler(user_agent):
                self.send_error(404, "This service is only for search engine crawlers")
                return
            
            # 路由处理
            if self.path == '/prerender/authors/':
                html_content = self.generate_authors_html()
            elif self.path == '/prerender/categories/':
                html_content = self.generate_categories_html()
            elif self.path == '/prerender/sources/':
                html_content = self.generate_sources_html()
            elif self.path == '/health':
                self.send_health_check()
                return
            else:
                self.send_error(404, "Page not found")
                return
            
            # 发送预渲染HTML
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.send_header('Cache-Control', 'public, max-age=3600')  # 缓存1小时
            self.send_header('X-Prerendered', 'true')
            self.end_headers()
            
            self.wfile.write(html_content.encode('utf-8'))
            logger.info(f"✅ Successfully served prerendered content for {self.path}")
            
        except Exception as e:
            logger.error(f"❌ Error processing request {self.path}: {str(e)}")
            self.send_error(500, f"Internal server error: {str(e)}")
    
    def send_health_check(self):
        """健康检查端点"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        
        health_data = {
            'status': 'healthy',
            'service': 'SEO Prerender Service',
            'version': '1.0',
            'timestamp': datetime.now().isoformat(),
            'supported_pages': ['/prerender/authors/', '/prerender/categories/', '/prerender/sources/']
        }
        
        self.wfile.write(json.dumps(health_data, indent=2).encode('utf-8'))
    
    def generate_authors_html(self) -> str:
        """生成Authors页面预渲染HTML"""
        try:
            # 尝试从数据库获取数据
            authors = None
            if Authors: <AUTHORS>
                    authors = list(Authors.objects.all()[:100])
                    logger.info(f"📊 Loaded {len(authors)} authors from database")
                except Exception as db_error:
                    logger.warning(f"⚠️ Database connection failed: {str(db_error)}")
                    authors = None

            if not authors:
                # 使用模拟数据
                authors = self.get_mock_authors()
                logger.info("📊 Using mock authors data")
            
            # 生成作者HTML
            authors_html = ""
            for author in authors:
                author_name = getattr(author, 'name', str(author))
                author_slug = author_name.lower().replace(' ', '-').replace('.', '')
                quotes_count = getattr(author, 'quotes_count', getattr(author, 'count', 0))
                
                authors_html += f"""
                <div class="author-card bg-white rounded-lg shadow-md p-6 mb-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-user text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">
                            <a href="/authors/{author_slug}/" class="hover:text-blue-600">{author_name}</a>
                        </h3>
                        <p class="text-gray-600 text-sm mb-4">{quotes_count} quotes</p>
                        <a href="/authors/{author_slug}/" class="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View Quotes <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
                """
            
            return self.generate_page_html(
                title="1000+ Famous Authors & Their Best Quotes | Quotese.com",
                description="Discover inspiring quotes from 1000+ famous authors including Einstein, Gandhi, and more. Find wisdom that changes lives. Browse now!",
                canonical="https://quotese.com/authors/",
                content=f"""
                <main class="container mx-auto px-4 py-8">
                    <div class="text-center mb-12">
                        <h1 class="text-4xl font-bold mb-4">Famous Authors & Their Best Quotes</h1>
                        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                            Discover inspiring quotes from history's greatest minds. Browse quotes by famous authors and find wisdom that changes lives.
                        </p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {authors_html}
                    </div>
                </main>
                """
            )
            
        except Exception as e:
            logger.error(f"❌ Error generating authors HTML: {str(e)}")
            return self.generate_error_html("Authors", str(e))
    
    def generate_categories_html(self) -> str:
        """生成Categories页面预渲染HTML"""
        try:
            # 尝试从数据库获取数据
            categories = None
            if Categories:
                try:
                    categories = list(Categories.objects.all()[:100])
                    logger.info(f"📊 Loaded {len(categories)} categories from database")
                except Exception as db_error:
                    logger.warning(f"⚠️ Database connection failed: {str(db_error)}")
                    categories = None

            if not categories:
                # 使用模拟数据
                categories = self.get_mock_categories()
                logger.info("📊 Using mock categories data")
            
            # 生成分类HTML
            categories_html = ""
            for category in categories:
                category_name = getattr(category, 'name', str(category))
                category_slug = category_name.lower().replace(' ', '-').replace('.', '')
                quotes_count = getattr(category, 'quotes_count', getattr(category, 'count', 0))
                
                categories_html += f"""
                <div class="category-card bg-white rounded-lg shadow-md p-6 mb-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-tag text-2xl text-purple-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">
                            <a href="/categories/{category_slug}/" class="hover:text-purple-600">{category_name}</a>
                        </h3>
                        <p class="text-gray-600 text-sm mb-4">{quotes_count} quotes</p>
                        <a href="/categories/{category_slug}/" class="inline-flex items-center text-purple-600 hover:text-purple-800 text-sm font-medium">
                            View Quotes <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
                """
            
            return self.generate_page_html(
                title="500+ Quote Categories: Life, Love, Success & More | Quotese.com",
                description="Find the perfect quote for any situation! 500+ categories including life advice, love quotes, success tips, and daily motivation. Start exploring!",
                canonical="https://quotese.com/categories/",
                content=f"""
                <main class="container mx-auto px-4 py-8">
                    <div class="text-center mb-12">
                        <h1 class="text-4xl font-bold mb-4">Browse All Quote Categories</h1>
                        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                            Find the perfect quote for any situation! Explore 500+ categories including life advice, love quotes, success tips, and daily motivation.
                        </p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {categories_html}
                    </div>
                </main>
                """
            )
            
        except Exception as e:
            logger.error(f"❌ Error generating categories HTML: {str(e)}")
            return self.generate_error_html("Categories", str(e))

    def generate_sources_html(self) -> str:
        """生成Sources页面预渲染HTML"""
        try:
            # 尝试从数据库获取数据
            sources = None
            if Sources:
                try:
                    sources = list(Sources.objects.all()[:100])
                    logger.info(f"📊 Loaded {len(sources)} sources from database")
                except Exception as db_error:
                    logger.warning(f"⚠️ Database connection failed: {str(db_error)}")
                    sources = None

            if not sources:
                # 使用模拟数据
                sources = self.get_mock_sources()
                logger.info("📊 Using mock sources data")

            # 生成来源HTML
            sources_html = ""
            for source in sources:
                source_name = getattr(source, 'name', str(source))
                source_slug = source_name.lower().replace(' ', '-').replace('.', '')
                quotes_count = getattr(source, 'quotes_count', getattr(source, 'count', 0))

                sources_html += f"""
                <div class="source-card bg-white rounded-lg shadow-md p-6 mb-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-book text-2xl text-green-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">
                            <a href="/sources/{source_slug}/" class="hover:text-green-600">{source_name}</a>
                        </h3>
                        <p class="text-gray-600 text-sm mb-4">{quotes_count} quotes</p>
                        <a href="/sources/{source_slug}/" class="inline-flex items-center text-green-600 hover:text-green-800 text-sm font-medium">
                            View Quotes <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
                """

            return self.generate_page_html(
                title="Quotes from Books, Movies & Speeches | Best Sources | Quotese.com",
                description="Discover powerful quotes from bestselling books, iconic movies, and historic speeches. Find inspiration from the world's greatest sources!",
                canonical="https://quotese.com/sources/",
                content=f"""
                <main class="container mx-auto px-4 py-8">
                    <div class="text-center mb-12">
                        <h1 class="text-4xl font-bold mb-4">Quotes from Books, Movies & Speeches</h1>
                        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                            Discover powerful quotes from bestselling books, iconic movies, and historic speeches. Find inspiration from the world's greatest sources!
                        </p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {sources_html}
                    </div>
                </main>
                """
            )

        except Exception as e:
            logger.error(f"❌ Error generating sources HTML: {str(e)}")
            return self.generate_error_html("Sources", str(e))

    def generate_page_html(self, title: str, description: str, canonical: str, content: str) -> str:
        """生成完整的页面HTML"""
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <meta name="description" content="{description}">
    <link rel="canonical" href="{canonical}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{title}">
    <meta property="og:description" content="{description}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{canonical}">
    <meta property="og:site_name" content="Quotese.com">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{title}">
    <meta name="twitter:description" content="{description}">

    <!-- Prerendered CSS -->
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f9fafb; }}
        .container {{ max-width: 1200px; margin: 0 auto; padding: 0 1rem; }}
        .text-center {{ text-align: center; }}
        .text-4xl {{ font-size: 2.25rem; font-weight: bold; }}
        .text-xl {{ font-size: 1.25rem; }}
        .text-lg {{ font-size: 1.125rem; }}
        .text-sm {{ font-size: 0.875rem; }}
        .mb-4 {{ margin-bottom: 1rem; }}
        .mb-12 {{ margin-bottom: 3rem; }}
        .py-8 {{ padding-top: 2rem; padding-bottom: 2rem; }}
        .px-4 {{ padding-left: 1rem; padding-right: 1rem; }}
        .grid {{ display: grid; }}
        .grid-cols-1 {{ grid-template-columns: repeat(1, minmax(0, 1fr)); }}
        .gap-6 {{ gap: 1.5rem; }}
        .bg-white {{ background-color: white; }}
        .rounded-lg {{ border-radius: 0.5rem; }}
        .shadow-md {{ box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }}
        .p-6 {{ padding: 1.5rem; }}
        .text-gray-600 {{ color: #6b7280; }}
        .hover\\:text-blue-600:hover {{ color: #2563eb; }}
        .hover\\:text-purple-600:hover {{ color: #9333ea; }}
        .hover\\:text-green-600:hover {{ color: #059669; }}
        a {{ text-decoration: none; color: inherit; }}
        @media (min-width: 768px) {{ .grid-cols-1 {{ grid-template-columns: repeat(2, minmax(0, 1fr)); }} }}
        @media (min-width: 1024px) {{ .grid-cols-1 {{ grid-template-columns: repeat(3, minmax(0, 1fr)); }} }}
        @media (min-width: 1280px) {{ .grid-cols-1 {{ grid-template-columns: repeat(4, minmax(0, 1fr)); }} }}
    </style>
</head>
<body>
    {content}

    <!-- Prerendered marker -->
    <script>
        window.PRERENDERED = true;
        console.log('✅ Prerendered content loaded for search engines');
    </script>
</body>
</html>"""

    def generate_error_html(self, page_type: str, error_message: str) -> str:
        """生成错误页面HTML"""
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <title>Error Loading {page_type} - Quotese.com</title>
    <meta name="description" content="Error loading {page_type} page">
</head>
<body>
    <div style="text-align: center; padding: 50px;">
        <h1>Error Loading {page_type}</h1>
        <p>Sorry, there was an error loading the {page_type} page.</p>
        <p>Error: {error_message}</p>
        <a href="https://quotese.com/">Return to Home</a>
    </div>
</body>
</html>"""

    def get_mock_authors(self):
        """获取模拟作者数据"""
        return [
            type('Author', (), {'name': 'Albert Einstein', 'quotes_count': 45}),
            type('Author', (), {'name': 'Mahatma Gandhi', 'quotes_count': 38}),
            type('Author', (), {'name': 'Winston Churchill', 'quotes_count': 32}),
            type('Author', (), {'name': 'Maya Angelou', 'quotes_count': 28}),
            type('Author', (), {'name': 'Nelson Mandela', 'quotes_count': 25}),
            type('Author', (), {'name': 'Steve Jobs', 'quotes_count': 22}),
            type('Author', (), {'name': 'Mark Twain', 'quotes_count': 35}),
            type('Author', (), {'name': 'Oscar Wilde', 'quotes_count': 30}),
            type('Author', (), {'name': 'Benjamin Franklin', 'quotes_count': 27}),
            type('Author', (), {'name': 'Theodore Roosevelt', 'quotes_count': 24})
        ]

    def get_mock_categories(self):
        """获取模拟分类数据"""
        return [
            type('Category', (), {'name': 'Life', 'quotes_count': 156}),
            type('Category', (), {'name': 'Love', 'quotes_count': 134}),
            type('Category', (), {'name': 'Success', 'quotes_count': 128}),
            type('Category', (), {'name': 'Wisdom', 'quotes_count': 112}),
            type('Category', (), {'name': 'Motivation', 'quotes_count': 98}),
            type('Category', (), {'name': 'Happiness', 'quotes_count': 87}),
            type('Category', (), {'name': 'Friendship', 'quotes_count': 76}),
            type('Category', (), {'name': 'Leadership', 'quotes_count': 65}),
            type('Category', (), {'name': 'Dreams', 'quotes_count': 54}),
            type('Category', (), {'name': 'Change', 'quotes_count': 43})
        ]

    def get_mock_sources(self):
        """获取模拟来源数据"""
        return [
            type('Source', (), {'name': 'The Great Gatsby', 'quotes_count': 23}),
            type('Source', (), {'name': 'To Kill a Mockingbird', 'quotes_count': 19}),
            type('Source', (), {'name': 'The Shawshank Redemption', 'quotes_count': 17}),
            type('Source', (), {'name': 'Forrest Gump', 'quotes_count': 15}),
            type('Source', (), {'name': 'The Godfather', 'quotes_count': 14}),
            type('Source', (), {'name': 'Star Wars', 'quotes_count': 12}),
            type('Source', (), {'name': 'The Lord of the Rings', 'quotes_count': 11}),
            type('Source', (), {'name': 'Harry Potter', 'quotes_count': 10}),
            type('Source', (), {'name': 'The Matrix', 'quotes_count': 9}),
            type('Source', (), {'name': 'Casablanca', 'quotes_count': 8})
        ]


def run_prerender_server(port=8082, host='127.0.0.1'):
    """启动预渲染服务器"""
    try:
        server_address = (host, port)
        httpd = HTTPServer(server_address, SEOPrerenderHandler)

        logger.info(f"🚀 SEO预渲染服务启动成功")
        logger.info(f"📍 服务地址: http://{host}:{port}")
        logger.info(f"🔍 支持的预渲染路径:")
        logger.info(f"   - /prerender/authors/ -> Authors列表页预渲染")
        logger.info(f"   - /prerender/categories/ -> Categories列表页预渲染")
        logger.info(f"   - /prerender/sources/ -> Sources列表页预渲染")
        logger.info(f"   - /health -> 健康检查")
        logger.info(f"🤖 仅为搜索引擎爬虫提供服务")

        print(f"\n🎯 SEO预渲染服务已启动在 http://{host}:{port}")
        print(f"📝 日志文件: seo_prerender.log")
        print(f"⏹️  按 Ctrl+C 停止服务\n")

        httpd.serve_forever()

    except KeyboardInterrupt:
        logger.info("🛑 收到停止信号，正在关闭预渲染服务...")
        httpd.shutdown()
        logger.info("✅ 预渲染服务已停止")
        print("\n✅ 预渲染服务已停止")
    except Exception as e:
        logger.error(f"❌ 预渲染服务启动失败: {str(e)}")
        print(f"❌ 服务启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='SEO预渲染服务')
    parser.add_argument('--port', type=int, default=8082, help='服务端口 (默认: 8082)')
    parser.add_argument('--host', type=str, default='127.0.0.1', help='服务主机 (默认: 127.0.0.1)')

    args = parser.parse_args()

    run_prerender_server(port=args.port, host=args.host)
