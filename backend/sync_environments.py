#!/usr/bin/env python3
"""
环境同步工具
用于同步不同环境之间的数据结构和配置
"""

import requests
import json
import os
import sys
import django
from django.conf import settings

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quotes_admin.settings_local')
django.setup()

from quotesapp.models import Authors, Categories, Sources

class EnvironmentSyncTool:
    def __init__(self):
        self.prod_api = "https://api.quotese.com/api/"
        self.local_api = "http://127.0.0.1:8000/api/"
    
    def sync_schema_from_production(self):
        """从生产环境同步数据结构"""
        print("📥 从生产环境同步数据结构...")
        
        try:
            # 获取生产环境的数据结构信息
            prod_authors = requests.get(f"{self.prod_api}authors/").json()
            prod_categories = requests.get(f"{self.prod_api}categories/").json()
            
            print(f"生产环境数据量:")
            print(f"  作者: {len(prod_authors)}")
            print(f"  类别: {len(prod_categories)}")
            
            # 同步作者数据结构（仅结构，不包含具体内容）
            for author_data in prod_authors[:10]:  # 只同步前10个作为示例
                author, created = Authors.objects.get_or_create(
                    name=author_data['name'],
                    defaults={
                        'quotes_count': 0,
                        'created_at': author_data.get('created_at'),
                        'updated_at': author_data.get('updated_at')
                    }
                )
                if created:
                    print(f"  同步作者: {author.name}")
            
            # 同步类别数据结构
            for category_data in prod_categories[:15]:  # 只同步前15个
                category, created = Categories.objects.get_or_create(
                    name=category_data['name'],
                    defaults={
                        'quotes_count': 0,
                        'created_at': category_data.get('created_at'),
                        'updated_at': category_data.get('updated_at')
                    }
                )
                if created:
                    print(f"  同步类别: {category.name}")
            
            print("✅ 数据结构同步完成")
            
        except Exception as e:
            print(f"❌ 同步失败: {e}")
    
    def compare_environments(self):
        """比较不同环境的差异"""
        print("🔍 比较环境差异...")
        
        try:
            # 获取本地和生产环境数据
            local_authors = requests.get(f"{self.local_api}authors/").json()
            prod_authors = requests.get(f"{self.prod_api}authors/").json()
            
            print(f"📊 环境对比:")
            print(f"  本地作者数量: {len(local_authors)}")
            print(f"  生产作者数量: {len(prod_authors)}")
            
            # 找出差异
            local_names = {author['name'] for author in local_authors}
            prod_names = {author['name'] for author in prod_authors}
            
            only_in_prod = prod_names - local_names
            only_in_local = local_names - prod_names
            
            if only_in_prod:
                print(f"  仅在生产环境: {len(only_in_prod)} 个作者")
            if only_in_local:
                print(f"  仅在本地环境: {len(only_in_local)} 个作者")
            
        except Exception as e:
            print(f"❌ 比较失败: {e}")

if __name__ == "__main__":
    tool = EnvironmentSyncTool()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "sync":
            tool.sync_schema_from_production()
        elif sys.argv[1] == "compare":
            tool.compare_environments()
        else:
            print("用法: python sync_environments.py [sync|compare]")
    else:
        tool.compare_environments()
