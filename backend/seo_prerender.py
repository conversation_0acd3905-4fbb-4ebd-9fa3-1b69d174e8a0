#!/usr/bin/env python
"""
SEO预渲染服务 v1.0
为搜索引擎爬虫提供预渲染的HTML内容
支持Authors、Categories、Sources三种页面类型的动态渲染

版本：v1.0 - SEO收录优化
更新日期：2025年6月27日
"""

import os
import sys
import django
import json
import re
from typing import Dict, List, Optional
from urllib.parse import urlparse, parse_qs

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quotes_admin.settings')
django.setup()

# 导入模型
from quotesapp.models import Quote, Author, Category, Source

class SEOPrerenderer:
    """SEO预渲染器类"""
    
    def __init__(self):
        self.base_url = "https://quotese.com"
        
        # 搜索引擎爬虫User-Agent模式
        self.crawler_patterns = [
            r'googlebot',
            r'bingbot',
            r'slurp',
            r'duckduckbot',
            r'baiduspider',
            r'yandexbot',
            r'facebookexternalhit',
            r'twitterbot',
            r'linkedinbot',
            r'whatsapp',
            r'telegrambot'
        ]
    
    def is_crawler(self, user_agent: str) -> bool:
        """
        检测是否为搜索引擎爬虫
        
        Args:
            user_agent (str): 用户代理字符串
            
        Returns:
            bool: 是否为爬虫
        """
        if not user_agent:
            return False
            
        user_agent_lower = user_agent.lower()
        return any(re.search(pattern, user_agent_lower) for pattern in self.crawler_patterns)
    
    def generate_authors_list_html(self) -> str:
        """生成Authors列表页面的预渲染HTML"""
        try:
            authors = Author.objects.all()[:50]  # 获取前50个作者
            
            html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Famous Authors | Quote Authors Collection - Quotese.com</title>
    <meta name="description" content="Browse quotes by famous authors. Discover wisdom from great minds throughout history.">
    <meta name="keywords" content="famous authors, quote authors, writers, philosophers, inspirational figures">
    <link rel="canonical" href="{self.base_url}/authors/">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Famous Authors | Quote Authors Collection - Quotese.com">
    <meta property="og:description" content="Browse quotes by famous authors. Discover wisdom from great minds throughout history.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{self.base_url}/authors/">
    <meta property="og:site_name" content="Quotese.com">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {{
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "Famous Authors Collection",
        "description": "Browse quotes by famous authors and discover wisdom from great minds throughout history.",
        "url": "{self.base_url}/authors/",
        "mainEntity": {{
            "@type": "ItemList",
            "numberOfItems": {len(authors)},
            "itemListElement": [
"""
            
            # 添加作者列表的结构化数据
            author_items = []
            for i, author in enumerate(authors):
                author_slug = self.slugify(author.name)
                author_items.append(f"""                {{
                    "@type": "ListItem",
                    "position": {i + 1},
                    "item": {{
                        "@type": "Person",
                        "name": "{author.name}",
                        "url": "{self.base_url}/authors/{author_slug}/"
                    }}
                }}""")
            
            html_content += ",\n".join(author_items)
            html_content += """
            ]
        }
    }
    </script>
</head>
<body>
    <h1>Famous Authors</h1>
    <p>Discover inspiring quotes from renowned authors, writers, and thinkers throughout history.</p>
    
    <div class="authors-list">
"""
            
            # 添加作者列表内容
            for author in authors:
                author_slug = self.slugify(author.name)
                quote_count = Quote.objects.filter(author=author).count()
                html_content += f"""        <div class="author-item">
            <h2><a href="/authors/{author_slug}/">{author.name}</a></h2>
            <p>{quote_count} quotes available</p>
        </div>
"""
            
            html_content += """    </div>
</body>
</html>"""
            
            return html_content
            
        except Exception as e:
            print(f"Error generating authors list HTML: {e}")
            return self.generate_error_html("Authors List", str(e))
    
    def generate_categories_list_html(self) -> str:
        """生成Categories列表页面的预渲染HTML"""
        try:
            categories = Category.objects.all()[:50]  # 获取前50个类别
            
            html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browse All Categories - Quotese.com</title>
    <meta name="description" content="Explore our complete collection of quote categories. Find inspirational quotes by topic including life, love, success, wisdom and more.">
    <meta name="keywords" content="quote categories, inspirational topics, quote themes, motivational categories">
    <link rel="canonical" href="{self.base_url}/categories/">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Browse All Quote Categories - Quotese.com">
    <meta property="og:description" content="Explore our complete collection of quote categories. Find inspirational quotes by topic.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{self.base_url}/categories/">
    <meta property="og:site_name" content="Quotese.com">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {{
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "Quote Categories Collection",
        "description": "Explore our complete collection of quote categories and find inspirational quotes by topic.",
        "url": "{self.base_url}/categories/",
        "mainEntity": {{
            "@type": "ItemList",
            "numberOfItems": {len(categories)},
            "itemListElement": [
"""
            
            # 添加类别列表的结构化数据
            category_items = []
            for i, category in enumerate(categories):
                category_slug = self.slugify(category.name)
                category_items.append(f"""                {{
                    "@type": "ListItem",
                    "position": {i + 1},
                    "item": {{
                        "@type": "Thing",
                        "name": "{category.name}",
                        "url": "{self.base_url}/categories/{category_slug}/"
                    }}
                }}""")
            
            html_content += ",\n".join(category_items)
            html_content += """
            ]
        }
    }
    </script>
</head>
<body>
    <h1>Browse All Categories</h1>
    <p>Explore our complete collection of quote categories. Find inspiration by topic and discover new themes that resonate with your interests.</p>
    
    <div class="categories-list">
"""
            
            # 添加类别列表内容
            for category in categories:
                category_slug = self.slugify(category.name)
                quote_count = Quote.objects.filter(category=category).count()
                html_content += f"""        <div class="category-item">
            <h2><a href="/categories/{category_slug}/">{category.name}</a></h2>
            <p>{quote_count} quotes in this category</p>
        </div>
"""
            
            html_content += """    </div>
</body>
</html>"""
            
            return html_content
            
        except Exception as e:
            print(f"Error generating categories list HTML: {e}")
            return self.generate_error_html("Categories List", str(e))
    
    def generate_sources_list_html(self) -> str:
        """生成Sources列表页面的预渲染HTML"""
        try:
            sources = Source.objects.all()[:50]  # 获取前50个来源
            
            html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Sources | Books and Speeches - Quotese.com</title>
    <meta name="description" content="Browse quotes by source. Discover wisdom from famous books, speeches, and works.">
    <meta name="keywords" content="quote sources, book quotes, speech quotes, literature quotes, famous works">
    <link rel="canonical" href="{self.base_url}/sources/">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Quote Sources | Books and Speeches - Quotese.com">
    <meta property="og:description" content="Browse quotes by source. Discover wisdom from famous books, speeches, and works.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{self.base_url}/sources/">
    <meta property="og:site_name" content="Quotese.com">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {{
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "Quote Sources Collection",
        "description": "Browse quotes by source and discover wisdom from famous books, speeches, and works.",
        "url": "{self.base_url}/sources/",
        "mainEntity": {{
            "@type": "ItemList",
            "numberOfItems": {len(sources)},
            "itemListElement": [
"""
            
            # 添加来源列表的结构化数据
            source_items = []
            for i, source in enumerate(sources):
                source_slug = self.slugify(source.name)
                source_items.append(f"""                {{
                    "@type": "ListItem",
                    "position": {i + 1},
                    "item": {{
                        "@type": "CreativeWork",
                        "name": "{source.name}",
                        "url": "{self.base_url}/sources/{source_slug}/"
                    }}
                }}""")
            
            html_content += ",\n".join(source_items)
            html_content += """
            ]
        }
    }
    </script>
</head>
<body>
    <h1>Quote Sources</h1>
    <p>Discover quotes from famous books, speeches, and literary works that have shaped our world.</p>
    
    <div class="sources-list">
"""
            
            # 添加来源列表内容
            for source in sources:
                source_slug = self.slugify(source.name)
                quote_count = Quote.objects.filter(source=source).count()
                html_content += f"""        <div class="source-item">
            <h2><a href="/sources/{source_slug}/">{source.name}</a></h2>
            <p>{quote_count} quotes from this source</p>
        </div>
"""
            
            html_content += """    </div>
</body>
</html>"""
            
            return html_content
            
        except Exception as e:
            print(f"Error generating sources list HTML: {e}")
            return self.generate_error_html("Sources List", str(e))
    
    def slugify(self, text: str) -> str:
        """
        将文本转换为URL友好的slug
        与前端JavaScript版本保持一致
        """
        if not text:
            return ''
        
        text = str(text).lower().strip()
        text = re.sub(r'\s+', '-', text)
        text = re.sub(r'[^\w\-]+', '', text)
        text = re.sub(r'\-\-+', '-', text)
        text = re.sub(r'^-+', '', text)
        text = re.sub(r'-+$', '', text)
        
        return text
    
    def generate_error_html(self, page_name: str, error_message: str) -> str:
        """生成错误页面HTML"""
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error - {page_name} | Quotese.com</title>
    <meta name="robots" content="noindex, nofollow">
</head>
<body>
    <h1>Error Loading {page_name}</h1>
    <p>Sorry, there was an error loading this page: {error_message}</p>
    <p><a href="/">Return to Home</a></p>
</body>
</html>"""
    
    def prerender_page(self, url_path: str, user_agent: str = "") -> Optional[str]:
        """
        根据URL路径预渲染页面
        
        Args:
            url_path (str): URL路径
            user_agent (str): 用户代理字符串
            
        Returns:
            Optional[str]: 预渲染的HTML内容，如果不需要预渲染则返回None
        """
        # 检查是否为爬虫
        if not self.is_crawler(user_agent):
            return None
        
        # 根据URL路径确定页面类型
        if url_path == '/authors/' or url_path == '/authors':
            return self.generate_authors_list_html()
        elif url_path == '/categories/' or url_path == '/categories':
            return self.generate_categories_list_html()
        elif url_path == '/sources/' or url_path == '/sources':
            return self.generate_sources_list_html()
        
        # 其他页面不需要预渲染
        return None


def main():
    """测试函数"""
    prerenderer = SEOPrerenderer()
    
    # 测试爬虫检测
    test_user_agents = [
        "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",
        "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    ]
    
    for ua in test_user_agents:
        is_crawler = prerenderer.is_crawler(ua)
        print(f"User-Agent: {ua[:50]}... -> Crawler: {is_crawler}")
    
    # 测试页面预渲染
    test_paths = ['/authors/', '/categories/', '/sources/']
    crawler_ua = "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)"
    
    for path in test_paths:
        html = prerenderer.prerender_page(path, crawler_ua)
        if html:
            print(f"\n预渲染 {path} 成功，HTML长度: {len(html)} 字符")
        else:
            print(f"\n{path} 不需要预渲染")


if __name__ == "__main__":
    main()
