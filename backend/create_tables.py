#!/usr/bin/env python3
"""
创建数据库表脚本
为本地开发环境创建必要的数据库表
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quotes_admin.settings_local')
django.setup()

from django.db import connection

def create_tables():
    print("开始创建数据库表...")
    
    cursor = connection.cursor()
    
    # 创建authors表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS authors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        quotes_count INTEGER DEFAULT 0
    )
    ''')
    print("创建authors表")
    
    # 创建categories表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        quotes_count INTEGER DEFAULT 0
    )
    ''')
    print("创建categories表")
    
    # 创建sources表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS sources (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        quotes_count INTEGER DEFAULT 0
    )
    ''')
    print("创建sources表")
    
    # 创建quotes表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS quotes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL,
        author_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (author_id) REFERENCES authors (id)
    )
    ''')
    print("创建quotes表")
    
    # 创建quote_categories表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS quote_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quote_id INTEGER NOT NULL,
        category_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (quote_id) REFERENCES quotes (id),
        FOREIGN KEY (category_id) REFERENCES categories (id),
        UNIQUE(quote_id, category_id)
    )
    ''')
    print("创建quote_categories表")
    
    # 创建quote_sources表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS quote_sources (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quote_id INTEGER NOT NULL,
        source_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (quote_id) REFERENCES quotes (id),
        FOREIGN KEY (source_id) REFERENCES sources (id),
        UNIQUE(quote_id, source_id)
    )
    ''')
    print("创建quote_sources表")
    
    connection.commit()
    print("数据库表创建完成!")

if __name__ == "__main__":
    create_tables()
