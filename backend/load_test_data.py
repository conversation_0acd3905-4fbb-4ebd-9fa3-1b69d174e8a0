#!/usr/bin/env python3
"""
加载测试数据脚本
为Quotese项目创建基础的测试数据，包括作者、类别、来源和名言
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quotes_admin.settings_local')
django.setup()

from quotesapp.models import Authors, Categories, Sources, Quotes, QuoteCategories, QuoteSources

def create_test_data():
    print("开始创建测试数据...")
    
    # 创建作者
    authors_data = [
        "<PERSON>",
        "<PERSON>", 
        "<PERSON> Angelo<PERSON>",
        "<PERSON>s",
        "<PERSON>",
        "Mahat<PERSON> Gandhi",
        "<PERSON>",
        "<PERSON> Wilde",
        "<PERSON>",
        "<PERSON>"
    ]
    
    authors = {}
    for author_name in authors_data:
        author, created = Authors.objects.get_or_create(name=author_name)
        authors[author_name] = author
        if created:
            print(f"创建作者: {author_name}")
    
    # 创建类别
    categories_data = [
        "Inspirational",
        "Motivational", 
        "Life",
        "Success",
        "Wisdom",
        "Leadership",
        "Science",
        "Philosophy",
        "Humor",
        "Education"
    ]
    
    categories = {}
    for category_name in categories_data:
        category, created = Categories.objects.get_or_create(name=category_name)
        categories[category_name] = category
        if created:
            print(f"创建类别: {category_name}")
    
    # 创建来源
    sources_data = [
        "The Art of War",
        "Think and Grow Rich",
        "The 7 Habits of Highly Effective People",
        "How to Win Friends and Influence People",
        "The Autobiography of Benjamin Franklin",
        "Long Walk to Freedom",
        "The Wit and Wisdom of Mark Twain",
        "The Picture of Dorian Gray",
        "Stanford Commencement Address",
        "Nobel Prize Speech"
    ]
    
    sources = {}
    for source_name in sources_data:
        source, created = Sources.objects.get_or_create(name=source_name)
        sources[source_name] = source
        if created:
            print(f"创建来源: {source_name}")
    
    # 创建名言
    quotes_data = [
        {
            "content": "Imagination is more important than knowledge.",
            "author": "Albert Einstein",
            "categories": ["Wisdom", "Science"],
            "sources": ["Nobel Prize Speech"]
        },
        {
            "content": "The only way to do great work is to love what you do.",
            "author": "Steve Jobs",
            "categories": ["Inspirational", "Success"],
            "sources": ["Stanford Commencement Address"]
        },
        {
            "content": "Success is not final, failure is not fatal: it is the courage to continue that counts.",
            "author": "Winston Churchill",
            "categories": ["Motivational", "Leadership"],
            "sources": []
        },
        {
            "content": "There is no greater agony than bearing an untold story inside you.",
            "author": "Maya Angelou",
            "categories": ["Life", "Wisdom"],
            "sources": []
        },
        {
            "content": "Education is the most powerful weapon which you can use to change the world.",
            "author": "Nelson Mandela",
            "categories": ["Education", "Inspirational"],
            "sources": ["Long Walk to Freedom"]
        },
        {
            "content": "Be yourself; everyone else is already taken.",
            "author": "Oscar Wilde",
            "categories": ["Life", "Philosophy"],
            "sources": ["The Picture of Dorian Gray"]
        },
        {
            "content": "The way to get started is to quit talking and begin doing.",
            "author": "Walt Disney",
            "categories": ["Motivational", "Success"],
            "sources": []
        },
        {
            "content": "Be the change that you wish to see in the world.",
            "author": "Mahatma Gandhi",
            "categories": ["Inspirational", "Philosophy"],
            "sources": []
        },
        {
            "content": "Tell me and I forget. Teach me and I remember. Involve me and I learn.",
            "author": "Benjamin Franklin",
            "categories": ["Education", "Wisdom"],
            "sources": ["The Autobiography of Benjamin Franklin"]
        },
        {
            "content": "Believe you can and you're halfway there.",
            "author": "Theodore Roosevelt",
            "categories": ["Motivational", "Success"],
            "sources": []
        },
        {
            "content": "The secret of getting ahead is getting started.",
            "author": "Mark Twain",
            "categories": ["Motivational", "Success"],
            "sources": ["The Wit and Wisdom of Mark Twain"]
        },
        {
            "content": "Innovation distinguishes between a leader and a follower.",
            "author": "Steve Jobs",
            "categories": ["Leadership", "Success"],
            "sources": ["Stanford Commencement Address"]
        }
    ]
    
    for quote_data in quotes_data:
        # 创建作者（如果不存在）
        author_name = quote_data["author"]
        if author_name not in authors:
            author, created = Authors.objects.get_or_create(name=author_name)
            authors[author_name] = author
            if created:
                print(f"创建新作者: {author_name}")
        
        # 创建名言
        quote, created = Quotes.objects.get_or_create(
            content=quote_data["content"],
            defaults={'author': authors[author_name]}
        )
        
        if created:
            print(f"创建名言: {quote_data['content'][:50]}...")
            
            # 添加类别关联
            for category_name in quote_data["categories"]:
                if category_name in categories:
                    QuoteCategories.objects.get_or_create(
                        quote=quote,
                        category=categories[category_name]
                    )
            
            # 添加来源关联
            for source_name in quote_data["sources"]:
                if source_name in sources:
                    QuoteSources.objects.get_or_create(
                        quote=quote,
                        source=sources[source_name]
                    )
    
    print("\n测试数据创建完成!")
    print(f"作者数量: {Authors.objects.count()}")
    print(f"类别数量: {Categories.objects.count()}")
    print(f"来源数量: {Sources.objects.count()}")
    print(f"名言数量: {Quotes.objects.count()}")

if __name__ == "__main__":
    create_test_data()
