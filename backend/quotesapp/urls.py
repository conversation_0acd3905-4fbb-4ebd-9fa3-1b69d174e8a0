"""
quotesapp URL配置
定义REST API的URL路由
"""

from django.urls import path
from . import views

# API URL模式
urlpatterns = [
    # API根路径
    path('', views.api_root, name='api_root'),

    # REST API端点
    path('authors/', views.authors_api, name='authors_api'),
    path('categories/', views.categories_api, name='categories_api'),
    path('sources/', views.sources_api, name='sources_api'),
    path('quotes/', views.quotes_api, name='quotes_api'),
    
    # 图表数据API（保留现有功能）
    path('chart/authors/', views.author_quotes_chart_data, name='author_quotes_chart_data'),
    path('chart/categories/', views.category_quotes_chart_data, name='category_quotes_chart_data'),
    path('chart/sources/', views.source_quotes_chart_data, name='source_quotes_chart_data'),
]
