# Generated by Django 5.2 on 2025-04-15 11:39

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Authors',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.TextField(verbose_name='作者名称')),
                ('created_at', models.DateTimeField(blank=True, null=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(blank=True, null=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '作者',
                'verbose_name_plural': '作者',
                'db_table': 'authors',
                'ordering': ['name'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Categories',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.TextField(verbose_name='类别名称')),
                ('created_at', models.DateTimeField(blank=True, null=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(blank=True, null=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '类别',
                'verbose_name_plural': '类别',
                'db_table': 'categories',
                'ordering': ['name'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='QuoteCategories',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(blank=True, null=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '名言-类别关联',
                'verbose_name_plural': '名言-类别关联',
                'db_table': 'quote_categories',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Quotes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('content', models.TextField(verbose_name='名言内容')),
                ('created_at', models.DateTimeField(blank=True, null=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(blank=True, null=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '名言',
                'verbose_name_plural': '名言',
                'db_table': 'quotes',
                'ordering': ['-created_at'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='QuoteSources',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(blank=True, null=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '名言-来源关联',
                'verbose_name_plural': '名言-来源关联',
                'db_table': 'quote_sources',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='QuotesWithAuthorId',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('quote', models.TextField(verbose_name='名言内容')),
                ('author_id', models.IntegerField(blank=True, null=True, verbose_name='作者ID')),
                ('created_at', models.DateTimeField(blank=True, null=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(blank=True, null=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '带作者ID的名言',
                'verbose_name_plural': '带作者ID的名言',
                'db_table': 'quotes_with_author_id',
                'ordering': ['-created_at'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Sources',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.TextField(verbose_name='来源名称')),
                ('created_at', models.DateTimeField(blank=True, null=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(blank=True, null=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '来源',
                'verbose_name_plural': '来源',
                'db_table': 'sources',
                'ordering': ['name'],
                'managed': False,
            },
        ),
    ]
