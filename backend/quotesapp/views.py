from django.shortcuts import render
from django.http import JsonResponse
from django.db.models import Count
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
import json
from .models import Authors, Categories, Sources, Quotes, QuoteCategories, QuoteSources


def author_quotes_chart_data(request):
    """获取作者名言数量分布数据"""
    authors_with_count = Authors.objects.annotate(quotes_count=Count('quotes'))

    # 统计不同名言数量范围的作者数量
    no_quotes = authors_with_count.filter(quotes_count=0).count()
    quotes_1_10 = authors_with_count.filter(quotes_count__gte=1, quotes_count__lte=10).count()
    quotes_11_50 = authors_with_count.filter(quotes_count__gte=11, quotes_count__lte=50).count()
    quotes_51_100 = authors_with_count.filter(quotes_count__gte=51, quotes_count__lte=100).count()
    quotes_100_plus = authors_with_count.filter(quotes_count__gt=100).count()

    data = {
        'labels': ['无名言', '1-10', '11-50', '51-100', '100+'],
        'data': [no_quotes, quotes_1_10, quotes_11_50, quotes_51_100, quotes_100_plus]
    }

    return JsonResponse(data)


def category_quotes_chart_data(request):
    """获取类别名言数量分布数据"""
    categories_with_count = Categories.objects.annotate(quotes_count=Count('quotecategories'))

    # 统计不同名言数量范围的类别数量
    no_quotes = categories_with_count.filter(quotes_count=0).count()
    quotes_1_10 = categories_with_count.filter(quotes_count__gte=1, quotes_count__lte=10).count()
    quotes_11_50 = categories_with_count.filter(quotes_count__gte=11, quotes_count__lte=50).count()
    quotes_51_100 = categories_with_count.filter(quotes_count__gte=51, quotes_count__lte=100).count()
    quotes_100_plus = categories_with_count.filter(quotes_count__gt=100).count()

    data = {
        'labels': ['无名言', '1-10', '11-50', '51-100', '100+'],
        'data': [no_quotes, quotes_1_10, quotes_11_50, quotes_51_100, quotes_100_plus]
    }

    return JsonResponse(data)


def source_quotes_chart_data(request):
    """获取来源名言数量分布数据"""
    sources_with_count = Sources.objects.annotate(quotes_count=Count('quotesources'))

    # 统计不同名言数量范围的来源数量
    no_quotes = sources_with_count.filter(quotes_count=0).count()
    quotes_1_10 = sources_with_count.filter(quotes_count__gte=1, quotes_count__lte=10).count()
    quotes_11_50 = sources_with_count.filter(quotes_count__gte=11, quotes_count__lte=50).count()
    quotes_51_100 = sources_with_count.filter(quotes_count__gte=51, quotes_count__lte=100).count()
    quotes_100_plus = sources_with_count.filter(quotes_count__gt=100).count()

    data = {
        'labels': ['无名言', '1-10', '11-50', '51-100', '100+'],
        'data': [no_quotes, quotes_1_10, quotes_11_50, quotes_51_100, quotes_100_plus]
    }

    return JsonResponse(data)


# ==================== REST API Views ====================

@csrf_exempt
@require_http_methods(["GET"])
def api_root(request):
    """API根路径，提供可用端点列表"""
    api_endpoints = {
        "message": "Welcome to Quotese.com API",
        "version": "1.0",
        "endpoints": {
            "authors": "/api/authors/",
            "categories": "/api/categories/",
            "sources": "/api/sources/",
            "quotes": "/api/quotes/"
        },
        "documentation": {
            "authors": "获取作者列表，支持分页和搜索",
            "categories": "获取分类列表，支持分页和搜索",
            "sources": "获取来源列表，支持分页和搜索",
            "quotes": "获取名言列表，支持分页和搜索"
        },
        "parameters": {
            "page": "页码，默认为1",
            "page_size": "每页数量，默认为20",
            "search": "搜索关键词"
        }
    }
    return JsonResponse(api_endpoints, json_dumps_params={'ensure_ascii': False, 'indent': 2})

@csrf_exempt
@require_http_methods(["GET"])
def authors_api(request):
    """获取作者列表API"""
    try:
        # 获取查询参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        search = request.GET.get('search', '')

        # 查询作者
        authors = Authors.objects.all()
        if search:
            authors = authors.filter(name__icontains=search)

        # 分页
        paginator = Paginator(authors, page_size)
        page_obj = paginator.get_page(page)

        # 序列化数据
        authors_data = []
        for author in page_obj:
            authors_data.append({
                'id': author.id,
                'name': author.name,
                'quotes_count': getattr(author, 'quotes_count', 0),
                'created_at': author.created_at.isoformat() if hasattr(author, 'created_at') else None,
                'updated_at': author.updated_at.isoformat() if hasattr(author, 'updated_at') else None,
            })

        return JsonResponse(authors_data, safe=False)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def categories_api(request):
    """获取类别列表API"""
    try:
        # 获取查询参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        search = request.GET.get('search', '')

        # 查询类别
        categories = Categories.objects.all()
        if search:
            categories = categories.filter(name__icontains=search)

        # 分页
        paginator = Paginator(categories, page_size)
        page_obj = paginator.get_page(page)

        # 序列化数据
        categories_data = []
        for category in page_obj:
            categories_data.append({
                'id': category.id,
                'name': category.name,
                'quotes_count': getattr(category, 'quotes_count', 0),
                'created_at': category.created_at.isoformat() if hasattr(category, 'created_at') else None,
                'updated_at': category.updated_at.isoformat() if hasattr(category, 'updated_at') else None,
            })

        return JsonResponse(categories_data, safe=False)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def sources_api(request):
    """获取来源列表API"""
    try:
        # 获取查询参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        search = request.GET.get('search', '')

        # 查询来源
        sources = Sources.objects.all()
        if search:
            sources = sources.filter(name__icontains=search)

        # 分页
        paginator = Paginator(sources, page_size)
        page_obj = paginator.get_page(page)

        # 序列化数据
        sources_data = []
        for source in page_obj:
            sources_data.append({
                'id': source.id,
                'name': source.name,
                'quotes_count': getattr(source, 'quotes_count', 0),
                'created_at': source.created_at.isoformat() if hasattr(source, 'created_at') else None,
                'updated_at': source.updated_at.isoformat() if hasattr(source, 'updated_at') else None,
            })

        return JsonResponse(sources_data, safe=False)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def quotes_api(request):
    """获取名言列表API"""
    try:
        # 获取查询参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        search = request.GET.get('search', '')
        author_id = request.GET.get('author_id')

        # 查询名言
        quotes = Quotes.objects.all()
        if search:
            quotes = quotes.filter(content__icontains=search)
        if author_id:
            quotes = quotes.filter(author_id=author_id)

        # 分页
        paginator = Paginator(quotes, page_size)
        page_obj = paginator.get_page(page)

        # 序列化数据
        quotes_data = []
        for quote in page_obj:
            # 获取作者信息
            author_name = None
            if quote.author_id:
                try:
                    author = Authors.objects.get(id=quote.author_id)
                    author_name = author.name
                except Authors.DoesNotExist:
                    pass

            quotes_data.append({
                'id': quote.id,
                'content': quote.content,
                'author_id': quote.author_id,
                'author_name': author_name,
                'created_at': quote.created_at.isoformat() if hasattr(quote, 'created_at') else None,
                'updated_at': quote.updated_at.isoformat() if hasattr(quote, 'updated_at') else None,
            })

        return JsonResponse(quotes_data, safe=False)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)