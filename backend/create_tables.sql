-- 创建数据库表的SQL脚本
-- 用于本地开发环境

-- 创建authors表
CREATE TABLE IF NOT EXISTS authors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    quotes_count INTEGER DEFAULT 0
);

-- 创建categories表
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    quotes_count INTEGER DEFAULT 0
);

-- 创建sources表
CREATE TABLE IF NOT EXISTS sources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    quotes_count INTEGER DEFAULT 0
);

-- 创建quotes表
CREATE TABLE IF NOT EXISTS quotes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content TEXT NOT NULL,
    author_id INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES authors (id)
);

-- 创建quote_categories表
CREATE TABLE IF NOT EXISTS quote_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    quote_id INTEGER NOT NULL,
    category_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quote_id) REFERENCES quotes (id),
    FOREIGN KEY (category_id) REFERENCES categories (id),
    UNIQUE(quote_id, category_id)
);

-- 创建quote_sources表
CREATE TABLE IF NOT EXISTS quote_sources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    quote_id INTEGER NOT NULL,
    source_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quote_id) REFERENCES quotes (id),
    FOREIGN KEY (source_id) REFERENCES sources (id),
    UNIQUE(quote_id, source_id)
);

-- 插入测试数据

-- 插入作者
INSERT OR IGNORE INTO authors (name) VALUES 
('Albert Einstein'),
('Winston Churchill'),
('Maya Angelou'),
('Steve Jobs'),
('Nelson Mandela'),
('Mahatma Gandhi'),
('Mark Twain'),
('Oscar Wilde'),
('Benjamin Franklin'),
('Theodore Roosevelt'),
('Walt Disney');

-- 插入类别
INSERT OR IGNORE INTO categories (name) VALUES 
('Inspirational'),
('Motivational'),
('Life'),
('Success'),
('Wisdom'),
('Leadership'),
('Science'),
('Philosophy'),
('Humor'),
('Education');

-- 插入来源
INSERT OR IGNORE INTO sources (name) VALUES 
('The Art of War'),
('Think and Grow Rich'),
('The 7 Habits of Highly Effective People'),
('How to Win Friends and Influence People'),
('The Autobiography of Benjamin Franklin'),
('Long Walk to Freedom'),
('The Wit and Wisdom of Mark Twain'),
('The Picture of Dorian Gray'),
('Stanford Commencement Address'),
('Nobel Prize Speech');

-- 插入名言
INSERT OR IGNORE INTO quotes (content, author_id) VALUES 
('Imagination is more important than knowledge.', 1),
('The only way to do great work is to love what you do.', 4),
('Success is not final, failure is not fatal: it is the courage to continue that counts.', 2),
('There is no greater agony than bearing an untold story inside you.', 3),
('Education is the most powerful weapon which you can use to change the world.', 5),
('Be yourself; everyone else is already taken.', 8),
('The way to get started is to quit talking and begin doing.', 11),
('Be the change that you wish to see in the world.', 6),
('Tell me and I forget. Teach me and I remember. Involve me and I learn.', 9),
('Believe you can and you''re halfway there.', 10),
('The secret of getting ahead is getting started.', 7),
('Innovation distinguishes between a leader and a follower.', 4);

-- 插入名言-类别关联
INSERT OR IGNORE INTO quote_categories (quote_id, category_id) VALUES 
(1, 5), (1, 7),  -- Einstein: Wisdom, Science
(2, 1), (2, 4),  -- Jobs: Inspirational, Success
(3, 2), (3, 6),  -- Churchill: Motivational, Leadership
(4, 3), (4, 5),  -- Angelou: Life, Wisdom
(5, 10), (5, 1), -- Mandela: Education, Inspirational
(6, 3), (6, 8),  -- Wilde: Life, Philosophy
(7, 2), (7, 4),  -- Disney: Motivational, Success
(8, 1), (8, 8),  -- Gandhi: Inspirational, Philosophy
(9, 10), (9, 5), -- Franklin: Education, Wisdom
(10, 2), (10, 4), -- Roosevelt: Motivational, Success
(11, 2), (11, 4), -- Twain: Motivational, Success
(12, 6), (12, 4); -- Jobs: Leadership, Success

-- 插入名言-来源关联
INSERT OR IGNORE INTO quote_sources (quote_id, source_id) VALUES 
(1, 10),  -- Einstein: Nobel Prize Speech
(2, 9),   -- Jobs: Stanford Commencement Address
(5, 6),   -- Mandela: Long Walk to Freedom
(6, 8),   -- Wilde: The Picture of Dorian Gray
(9, 5),   -- Franklin: The Autobiography of Benjamin Franklin
(11, 7),  -- Twain: The Wit and Wisdom of Mark Twain
(12, 9);  -- Jobs: Stanford Commencement Address
