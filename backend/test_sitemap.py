#!/usr/bin/env python
"""
Quotese Sitemap生成测试脚本
用于测试sitemap生成功能的正确性
版本：v2.0 - SEO重启实施
更新日期：2025年6月16日
"""

import os
import sys
import unittest
import tempfile
import xml.etree.ElementTree as ET
from unittest.mock import Mock, patch

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quotes_admin.settings')

try:
    import django
    django.setup()
    from quotesapp.models import Quote, Author, Category, Source
    DJANGO_AVAILABLE = True
except ImportError:
    DJANGO_AVAILABLE = False
    print("警告: Django环境不可用，将使用模拟数据进行测试")

# 导入sitemap生成器
from generate_sitemap import SitemapGenerator, SitemapConfig, slugify_custom, is_valid_slug


class TestSitemapGeneration(unittest.TestCase):
    """Sitemap生成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = SitemapConfig()
        self.generator = SitemapGenerator(self.config)
        self.temp_dir = tempfile.mkdtemp()
        self.test_output_path = os.path.join(self.temp_dir, 'test_sitemap.xml')
        
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        if os.path.exists(self.test_output_path):
            os.remove(self.test_output_path)
        os.rmdir(self.temp_dir)
    
    def test_slugify_function(self):
        """测试slugify函数"""
        test_cases = [
            ("Albert Einstein", "albert-einstein"),
            ("Self-Improvement", "self-improvement"),
            ("Life & Success", "life-success"),
            ("100 Best Quotes", "100-best-quotes"),
            ("  Spaced  Text  ", "spaced-text"),
            ("Special@#$%Characters", "specialcharacters"),
            ("", ""),
            ("Multiple---Dashes", "multiple-dashes"),
            ("CamelCaseText", "camelcasetext")
        ]
        
        for input_text, expected in test_cases:
            with self.subTest(input=input_text):
                result = slugify_custom(input_text)
                self.assertEqual(result, expected, f"slugify('{input_text}') 应该返回 '{expected}'，但返回了 '{result}'")
    
    def test_slug_validation(self):
        """测试slug验证函数"""
        valid_slugs = [
            "albert-einstein",
            "self-improvement", 
            "100-best-quotes",
            "simple",
            "a-b-c-d-e"
        ]
        
        invalid_slugs = [
            "",
            "-invalid-start",
            "invalid-end-",
            "invalid--double",
            "Invalid-Capital",
            "invalid@symbol",
            "invalid space",
            None,
            123
        ]
        
        for slug in valid_slugs:
            with self.subTest(slug=slug):
                self.assertTrue(is_valid_slug(slug), f"'{slug}' 应该是有效的slug")
        
        for slug in invalid_slugs:
            with self.subTest(slug=slug):
                self.assertFalse(is_valid_slug(slug), f"'{slug}' 应该是无效的slug")
    
    def test_config_validation(self):
        """测试配置验证"""
        self.assertTrue(self.config.validate_config(), "默认配置应该是有效的")
        
        # 测试无效配置
        invalid_config = SitemapConfig()
        invalid_config.BASE_URL = "invalid-url"
        self.assertFalse(invalid_config.validate_config(), "无效的BASE_URL应该导致配置验证失败")
    
    def test_url_generation(self):
        """测试URL生成"""
        test_cases = [
            ("authors", "albert-einstein", None, "https://quotese.com/authors/albert-einstein/"),
            ("categories", "inspirational", None, "https://quotese.com/categories/inspirational/"),
            ("sources", "book-title", None, "https://quotese.com/sources/book-title/"),
            ("quotes", None, 123, "https://quotese.com/quotes/123/"),
            ("authors", None, None, "https://quotese.com/authors/")
        ]
        
        for url_type, slug, id_val, expected in test_cases:
            with self.subTest(type=url_type, slug=slug, id=id_val):
                result = self.config.get_url_for_type(url_type, slug, id_val)
                self.assertEqual(result, expected)
    
    def test_seo_config_retrieval(self):
        """测试SEO配置获取"""
        home_config = self.config.get_seo_config('home')
        self.assertEqual(home_config['priority'], '1.0')
        self.assertEqual(home_config['changefreq'], 'daily')
        
        # 测试不存在的页面类型
        unknown_config = self.config.get_seo_config('unknown_type')
        self.assertEqual(unknown_config, self.config.SEO_CONFIG['quote_detail'])
    
    def test_add_url_functionality(self):
        """测试添加URL功能"""
        self.generator.add_url(
            loc="https://quotese.com/test/",
            changefreq="weekly",
            priority="0.8"
        )
        
        self.assertEqual(len(self.generator.urls), 1)
        url_data = self.generator.urls[0]
        self.assertEqual(url_data['loc'], "https://quotese.com/test/")
        self.assertEqual(url_data['changefreq'], "weekly")
        self.assertEqual(url_data['priority'], "0.8")
        self.assertIsNotNone(url_data['lastmod'])
    
    def test_home_page_generation(self):
        """测试首页生成"""
        self.generator.generate_home_page()
        
        self.assertEqual(len(self.generator.urls), 1)
        home_url = self.generator.urls[0]
        self.assertEqual(home_url['loc'], "https://quotese.com/")
        self.assertEqual(home_url['priority'], "1.0")
        self.assertEqual(home_url['changefreq'], "daily")
    
    def test_list_pages_generation(self):
        """测试列表页面生成"""
        self.generator.generate_list_pages()
        
        self.assertEqual(len(self.generator.urls), 4)  # authors, categories, sources, quotes
        
        expected_urls = [
            "https://quotese.com/authors/",
            "https://quotese.com/categories/",
            "https://quotese.com/sources/",
            "https://quotese.com/quotes/"
        ]
        
        generated_urls = [url['loc'] for url in self.generator.urls]
        for expected_url in expected_urls:
            self.assertIn(expected_url, generated_urls)
    
    @patch('generate_sitemap.Author')
    def test_author_pages_generation_with_mock(self, mock_author_model):
        """测试作者页面生成（使用模拟数据）"""
        # 创建模拟作者数据
        mock_author1 = Mock()
        mock_author1.name = "Albert Einstein"
        mock_author2 = Mock()
        mock_author2.name = "Maya Angelou"
        
        mock_author_model.objects.all.return_value = [mock_author1, mock_author2]
        
        self.generator.generate_author_pages()
        
        # 应该生成4个URL：2个作者详情页 + 2个作者名言列表页
        self.assertEqual(len(self.generator.urls), 4)
        
        expected_urls = [
            "https://quotese.com/authors/albert-einstein/",
            "https://quotese.com/authors/albert-einstein/quotes/",
            "https://quotese.com/authors/maya-angelou/",
            "https://quotese.com/authors/maya-angelou/quotes/"
        ]
        
        generated_urls = [url['loc'] for url in self.generator.urls]
        for expected_url in expected_urls:
            self.assertIn(expected_url, generated_urls)
    
    def test_xml_file_generation(self):
        """测试XML文件生成"""
        # 添加一些测试URL
        self.generator.add_url("https://quotese.com/", priority="1.0", changefreq="daily")
        self.generator.add_url("https://quotese.com/authors/", priority="0.9", changefreq="weekly")
        
        # 生成XML文件
        self.generator.write_xml_file(self.test_output_path)
        
        # 验证文件存在
        self.assertTrue(os.path.exists(self.test_output_path))
        
        # 验证XML格式
        try:
            tree = ET.parse(self.test_output_path)
            root = tree.getroot()
            
            # 验证根元素
            self.assertEqual(root.tag, 'urlset')
            
            # 验证URL数量
            urls = root.findall('url')
            self.assertEqual(len(urls), 2)
            
            # 验证第一个URL
            first_url = urls[0]
            loc = first_url.find('loc').text
            priority = first_url.find('priority').text
            changefreq = first_url.find('changefreq').text
            
            self.assertEqual(loc, "https://quotese.com/")
            self.assertEqual(priority, "1.0")
            self.assertEqual(changefreq, "daily")
            
        except ET.ParseError as e:
            self.fail(f"生成的XML文件格式无效: {e}")
    
    def test_complete_sitemap_generation(self):
        """测试完整的sitemap生成流程"""
        with patch('generate_sitemap.Author') as mock_author, \
             patch('generate_sitemap.Category') as mock_category, \
             patch('generate_sitemap.Source') as mock_source, \
             patch('generate_sitemap.Quote') as mock_quote:
            
            # 设置模拟数据
            mock_author.objects.all.return_value = [Mock(name="Test Author")]
            mock_category.objects.all.return_value = [Mock(name="Test Category")]
            mock_source.objects.all.return_value = [Mock(name="Test Source")]
            mock_quote.objects.all.return_value = [Mock(id=1)]
            
            # 生成sitemap
            url_count = self.generator.generate_sitemap(self.test_output_path)
            
            # 验证URL数量
            self.assertGreater(url_count, 0)
            
            # 验证文件存在
            self.assertTrue(os.path.exists(self.test_output_path))
            
            # 验证XML格式
            try:
                tree = ET.parse(self.test_output_path)
                root = tree.getroot()
                urls = root.findall('url')
                self.assertEqual(len(urls), url_count)
            except ET.ParseError as e:
                self.fail(f"生成的sitemap XML格式无效: {e}")


def run_tests():
    """运行所有测试"""
    print("开始运行Quotese Sitemap生成测试...")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestSitemapGeneration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print("=" * 60)
    if result.wasSuccessful():
        print("✅ 所有测试通过!")
        return True
    else:
        print("❌ 部分测试失败!")
        print(f"失败: {len(result.failures)}, 错误: {len(result.errors)}")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
