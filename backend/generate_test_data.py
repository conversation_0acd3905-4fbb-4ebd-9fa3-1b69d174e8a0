#!/usr/bin/env python3
"""
测试数据生成器
用于生成大量测试数据以支持性能测试和UI测试
"""

import os
import sys
import django
from django.conf import settings

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quotes_admin.settings_local')
django.setup()

from quotesapp.models import Authors, Categories, Sources, Quotes, QuoteCategories, QuoteSources
import random
from datetime import datetime, timedelta

class TestDataGenerator:
    def __init__(self):
        self.famous_quotes = [
            "The only way to do great work is to love what you do.",
            "Innovation distinguishes between a leader and a follower.",
            "Life is what happens to you while you're busy making other plans.",
            "The future belongs to those who believe in the beauty of their dreams.",
            "It is during our darkest moments that we must focus to see the light.",
            "Success is not final, failure is not fatal: it is the courage to continue that counts.",
            "The only impossible journey is the one you never begin.",
            "In the end, we will remember not the words of our enemies, but the silence of our friends.",
            "The way to get started is to quit talking and begin doing.",
            "<PERSON>'t be afraid to give up the good to go for the great."
        ]
        
        self.authors_pool = [
            "<PERSON> Jobs", "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON>",
            "<PERSON>", "<PERSON> <PERSON>u", "<PERSON> <PERSON>", "<PERSON> <PERSON>",
            "<PERSON> <PERSON>", "<PERSON> <PERSON> <PERSON>.", "<PERSON> <PERSON><PERSON>", "<PERSON>",
            "<PERSON> Twain", "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON>"
        ]
        
        self.categories_pool = [
            "Inspirational", "Motivational", "Success", "Leadership", "Wisdom",
            "Life", "Love", "Happiness", "Education", "Philosophy", "Science",
            "Art", "Business", "Technology", "Health", "Friendship"
        ]
        
        self.sources_pool = [
            "Stanford Commencement Address", "Nobel Prize Speech", "TED Talk",
            "Biography", "Interview", "Book", "Speech", "Letter", "Diary",
            "Conference", "Documentary", "Podcast", "Article", "Essay"
        ]
    
    def generate_authors(self, count=50):
        """生成作者数据"""
        print(f"生成 {count} 个作者...")
        
        for i in range(count):
            author_name = f"{random.choice(self.authors_pool)} {i+1}"
            author, created = Authors.objects.get_or_create(
                name=author_name,
                defaults={
                    'created_at': datetime.now() - timedelta(days=random.randint(1, 365)),
                    'updated_at': datetime.now(),
                    'quotes_count': 0
                }
            )
            if created:
                print(f"  创建作者: {author_name}")
    
    def generate_categories(self, count=20):
        """生成类别数据"""
        print(f"生成 {count} 个类别...")
        
        for category_name in self.categories_pool[:count]:
            category, created = Categories.objects.get_or_create(
                name=category_name,
                defaults={
                    'created_at': datetime.now() - timedelta(days=random.randint(1, 365)),
                    'updated_at': datetime.now(),
                    'quotes_count': 0
                }
            )
            if created:
                print(f"  创建类别: {category_name}")
    
    def generate_sources(self, count=15):
        """生成来源数据"""
        print(f"生成 {count} 个来源...")
        
        for source_name in self.sources_pool[:count]:
            source, created = Sources.objects.get_or_create(
                name=source_name,
                defaults={
                    'created_at': datetime.now() - timedelta(days=random.randint(1, 365)),
                    'updated_at': datetime.now(),
                    'quotes_count': 0
                }
            )
            if created:
                print(f"  创建来源: {source_name}")
    
    def generate_quotes(self, count=100):
        """生成名言数据"""
        print(f"生成 {count} 条名言...")
        
        authors = list(Authors.objects.all())
        categories = list(Categories.objects.all())
        sources = list(Sources.objects.all())
        
        for i in range(count):
            # 生成名言内容
            base_quote = random.choice(self.famous_quotes)
            quote_content = f"{base_quote} (Variation {i+1})"
            
            # 随机选择作者
            author = random.choice(authors)
            
            # 创建名言
            quote, created = Quotes.objects.get_or_create(
                content=quote_content,
                defaults={
                    'author': author,
                    'created_at': datetime.now() - timedelta(days=random.randint(1, 365)),
                    'updated_at': datetime.now()
                }
            )
            
            if created:
                # 随机关联类别 (1-3个)
                selected_categories = random.sample(categories, random.randint(1, 3))
                for category in selected_categories:
                    QuoteCategories.objects.get_or_create(
                        quote=quote,
                        category=category,
                        defaults={'created_at': datetime.now()}
                    )
                
                # 随机关联来源 (0-2个)
                if random.random() > 0.3:  # 70%的概率有来源
                    selected_sources = random.sample(sources, random.randint(1, 2))
                    for source in selected_sources:
                        QuoteSources.objects.get_or_create(
                            quote=quote,
                            source=source,
                            defaults={'created_at': datetime.now()}
                        )
                
                if (i + 1) % 10 == 0:
                    print(f"  已生成 {i + 1} 条名言")
    
    def update_counts(self):
        """更新统计数据"""
        print("更新统计数据...")
        
        # 更新作者名言数量
        for author in Authors.objects.all():
            count = Quotes.objects.filter(author=author).count()
            author.quotes_count = count
            author.save()
        
        # 更新类别名言数量
        for category in Categories.objects.all():
            count = QuoteCategories.objects.filter(category=category).count()
            category.quotes_count = count
            category.save()
        
        # 更新来源名言数量
        for source in Sources.objects.all():
            count = QuoteSources.objects.filter(source=source).count()
            source.quotes_count = count
            source.save()
        
        print("✅ 统计数据更新完成")
    
    def generate_all(self):
        """生成所有测试数据"""
        print("🚀 开始生成测试数据...")
        
        self.generate_authors(50)
        self.generate_categories(20)
        self.generate_sources(15)
        self.generate_quotes(100)
        self.update_counts()
        
        print("🎉 测试数据生成完成！")
        print(f"📊 数据统计:")
        print(f"   作者: {Authors.objects.count()}")
        print(f"   类别: {Categories.objects.count()}")
        print(f"   来源: {Sources.objects.count()}")
        print(f"   名言: {Quotes.objects.count()}")

if __name__ == "__main__":
    generator = TestDataGenerator()
    generator.generate_all()
