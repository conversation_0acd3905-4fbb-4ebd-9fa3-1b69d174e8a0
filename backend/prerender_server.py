#!/usr/bin/env python
"""
SEO预渲染服务器
为搜索引擎爬虫提供预渲染的HTML内容
运行在端口8082，与Nginx配置配合使用

版本：v1.0 - SEO收录优化
更新日期：2025年6月27日
"""

import os
import sys
import django
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import json
import logging

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quotes_admin.settings')
django.setup()

# 导入预渲染器
from seo_prerender import SEOPrerenderer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('prerender_server.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class PrerenderHandler(BaseHTTPRequestHandler):
    """预渲染请求处理器"""
    
    def __init__(self, *args, **kwargs):
        self.prerenderer = SEOPrerenderer()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        try:
            # 解析URL
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            # 获取User-Agent
            user_agent = self.headers.get('User-Agent', '')
            
            logger.info(f"预渲染请求: {path}, User-Agent: {user_agent[:100]}...")
            
            # 检查是否为预渲染请求
            if path.startswith('/prerender/'):
                # 提取实际路径
                actual_path = path.replace('/prerender', '')
                
                # 生成预渲染内容
                html_content = self.prerenderer.prerender_page(actual_path, user_agent)
                
                if html_content:
                    # 返回预渲染的HTML
                    self.send_response(200)
                    self.send_header('Content-Type', 'text/html; charset=utf-8')
                    self.send_header('Cache-Control', 'public, max-age=3600')  # 缓存1小时
                    self.end_headers()
                    self.wfile.write(html_content.encode('utf-8'))
                    
                    logger.info(f"预渲染成功: {actual_path}, 内容长度: {len(html_content)}")
                else:
                    # 不需要预渲染，返回404
                    self.send_error(404, "Page not found or not eligible for prerendering")
                    logger.warning(f"预渲染失败: {actual_path}")
            else:
                # 健康检查端点
                if path == '/health':
                    self.send_response(200)
                    self.send_header('Content-Type', 'application/json')
                    self.end_headers()
                    
                    health_data = {
                        'status': 'healthy',
                        'service': 'SEO Prerender Server',
                        'version': '1.0'
                    }
                    self.wfile.write(json.dumps(health_data).encode('utf-8'))
                else:
                    self.send_error(404, "Not found")
                    
        except Exception as e:
            logger.error(f"预渲染服务器错误: {e}")
            self.send_error(500, f"Internal server error: {str(e)}")
    
    def log_message(self, format, *args):
        """重写日志方法，使用我们的logger"""
        logger.info(f"{self.address_string()} - {format % args}")


def run_server(port=8082):
    """启动预渲染服务器"""
    server_address = ('127.0.0.1', port)
    httpd = HTTPServer(server_address, PrerenderHandler)
    
    logger.info(f"SEO预渲染服务器启动在端口 {port}")
    logger.info(f"服务器地址: http://127.0.0.1:{port}")
    logger.info("支持的预渲染路径:")
    logger.info("- /prerender/authors/ -> Authors列表页预渲染")
    logger.info("- /prerender/categories/ -> Categories列表页预渲染")
    logger.info("- /prerender/sources/ -> Sources列表页预渲染")
    logger.info("- /health -> 健康检查")
    logger.info("\n按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("\n正在停止预渲染服务器...")
        httpd.shutdown()
        logger.info("预渲染服务器已停止")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='SEO预渲染服务器')
    parser.add_argument('--port', type=int, default=8082, help='服务器端口 (默认: 8082)')
    
    args = parser.parse_args()
    
    try:
        run_server(args.port)
    except Exception as e:
        logger.error(f"启动服务器失败: {e}")
        sys.exit(1)
