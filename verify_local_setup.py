#!/usr/bin/env python3
"""
本地开发环境验证脚本
验证Quotese项目的所有核心功能是否正常工作
"""

import requests
import json
import sys
from urllib.parse import urljoin

def test_api_endpoint(base_url, endpoint, description):
    """测试API端点"""
    try:
        url = urljoin(base_url, endpoint)
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            print(f"✅ {description}: {url}")
            return True, response.json()
        else:
            print(f"❌ {description}: {url} (状态码: {response.status_code})")
            return False, None
    except Exception as e:
        print(f"❌ {description}: {url} (错误: {e})")
        return False, None

def test_graphql_endpoint(base_url, query, description):
    """测试GraphQL端点"""
    try:
        url = urljoin(base_url, '/graphql/')
        headers = {'Content-Type': 'application/json'}
        data = {'query': query}
        response = requests.post(url, headers=headers, json=data, timeout=5)
        if response.status_code == 200:
            result = response.json()
            if 'errors' not in result:
                print(f"✅ {description}: GraphQL查询成功")
                return True, result
            else:
                print(f"❌ {description}: GraphQL错误 - {result['errors']}")
                return False, None
        else:
            print(f"❌ {description}: GraphQL请求失败 (状态码: {response.status_code})")
            return False, None
    except Exception as e:
        print(f"❌ {description}: GraphQL请求异常 - {e}")
        return False, None

def test_frontend_page(base_url, path, description):
    """测试前端页面"""
    try:
        url = urljoin(base_url, path)
        response = requests.get(url, timeout=5)
        if response.status_code == 200 and '<!DOCTYPE html' in response.text:
            print(f"✅ {description}: {url}")
            return True
        else:
            print(f"❌ {description}: {url} (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ {description}: {url} (错误: {e})")
        return False

def main():
    print("🚀 开始验证Quotese本地开发环境...")
    print("=" * 50)
    
    # 配置
    backend_url = "http://127.0.0.1:8000"
    frontend_url = "http://localhost:8081"
    
    success_count = 0
    total_tests = 0
    
    # 测试后端API
    print("\n📡 测试后端API...")
    
    api_tests = [
        ("/api/", "API根路径"),
        ("/api/authors/", "作者API"),
        ("/api/categories/", "类别API"),
        ("/api/sources/", "来源API"),
        ("/api/quotes/", "名言API")
    ]
    
    for endpoint, description in api_tests:
        total_tests += 1
        success, data = test_api_endpoint(backend_url, endpoint, description)
        if success:
            success_count += 1
            if endpoint == "/api/authors/" and data:
                print(f"   📊 作者数量: {len(data)}")
            elif endpoint == "/api/quotes/" and data:
                print(f"   📊 名言数量: {len(data)}")
    
    # 测试GraphQL API
    print("\n🔗 测试GraphQL API...")
    
    graphql_tests = [
        ('{ authors { id name } }', "获取作者列表"),
        ('{ categories { id name } }', "获取类别列表"),
        ('{ sources { id name } }', "获取来源列表"),
        ('{ quotes { id content author { name } } }', "获取名言列表")
    ]
    
    for query, description in graphql_tests:
        total_tests += 1
        success, data = test_graphql_endpoint(backend_url, query, description)
        if success:
            success_count += 1
            if 'quotes' in data.get('data', {}):
                quotes = data['data']['quotes']
                print(f"   📊 GraphQL名言数量: {len(quotes)}")
    
    # 测试前端页面
    print("\n🌐 测试前端页面...")
    
    frontend_tests = [
        ("/", "首页"),
        ("/authors/", "作者列表页"),
        ("/categories/", "类别列表页"),
        ("/sources/", "来源列表页"),
        ("/authors/albert-einstein/", "作者详情页"),
        ("/categories/life/", "类别详情页")
    ]
    
    for path, description in frontend_tests:
        total_tests += 1
        success = test_frontend_page(frontend_url, path, description)
        if success:
            success_count += 1
    
    # 总结
    print("\n" + "=" * 50)
    print(f"🎯 验证完成: {success_count}/{total_tests} 项测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！本地开发环境运行正常。")
        print("\n📋 访问地址:")
        print(f"   - 前端首页: {frontend_url}/")
        print(f"   - Django管理后台: {backend_url}/admin/")
        print(f"   - API文档: {backend_url}/api/")
        print(f"   - GraphQL Playground: {backend_url}/graphql/")
        return 0
    else:
        print(f"⚠️  有 {total_tests - success_count} 项测试失败，请检查服务状态。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
