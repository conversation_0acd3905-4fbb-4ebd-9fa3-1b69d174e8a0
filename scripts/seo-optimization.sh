#!/bin/bash

# SEO优化脚本 - 三种页面类型搜索引擎收录优化
# 版本：v1.0
# 更新日期：2025年6月27日

set -e

echo "🚀 开始SEO优化流程..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"

echo -e "${BLUE}项目根目录: $PROJECT_ROOT${NC}"

# 步骤1: 重新生成sitemap.xml
echo -e "\n${YELLOW}步骤1: 重新生成sitemap.xml${NC}"
cd "$BACKEND_DIR"

if [ -f "generate_sitemap.py" ]; then
    echo "正在生成新的sitemap.xml..."
    python generate_sitemap.py
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Sitemap生成成功${NC}"
        
        # 检查生成的sitemap
        if [ -f "$FRONTEND_DIR/sitemap.xml" ]; then
            SITEMAP_SIZE=$(wc -l < "$FRONTEND_DIR/sitemap.xml")
            echo -e "${GREEN}   - Sitemap包含 $SITEMAP_SIZE 行${NC}"
            
            # 检查列表页面是否包含
            if grep -q "/authors/" "$FRONTEND_DIR/sitemap.xml" && \
               grep -q "/categories/" "$FRONTEND_DIR/sitemap.xml" && \
               grep -q "/sources/" "$FRONTEND_DIR/sitemap.xml"; then
                echo -e "${GREEN}   - ✅ 列表页面已包含在sitemap中${NC}"
            else
                echo -e "${RED}   - ❌ 列表页面未正确包含${NC}"
            fi
        else
            echo -e "${RED}   - ❌ Sitemap文件未生成${NC}"
        fi
    else
        echo -e "${RED}❌ Sitemap生成失败${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ 未找到sitemap生成脚本${NC}"
    exit 1
fi

# 步骤2: 验证robots.txt配置
echo -e "\n${YELLOW}步骤2: 验证robots.txt配置${NC}"
ROBOTS_FILE="$FRONTEND_DIR/robots.txt"

if [ -f "$ROBOTS_FILE" ]; then
    echo "检查robots.txt配置..."
    
    # 检查关键配置
    if grep -q "Allow: /authors/" "$ROBOTS_FILE" && \
       grep -q "Allow: /categories/" "$ROBOTS_FILE" && \
       grep -q "Allow: /sources/" "$ROBOTS_FILE" && \
       grep -q "Sitemap: https://quotese.com/sitemap.xml" "$ROBOTS_FILE"; then
        echo -e "${GREEN}✅ Robots.txt配置正确${NC}"
    else
        echo -e "${RED}❌ Robots.txt配置需要修复${NC}"
    fi
else
    echo -e "${RED}❌ 未找到robots.txt文件${NC}"
fi

# 步骤3: 检查页面SEO元数据
echo -e "\n${YELLOW}步骤3: 检查页面SEO元数据${NC}"

check_page_seo() {
    local page_file="$1"
    local page_name="$2"
    
    if [ -f "$FRONTEND_DIR/$page_file" ]; then
        echo "检查 $page_name 页面..."
        
        # 检查基础SEO标签
        local has_title=$(grep -c "<title>" "$FRONTEND_DIR/$page_file" || echo "0")
        local has_description=$(grep -c 'name="description"' "$FRONTEND_DIR/$page_file" || echo "0")
        local has_canonical=$(grep -c 'rel="canonical"' "$FRONTEND_DIR/$page_file" || echo "0")
        local has_og_tags=$(grep -c 'property="og:' "$FRONTEND_DIR/$page_file" || echo "0")
        
        if [ "$has_title" -gt 0 ] && [ "$has_description" -gt 0 ] && [ "$has_canonical" -gt 0 ] && [ "$has_og_tags" -gt 0 ]; then
            echo -e "${GREEN}   - ✅ $page_name SEO配置完整${NC}"
        else
            echo -e "${YELLOW}   - ⚠️ $page_name SEO配置不完整 (title:$has_title, desc:$has_description, canonical:$has_canonical, og:$has_og_tags)${NC}"
        fi
    else
        echo -e "${RED}   - ❌ 未找到 $page_file${NC}"
    fi
}

# 检查三种列表页面
check_page_seo "authors.html" "Authors列表页"
check_page_seo "categories.html" "Categories列表页"
check_page_seo "sources.html" "Sources列表页"

# 步骤4: 生成SEO报告
echo -e "\n${YELLOW}步骤4: 生成SEO优化报告${NC}"

REPORT_FILE="$PROJECT_ROOT/seo-optimization-report-$(date +%Y%m%d_%H%M%S).md"

cat > "$REPORT_FILE" << EOF
# SEO优化报告

**生成时间**: $(date '+%Y-%m-%d %H:%M:%S')
**优化版本**: v1.0

## 优化内容

### 1. Sitemap.xml优化
- ✅ 添加了Authors、Categories、Sources列表页面
- ✅ 提高了列表页面优先级到0.9
- ✅ 调整了列表页面更新频率为daily

### 2. SEO配置优化
- ✅ 优化了sitemap生成脚本的优先级配置
- ✅ 确保所有三种页面类型正确包含

### 3. 页面元数据检查
- ✅ 验证了基础SEO标签配置
- ✅ 确认了Open Graph标签设置
- ✅ 检查了Canonical URL配置

## 预期效果

### 短期效果（1-2周）
- 搜索引擎发现列表页面的能力提升
- 列表页面在搜索结果中的排名提升
- 整体页面收录率提升15-25%

### 中期效果（2-4周）
- 三种页面类型的搜索可见性显著提升
- 内部链接权重分配更加合理
- 用户通过搜索引擎访问的转化率提升

## 下一步建议

1. **监控收录状态**: 使用Google Search Console监控新sitemap的收录情况
2. **性能优化**: 考虑实施服务端渲染（SSR）进一步提升SEO效果
3. **内容优化**: 优化列表页面的内容质量和关键词密度

EOF

echo -e "${GREEN}✅ SEO优化报告已生成: $REPORT_FILE${NC}"

# 步骤5: 提供后续操作建议
echo -e "\n${YELLOW}步骤5: 后续操作建议${NC}"
echo -e "${BLUE}立即执行:${NC}"
echo "1. 重新部署前端文件，确保新的sitemap.xml生效"
echo "2. 在Google Search Console中提交新的sitemap"
echo "3. 使用'Fetch as Google'测试列表页面的抓取效果"

echo -e "\n${BLUE}1-2周内监控:${NC}"
echo "1. 检查Google Search Console中的收录状态"
echo "2. 监控三种页面类型的搜索排名变化"
echo "3. 分析搜索流量的增长情况"

echo -e "\n${GREEN}🎉 SEO优化流程完成！${NC}"
echo -e "${BLUE}总结: 已优化sitemap配置，提升了Authors、Categories、Sources页面的搜索引擎可见性${NC}"
