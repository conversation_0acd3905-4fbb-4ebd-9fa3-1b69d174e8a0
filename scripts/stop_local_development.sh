#!/bin/bash

# 本地开发环境停止脚本
# 停止Django后端和前端语义化URL服务器

echo "🛑 停止本地开发环境..."
echo "=================================="

# 从文件读取进程ID
if [ -f ".dev_pids" ]; then
    read DJANGO_PID FRONTEND_PID < .dev_pids
    echo "📝 从 .dev_pids 文件读取进程ID:"
    echo "   - Django PID: $DJANGO_PID"
    echo "   - 前端 PID: $FRONTEND_PID"
    echo ""
    
    # 停止Django服务器
    if [ ! -z "$DJANGO_PID" ] && kill -0 $DJANGO_PID 2>/dev/null; then
        echo "🔧 停止Django后端服务器 (PID: $DJANGO_PID)..."
        kill $DJANGO_PID
        sleep 2
        if kill -0 $DJANGO_PID 2>/dev/null; then
            echo "⚠️  强制停止Django服务器..."
            kill -9 $DJANGO_PID
        fi
        echo "✅ Django后端服务器已停止"
    else
        echo "ℹ️  Django服务器未运行或PID无效"
    fi
    
    # 停止前端服务器
    if [ ! -z "$FRONTEND_PID" ] && kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "🌐 停止前端语义化URL服务器 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        sleep 2
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            echo "⚠️  强制停止前端服务器..."
            kill -9 $FRONTEND_PID
        fi
        echo "✅ 前端语义化URL服务器已停止"
    else
        echo "ℹ️  前端服务器未运行或PID无效"
    fi
    
    # 删除PID文件
    rm -f .dev_pids
    echo "🗑️  已删除 .dev_pids 文件"
    
else
    echo "⚠️  未找到 .dev_pids 文件，尝试通过端口查找进程..."
    
    # 通过端口查找并停止进程
    DJANGO_PID=$(lsof -ti:8000 2>/dev/null)
    FRONTEND_PID=$(lsof -ti:8081 2>/dev/null)
    
    if [ ! -z "$DJANGO_PID" ]; then
        echo "🔧 发现Django服务器进程 (PID: $DJANGO_PID)，正在停止..."
        kill $DJANGO_PID
        sleep 2
        if kill -0 $DJANGO_PID 2>/dev/null; then
            kill -9 $DJANGO_PID
        fi
        echo "✅ Django后端服务器已停止"
    else
        echo "ℹ️  端口8000上未发现Django服务器进程"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        echo "🌐 发现前端服务器进程 (PID: $FRONTEND_PID)，正在停止..."
        kill $FRONTEND_PID
        sleep 2
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill -9 $FRONTEND_PID
        fi
        echo "✅ 前端语义化URL服务器已停止"
    else
        echo "ℹ️  端口8081上未发现前端服务器进程"
    fi
fi

echo ""

# 验证端口是否已释放
echo "🔍 验证端口状态..."
if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  端口8000仍被占用"
    lsof -i :8000
else
    echo "✅ 端口8000已释放"
fi

if lsof -Pi :8081 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  端口8081仍被占用"
    lsof -i :8081
else
    echo "✅ 端口8081已释放"
fi

echo ""
echo "🎉 本地开发环境已停止！"
echo "=================================="
echo ""
echo "💡 重新启动开发环境："
echo "   bash scripts/start_local_development.sh"
echo ""
echo "🔍 如果仍有进程占用端口，可以手动查找并停止："
echo "   lsof -i :8000  # 查看端口8000占用情况"
echo "   lsof -i :8081  # 查看端口8081占用情况"
echo "   kill -9 <PID>  # 强制停止指定进程"
