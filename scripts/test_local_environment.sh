#!/bin/bash
# 本地环境测试验证脚本
# 用于验证localhost:8081的功能状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
LOCAL_URL="http://localhost:8081"
TIMEOUT=5

# 函数：打印状态
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️ $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}🔍 $message${NC}"
            ;;
    esac
}

# 函数：测试URL
test_url() {
    local url=$1
    local description=$2
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$url" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        print_status "SUCCESS" "$description - OK"
        return 0
    else
        print_status "ERROR" "$description - 状态码: $response"
        return 1
    fi
}

# 主测试函数
main() {
    echo "========================================"
    echo "本地环境功能验证 (localhost:8081)"
    echo "时间: $(date)"
    echo "========================================"
    
    local total_tests=0
    local passed_tests=0
    
    # 检查服务器是否运行
    print_status "INFO" "检查服务器状态..."
    if ! curl -s --max-time 2 "$LOCAL_URL" > /dev/null; then
        print_status "ERROR" "服务器未运行，请先启动 semantic_url_server.py"
        exit 1
    fi
    
    # 测试主要页面
    print_status "INFO" "=== 测试主要页面 ==="
    
    local main_urls=(
        "$LOCAL_URL/|首页"
        "$LOCAL_URL/authors/|作者列表"
        "$LOCAL_URL/categories/|类别列表"
        "$LOCAL_URL/sources/|来源列表"
        "$LOCAL_URL/quotes/|名言列表"
    )
    
    for url_desc in "${main_urls[@]}"; do
        IFS='|' read -r url desc <<< "$url_desc"
        ((total_tests++))
        if test_url "$url" "$desc"; then
            ((passed_tests++))
        fi
    done
    
    # 测试详情页面
    print_status "INFO" "=== 测试详情页面 ==="
    
    local detail_urls=(
        "$LOCAL_URL/authors/albert-einstein/|爱因斯坦页面"
        "$LOCAL_URL/categories/life/|生活类别页面"
        "$LOCAL_URL/sources/the-art-of-war/|孙子兵法页面"
        "$LOCAL_URL/quotes/123/|名言详情页面"
    )

    for url_desc in "${detail_urls[@]}"; do
        IFS='|' read -r url desc <<< "$url_desc"
        ((total_tests++))
        if test_url "$url" "$desc"; then
            ((passed_tests++))
        fi
    done
    
    # 测试静态资源
    print_status "INFO" "=== 测试静态资源 ==="
    
    local static_urls=(
        "$LOCAL_URL/css/styles.css|样式文件"
        "$LOCAL_URL/js/config.js|配置文件"
        "$LOCAL_URL/js/url-handler.js|URL处理器"
        "$LOCAL_URL/robots.txt|robots.txt"
    )

    for url_desc in "${static_urls[@]}"; do
        IFS='|' read -r url desc <<< "$url_desc"
        ((total_tests++))
        if test_url "$url" "$desc"; then
            ((passed_tests++))
        fi
    done
    
    # 输出结果
    echo "========================================"
    echo "测试结果总结"
    echo "========================================"
    
    if [ $passed_tests -eq $total_tests ]; then
        print_status "SUCCESS" "所有测试通过! ($passed_tests/$total_tests)"
        echo "🎉 本地环境运行正常，可以进行生产环境部署！"
        exit 0
    else
        print_status "ERROR" "部分测试失败 ($passed_tests/$total_tests)"
        echo "⚠️ 请修复失败的测试项目后再进行部署"
        exit 1
    fi
}

# 运行测试
main "$@"
