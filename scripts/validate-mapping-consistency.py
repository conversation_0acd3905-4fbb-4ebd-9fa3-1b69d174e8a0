#!/usr/bin/env python3
"""
映射表数据一致性验证脚本
验证本地环境和生产环境映射表的数据一致性

使用方法:
    python3 scripts/validate-mapping-consistency.py
    
功能:
    1. 解析本地和生产环境的映射表配置
    2. 验证数据格式和结构一致性
    3. 检查重复和冲突的映射
    4. 生成一致性报告和建议

@version 1.0.0
@date 2025-06-27
<AUTHOR>
"""

import re
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple

class MappingConsistencyValidator:
    def __init__(self):
        self.local_mapping_file = Path("frontend/js/entity-id-mapper.js")
        self.production_mapping_file = Path("frontend/js/entity-id-mapper-production.js")
        
        self.local_mappings = {'categories': {}, 'authors': {}, 'sources': {}}
        self.production_mappings = {'categories': {}, 'authors': {}, 'sources': {}}
        
        self.issues = []
        self.stats = {
            'local_total': 0,
            'production_total': 0,
            'common_entities': 0,
            'conflicts': 0,
            'duplicates': 0,
            'format_issues': 0
        }
    
    def parse_js_mapping_file(self, file_path: Path) -> Dict:
        """解析JavaScript映射文件"""
        if not file_path.exists():
            self.issues.append(f"❌ 文件不存在: {file_path}")
            return {'categories': {}, 'authors': {}, 'sources': {}}
        
        try:
            content = file_path.read_text(encoding='utf-8')
            mappings = {'categories': {}, 'authors': {}, 'sources': {}}
            
            # 解析categories
            categories_match = re.search(
                r'categories:\s*\{([^}]+)\}', 
                content, 
                re.DOTALL
            )
            if categories_match:
                mappings['categories'] = self.parse_mapping_section(categories_match.group(1))
            
            # 解析authors
            authors_match = re.search(
                r'authors:\s*\{([^}]+)\}', 
                content, 
                re.DOTALL
            )
            if authors_match:
                mappings['authors'] = self.parse_mapping_section(authors_match.group(1))
            
            # 解析sources
            sources_match = re.search(
                r'sources:\s*\{([^}]+)\}', 
                content, 
                re.DOTALL
            )
            if sources_match:
                mappings['sources'] = self.parse_mapping_section(sources_match.group(1))
            
            return mappings
            
        except Exception as e:
            self.issues.append(f"❌ 解析文件失败 {file_path}: {e}")
            return {'categories': {}, 'authors': {}, 'sources': {}}
    
    def parse_mapping_section(self, section_content: str) -> Dict[str, int]:
        """解析映射表的一个部分"""
        mappings = {}
        
        # 匹配 'key': value 格式的行
        pattern = r"'([^']+)':\s*(\d+|null)"
        matches = re.findall(pattern, section_content)
        
        for slug, value in matches:
            if value != 'null':
                try:
                    mappings[slug] = int(value)
                except ValueError:
                    self.stats['format_issues'] += 1
                    self.issues.append(f"⚠️ 格式错误: {slug} -> {value}")
        
        return mappings
    
    def validate_slug_format(self, slug: str) -> bool:
        """验证slug格式是否符合URL路由标准"""
        # slug应该是小写，使用连字符分隔，不包含特殊字符
        pattern = r'^[a-z0-9]+(-[a-z0-9]+)*$'
        return bool(re.match(pattern, slug))
    
    def check_format_consistency(self):
        """检查格式一致性"""
        print("🔍 检查格式一致性...")
        
        all_mappings = [
            ("本地环境", self.local_mappings),
            ("生产环境", self.production_mappings)
        ]
        
        for env_name, mappings in all_mappings:
            for entity_type, entities in mappings.items():
                for slug, entity_id in entities.items():
                    # 检查slug格式
                    if not self.validate_slug_format(slug):
                        self.stats['format_issues'] += 1
                        self.issues.append(f"⚠️ {env_name} {entity_type} slug格式错误: '{slug}'")
                    
                    # 检查ID格式
                    if not isinstance(entity_id, int) or entity_id <= 0:
                        self.stats['format_issues'] += 1
                        self.issues.append(f"⚠️ {env_name} {entity_type} ID格式错误: '{slug}' -> {entity_id}")
    
    def check_duplicate_mappings(self):
        """检查重复映射"""
        print("🔍 检查重复映射...")
        
        all_mappings = [
            ("本地环境", self.local_mappings),
            ("生产环境", self.production_mappings)
        ]
        
        for env_name, mappings in all_mappings:
            for entity_type, entities in mappings.items():
                # 检查重复的ID
                id_to_slugs = {}
                for slug, entity_id in entities.items():
                    if entity_id in id_to_slugs:
                        id_to_slugs[entity_id].append(slug)
                    else:
                        id_to_slugs[entity_id] = [slug]
                
                for entity_id, slugs in id_to_slugs.items():
                    if len(slugs) > 1:
                        self.stats['duplicates'] += 1
                        self.issues.append(f"⚠️ {env_name} {entity_type} 重复ID {entity_id}: {slugs}")
    
    def check_cross_environment_conflicts(self):
        """检查跨环境冲突"""
        print("🔍 检查跨环境冲突...")
        
        for entity_type in ['categories', 'authors', 'sources']:
            local_entities = self.local_mappings[entity_type]
            prod_entities = self.production_mappings[entity_type]
            
            # 找到共同的slug
            common_slugs = set(local_entities.keys()) & set(prod_entities.keys())
            self.stats['common_entities'] += len(common_slugs)
            
            # 检查ID冲突
            for slug in common_slugs:
                local_id = local_entities[slug]
                prod_id = prod_entities[slug]
                
                if local_id != prod_id:
                    self.stats['conflicts'] += 1
                    self.issues.append(
                        f"⚠️ {entity_type} '{slug}' ID冲突: "
                        f"本地={local_id}, 生产={prod_id}"
                    )
    
    def calculate_coverage_stats(self):
        """计算覆盖率统计"""
        print("📊 计算覆盖率统计...")
        
        for entity_type in ['categories', 'authors', 'sources']:
            local_count = len(self.local_mappings[entity_type])
            prod_count = len(self.production_mappings[entity_type])
            
            self.stats['local_total'] += local_count
            self.stats['production_total'] += prod_count
            
            print(f"  {entity_type}: 本地={local_count}, 生产={prod_count}")
    
    def generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if self.stats['conflicts'] > 0:
            recommendations.append(
                f"🔧 解决 {self.stats['conflicts']} 个ID冲突，确保同一实体在不同环境使用相同ID"
            )
        
        if self.stats['duplicates'] > 0:
            recommendations.append(
                f"🔧 清理 {self.stats['duplicates']} 个重复映射，确保一个ID只对应一个slug"
            )
        
        if self.stats['format_issues'] > 0:
            recommendations.append(
                f"🔧 修复 {self.stats['format_issues']} 个格式问题，确保slug和ID格式正确"
            )
        
        # 覆盖率建议
        if self.stats['production_total'] < self.stats['local_total']:
            recommendations.append(
                "📈 生产环境映射表覆盖率低于本地环境，建议增加更多热门实体"
            )
        
        if self.stats['common_entities'] < min(self.stats['local_total'], self.stats['production_total']) * 0.5:
            recommendations.append(
                "🔄 本地和生产环境的实体重叠度较低，建议增加核心实体的一致性"
            )
        
        if not recommendations:
            recommendations.append("✅ 映射表数据一致性良好，无需特别改进")
        
        return recommendations
    
    def print_summary_report(self):
        """打印摘要报告"""
        print("\n" + "="*70)
        print("📋 映射表数据一致性验证报告")
        print("="*70)
        
        # 基本统计
        print("📊 基本统计:")
        print(f"  本地环境总实体: {self.stats['local_total']}")
        print(f"  生产环境总实体: {self.stats['production_total']}")
        print(f"  共同实体: {self.stats['common_entities']}")
        print()
        
        # 问题统计
        print("⚠️ 问题统计:")
        print(f"  ID冲突: {self.stats['conflicts']}")
        print(f"  重复映射: {self.stats['duplicates']}")
        print(f"  格式问题: {self.stats['format_issues']}")
        print(f"  总问题数: {len(self.issues)}")
        print()
        
        # 详细问题列表
        if self.issues:
            print("🔍 详细问题列表:")
            for issue in self.issues[:20]:  # 只显示前20个问题
                print(f"  {issue}")
            if len(self.issues) > 20:
                print(f"  ... 还有 {len(self.issues) - 20} 个问题")
            print()
        
        # 改进建议
        recommendations = self.generate_recommendations()
        print("💡 改进建议:")
        for rec in recommendations:
            print(f"  {rec}")
        print()
        
        # 总体评估
        total_issues = self.stats['conflicts'] + self.stats['duplicates'] + self.stats['format_issues']
        if total_issues == 0:
            print("✅ 总体评估: 优秀 - 数据一致性良好")
        elif total_issues <= 5:
            print("⚠️ 总体评估: 良好 - 有少量问题需要修复")
        elif total_issues <= 15:
            print("⚠️ 总体评估: 一般 - 有一些问题需要关注")
        else:
            print("❌ 总体评估: 需要改进 - 存在较多一致性问题")
    
    def run(self):
        """执行完整的验证流程"""
        print("🚀 开始映射表数据一致性验证...")
        print()
        
        # 解析映射文件
        print("📖 解析映射文件...")
        self.local_mappings = self.parse_js_mapping_file(self.local_mapping_file)
        self.production_mappings = self.parse_js_mapping_file(self.production_mapping_file)
        
        # 执行各项检查
        self.check_format_consistency()
        self.check_duplicate_mappings()
        self.check_cross_environment_conflicts()
        self.calculate_coverage_stats()
        
        # 生成报告
        self.print_summary_report()

if __name__ == "__main__":
    validator = MappingConsistencyValidator()
    validator.run()
