#!/bin/bash

# 本地开发环境测试脚本
# 验证Django后端和前端语义化URL服务器是否正常工作

echo "🚀 开始测试本地开发环境..."
echo "=================================="

# 检查Django后端服务器
echo "📡 测试Django后端API服务器 (http://127.0.0.1:8000)..."
if curl -4 -s --max-time 5 "http://127.0.0.1:8000/api/authors/" > /dev/null; then
    echo "✅ Django后端API服务器运行正常"
    
    # 测试各个API端点
    echo "🔍 测试API端点..."
    
    # 测试API根路径
    api_message=$(curl -4 -s "http://127.0.0.1:8000/api/" | jq -r '.message' 2>/dev/null || echo "错误")
    echo "   - API根路径: $api_message"

    # 测试Authors API
    authors_count=$(curl -4 -s "http://127.0.0.1:8000/api/authors/" | jq '. | length' 2>/dev/null || echo "0")
    echo "   - Authors API: $authors_count 个作者"

    # 测试Categories API
    categories_count=$(curl -4 -s "http://127.0.0.1:8000/api/categories/" | jq '. | length' 2>/dev/null || echo "0")
    echo "   - Categories API: $categories_count 个分类"

    # 测试Sources API
    sources_count=$(curl -4 -s "http://127.0.0.1:8000/api/sources/" | jq '. | length' 2>/dev/null || echo "0")
    echo "   - Sources API: $sources_count 个来源"
    
else
    echo "❌ Django后端API服务器无法访问"
    echo "   请确保Django服务器在端口8000上运行："
    echo "   cd backend && python3 manage.py runserver 127.0.0.1:8000 --settings=quotes_admin.settings_local"
    exit 1
fi

echo ""

# 检查前端语义化URL服务器
echo "🌐 测试前端语义化URL服务器 (http://localhost:8081)..."
if curl -s --max-time 5 "http://localhost:8081/" > /dev/null; then
    echo "✅ 前端语义化URL服务器运行正常"
    
    # 测试语义化URL
    echo "🔍 测试语义化URL..."
    
    # 测试首页
    if curl -s --max-time 5 "http://localhost:8081/" | grep -q "<!DOCTYPE html"; then
        echo "   - 首页 (/): ✅"
    else
        echo "   - 首页 (/): ❌"
    fi
    
    # 测试Authors列表页
    if curl -s --max-time 5 "http://localhost:8081/authors/" | grep -q "<!DOCTYPE html"; then
        echo "   - Authors列表页 (/authors/): ✅"
    else
        echo "   - Authors列表页 (/authors/): ❌"
    fi
    
    # 测试Categories列表页
    if curl -s --max-time 5 "http://localhost:8081/categories/" | grep -q "<!DOCTYPE html"; then
        echo "   - Categories列表页 (/categories/): ✅"
    else
        echo "   - Categories列表页 (/categories/): ❌"
    fi
    
    # 测试Sources列表页
    if curl -s --max-time 5 "http://localhost:8081/sources/" | grep -q "<!DOCTYPE html"; then
        echo "   - Sources列表页 (/sources/): ✅"
    else
        echo "   - Sources列表页 (/sources/): ❌"
    fi
    
    # 测试Author详情页
    if curl -s --max-time 5 "http://localhost:8081/authors/albert-einstein/" | grep -q "<!DOCTYPE html"; then
        echo "   - Author详情页 (/authors/albert-einstein/): ✅"
    else
        echo "   - Author详情页 (/authors/albert-einstein/): ❌"
    fi
    
    # 测试Category详情页
    if curl -s --max-time 5 "http://localhost:8081/categories/life/" | grep -q "<!DOCTYPE html"; then
        echo "   - Category详情页 (/categories/life/): ✅"
    else
        echo "   - Category详情页 (/categories/life/): ❌"
    fi
    
else
    echo "❌ 前端语义化URL服务器无法访问"
    echo "   请确保前端服务器在端口8081上运行："
    echo "   cd frontend && python3 semantic_url_server.py"
    exit 1
fi

echo ""

# 检查数据库文件
echo "💾 检查SQLite数据库..."
if [ -f "backend/db.sqlite3" ]; then
    echo "✅ SQLite数据库文件存在"
    
    # 检查数据库大小
    db_size=$(ls -lh backend/db.sqlite3 | awk '{print $5}')
    echo "   - 数据库大小: $db_size"
else
    echo "❌ SQLite数据库文件不存在"
    echo "   请运行数据库迁移："
    echo "   cd backend && python3 manage.py migrate --settings=quotes_admin.settings_local"
fi

echo ""

# 检查配置文件
echo "⚙️  检查前端配置..."
if grep -q "127.0.0.1:8000" frontend/js/config.js; then
    echo "✅ 前端配置正确指向本地API端点"
else
    echo "❌ 前端配置未指向本地API端点"
    echo "   请检查 frontend/js/config.js 中的 apiEndpoint 配置"
fi

echo ""
echo "🎉 本地开发环境测试完成！"
echo "=================================="
echo ""
echo "📋 访问地址："
echo "   - 前端首页: http://localhost:8081/"
echo "   - Django管理后台: http://localhost:8000/admin/"
echo "   - API文档: http://localhost:8000/api/"
echo ""
echo "🔧 常用命令："
echo "   - 启动Django后端: cd backend && python3 manage.py runserver 8000 --settings=quotes_admin.settings_local"
echo "   - 启动前端服务器: cd frontend && python3 semantic_url_server.py"
echo "   - 查看Django日志: 查看运行Django的终端输出"
echo "   - 查看前端日志: 查看运行semantic_url_server.py的终端输出"
