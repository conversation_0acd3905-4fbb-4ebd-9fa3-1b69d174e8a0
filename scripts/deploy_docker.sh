#!/bin/bash

# Quotese.com 容器环境部署脚本
# 版本: 2.0.0
# 日期: 2025-06-23
# 作者: Quotese 开发团队

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
PROJECT_NAME="quotese"
DOCKER_COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
ENVIRONMENT=${1:-production}  # 默认为生产环境

# 显示帮助信息
show_help() {
    echo "Quotese.com 容器环境部署脚本"
    echo ""
    echo "用法: $0 [ENVIRONMENT] [COMMAND]"
    echo ""
    echo "环境:"
    echo "  production    生产环境 (默认)"
    echo "  staging       测试环境"
    echo "  development   开发环境"
    echo ""
    echo "命令:"
    echo "  deploy        完整部署 (默认)"
    echo "  update        更新部署"
    echo "  restart       重启服务"
    echo "  stop          停止服务"
    echo "  logs          查看日志"
    echo "  backup        备份数据"
    echo "  restore       恢复数据"
    echo "  cleanup       清理资源"
    echo "  health        健康检查"
    echo ""
    echo "示例:"
    echo "  $0 production deploy    # 生产环境完整部署"
    echo "  $0 staging update       # 测试环境更新部署"
    echo "  $0 development logs     # 开发环境查看日志"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
        log_error "Docker Compose 配置文件不存在: $DOCKER_COMPOSE_FILE"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 设置环境变量
setup_environment() {
    log_info "设置 $ENVIRONMENT 环境变量..."
    
    case $ENVIRONMENT in
        production)
            export DJANGO_SETTINGS_MODULE="quotes_admin.settings_prod"
            export ALLOWED_HOSTS="quotese.com,www.quotese.com,api.quotese.com,************"
            export DEBUG="False"
            ;;
        staging)
            export DJANGO_SETTINGS_MODULE="quotes_admin.settings_staging"
            export ALLOWED_HOSTS="staging.quotese.com,************"
            export DEBUG="True"
            ;;
        development)
            export DJANGO_SETTINGS_MODULE="quotes_admin.settings_local"
            export ALLOWED_HOSTS="localhost,127.0.0.1,0.0.0.0"
            export DEBUG="True"
            ;;
        *)
            log_error "未知环境: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    log_success "环境变量设置完成"
}

# 创建备份
create_backup() {
    log_info "创建数据备份..."
    
    mkdir -p "$BACKUP_DIR"
    
    # 备份数据库
    if docker-compose ps db | grep -q "Up"; then
        log_info "备份MySQL数据库..."
        docker-compose exec -T db mysqldump -u quotes_user -plixiaohua_2025 quotes_db > "$BACKUP_DIR/database.sql"
        log_success "数据库备份完成: $BACKUP_DIR/database.sql"
    else
        log_warning "数据库容器未运行，跳过数据库备份"
    fi
    
    # 备份静态文件
    if [ -d "./frontend" ]; then
        log_info "备份前端文件..."
        tar -czf "$BACKUP_DIR/frontend.tar.gz" ./frontend
        log_success "前端文件备份完成: $BACKUP_DIR/frontend.tar.gz"
    fi
    
    # 备份配置文件
    if [ -d "./config" ]; then
        log_info "备份配置文件..."
        tar -czf "$BACKUP_DIR/config.tar.gz" ./config
        log_success "配置文件备份完成: $BACKUP_DIR/config.tar.gz"
    fi
    
    log_success "备份创建完成: $BACKUP_DIR"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker-compose build backend
    
    log_success "镜像构建完成"
}

# 部署服务
deploy_services() {
    log_info "部署容器服务..."
    
    # 启动服务
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 执行数据库迁移
    log_info "执行数据库迁移..."
    docker-compose exec backend python manage.py migrate --settings=$DJANGO_SETTINGS_MODULE
    
    # 收集静态文件
    log_info "收集静态文件..."
    docker-compose exec backend python manage.py collectstatic --noinput --settings=$DJANGO_SETTINGS_MODULE
    
    # 创建超级用户（仅在首次部署时）
    if [ "$ENVIRONMENT" = "production" ] && [ ! -f ".superuser_created" ]; then
        log_info "创建超级用户..."
        docker-compose exec backend python manage.py shell --settings=$DJANGO_SETTINGS_MODULE << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123456')
    print('超级用户创建成功')
else:
    print('超级用户已存在')
EOF
        touch .superuser_created
    fi
    
    log_success "服务部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查容器状态
    log_info "检查容器状态..."
    docker-compose ps
    
    # 检查数据库连接
    log_info "检查数据库连接..."
    if docker-compose exec backend python manage.py check --database default --settings=$DJANGO_SETTINGS_MODULE; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        return 1
    fi
    
    # 检查API端点
    log_info "检查API端点..."
    sleep 5
    if curl -f http://localhost:8000/api/ > /dev/null 2>&1; then
        log_success "API端点响应正常"
    else
        log_error "API端点无响应"
        return 1
    fi
    
    # 检查前端服务
    log_info "检查前端服务..."
    if curl -f http://localhost:8080/ > /dev/null 2>&1; then
        log_success "前端服务响应正常"
    else
        log_error "前端服务无响应"
        return 1
    fi
    
    log_success "健康检查完成"
}

# 更新部署
update_deployment() {
    log_info "更新部署..."
    
    # 拉取最新代码
    if [ -d ".git" ]; then
        log_info "拉取最新代码..."
        git pull origin main
    fi
    
    # 重新构建镜像
    build_images
    
    # 重启服务
    docker-compose up -d --force-recreate
    
    # 执行迁移
    docker-compose exec backend python manage.py migrate --settings=$DJANGO_SETTINGS_MODULE
    
    # 收集静态文件
    docker-compose exec backend python manage.py collectstatic --noinput --settings=$DJANGO_SETTINGS_MODULE
    
    log_success "更新部署完成"
}

# 查看日志
view_logs() {
    log_info "查看服务日志..."
    docker-compose logs -f --tail=100
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    docker-compose restart
    log_success "服务已重启"
}

# 清理资源
cleanup_resources() {
    log_warning "清理Docker资源..."
    
    read -p "确定要清理未使用的Docker资源吗？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker system prune -f
        docker volume prune -f
        log_success "资源清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 恢复数据
restore_data() {
    log_info "恢复数据..."
    
    if [ -z "$2" ]; then
        log_error "请指定备份目录"
        echo "用法: $0 $ENVIRONMENT restore <backup_directory>"
        exit 1
    fi
    
    RESTORE_DIR="$2"
    
    if [ ! -d "$RESTORE_DIR" ]; then
        log_error "备份目录不存在: $RESTORE_DIR"
        exit 1
    fi
    
    # 恢复数据库
    if [ -f "$RESTORE_DIR/database.sql" ]; then
        log_info "恢复数据库..."
        docker-compose exec -T db mysql -u quotes_user -plixiaohua_2025 quotes_db < "$RESTORE_DIR/database.sql"
        log_success "数据库恢复完成"
    fi
    
    log_success "数据恢复完成"
}

# 主函数
main() {
    COMMAND=${2:-deploy}
    
    case $COMMAND in
        help|--help|-h)
            show_help
            exit 0
            ;;
        deploy)
            check_dependencies
            setup_environment
            create_backup
            build_images
            deploy_services
            health_check
            ;;
        update)
            check_dependencies
            setup_environment
            create_backup
            update_deployment
            health_check
            ;;
        restart)
            restart_services
            ;;
        stop)
            stop_services
            ;;
        logs)
            view_logs
            ;;
        backup)
            create_backup
            ;;
        restore)
            restore_data "$@"
            ;;
        cleanup)
            cleanup_resources
            ;;
        health)
            health_check
            ;;
        *)
            log_error "未知命令: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
