#!/bin/bash
# 生产环境部署验证脚本
# 用于验证Quotese.com URL重构项目的部署状态

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
DOMAIN="quotese.com"
PROTOCOL="https"
BASE_URL="${PROTOCOL}://${DOMAIN}"
TIMEOUT=10

# 日志文件
LOG_FILE="/tmp/deployment_verification_$(date +%Y%m%d_%H%M%S).log"

# 函数：打印带颜色的消息
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}" | tee -a "$LOG_FILE"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}" | tee -a "$LOG_FILE"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️ $message${NC}" | tee -a "$LOG_FILE"
            ;;
        "INFO")
            echo -e "${BLUE}🔍 $message${NC}" | tee -a "$LOG_FILE"
            ;;
    esac
}

# 函数：测试URL状态
test_url() {
    local url=$1
    local expected_status=${2:-200}
    local description=$3
    
    print_status "INFO" "测试: $description"
    
    local response=$(curl -s -o /dev/null -w "%{http_code}:%{time_total}:%{size_download}" \
                     --max-time $TIMEOUT "$url" 2>/dev/null || echo "000:0:0")
    
    IFS=':' read -r status_code time_total size_download <<< "$response"
    
    if [ "$status_code" = "$expected_status" ]; then
        local time_ms=$(echo "$time_total * 1000" | bc -l 2>/dev/null || echo "0")
        print_status "SUCCESS" "$url - 状态码: $status_code, 响应时间: ${time_ms}ms, 大小: ${size_download}字节"
        return 0
    else
        print_status "ERROR" "$url - 期望状态码: $expected_status, 实际: $status_code"
        return 1
    fi
}

# 函数：测试重定向
test_redirect() {
    local url=$1
    local expected_location=$2
    local description=$3
    
    print_status "INFO" "测试重定向: $description"
    
    local response=$(curl -s -I --max-time $TIMEOUT "$url" 2>/dev/null || echo "")
    local status_code=$(echo "$response" | grep -i "^HTTP" | awk '{print $2}' | head -1)
    local location=$(echo "$response" | grep -i "^Location:" | awk '{print $2}' | tr -d '\r\n' | head -1)
    
    if [ "$status_code" = "301" ] || [ "$status_code" = "302" ]; then
        if [[ "$location" == *"$expected_location"* ]]; then
            print_status "SUCCESS" "$url → $location (状态码: $status_code)"
            return 0
        else
            print_status "ERROR" "$url - 期望重定向到: $expected_location, 实际: $location"
            return 1
        fi
    else
        print_status "ERROR" "$url - 期望重定向状态码301/302, 实际: $status_code"
        return 1
    fi
}

# 函数：测试API连接
test_api() {
    local api_url="http://************:8000/api/"
    print_status "INFO" "测试API连接"
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" \
                     --max-time $TIMEOUT "${api_url}authors/" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        print_status "SUCCESS" "API连接正常 - ${api_url}"
        return 0
    else
        print_status "ERROR" "API连接失败 - 状态码: $response"
        return 1
    fi
}

# 函数：测试静态资源
test_static_resources() {
    print_status "INFO" "测试静态资源"
    
    local static_urls=(
        "${BASE_URL}/css/styles.css"
        "${BASE_URL}/js/config.js"
        "${BASE_URL}/js/url-handler.js"
        "${BASE_URL}/robots.txt"
        "${BASE_URL}/sitemap.xml"
    )
    
    local success_count=0
    local total_count=${#static_urls[@]}
    
    for url in "${static_urls[@]}"; do
        if test_url "$url" "200" "静态资源"; then
            ((success_count++))
        fi
    done
    
    if [ $success_count -eq $total_count ]; then
        print_status "SUCCESS" "所有静态资源测试通过 ($success_count/$total_count)"
        return 0
    else
        print_status "ERROR" "静态资源测试失败 ($success_count/$total_count)"
        return 1
    fi
}

# 主验证函数
main() {
    echo "========================================"
    echo "Quotese.com 生产环境部署验证"
    echo "时间: $(date)"
    echo "日志文件: $LOG_FILE"
    echo "========================================"
    
    local total_tests=0
    local passed_tests=0
    
    # 1. 测试主要页面
    print_status "INFO" "=== 测试主要页面 ==="
    
    local main_urls=(
        "${BASE_URL}/:首页"
        "${BASE_URL}/authors/:作者列表页"
        "${BASE_URL}/categories/:类别列表页"
        "${BASE_URL}/sources/:来源列表页"
        "${BASE_URL}/quotes/:名言列表页"
    )
    
    for url_desc in "${main_urls[@]}"; do
        IFS=':' read -r url desc <<< "$url_desc"
        ((total_tests++))
        if test_url "$url" "200" "$desc"; then
            ((passed_tests++))
        fi
    done
    
    # 2. 测试详情页面
    print_status "INFO" "=== 测试详情页面 ==="
    
    local detail_urls=(
        "${BASE_URL}/authors/albert-einstein/:爱因斯坦作者页"
        "${BASE_URL}/categories/life/:生活类别页"
        "${BASE_URL}/sources/the-art-of-war/:孙子兵法来源页"
        "${BASE_URL}/quotes/123/:名言详情页"
    )
    
    for url_desc in "${detail_urls[@]}"; do
        IFS=':' read -r url desc <<< "$url_desc"
        ((total_tests++))
        if test_url "$url" "200" "$desc"; then
            ((passed_tests++))
        fi
    done
    
    # 3. 测试子页面
    print_status "INFO" "=== 测试子页面 ==="
    
    local sub_urls=(
        "${BASE_URL}/authors/albert-einstein/quotes/:爱因斯坦名言列表"
        "${BASE_URL}/categories/life/quotes/:生活类别名言列表"
    )
    
    for url_desc in "${sub_urls[@]}"; do
        IFS=':' read -r url desc <<< "$url_desc"
        ((total_tests++))
        if test_url "$url" "200" "$desc"; then
            ((passed_tests++))
        fi
    done
    
    # 4. 测试特殊页面
    print_status "INFO" "=== 测试特殊页面 ==="
    
    ((total_tests++))
    if test_url "${BASE_URL}/search/" "200" "搜索页面"; then
        ((passed_tests++))
    fi
    
    ((total_tests++))
    if test_url "${BASE_URL}/404.html" "200" "404错误页面"; then
        ((passed_tests++))
    fi
    
    # 5. 测试重定向
    print_status "INFO" "=== 测试301重定向 ==="
    
    local redirect_tests=(
        "${BASE_URL}/index.html:/:HTML文件重定向"
        "${BASE_URL}/authors.html:/authors/:作者页面重定向"
        "${BASE_URL}/categories.html:/categories/:类别页面重定向"
        "${BASE_URL}/sources.html:/sources/:来源页面重定向"
    )
    
    for redirect_test in "${redirect_tests[@]}"; do
        IFS=':' read -r url expected_location desc <<< "$redirect_test"
        ((total_tests++))
        if test_redirect "$url" "$expected_location" "$desc"; then
            ((passed_tests++))
        fi
    done
    
    # 6. 测试尾斜杠重定向
    print_status "INFO" "=== 测试尾斜杠重定向 ==="
    
    local slash_redirects=(
        "${BASE_URL}/authors:/authors/:作者列表尾斜杠"
        "${BASE_URL}/categories:/categories/:类别列表尾斜杠"
        "${BASE_URL}/sources:/sources/:来源列表尾斜杠"
    )
    
    for redirect_test in "${slash_redirects[@]}"; do
        IFS=':' read -r url expected_location desc <<< "$redirect_test"
        ((total_tests++))
        if test_redirect "$url" "$expected_location" "$desc"; then
            ((passed_tests++))
        fi
    done
    
    # 7. 测试静态资源
    print_status "INFO" "=== 测试静态资源 ==="
    ((total_tests++))
    if test_static_resources; then
        ((passed_tests++))
    fi
    
    # 8. 测试API连接
    print_status "INFO" "=== 测试API连接 ==="
    ((total_tests++))
    if test_api; then
        ((passed_tests++))
    fi
    
    # 9. 测试SEO文件
    print_status "INFO" "=== 测试SEO文件 ==="
    
    local seo_files=(
        "${BASE_URL}/robots.txt:robots.txt文件"
        "${BASE_URL}/sitemap.xml:sitemap.xml文件"
    )
    
    for url_desc in "${seo_files[@]}"; do
        IFS=':' read -r url desc <<< "$url_desc"
        ((total_tests++))
        if test_url "$url" "200" "$desc"; then
            ((passed_tests++))
        fi
    done
    
    # 输出总结
    echo "========================================"
    echo "验证结果总结"
    echo "========================================"
    
    local success_rate=$(echo "scale=1; $passed_tests * 100 / $total_tests" | bc -l 2>/dev/null || echo "0")
    
    if [ $passed_tests -eq $total_tests ]; then
        print_status "SUCCESS" "所有测试通过! ($passed_tests/$total_tests, 100%)"
        echo "🎉 部署验证成功，网站运行正常！"
        exit 0
    else
        print_status "ERROR" "部分测试失败 ($passed_tests/$total_tests, ${success_rate}%)"
        echo "⚠️ 请检查失败的测试项目并修复问题"
        echo "详细日志: $LOG_FILE"
        exit 1
    fi
}

# 检查依赖
if ! command -v curl &> /dev/null; then
    print_status "ERROR" "curl命令未找到，请安装curl"
    exit 1
fi

if ! command -v bc &> /dev/null; then
    print_status "WARNING" "bc命令未找到，响应时间计算可能不准确"
fi

# 运行主函数
main "$@"
