#!/bin/bash

# 本地开发环境启动脚本
# 自动启动Django后端和前端语义化URL服务器

echo "🚀 启动本地开发环境..."
echo "=================================="

# 检查是否在正确的目录
if [ ! -f "backend/manage.py" ] || [ ! -f "frontend/semantic_url_server.py" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    echo "   当前目录: $(pwd)"
    echo "   应该包含 backend/ 和 frontend/ 目录"
    exit 1
fi

# 检查Python3是否可用
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误：未找到python3命令"
    echo "   请确保已安装Python 3"
    exit 1
fi

# 检查数据库文件
if [ ! -f "backend/db.sqlite3" ]; then
    echo "⚠️  警告：SQLite数据库文件不存在"
    echo "🔧 正在创建数据库..."
    cd backend
    python3 manage.py migrate --settings=quotes_admin.settings_local
    cd ..
    echo "✅ 数据库创建完成"
fi

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  警告：端口 $port 已被占用 ($service)"
        echo "   请先停止占用端口的进程，或使用其他端口"
        return 1
    fi
    return 0
}

# 检查Django端口 (8000)
if ! check_port 8000 "Django后端"; then
    echo "💡 提示：可以使用以下命令查看占用端口的进程："
    echo "   lsof -i :8000"
    echo "   kill -9 <PID>"
    exit 1
fi

# 检查前端端口 (8081)
if ! check_port 8081 "前端服务器"; then
    echo "💡 提示：可以使用以下命令查看占用端口的进程："
    echo "   lsof -i :8081"
    echo "   kill -9 <PID>"
    exit 1
fi

echo "✅ 端口检查通过"
echo ""

# 启动Django后端服务器
echo "🔧 启动Django后端服务器..."
cd backend
python3 manage.py runserver 127.0.0.1:8000 --settings=quotes_admin.settings_local &
DJANGO_PID=$!
cd ..

# 等待Django服务器启动
echo "⏳ 等待Django服务器启动..."
sleep 3

# 检查Django是否成功启动
if curl -4 -s --max-time 5 "http://127.0.0.1:8000/api/authors/" > /dev/null; then
    echo "✅ Django后端服务器启动成功 (PID: $DJANGO_PID)"
else
    echo "❌ Django后端服务器启动失败"
    kill $DJANGO_PID 2>/dev/null
    exit 1
fi

echo ""

# 启动前端语义化URL服务器
echo "🌐 启动前端语义化URL服务器..."
cd frontend
python3 semantic_url_server.py &
FRONTEND_PID=$!
cd ..

# 等待前端服务器启动
echo "⏳ 等待前端服务器启动..."
sleep 2

# 检查前端服务器是否成功启动
if curl -s --max-time 5 "http://localhost:8081/" > /dev/null; then
    echo "✅ 前端语义化URL服务器启动成功 (PID: $FRONTEND_PID)"
else
    echo "❌ 前端语义化URL服务器启动失败"
    kill $DJANGO_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 1
fi

echo ""
echo "🎉 本地开发环境启动完成！"
echo "=================================="
echo ""
echo "📋 服务信息："
echo "   - Django后端 (PID: $DJANGO_PID): http://localhost:8000/"
echo "   - 前端服务器 (PID: $FRONTEND_PID): http://localhost:8081/"
echo ""
echo "🌐 访问地址："
echo "   - 前端首页: http://localhost:8081/"
echo "   - Django管理后台: http://localhost:8000/admin/"
echo "   - API文档: http://localhost:8000/api/"
echo ""
echo "🛑 停止服务："
echo "   - 停止Django: kill $DJANGO_PID"
echo "   - 停止前端: kill $FRONTEND_PID"
echo "   - 停止所有: kill $DJANGO_PID $FRONTEND_PID"
echo ""
echo "📝 进程ID已保存到 .dev_pids 文件"
echo "$DJANGO_PID $FRONTEND_PID" > .dev_pids

echo ""
echo "🔍 运行测试验证环境："
echo "   bash scripts/test_local_development.sh"
echo ""
echo "💡 提示：按 Ctrl+C 可以停止此脚本，但服务器将继续在后台运行"
echo "   使用上面的 kill 命令来停止服务器"

# 保持脚本运行，显示日志
echo ""
echo "📊 实时日志 (按 Ctrl+C 退出日志查看，服务器继续运行):"
echo "=================================="

# 等待用户中断
trap 'echo ""; echo "📝 服务器仍在后台运行"; echo "   Django PID: $DJANGO_PID"; echo "   前端 PID: $FRONTEND_PID"; echo "   使用 kill $DJANGO_PID $FRONTEND_PID 停止所有服务"; exit 0' INT

# 显示进程状态
while true; do
    sleep 5
    if ! kill -0 $DJANGO_PID 2>/dev/null; then
        echo "❌ Django服务器已停止"
        break
    fi
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "❌ 前端服务器已停止"
        break
    fi
    echo "✅ $(date '+%H:%M:%S') - 所有服务运行正常"
done
