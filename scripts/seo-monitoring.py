#!/usr/bin/env python
"""
SEO监控脚本
监控三种页面类型的搜索引擎收录状态和SEO指标

版本：v1.0 - SEO收录优化
更新日期：2025年6月27日
"""

import requests
import time
import json
import csv
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import xml.etree.ElementTree as ET
from urllib.parse import urljoin, urlparse
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('seo_monitoring.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class SEOMonitor:
    """SEO监控器类"""
    
    def __init__(self, base_url: str = "https://quotese.com"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (compatible; SEOMonitor/1.0; +https://quotese.com/bot)'
        })
        
        # 监控的页面类型
        self.page_types = {
            'authors_list': '/authors/',
            'categories_list': '/categories/',
            'sources_list': '/sources/',
            'home': '/'
        }
        
        # 搜索引擎爬虫User-Agent
        self.crawler_user_agents = {
            'googlebot': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
            'bingbot': 'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)',
            'baiduspider': 'Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)'
        }
    
    def check_sitemap_status(self) -> Dict:
        """检查sitemap.xml状态"""
        logger.info("检查sitemap.xml状态...")
        
        sitemap_url = urljoin(self.base_url, '/sitemap.xml')
        
        try:
            response = self.session.get(sitemap_url, timeout=10)
            
            if response.status_code == 200:
                # 解析XML
                root = ET.fromstring(response.content)
                
                # 统计URL数量
                urls = root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url')
                total_urls = len(urls)
                
                # 统计各类页面
                page_counts = {
                    'authors_list': 0,
                    'categories_list': 0,
                    'sources_list': 0,
                    'authors_detail': 0,
                    'categories_detail': 0,
                    'sources_detail': 0,
                    'quotes': 0,
                    'home': 0
                }
                
                for url_elem in urls:
                    loc_elem = url_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                    if loc_elem is not None:
                        url = loc_elem.text
                        
                        if url.endswith('/authors/'):
                            page_counts['authors_list'] += 1
                        elif url.endswith('/categories/'):
                            page_counts['categories_list'] += 1
                        elif url.endswith('/sources/'):
                            page_counts['sources_list'] += 1
                        elif '/authors/' in url and not url.endswith('/authors/'):
                            page_counts['authors_detail'] += 1
                        elif '/categories/' in url and not url.endswith('/categories/'):
                            page_counts['categories_detail'] += 1
                        elif '/sources/' in url and not url.endswith('/sources/'):
                            page_counts['sources_detail'] += 1
                        elif '/quotes/' in url:
                            page_counts['quotes'] += 1
                        elif url.endswith('/'):
                            page_counts['home'] += 1
                
                return {
                    'status': 'success',
                    'total_urls': total_urls,
                    'page_counts': page_counts,
                    'last_modified': response.headers.get('Last-Modified', 'Unknown'),
                    'size_bytes': len(response.content)
                }
            else:
                return {
                    'status': 'error',
                    'error': f'HTTP {response.status_code}',
                    'message': 'Sitemap not accessible'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'message': 'Failed to fetch sitemap'
            }
    
    def check_page_accessibility(self, page_type: str, path: str) -> Dict:
        """检查页面可访问性"""
        url = urljoin(self.base_url, path)
        
        try:
            # 普通用户访问
            response = self.session.get(url, timeout=10)
            
            result = {
                'page_type': page_type,
                'url': url,
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds(),
                'content_length': len(response.content),
                'has_content': len(response.content) > 1000,  # 基本内容检查
                'crawler_tests': {}
            }
            
            # 检查基础SEO元素
            if response.status_code == 200:
                content = response.text.lower()
                result.update({
                    'has_title': '<title>' in content,
                    'has_meta_description': 'name="description"' in content,
                    'has_canonical': 'rel="canonical"' in content,
                    'has_og_tags': 'property="og:' in content,
                    'has_structured_data': 'application/ld+json' in content
                })
            
            # 测试搜索引擎爬虫访问
            for crawler_name, user_agent in self.crawler_user_agents.items():
                crawler_session = requests.Session()
                crawler_session.headers.update({'User-Agent': user_agent})
                
                try:
                    crawler_response = crawler_session.get(url, timeout=10)
                    result['crawler_tests'][crawler_name] = {
                        'status_code': crawler_response.status_code,
                        'content_length': len(crawler_response.content),
                        'response_time': crawler_response.elapsed.total_seconds(),
                        'is_prerendered': len(crawler_response.content) != len(response.content)
                    }
                except Exception as e:
                    result['crawler_tests'][crawler_name] = {
                        'error': str(e)
                    }
            
            return result
            
        except Exception as e:
            return {
                'page_type': page_type,
                'url': url,
                'error': str(e),
                'status': 'failed'
            }
    
    def check_robots_txt(self) -> Dict:
        """检查robots.txt配置"""
        logger.info("检查robots.txt配置...")
        
        robots_url = urljoin(self.base_url, '/robots.txt')
        
        try:
            response = self.session.get(robots_url, timeout=10)
            
            if response.status_code == 200:
                content = response.text
                
                # 检查关键配置
                checks = {
                    'allows_authors': 'Allow: /authors/' in content,
                    'allows_categories': 'Allow: /categories/' in content,
                    'allows_sources': 'Allow: /sources/' in content,
                    'has_sitemap': 'Sitemap:' in content,
                    'sitemap_url_correct': 'Sitemap: https://quotese.com/sitemap.xml' in content
                }
                
                return {
                    'status': 'success',
                    'checks': checks,
                    'content_length': len(content),
                    'all_checks_passed': all(checks.values())
                }
            else:
                return {
                    'status': 'error',
                    'error': f'HTTP {response.status_code}',
                    'message': 'Robots.txt not accessible'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'message': 'Failed to fetch robots.txt'
            }
    
    def run_full_monitoring(self) -> Dict:
        """运行完整的SEO监控"""
        logger.info("开始SEO监控...")
        
        monitoring_results = {
            'timestamp': datetime.now().isoformat(),
            'base_url': self.base_url,
            'sitemap_status': self.check_sitemap_status(),
            'robots_status': self.check_robots_txt(),
            'page_accessibility': {}
        }
        
        # 检查各页面类型的可访问性
        for page_type, path in self.page_types.items():
            logger.info(f"检查页面: {page_type} ({path})")
            monitoring_results['page_accessibility'][page_type] = self.check_page_accessibility(page_type, path)
            time.sleep(1)  # 避免请求过于频繁
        
        return monitoring_results
    
    def generate_report(self, results: Dict) -> str:
        """生成监控报告"""
        report_lines = []
        
        report_lines.append("# SEO监控报告")
        report_lines.append(f"**生成时间**: {results['timestamp']}")
        report_lines.append(f"**监控网站**: {results['base_url']}")
        report_lines.append("")
        
        # Sitemap状态
        sitemap = results['sitemap_status']
        report_lines.append("## Sitemap.xml状态")
        if sitemap['status'] == 'success':
            report_lines.append(f"- ✅ 状态: 正常")
            report_lines.append(f"- 📊 总URL数量: {sitemap['total_urls']}")
            report_lines.append(f"- 📄 文件大小: {sitemap['size_bytes']:,} 字节")
            report_lines.append("")
            report_lines.append("### 页面类型统计")
            for page_type, count in sitemap['page_counts'].items():
                status = "✅" if count > 0 else "❌"
                report_lines.append(f"- {status} {page_type}: {count}")
        else:
            report_lines.append(f"- ❌ 状态: 错误 - {sitemap.get('error', 'Unknown')}")
        
        report_lines.append("")
        
        # Robots.txt状态
        robots = results['robots_status']
        report_lines.append("## Robots.txt状态")
        if robots['status'] == 'success':
            report_lines.append(f"- ✅ 状态: 正常")
            report_lines.append(f"- 📊 所有检查通过: {'是' if robots['all_checks_passed'] else '否'}")
            report_lines.append("")
            report_lines.append("### 配置检查")
            for check_name, passed in robots['checks'].items():
                status = "✅" if passed else "❌"
                report_lines.append(f"- {status} {check_name}")
        else:
            report_lines.append(f"- ❌ 状态: 错误 - {robots.get('error', 'Unknown')}")
        
        report_lines.append("")
        
        # 页面可访问性
        report_lines.append("## 页面可访问性测试")
        for page_type, page_data in results['page_accessibility'].items():
            if 'error' in page_data:
                report_lines.append(f"### {page_type} - ❌ 错误")
                report_lines.append(f"- 错误: {page_data['error']}")
            else:
                status = "✅" if page_data['status_code'] == 200 else "❌"
                report_lines.append(f"### {page_type} - {status}")
                report_lines.append(f"- URL: {page_data['url']}")
                report_lines.append(f"- 状态码: {page_data['status_code']}")
                report_lines.append(f"- 响应时间: {page_data['response_time']:.2f}s")
                report_lines.append(f"- 内容长度: {page_data['content_length']:,} 字节")
                
                if page_data['status_code'] == 200:
                    seo_checks = ['has_title', 'has_meta_description', 'has_canonical', 'has_og_tags', 'has_structured_data']
                    report_lines.append("- SEO元素:")
                    for check in seo_checks:
                        if check in page_data:
                            status = "✅" if page_data[check] else "❌"
                            report_lines.append(f"  - {status} {check}")
                
                # 爬虫测试结果
                if 'crawler_tests' in page_data:
                    report_lines.append("- 爬虫测试:")
                    for crawler, test_result in page_data['crawler_tests'].items():
                        if 'error' in test_result:
                            report_lines.append(f"  - ❌ {crawler}: {test_result['error']}")
                        else:
                            prerender_status = "预渲染" if test_result.get('is_prerendered', False) else "标准"
                            report_lines.append(f"  - ✅ {crawler}: {test_result['status_code']} ({prerender_status})")
            
            report_lines.append("")
        
        return "\n".join(report_lines)
    
    def save_results(self, results: Dict, filename: str = None):
        """保存监控结果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"seo_monitoring_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"监控结果已保存到: {filename}")
        
        # 生成并保存报告
        report_filename = filename.replace('.json', '_report.md')
        report_content = self.generate_report(results)
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"监控报告已保存到: {report_filename}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SEO监控脚本')
    parser.add_argument('--url', default='https://quotese.com', help='监控的网站URL')
    parser.add_argument('--output', help='输出文件名')
    
    args = parser.parse_args()
    
    try:
        monitor = SEOMonitor(args.url)
        results = monitor.run_full_monitoring()
        monitor.save_results(results, args.output)
        
        # 输出简要统计
        print("\n📊 监控完成！")
        print(f"Sitemap状态: {'✅ 正常' if results['sitemap_status']['status'] == 'success' else '❌ 异常'}")
        print(f"Robots.txt状态: {'✅ 正常' if results['robots_status']['status'] == 'success' else '❌ 异常'}")
        
        accessible_pages = sum(1 for page_data in results['page_accessibility'].values() 
                             if page_data.get('status_code') == 200)
        total_pages = len(results['page_accessibility'])
        print(f"页面可访问性: {accessible_pages}/{total_pages} 页面正常")
        
    except Exception as e:
        logger.error(f"监控失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
