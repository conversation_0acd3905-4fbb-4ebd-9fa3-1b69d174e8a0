#!/usr/bin/env python3
"""
生产环境映射表数据同步脚本
用于从生产环境GraphQL API查询真实的实体ID并更新映射表

使用方法:
    python3 scripts/sync-production-mapping.py
    
功能:
    1. 查询生产环境的真实实体ID
    2. 过滤高质量的实体数据
    3. 生成更新后的映射表配置
    4. 验证数据质量和一致性

@version 1.0.0
@date 2025-06-27
<AUTHOR>
"""

import requests
import json
import re
import time
from typing import Dict, List, Optional, Tuple

class ProductionMappingSyncer:
    def __init__(self):
        self.api_url = "https://api.quotese.com/graphql/"
        self.headers = {
            "Content-Type": "application/json",
            "User-Agent": "Quotese-Mapping-Syncer/1.0"
        }
        
        # 高质量实体列表 (基于常见性和重要性)
        self.target_categories = [
            'life', 'love', 'success', 'wisdom', 'happiness', 'motivation',
            'inspiration', 'hope', 'courage', 'strength', 'friendship',
            'family', 'leadership', 'business', 'education', 'health',
            'creativity', 'art', 'science', 'technology', 'philosophy',
            'spirituality', 'peace', 'freedom', 'truth', 'knowledge',
            'learning', 'growth', 'change', 'perseverance', 'patience',
            'kindness', 'compassion', 'gratitude', 'forgiveness', 'trust',
            'respect', 'honesty', 'integrity', 'responsibility', 'discipline',
            'focus', 'mindfulness', 'balance', 'wellness', 'fitness',
            'nature', 'beauty', 'time', 'future', 'dreams'
        ]
        
        self.target_authors = [
            'Albert Einstein', 'Steve Jobs', 'Mark Twain', 'Oscar Wilde',
            'Winston Churchill', 'Maya Angelou', 'Nelson Mandela',
            'Martin Luther King Jr.', 'Mahatma Gandhi', 'Benjamin Franklin',
            'Abraham Lincoln', 'John F. Kennedy', 'Theodore Roosevelt',
            'Ralph Waldo Emerson', 'Henry David Thoreau', 'William Shakespeare',
            'Aristotle', 'Plato', 'Confucius', 'Lao Tzu', 'Buddha',
            'Socrates', 'Marcus Aurelius', 'Seneca', 'Epictetus',
            'Dale Carnegie', 'Stephen Covey', 'Tony Robbins', 'Oprah Winfrey',
            'Bill Gates', 'Warren Buffett', 'Elon Musk', 'Richard Branson',
            'Walt Disney', 'Henry Ford', 'Thomas Edison', 'Nikola Tesla',
            'Marie Curie', 'Stephen Hawking', 'Charles Darwin', 'Isaac Newton',
            'Leonardo da Vinci', 'Pablo Picasso', 'Vincent van Gogh',
            'Frida Kahlo', 'Michelangelo', 'Mozart', 'Beethoven',
            'Jane Austen', 'Virginia Woolf', 'Ernest Hemingway'
        ]
        
        self.target_sources = [
            'Interview', 'Speech', 'Book', 'Article', 'Biography',
            'Autobiography', 'Essay', 'Letter', 'Diary', 'Memoir',
            'TED Talk', 'Podcast', 'Documentary', 'Conference',
            'Think and Grow Rich', 'The 7 Habits of Highly Effective People',
            'How to Win Friends and Influence People', 'The Art of War',
            'Meditations', 'The Republic', 'Walden', 'Self-Reliance',
            'The Wealth of Nations', 'The Origin of Species',
            'A Brief History of Time', 'The Innovator\'s Dilemma'
        ]
        
        self.results = {
            'categories': {},
            'authors': {},
            'sources': {},
            'stats': {
                'total_queries': 0,
                'successful_matches': 0,
                'failed_matches': 0,
                'data_quality_issues': 0
            }
        }
    
    def query_graphql(self, query: str) -> Optional[Dict]:
        """执行GraphQL查询"""
        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json={"query": query},
                timeout=10
            )
            
            self.results['stats']['total_queries'] += 1
            
            if response.status_code == 200:
                data = response.json()
                if 'errors' in data:
                    print(f"GraphQL错误: {data['errors']}")
                    self.results['stats']['failed_matches'] += 1
                    return None
                return data.get('data')
            else:
                print(f"HTTP错误: {response.status_code}")
                self.results['stats']['failed_matches'] += 1
                return None
                
        except Exception as e:
            print(f"查询异常: {e}")
            self.results['stats']['failed_matches'] += 1
            return None
    
    def create_slug(self, name: str) -> str:
        """创建符合前端URL路由的slug"""
        slug = name.lower()
        slug = re.sub(r'[^\w\s-]', '', slug)
        slug = re.sub(r'\s+', '-', slug)
        slug = re.sub(r'-+', '-', slug)
        slug = slug.strip('-')
        return slug
    
    def validate_entity_name(self, name: str, entity_type: str) -> bool:
        """验证实体名称的质量"""
        if not name or len(name.strip()) < 2:
            return False
        
        name = name.strip()
        
        # 过滤明显无效的名称
        invalid_patterns = [
            r'^[-_"\']+',  # 以特殊字符开头
            r'^\d+$',      # 纯数字
            r'^[^a-zA-Z]*$',  # 不包含字母
            r'^\s*$',      # 空白
        ]
        
        for pattern in invalid_patterns:
            if re.match(pattern, name):
                self.results['stats']['data_quality_issues'] += 1
                return False
        
        return True
    
    def search_entity_by_name(self, name: str, entity_type: str) -> Optional[Tuple[int, str]]:
        """通过名称搜索实体"""
        if entity_type == 'categories':
            query = f'''
            query {{
                categories(first: 10, name_Icontains: "{name}") {{
                    id
                    name
                    quotesCount
                }}
            }}
            '''
        elif entity_type == 'authors':
            query = f'''
            query {{
                authors(first: 10, name_Icontains: "{name}") {{
                    id
                    name
                    quotesCount
                }}
            }}
            '''
        elif entity_type == 'sources':
            query = f'''
            query {{
                sources(first: 10, name_Icontains: "{name}") {{
                    id
                    name
                    quotesCount
                }}
            }}
            '''
        else:
            return None
        
        data = self.query_graphql(query)
        if not data or entity_type not in data:
            return None
        
        entities = data[entity_type]
        
        # 寻找最佳匹配
        best_match = None
        best_score = 0
        
        for entity in entities:
            entity_name = entity.get('name', '').strip()
            if not self.validate_entity_name(entity_name, entity_type):
                continue
            
            # 计算匹配分数
            score = 0
            if entity_name.lower() == name.lower():
                score = 100  # 完全匹配
            elif name.lower() in entity_name.lower():
                score = 80   # 包含匹配
            elif entity_name.lower() in name.lower():
                score = 60   # 被包含匹配
            
            # 考虑引用数量
            quotes_count = entity.get('quotesCount', 0)
            if quotes_count > 0:
                score += min(quotes_count, 20)  # 最多加20分
            
            if score > best_score:
                best_score = score
                best_match = (entity['id'], entity_name)
        
        if best_match and best_score >= 60:  # 最低匹配阈值
            self.results['stats']['successful_matches'] += 1
            return best_match
        else:
            self.results['stats']['failed_matches'] += 1
            return None
    
    def sync_categories(self):
        """同步类别数据"""
        print("🔄 同步Categories数据...")
        
        for category_name in self.target_categories:
            print(f"  查询: {category_name}")
            result = self.search_entity_by_name(category_name, 'categories')
            
            if result:
                entity_id, actual_name = result
                slug = self.create_slug(category_name)
                self.results['categories'][slug] = {
                    'id': entity_id,
                    'name': actual_name,
                    'slug': slug
                }
                print(f"    ✅ 找到: {actual_name} (ID: {entity_id})")
            else:
                print(f"    ❌ 未找到: {category_name}")
            
            time.sleep(0.5)  # 避免请求过于频繁
    
    def sync_authors(self):
        """同步作者数据"""
        print("🔄 同步Authors数据...")
        
        for author_name in self.target_authors:
            print(f"  查询: {author_name}")
            result = self.search_entity_by_name(author_name, 'authors')
            
            if result:
                entity_id, actual_name = result
                slug = self.create_slug(author_name)
                self.results['authors'][slug] = {
                    'id': entity_id,
                    'name': actual_name,
                    'slug': slug
                }
                print(f"    ✅ 找到: {actual_name} (ID: {entity_id})")
            else:
                print(f"    ❌ 未找到: {author_name}")
            
            time.sleep(0.5)  # 避免请求过于频繁
    
    def sync_sources(self):
        """同步来源数据"""
        print("🔄 同步Sources数据...")
        
        for source_name in self.target_sources:
            print(f"  查询: {source_name}")
            result = self.search_entity_by_name(source_name, 'sources')
            
            if result:
                entity_id, actual_name = result
                slug = self.create_slug(source_name)
                self.results['sources'][slug] = {
                    'id': entity_id,
                    'name': actual_name,
                    'slug': slug
                }
                print(f"    ✅ 找到: {actual_name} (ID: {entity_id})")
            else:
                print(f"    ❌ 未找到: {source_name}")
            
            time.sleep(0.5)  # 避免请求过于频繁
    
    def generate_mapping_config(self) -> str:
        """生成映射表配置代码"""
        config_lines = []
        
        # Categories
        config_lines.append("    categories: {")
        for slug, data in sorted(self.results['categories'].items()):
            config_lines.append(f"        '{slug}': {data['id']},  // {data['name']}")
        config_lines.append("    },")
        config_lines.append("")
        
        # Authors
        config_lines.append("    authors: {")
        for slug, data in sorted(self.results['authors'].items()):
            config_lines.append(f"        '{slug}': {data['id']},  // {data['name']}")
        config_lines.append("    },")
        config_lines.append("")
        
        # Sources
        config_lines.append("    sources: {")
        for slug, data in sorted(self.results['sources'].items()):
            config_lines.append(f"        '{slug}': {data['id']},  // {data['name']}")
        config_lines.append("    }")
        
        return "\n".join(config_lines)
    
    def print_summary(self):
        """打印同步结果摘要"""
        stats = self.results['stats']
        
        print("\n" + "="*60)
        print("📊 同步结果摘要")
        print("="*60)
        print(f"总查询次数: {stats['total_queries']}")
        print(f"成功匹配: {stats['successful_matches']}")
        print(f"失败匹配: {stats['failed_matches']}")
        print(f"数据质量问题: {stats['data_quality_issues']}")
        print(f"成功率: {stats['successful_matches']/(stats['successful_matches']+stats['failed_matches'])*100:.1f}%")
        print()
        print(f"Categories: {len(self.results['categories'])} 个")
        print(f"Authors: <AUTHORS>
        print(f"Sources: {len(self.results['sources'])} 个")
        print(f"总映射实体: {len(self.results['categories']) + len(self.results['authors']) + len(self.results['sources'])} 个")
    
    def run(self):
        """执行完整的同步流程"""
        print("🚀 开始生产环境映射表数据同步...")
        print(f"目标API: {self.api_url}")
        print()
        
        # 执行同步
        self.sync_categories()
        self.sync_authors()
        self.sync_sources()
        
        # 打印结果
        self.print_summary()
        
        # 生成配置
        if self.results['stats']['successful_matches'] > 0:
            print("\n" + "="*60)
            print("📝 生成的映射表配置:")
            print("="*60)
            print(self.generate_mapping_config())
            
            # 保存到文件
            with open('production-mapping-config.js', 'w', encoding='utf-8') as f:
                f.write("// 生产环境映射表配置\n")
                f.write("// 自动生成时间: " + time.strftime("%Y-%m-%d %H:%M:%S") + "\n\n")
                f.write("const PRODUCTION_ENTITY_IDS = {\n")
                f.write(self.generate_mapping_config())
                f.write("\n};\n")
            
            print(f"\n✅ 配置已保存到: production-mapping-config.js")
        else:
            print("\n❌ 没有成功匹配的实体，无法生成配置")

if __name__ == "__main__":
    syncer = ProductionMappingSyncer()
    syncer.run()
