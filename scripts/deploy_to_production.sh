#!/bin/bash
# Quotese.com 生产环境部署脚本
# 自动化部署URL重构项目到生产环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
DOMAIN="quotese.com"
WEB_ROOT="/var/www/quotese.com"
NGINX_SITES="/etc/nginx/sites-available"
NGINX_CONF_DIR="/etc/nginx/conf.d"
BACKUP_DIR="/backup/quotese_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="/var/log/quotese_deployment_$(date +%Y%m%d_%H%M%S).log"

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
CONFIG_DIR="$PROJECT_ROOT/config"
BACKEND_DIR="$PROJECT_ROOT/backend"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"

# 函数：打印带颜色的消息
print_status() {
    local status=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ [$timestamp] $message${NC}" | tee -a "$LOG_FILE"
            ;;
        "ERROR")
            echo -e "${RED}❌ [$timestamp] $message${NC}" | tee -a "$LOG_FILE"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️ [$timestamp] $message${NC}" | tee -a "$LOG_FILE"
            ;;
        "INFO")
            echo -e "${BLUE}🔍 [$timestamp] $message${NC}" | tee -a "$LOG_FILE"
            ;;
    esac
}

# 函数：检查权限
check_permissions() {
    print_status "INFO" "检查部署权限..."
    
    if [ "$EUID" -ne 0 ]; then
        print_status "ERROR" "此脚本需要root权限运行"
        exit 1
    fi
    
    if [ ! -d "$WEB_ROOT" ]; then
        print_status "ERROR" "网站根目录不存在: $WEB_ROOT"
        exit 1
    fi
    
    if [ ! -d "$NGINX_SITES" ]; then
        print_status "ERROR" "Nginx配置目录不存在: $NGINX_SITES"
        exit 1
    fi
    
    print_status "SUCCESS" "权限检查通过"
}

# 函数：创建备份
create_backup() {
    print_status "INFO" "创建备份..."
    
    mkdir -p "$BACKUP_DIR"
    
    # 备份Nginx配置
    if [ -f "$NGINX_SITES/$DOMAIN" ]; then
        cp "$NGINX_SITES/$DOMAIN" "$BACKUP_DIR/nginx_$DOMAIN.conf"
        print_status "SUCCESS" "Nginx配置已备份"
    fi
    
    # 备份网站文件
    if [ -d "$WEB_ROOT" ]; then
        rsync -av "$WEB_ROOT/" "$BACKUP_DIR/www/" --exclude="*.log"
        print_status "SUCCESS" "网站文件已备份到: $BACKUP_DIR"
    fi
    
    # 记录备份信息
    echo "备份时间: $(date)" > "$BACKUP_DIR/backup_info.txt"
    echo "备份路径: $BACKUP_DIR" >> "$BACKUP_DIR/backup_info.txt"
    echo "项目版本: $(git rev-parse HEAD 2>/dev/null || echo 'unknown')" >> "$BACKUP_DIR/backup_info.txt"
}

# 函数：部署前端文件
deploy_frontend() {
    print_status "INFO" "部署前端文件..."
    
    # 复制HTML文件
    cp "$FRONTEND_DIR"/*.html "$WEB_ROOT/"
    print_status "SUCCESS" "HTML文件已部署"
    
    # 复制静态资源
    rsync -av "$FRONTEND_DIR/css/" "$WEB_ROOT/css/"
    rsync -av "$FRONTEND_DIR/js/" "$WEB_ROOT/js/"
    rsync -av "$FRONTEND_DIR/images/" "$WEB_ROOT/images/" 2>/dev/null || true
    rsync -av "$FRONTEND_DIR/components/" "$WEB_ROOT/components/"
    print_status "SUCCESS" "静态资源已部署"
    
    # 复制SEO文件
    cp "$FRONTEND_DIR/robots.txt" "$WEB_ROOT/"
    cp "$FRONTEND_DIR/sitemap.xml" "$WEB_ROOT/" 2>/dev/null || true
    print_status "SUCCESS" "SEO文件已部署"
    
    # 设置文件权限
    chown -R www-data:www-data "$WEB_ROOT"
    chmod -R 644 "$WEB_ROOT"
    find "$WEB_ROOT" -type d -exec chmod 755 {} \;
    print_status "SUCCESS" "文件权限已设置"
}

# 函数：部署Nginx配置
deploy_nginx_config() {
    print_status "INFO" "部署Nginx配置..."
    
    # 复制主配置文件
    cp "$CONFIG_DIR/nginx_frontend.conf" "$NGINX_SITES/$DOMAIN"
    
    # 复制重定向配置
    cp "$CONFIG_DIR/nginx_redirects.conf" "$NGINX_CONF_DIR/quotese_redirects.conf"
    
    # 更新配置文件中的路径
    sed -i "s|/path/to/config/nginx_redirects.conf|$NGINX_CONF_DIR/quotese_redirects.conf|g" "$NGINX_SITES/$DOMAIN"
    
    # 更新网站根目录路径
    sed -i "s|/var/www/html|$WEB_ROOT|g" "$NGINX_SITES/$DOMAIN"
    
    print_status "SUCCESS" "Nginx配置已部署"
}

# 函数：验证Nginx配置
validate_nginx_config() {
    print_status "INFO" "验证Nginx配置..."
    
    if nginx -t; then
        print_status "SUCCESS" "Nginx配置语法正确"
        return 0
    else
        print_status "ERROR" "Nginx配置语法错误"
        return 1
    fi
}

# 函数：重新加载Nginx
reload_nginx() {
    print_status "INFO" "重新加载Nginx..."
    
    if systemctl reload nginx; then
        print_status "SUCCESS" "Nginx已重新加载"
        return 0
    else
        print_status "ERROR" "Nginx重新加载失败"
        return 1
    fi
}

# 函数：生成Sitemap
generate_sitemap() {
    print_status "INFO" "生成Sitemap..."
    
    if [ -f "$BACKEND_DIR/generate_sitemap.py" ]; then
        cd "$BACKEND_DIR"
        if python3 generate_sitemap.py; then
            cp "../frontend/sitemap.xml" "$WEB_ROOT/"
            print_status "SUCCESS" "Sitemap已生成并部署"
        else
            print_status "WARNING" "Sitemap生成失败，使用现有文件"
        fi
        cd - > /dev/null
    else
        print_status "WARNING" "Sitemap生成脚本不存在"
    fi
}

# 函数：运行部署验证
run_verification() {
    print_status "INFO" "运行部署验证..."
    
    if [ -f "$SCRIPTS_DIR/deployment_verification.sh" ]; then
        if bash "$SCRIPTS_DIR/deployment_verification.sh"; then
            print_status "SUCCESS" "部署验证通过"
            return 0
        else
            print_status "ERROR" "部署验证失败"
            return 1
        fi
    else
        print_status "WARNING" "验证脚本不存在，跳过自动验证"
        return 0
    fi
}

# 函数：回滚部署
rollback_deployment() {
    print_status "WARNING" "开始回滚部署..."
    
    # 恢复Nginx配置
    if [ -f "$BACKUP_DIR/nginx_$DOMAIN.conf" ]; then
        cp "$BACKUP_DIR/nginx_$DOMAIN.conf" "$NGINX_SITES/$DOMAIN"
        print_status "INFO" "Nginx配置已回滚"
    fi
    
    # 恢复网站文件
    if [ -d "$BACKUP_DIR/www" ]; then
        rsync -av "$BACKUP_DIR/www/" "$WEB_ROOT/"
        print_status "INFO" "网站文件已回滚"
    fi
    
    # 重新加载Nginx
    if nginx -t && systemctl reload nginx; then
        print_status "SUCCESS" "回滚完成，Nginx已重新加载"
    else
        print_status "ERROR" "回滚后Nginx配置仍有问题"
    fi
}

# 函数：清理临时文件
cleanup() {
    print_status "INFO" "清理临时文件..."
    
    # 清理旧的日志文件（保留最近7天）
    find /var/log -name "quotese_deployment_*.log" -mtime +7 -delete 2>/dev/null || true
    
    # 清理旧的备份（保留最近30天）
    find /backup -name "quotese_*" -type d -mtime +30 -exec rm -rf {} \; 2>/dev/null || true
    
    print_status "SUCCESS" "清理完成"
}

# 函数：发送通知
send_notification() {
    local status=$1
    local message=$2
    
    # 这里可以添加邮件、Slack或其他通知方式
    echo "部署通知: $status - $message" >> /var/log/deployment_notifications.log
    
    # 示例：发送邮件通知（需要配置邮件服务）
    # echo "$message" | mail -s "Quotese部署通知: $status" <EMAIL>
}

# 主部署函数
main() {
    echo "========================================"
    echo "Quotese.com 生产环境部署"
    echo "时间: $(date)"
    echo "项目路径: $PROJECT_ROOT"
    echo "日志文件: $LOG_FILE"
    echo "========================================"
    
    # 检查参数
    if [ "$1" = "--rollback" ]; then
        if [ -z "$2" ]; then
            print_status "ERROR" "请指定备份目录路径"
            exit 1
        fi
        BACKUP_DIR="$2"
        rollback_deployment
        exit 0
    fi
    
    # 确认部署
    if [ "$1" != "--force" ]; then
        echo -n "确认要部署到生产环境吗？(y/N): "
        read -r confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            print_status "INFO" "部署已取消"
            exit 0
        fi
    fi
    
    # 开始部署流程
    print_status "INFO" "开始部署流程..."
    
    # 1. 检查权限
    check_permissions
    
    # 2. 创建备份
    create_backup
    
    # 3. 部署前端文件
    if ! deploy_frontend; then
        print_status "ERROR" "前端文件部署失败"
        rollback_deployment
        exit 1
    fi
    
    # 4. 生成Sitemap
    generate_sitemap
    
    # 5. 部署Nginx配置
    if ! deploy_nginx_config; then
        print_status "ERROR" "Nginx配置部署失败"
        rollback_deployment
        exit 1
    fi
    
    # 6. 验证Nginx配置
    if ! validate_nginx_config; then
        print_status "ERROR" "Nginx配置验证失败"
        rollback_deployment
        exit 1
    fi
    
    # 7. 重新加载Nginx
    if ! reload_nginx; then
        print_status "ERROR" "Nginx重新加载失败"
        rollback_deployment
        exit 1
    fi
    
    # 8. 等待服务稳定
    print_status "INFO" "等待服务稳定..."
    sleep 5
    
    # 9. 运行验证
    if ! run_verification; then
        print_status "ERROR" "部署验证失败"
        echo -n "是否要回滚部署？(y/N): "
        read -r rollback_confirm
        if [ "$rollback_confirm" = "y" ] || [ "$rollback_confirm" = "Y" ]; then
            rollback_deployment
            exit 1
        else
            print_status "WARNING" "部署完成但验证失败，请手动检查"
        fi
    fi
    
    # 10. 清理
    cleanup
    
    # 部署成功
    print_status "SUCCESS" "部署成功完成！"
    echo "========================================"
    echo "部署总结:"
    echo "- 备份路径: $BACKUP_DIR"
    echo "- 日志文件: $LOG_FILE"
    echo "- 网站地址: https://$DOMAIN"
    echo "========================================"
    
    # 发送成功通知
    send_notification "SUCCESS" "Quotese.com URL重构项目部署成功"
}

# 显示帮助信息
show_help() {
    echo "Quotese.com 生产环境部署脚本"
    echo ""
    echo "用法:"
    echo "  $0                    # 交互式部署"
    echo "  $0 --force           # 强制部署（跳过确认）"
    echo "  $0 --rollback <path> # 回滚到指定备份"
    echo "  $0 --help           # 显示帮助信息"
    echo ""
    echo "示例:"
    echo "  sudo $0"
    echo "  sudo $0 --force"
    echo "  sudo $0 --rollback /backup/quotese_20250617_093000"
}

# 处理命令行参数
case "$1" in
    "--help"|"-h")
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
